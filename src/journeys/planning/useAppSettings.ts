import { StyleSheet } from 'react-native';
import { useSettings } from '../../contexts/SettingsContext';

/**
 * Hook to provide app settings and derived styles/configurations
 * This demonstrates how settings can be applied throughout the app
 */
export const useAppSettings = () => {
  const { settings, updateSetting, resetSettings, isLoading, isDarkMode, systemColorScheme } = useSettings();

  // Derive current theme from settings
  const getCurrentTheme = () => {
    // This would typically come from a theme system
    // For now, return a basic theme object based on dark mode
    return {
      background: isDarkMode ? '#1A1A1A' : '#FAFAFA',
      backgroundSecondary: isDarkMode ? '#2A2A2A' : '#FFFFFF',
      surface: isDarkMode ? '#2A2A2A' : '#FFFFFF',
      text: isDarkMode ? '#E5E5E5' : '#393939',
      textPrimary: isDarkMode ? '#E5E5E5' : '#393939',
      textSecondary: isDarkMode ? '#B0B0B0' : '#6B7280',
      border: isDarkMode ? '#404040' : '#E5E7EB',
      borderLight: isDarkMode ? '#404040' : '#E5E7EB',
      primary: '#9CAF88',
      secondary: '#CBC3E3',
      accent: '#F3E0DA',
      accent1: '#F3E0DA',
      success: '#10B981',
      warning: '#F59E0B',
      error: '#EF4444',
      info: '#3B82F6',
    };
  };

  // Derive font sizes based on settings
  const getFontSizes = () => {
    const baseSize = {
      small: { title: 18, body: 14, caption: 12 },
      medium: { title: 20, body: 16, caption: 14 },
      large: { title: 24, body: 18, caption: 16 },
    };
    return baseSize[settings.fontSize];
  };

  // Get theme colors from the current theme
  const getThemeColors = () => {
    const currentTheme = getCurrentTheme();

    return {
      background: currentTheme.background,
      surface: currentTheme.backgroundSecondary,
      text: currentTheme.textPrimary,
      textSecondary: currentTheme.textSecondary,
      border: currentTheme.borderLight,
      primary: currentTheme.primary,
      secondary: currentTheme.secondary,
      accent: currentTheme.accent1,
      success: currentTheme.success,
      warning: currentTheme.warning,
      error: currentTheme.error,
      info: currentTheme.info,
    };
  };

  // Create dynamic styles based on settings
  const createDynamicStyles = () => {
    const fontSizes = getFontSizes();
    const themeColors = getThemeColors();

    return StyleSheet.create({
      container: {
        backgroundColor: themeColors.background,
        flex: 1,
      },
      surface: {
        backgroundColor: themeColors.surface,
        borderColor: themeColors.border,
      },
      title: {
        fontSize: fontSizes.title,
        color: themeColors.text,
        fontWeight: '600',
      },
      body: {
        fontSize: fontSizes.body,
        color: themeColors.text,
        lineHeight: fontSizes.body * 1.4,
      },
      caption: {
        fontSize: fontSizes.caption,
        color: themeColors.textSecondary,
      },
      card: {
        backgroundColor: themeColors.surface,
        borderRadius: 12,
        padding: 16,
        marginVertical: 8,
        borderWidth: 1,
        borderColor: themeColors.border,
      },
      // Button styles
      button: {
        backgroundColor: themeColors.primary,
        borderRadius: 8,
        padding: 12,
      },
      buttonSecondary: {
        backgroundColor: themeColors.secondary,
        borderRadius: 8,
        padding: 12,
      },
      buttonText: {
        color: themeColors.background,
        fontSize: fontSizes.body,
        fontWeight: '600',
        textAlign: 'center',
      },
      // Input styles
      input: {
        backgroundColor: themeColors.surface,
        borderColor: themeColors.border,
        borderWidth: 1,
        borderRadius: 8,
        padding: 12,
        color: themeColors.text,
        fontSize: fontSizes.body,
      },
    });
  };

  // Check if a feature is enabled
  const isFeatureEnabled = (feature: keyof typeof settings): boolean => {
    return Boolean(settings[feature]);
  };

  // Get accessibility props based on settings
  const getAccessibilityProps = (label: string, hint?: string) => {
    if (!settings.accessibility) {
      return {};
    }

    return {
      accessible: true,
      accessibilityLabel: label,
      accessibilityHint: hint,
      accessibilityRole: 'button' as const,
    };
  };

  // Get notification preferences
  const getNotificationConfig = () => {
    return {
      enabled: settings.notifications,
      sound: settings.soundEnabled,
      vibration: settings.vibrationEnabled,
    };
  };

  // Apply settings to component props
  const applySettingsToProps = (baseProps: any) => {
    const dynamicStyles = createDynamicStyles();

    return {
      ...baseProps,
      style: [dynamicStyles.container, baseProps.style],
      // Add other setting-based modifications here
    };
  };

  return {
    // Settings state
    settings,
    isLoading,

    // Settings actions
    updateSetting,
    resetSettings,

    // Derived values
    fontSizes: getFontSizes(),
    themeColors: getThemeColors(),
    dynamicStyles: createDynamicStyles(),

    // Utility functions
    isFeatureEnabled,
    getAccessibilityProps,
    getNotificationConfig,
    applySettingsToProps,

    // Convenience getters
    isDarkMode,
    isAccessibilityEnabled: settings.accessibility,
    currentLanguage: settings.language,
    currentFontSize: settings.fontSize,
    currentTheme: getCurrentTheme(),
    systemColorScheme,
  };
};

export default useAppSettings;
