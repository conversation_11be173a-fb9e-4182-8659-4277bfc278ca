/**
 * Planning Journey - Index
 *
 * Centralized exports for all planning and goal-setting functionality:
 * - User preferences and settings
 * - Goal setting and tracking
 * - Activity scheduling and planning
 * - Configuration management
 *
 * <AUTHOR> Us Team
 */

// Hooks
export { useAppSettings } from './useAppSettings';
export { useUserPreferences } from './useUserPreferences';

// Services
// userPreferencesService is available from other locations if needed

// Utils
export * from './config';

// Types
export type {
    UserSettings
} from '../../shared/types';

export type {
    AppSettings
} from '../../shared/services/contexts/SettingsContext';
