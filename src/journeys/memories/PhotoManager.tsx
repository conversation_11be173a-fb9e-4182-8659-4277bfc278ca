import {
    <PERSON><PERSON>ircle,
    ChevronLeft,
    ChevronRight,
    Plus,
    X
} from 'lucide-react-native';
import React, { useState } from 'react';
import {
    ActivityIndicator,
    Alert,
    Dimensions,
    Image,
    Modal,
    ScrollView,
    StyleSheet,
    Text,
    TouchableOpacity,
    View
} from 'react-native';
import { colors } from '../../utils/colors';
// requireAuthentication removed with onboarding functionality
import { imageStorageService } from './imageStorageService';

// Flexible photo interface that can handle both types
interface StoryPhoto {
  id: string;
  url?: string;
  localUri?: string;
  uri?: string; // For backward compatibility
  timestamp: number;
  caption?: string;
}

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

interface PhotoManagerProps {
  photos: StoryPhoto[];
  sectionTitle: string;
  mode: 'view' | 'edit';
  onAddPhoto?: (_photo: StoryPhoto) => void;
  onRemovePhoto?: (_photoId: string) => void;
  onUploadPhoto?: (_photo: StoryPhoto) => Promise<boolean>;
  maxPhotos?: number;
}

export default function PhotoManager({
  photos,
  sectionTitle,
  mode,
  onAddPhoto,
  onRemovePhoto,
  onUploadPhoto,
  maxPhotos = 5
}: PhotoManagerProps) {
  const [modalVisible, setModalVisible] = useState(false);
  const [selectedPhotoIndex, setSelectedPhotoIndex] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [uploadingPhotos, setUploadingPhotos] = useState<Set<string>>(new Set());
  // Authentication removed with onboarding functionality

  // Don't render if no photos and in view mode
  if (photos.length === 0 && mode === 'view') {
    return null;
  }

  const openModal = (index: number) => {
    setSelectedPhotoIndex(index);
    setModalVisible(true);
  };

  const closeModal = () => {
    setModalVisible(false);
  };

  const navigatePhoto = (direction: 'prev' | 'next') => {
    if (direction === 'prev') {
      setSelectedPhotoIndex(prev => prev > 0 ? prev - 1 : photos.length - 1);
    } else {
      setSelectedPhotoIndex(prev => prev < photos.length - 1 ? prev + 1 : 0);
    }
  };

  const requestPermissions = async () => {
    return await imageStorageService.requestPermissions();
  };

  const pickImage = async () => {
    if (photos.length >= maxPhotos) {
      Alert.alert(
        'Maximum Photos Reached',
        `You can add up to ${maxPhotos} photos for this section.`,
        [{ text: 'OK' }]
      );
      return;
    }

    // Authentication check removed with onboarding functionality
    // Proceed directly to image picking
    performImagePick();
  };

  const performImagePick = async () => {
    const hasPermission = await requestPermissions();
    if (!hasPermission) return;

    setIsLoading(true);
    try {
      const result = await imageStorageService.pickImageFromLibrary();

      if (!result.canceled && result.assets && result.assets[0]) {
        const asset = result.assets[0];
        const photo = imageStorageService.createStoryPhoto(asset.uri) as StoryPhoto;
        onAddPhoto?.(photo);

        // Upload to cloud if callback provided
        if (onUploadPhoto) {
          setUploadingPhotos(prev => new Set(prev).add(photo.id));
          try {
            await onUploadPhoto(photo);
          } catch (error) {
            console.error('Error uploading photo:', error);
          } finally {
            setUploadingPhotos(prev => {
              const newSet = new Set(prev);
              newSet.delete(photo.id);
              return newSet;
            });
          }
        }
      }
    } catch {
      Alert.alert('Error', 'Failed to pick image. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const takePhoto = async () => {
    if (photos.length >= maxPhotos) {
      Alert.alert(
        'Maximum Photos Reached',
        `You can add up to ${maxPhotos} photos for this section.`,
        [{ text: 'OK' }]
      );
      return;
    }

    setIsLoading(true);
    try {
      const result = await imageStorageService.takePhotoWithCamera();

      if (!result.canceled && result.assets && result.assets[0]) {
        const asset = result.assets[0];
        const photo = imageStorageService.createStoryPhoto(asset.uri) as StoryPhoto;
        onAddPhoto?.(photo);

        // Upload to cloud if callback provided
        if (onUploadPhoto) {
          setUploadingPhotos(prev => new Set(prev).add(photo.id));
          try {
            await onUploadPhoto(photo);
          } catch (error) {
            console.error('Error uploading photo:', error);
          } finally {
            setUploadingPhotos(prev => {
              const newSet = new Set(prev);
              newSet.delete(photo.id);
              return newSet;
            });
          }
        }
      }
    } catch {
      Alert.alert('Error', 'Failed to take photo. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const showImageOptions = () => {
    Alert.alert(
      'Add Photo',
      'Choose how you\'d like to add a photo to your story.',
      [
        { text: 'Camera', onPress: takePhoto },
        { text: 'Photo Library', onPress: pickImage },
        { text: 'Cancel', style: 'cancel' },
      ]
    );
  };

  const renderPhotoThumbnail = (photo: StoryPhoto, index: number) => (
    <TouchableOpacity
      key={photo.id}
      style={styles.photoWrapper}
      onPress={() => openModal(index)}
    >
      <Image
        source={{ uri: photo.url || photo.localUri || photo.uri || '' }}
        style={styles.photo}
        resizeMode="cover"
      />

      {/* Photo count overlay for view mode */}
      {mode === 'view' && (
        <View style={styles.photoOverlay}>
          <Text style={styles.photoCount}>{index + 1}</Text>
        </View>
      )}

      {/* Upload status overlay for edit mode */}
      {mode === 'edit' && uploadingPhotos.has(photo.id) && (
        <View style={styles.uploadOverlay}>
          <ActivityIndicator size="small" color={colors.white} />
          <Text style={styles.uploadText}>Uploading...</Text>
        </View>
      )}

      {/* Cloud sync indicator for edit mode */}
      {mode === 'edit' && photo.url && !uploadingPhotos.has(photo.id) && (
        <View style={styles.syncIndicator}>
          <CheckCircle size={12} color={colors.success} />
        </View>
      )}

      {/* Remove button for edit mode */}
      {mode === 'edit' && onRemovePhoto && (
        <TouchableOpacity
          style={styles.removeButton}
          onPress={() => onRemovePhoto(photo.id)}
        >
          <X size={16} color={colors.white} />
        </TouchableOpacity>
      )}
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.title}>
          {mode === 'edit' ? `Photos for ${sectionTitle}` : `Photos from ${sectionTitle}`}
        </Text>
        {mode === 'edit' && (
          <Text style={styles.subtitle}>
            {photos.length} of {maxPhotos} photos added
          </Text>
        )}
      </View>

      {/* Photo Grid */}
      {photos.length > 0 && (
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          style={styles.photoScrollView}
          contentContainerStyle={styles.photoContainer}
        >
          {photos.map((photo, index) => renderPhotoThumbnail(photo, index))}
        </ScrollView>
      )}

      {/* Add Photo Button (Edit Mode Only) */}
      {mode === 'edit' && photos.length < maxPhotos && (
        <TouchableOpacity
          style={[styles.addButton, isLoading && styles.addButtonDisabled]}
          onPress={showImageOptions}
          disabled={isLoading}
        >
          <View style={styles.addButtonContent}>
            {isLoading ? (
              <Text style={styles.addButtonText}>Adding...</Text>
            ) : (
              <>
                <Plus size={20} color={colors.primary} />
                <Text style={styles.addButtonText}>Add Photo</Text>
              </>
            )}
          </View>
        </TouchableOpacity>
      )}

      {/* Full Screen Modal */}
      <Modal
        visible={modalVisible}
        transparent={true}
        animationType="fade"
        onRequestClose={closeModal}
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <TouchableOpacity style={styles.closeButton} onPress={closeModal}>
              <X size={24} color={colors.white} />
            </TouchableOpacity>
            <Text style={styles.modalTitle}>
              {sectionTitle} - {selectedPhotoIndex + 1} of {photos.length}
            </Text>
          </View>

          <View style={styles.modalContent}>
            <TouchableOpacity
              style={styles.navButton}
              onPress={() => navigatePhoto('prev')}
            >
              <ChevronLeft size={32} color={colors.white} />
            </TouchableOpacity>

            <Image
              source={{ uri: photos[selectedPhotoIndex]?.url || photos[selectedPhotoIndex]?.localUri || photos[selectedPhotoIndex]?.uri || '' }}
              style={styles.fullSizePhoto}
              resizeMode="contain"
            />

            <TouchableOpacity
              style={styles.navButton}
              onPress={() => navigatePhoto('next')}
            >
              <ChevronRight size={32} color={colors.white} />
            </TouchableOpacity>
          </View>

          {photos[selectedPhotoIndex]?.caption && (
            <View style={styles.captionContainer}>
              <Text style={styles.caption}>{photos[selectedPhotoIndex].caption}</Text>
            </View>
          )}
        </View>
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    marginTop: 16,
  },
  header: {
    marginBottom: 12,
  },
  title: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.textGray,
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 12,
    color: colors.textSecondary,
  },
  photoScrollView: {
    marginBottom: 12,
  },
  photoContainer: {
    paddingRight: 16,
  },
  photoWrapper: {
    position: 'relative',
    marginRight: 12,
  },
  photo: {
    width: 80,
    height: 80,
    borderRadius: 12,
    backgroundColor: colors.backgroundTertiary,
  },
  photoOverlay: {
    position: 'absolute',
    bottom: 4,
    right: 4,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    borderRadius: 8,
    paddingHorizontal: 6,
    paddingVertical: 2,
  },
  photoCount: {
    fontSize: 10,
    fontWeight: '600',
    color: colors.white,
  },
  removeButton: {
    position: 'absolute',
    top: -6,
    right: -6,
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: colors.red,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 4,
  },
  addButton: {
    borderWidth: 2,
    borderColor: colors.lightPink,
    borderStyle: 'dashed',
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.backgroundPink,
  },
  addButtonDisabled: {
    opacity: 0.6,
  },
  addButtonContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  addButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.lightPink,
    marginLeft: 8,
  },
  uploadOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 8,
  },
  uploadText: {
    color: colors.white,
    fontSize: 12,
    marginTop: 4,
    fontWeight: '500',
  },
  syncIndicator: {
    position: 'absolute',
    top: 4,
    left: 4,
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    borderRadius: 10,
    width: 20,
    height: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  modalContainer: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.9)',
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingTop: 60,
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  closeButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  modalTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.white,
    flex: 1,
    textAlign: 'center',
    marginHorizontal: 20,
  },
  modalContent: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 20,
  },
  navButton: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    alignItems: 'center',
    justifyContent: 'center',
    marginHorizontal: 10,
  },
  fullSizePhoto: {
    width: screenWidth - 180,
    height: screenHeight - 200,
    borderRadius: 12,
  },
  captionContainer: {
    paddingHorizontal: 20,
    paddingBottom: 40,
    alignItems: 'center',
  },
  caption: {
    fontSize: 14,
    color: colors.white,
    textAlign: 'center',
    lineHeight: 20,
  },
});
