import * as FileSystem from 'expo-file-system';
// import { manipulateAsync, SaveFormat } from 'expo-image-manipulator'; // TODO: Install expo-image-manipulator package
import * as ImagePicker from 'expo-image-picker';
import { Alert } from 'react-native';
import { logger } from '../../shared/utils/logger';
import { supabase } from '../services/supabase/client';

export interface ImageUploadResult {
  success: boolean;
  url?: string;
  error?: string;
  retryable?: boolean;
}

export interface StoryPhoto {
  id: string;
  url?: string;
  localUri?: string;
  timestamp: number;
  caption?: string;
  uploadStatus?: 'pending' | 'uploading' | 'completed' | 'failed';
  retryCount?: number;
  fileSize?: number;
  compressedSize?: number;
}

export type ImageBucket = 'story-photos' | 'profile-pictures' | 'scrapbook-photos';

interface UploadQueueItem {
  photo: StoryPhoto;
  bucket: ImageBucket;
  userId: string;
  retryCount: number;
  maxRetries: number;
}

class EnhancedImageStorageService {
  private uploadQueue: UploadQueueItem[] = [];
  private isProcessingQueue = false;
  private readonly MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB
  private readonly COMPRESSION_QUALITY = 0.8;
  private readonly MAX_RETRIES = 3;

  /**
   * Request camera and media library permissions with better error handling
   */
  async requestPermissions(): Promise<boolean> {
    try {
      const [cameraStatus, mediaLibraryStatus] = await Promise.all([
        ImagePicker.requestCameraPermissionsAsync(),
        ImagePicker.requestMediaLibraryPermissionsAsync()
      ]);

      if (cameraStatus.status !== 'granted' || mediaLibraryStatus.status !== 'granted') {
        Alert.alert(
          'Permission Required',
          'We need access to your camera and photo library to add photos to your story. You can enable this in your device settings.',
          [
            { text: 'Cancel', style: 'cancel' },
            { text: 'Open Settings', onPress: () => ImagePicker.getMediaLibraryPermissionsAsync() }
          ]
        );
        return false;
      }

      return true;
    } catch (error) {
      logger.error('Error requesting permissions:', error);
      return false;
    }
  }

  /**
   * Pick image from library with compression and validation
   */
  async pickImageFromLibrary(): Promise<ImagePicker.ImagePickerResult> {
    const hasPermission = await this.requestPermissions();
    if (!hasPermission) {
      return { canceled: true } as ImagePicker.ImagePickerResult;
    }

    try {
      return await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [4, 3],
        quality: this.COMPRESSION_QUALITY,
        base64: false,
        exif: false, // Remove EXIF data for privacy
      });
    } catch (error) {
      logger.error('Error picking image from library:', error);
      return { canceled: true } as ImagePicker.ImagePickerResult;
    }
  }

  /**
   * Take photo with camera with compression and validation
   */
  async takePhotoWithCamera(): Promise<ImagePicker.ImagePickerResult> {
    const hasPermission = await this.requestPermissions();
    if (!hasPermission) {
      return { canceled: true } as ImagePicker.ImagePickerResult;
    }

    try {
      return await ImagePicker.launchCameraAsync({
        allowsEditing: true,
        aspect: [4, 3],
        quality: this.COMPRESSION_QUALITY,
        base64: false,
        exif: false, // Remove EXIF data for privacy
      });
    } catch (error) {
      logger.error('Error taking photo with camera:', error);
      return { canceled: true } as ImagePicker.ImagePickerResult;
    }
  }

  /**
   * Compress and optimize image before upload
   * TODO: Re-enable when expo-image-manipulator is installed
   */
  private async compressImage(uri: string): Promise<{ uri: string; size: number }> {
    try {
      // Get original file size
      const fileInfo = await FileSystem.getInfoAsync(uri);
      const originalSize = (fileInfo as FileSystem.FileInfo & { size?: number }).size || 0;

      // TODO: Re-enable compression when expo-image-manipulator is installed
      /*
      // Compress image if it's too large
      if (originalSize > this.MAX_FILE_SIZE) {
        const compressedImage = await manipulateAsync(
          uri,
          [{ resize: { width: 1920, height: 1440 } }], // Max 1920x1440
          {
            compress: this.COMPRESSION_QUALITY,
            format: SaveFormat.JPEG,
          }
        );

        const compressedInfo = await FileSystem.getInfoAsync(compressedImage.uri);
        return {
          uri: compressedImage.uri,
          size: (compressedInfo as FileSystem.FileInfo & { size?: number }).size || 0
        };
      }
      */

      return { uri, size: originalSize };
    } catch (error) {
      logger.error('Error compressing image:', error);
      return { uri, size: 0 };
    }
  }

  /**
   * Upload image to Supabase Storage with retry logic
   */
  async uploadImage(
    localUri: string,
    bucket: ImageBucket,
    userId: string,
    fileName?: string,
    retryCount = 0
  ): Promise<ImageUploadResult> {
    try {
      // Compress image first
      const { uri: compressedUri, size: _size } = await this.compressImage(localUri);

      // Generate unique filename if not provided
      const timestamp = Date.now();
      const randomId = Math.random().toString(36).substring(2, 15);
      const finalFileName = fileName || `image_${timestamp}_${randomId}.jpg`;

      // Create file path: userId/filename
      const filePath = `${userId}/${finalFileName}`;

      // Convert local URI to blob
      const response = await fetch(compressedUri);
      const blob = await response.blob();

      // Upload to Supabase Storage with timeout
      const uploadPromise = supabase.storage
        .from(bucket)
        .upload(filePath, blob, {
          contentType: 'image/jpeg',
          upsert: false
        });

      const timeoutPromise = new Promise((_, reject) =>
        setTimeout(() => reject(new Error('Upload timeout')), 30000)
      );

      const { data: _data, error } = await Promise.race([uploadPromise, timeoutPromise]) as any;

      if (error) {
        logger.error('Error uploading image to Supabase:', error);

        // Retry logic for certain errors
        if (retryCount < this.MAX_RETRIES && this.isRetryableError(error)) {
          logger.info(`Retrying upload, attempt ${retryCount + 1}/${this.MAX_RETRIES}`);
          await new Promise(resolve => setTimeout(resolve, 1000 * (retryCount + 1))); // Exponential backoff
          return this.uploadImage(localUri, bucket, userId, fileName, retryCount + 1);
        }

        return {
          success: false,
          error: error.message,
          retryable: this.isRetryableError(error)
        };
      }

      // Get public URL
      const { data: urlData } = supabase.storage
        .from(bucket)
        .getPublicUrl(filePath);

      return {
        success: true,
        url: urlData.publicUrl
      };

    } catch (error) {
      logger.error('Error in uploadImage:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        retryable: true
      };
    }
  }

  /**
   * Check if an error is retryable
   */
  private isRetryableError(error: any): boolean {
    const retryableErrors = [
      'network',
      'timeout',
      'connection',
      'ECONNRESET',
      'ETIMEDOUT'
    ];

    const errorMessage = error.message?.toLowerCase() || '';
    return retryableErrors.some(retryableError =>
      errorMessage.includes(retryableError)
    );
  }

  /**
   * Delete image from Supabase Storage
   */
  async deleteImage(bucket: ImageBucket, userId: string, fileName: string): Promise<boolean> {
    try {
      const filePath = `${userId}/${fileName}`;

      const { error } = await supabase.storage
        .from(bucket)
        .remove([filePath]);

      if (error) {
        logger.error('Error deleting image from Supabase:', error);
        return false;
      }

      return true;
    } catch (error) {
      logger.error('Error in deleteImage:', error);
      return false;
    }
  }

  /**
   * Create a StoryPhoto object with enhanced metadata
   */
  createStoryPhoto(
    localUri: string,
    cloudUrl?: string,
    caption?: string
  ): StoryPhoto {
    return {
      id: `photo_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`,
      url: cloudUrl,
      localUri: localUri,
      timestamp: Date.now(),
      caption: caption,
      uploadStatus: cloudUrl ? 'completed' : 'pending',
      retryCount: 0
    };
  }

  /**
   * Upload image and create StoryPhoto object with enhanced error handling
   */
  async uploadAndCreateStoryPhoto(
    localUri: string,
    bucket: ImageBucket,
    userId: string,
    caption?: string
  ): Promise<StoryPhoto | null> {
    try {
      // First create the photo object with local URI
      const photo = this.createStoryPhoto(localUri, undefined, caption);
      photo.uploadStatus = 'uploading';

      // Upload to cloud storage
      const uploadResult = await this.uploadImage(localUri, bucket, userId, `${photo.id}.jpg`);

      if (uploadResult.success && uploadResult.url) {
        // Update photo with cloud URL
        photo.url = uploadResult.url;
        photo.uploadStatus = 'completed';
        return photo;
      } else {
        logger.warn('Failed to upload image, keeping local URI:', uploadResult.error);
        photo.uploadStatus = uploadResult.retryable ? 'pending' : 'failed';
        return photo; // Return with local URI as fallback
      }
    } catch (error) {
      logger.error('Error in uploadAndCreateStoryPhoto:', error);
      return null;
    }
  }

  /**
   * Add photo to upload queue for background processing
   */
  addToUploadQueue(photo: StoryPhoto, bucket: ImageBucket, userId: string): void {
    const queueItem: UploadQueueItem = {
      photo,
      bucket,
      userId,
      retryCount: 0,
      maxRetries: this.MAX_RETRIES
    };

    this.uploadQueue.push(queueItem);
    this.processUploadQueue();
  }

  /**
   * Process upload queue in background
   */
  private async processUploadQueue(): Promise<void> {
    if (this.isProcessingQueue || this.uploadQueue.length === 0) {
      return;
    }

    this.isProcessingQueue = true;

    while (this.uploadQueue.length > 0) {
      const item = this.uploadQueue.shift();
      if (!item) continue;

      try {
        const result = await this.uploadImage(
          item.photo.localUri || item.photo.url || '',
          item.bucket,
          item.userId,
          `${item.photo.id}.jpg`,
          item.retryCount
        );

        if (result.success && result.url) {
          item.photo.url = result.url;
          item.photo.uploadStatus = 'completed';
        } else if (result.retryable && item.retryCount < item.maxRetries) {
          // Retry later
          item.retryCount++;
          this.uploadQueue.push(item);
        } else {
          item.photo.uploadStatus = 'failed';
        }
      } catch (error) {
        logger.error('Error processing upload queue item:', error);
        if (item.retryCount < item.maxRetries) {
          item.retryCount++;
          this.uploadQueue.push(item);
        } else {
          item.photo.uploadStatus = 'failed';
        }
      }

      // Small delay between uploads to avoid overwhelming the server
      await new Promise(resolve => setTimeout(resolve, 500));
    }

    this.isProcessingQueue = false;
  }

  /**
   * Get optimized image URL with transformations
   */
  getOptimizedImageUrl(bucket: ImageBucket, filePath: string, options?: {
    width?: number;
    height?: number;
    quality?: number;
    format?: 'webp' | 'jpeg' | 'png';
  }): string {
    const { data } = supabase.storage
      .from(bucket)
      .getPublicUrl(filePath);

    if (!options) {
      return data.publicUrl;
    }

    // Add transformation parameters
    const params = new URLSearchParams();
    if (options.width) params.append('width', options.width.toString());
    if (options.height) params.append('height', options.height.toString());
    if (options.quality) params.append('quality', options.quality.toString());
    if (options.format) params.append('format', options.format);

    const baseUrl = data.publicUrl;
    const separator = baseUrl.includes('?') ? '&' : '?';

    return `${baseUrl}${separator}${params.toString()}`;
  }

  /**
   * Batch upload multiple images with progress tracking
   */
  async uploadMultipleImages(
    localUris: string[],
    bucket: ImageBucket,
    userId: string,
    onProgress?: (_completed: number, _total: number) => void
  ): Promise<StoryPhoto[]> {
    const results: StoryPhoto[] = [];

    for (let i = 0; i < localUris.length; i++) {
      const photo = await this.uploadAndCreateStoryPhoto(localUris[i], bucket, userId);
      if (photo) {
        results.push(photo);
      }
      onProgress?.(i + 1, localUris.length);
    }

    return results;
  }

  /**
   * Clean up old local images and failed uploads
   */
  async cleanupLocalImages(photos: StoryPhoto[]): Promise<void> {
    try {
      const cleanupPromises = photos
        .filter(photo => photo.url && photo.localUri) // Only clean up if cloud URL exists
        .map(async (photo) => {
          try {
            await FileSystem.deleteAsync(photo.localUri!, { idempotent: true });
            logger.info('Cleaned up local image:', photo.id);
          } catch (error) {
            logger.warn('Failed to clean up local image:', { photoId: photo.id, error });
          }
        });

      await Promise.all(cleanupPromises);
      logger.info('Local image cleanup completed', { photoCount: photos.length });
    } catch (error) {
      logger.error('Error in cleanupLocalImages:', error);
    }
  }

  /**
   * Get upload queue status
   */
  getUploadQueueStatus(): { pending: number; failed: number } {
    const pending = this.uploadQueue.filter(item => item.photo.uploadStatus === 'pending').length;
    const failed = this.uploadQueue.filter(item => item.photo.uploadStatus === 'failed').length;

    return { pending, failed };
  }

  /**
   * Retry failed uploads
   */
  retryFailedUploads(): void {
    const failedItems = this.uploadQueue.filter(item => item.photo.uploadStatus === 'failed');
    failedItems.forEach(item => {
      item.photo.uploadStatus = 'pending';
      item.retryCount = 0;
    });

    if (failedItems.length > 0) {
      this.processUploadQueue();
    }
  }
}

export const enhancedImageStorageService = new EnhancedImageStorageService();
