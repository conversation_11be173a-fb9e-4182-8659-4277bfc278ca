import { logger } from '../../shared/utils/logger';
import { supabase } from '../services/supabase/client';

// ============================================================================
// TYPES & INTERFACES
// ============================================================================

export interface MilestoneTemplate {
  id: string;
  milestone_key: string;
  category: 'foundation' | 'getting_to_know' | 'commitment' | 'engagement' | 'modern';
  title: string;
  description: string;
  field_schema: {
    fields: MilestoneField[];
  };
  ui_config: {
    icon: string;
    color: string;
  };
  display_order: number;
  is_active: boolean;
  is_core_milestone: boolean;
  created_at: string;
  updated_at: string;
}

export type MilestonePrimitive = string | number | boolean | null;
export type MilestonePhoto = { url: string; id?: string };
export type MilestoneValue = MilestonePrimitive | string[] | MilestonePhoto[];

export interface FieldValidationConfig {
  required?: boolean;
  minLength?: number;
  maxLength?: number;
  min?: number;
  max?: number;
  pattern?: string; // regex string
  options?: string[]; // for select/checkbox_array
}

export interface MilestoneField {
  key: string;
  type:
    | 'text'
    | 'textarea'
    | 'date'
    | 'select'
    | 'boolean'
    | 'number'
    | 'photo_array'
    | 'text_array'
    | 'checkbox_array';
  label: string;
  required?: boolean;
  options?: string[];
  placeholder?: string;
  validation?: FieldValidationConfig;
}

export interface CoupleMilestone {
  id: string;
  couple_id: string;
  milestone_template_id: string;
  milestone_data: Record<string, MilestoneValue>;
  is_completed: boolean;
  completion_date?: string;
  completed_by?: string;
  timeline_event_id?: string;
  created_at: string;
  updated_at: string;

  // Joined data
  template?: MilestoneTemplate;
}

export interface MilestoneProgress {
  category: string;
  total_milestones: number;
  completed_milestones: number;
  completion_percentage: number;
}

// ============================================================================
// MILESTONE SERVICE CLASS
// ============================================================================

class MilestoneService {
  /**
   * Get all active milestone templates
   */
  async getMilestoneTemplates(): Promise<MilestoneTemplate[]> {
    try {
      const { data, error } = await supabase
        .from('milestone_templates')
        .select('*')
        .eq('is_active', true)
        .order('category, display_order');

      if (error) {
        logger.error('Error fetching milestone templates:', error);
        throw new Error(`Failed to fetch milestone templates: ${error.message}`);
      }

      return (data || []).map(item => ({
        ...item,
        category: item.category as "foundation" | "getting_to_know" | "commitment" | "engagement" | "modern",
        field_schema: (item.field_schema as unknown as { fields: MilestoneField[] }) || { fields: [] }
      })) as MilestoneTemplate[];
    } catch (error) {
      logger.error('Error in getMilestoneTemplates:', error);
      throw error;
    }
  }

  /**
   * Get milestone templates by category
   */
  async getMilestoneTemplatesByCategory(category: string): Promise<MilestoneTemplate[]> {
    try {
      const { data, error } = await supabase
        .from('milestone_templates')
        .select('*')
        .eq('category', category)
        .eq('is_active', true)
        .order('display_order');

      if (error) {
        logger.error('Error fetching milestone templates by category:', error);
        throw new Error(`Failed to fetch milestone templates: ${error.message}`);
      }

      return (data || []).map(item => ({
        ...item,
        field_schema: (item.field_schema as unknown as { fields: MilestoneField[] }) || { fields: [] }
      })) as MilestoneTemplate[];
    } catch (error) {
      logger.error('Error in getMilestoneTemplatesByCategory:', error);
      throw error;
    }
  }

  /**
   * Get all milestones for a couple with template data
   */
  async getCoupleMilestones(coupleId: string): Promise<CoupleMilestone[]> {
    try {
      const { data, error } = await supabase
        .from('couple_milestones')
        .select(`
          *,
          template:milestone_templates(*)
        `)
        .eq('couple_id', coupleId)
        .order('template.category, template.display_order');

      if (error) {
        logger.error('Error fetching couple milestones:', error);
        throw new Error(`Failed to fetch couple milestones: ${error.message}`);
      }

      return (data || []).map(item => ({
        ...item,
        milestone_data: (item.milestone_data as Record<string, MilestoneValue>) || {},
        is_completed: item.is_completed ?? false,
        template: item.template ? {
          ...item.template,
          field_schema: (item.template.field_schema as unknown as { fields: MilestoneField[] }) || { fields: [] }
        } : undefined
      })) as CoupleMilestone[];
    } catch (error) {
      logger.error('Error in getCoupleMilestones:', error);
      throw error;
    }
  }

  /**
   * Get completed milestones for a couple
   */
  async getCompletedMilestones(coupleId: string): Promise<CoupleMilestone[]> {
    try {
      const { data, error } = await supabase
        .from('couple_milestones')
        .select(`
          *,
          template:milestone_templates(*)
        `)
        .eq('couple_id', coupleId)
        .eq('is_completed', true)
        .order('completion_date DESC');

      if (error) {
        logger.error('Error fetching completed milestones:', error);
        throw new Error(`Failed to fetch completed milestones: ${error.message}`);
      }

      return (data || []).map(item => ({
        ...item,
        milestone_data: (item.milestone_data as Record<string, MilestoneValue>) || {},
        is_completed: item.is_completed ?? false,
        template: item.template ? {
          ...item.template,
          field_schema: (item.template.field_schema as unknown as { fields: MilestoneField[] }) || { fields: [] }
        } : undefined
      })) as CoupleMilestone[];
    } catch (error) {
      logger.error('Error in getCompletedMilestones:', error);
      throw error;
    }
  }

  /**
   * Initialize milestones for a new couple
   */
  async initializeCoupleMilestones(coupleId: string): Promise<number> {
    try {
      const { data, error } = await supabase
        .rpc('initialize_couple_milestones', { p_couple_id: coupleId });

      if (error) {
        logger.error('Error initializing couple milestones:', error);
        throw new Error(`Failed to initialize milestones: ${error.message}`);
      }

      logger.info(`Initialized ${data} milestones for couple ${coupleId}`);
      return data || 0;
    } catch (error) {
      logger.error('Error in initializeCoupleMilestones:', error);
      throw error;
    }
  }

  /**
   * Update milestone data
   */
  async updateMilestone(
    milestoneId: string,
    milestoneData: Record<string, MilestoneValue>,
    completedBy?: string
  ): Promise<CoupleMilestone | null> {
    try {
      // Determine if milestone should be marked as completed
      const isCompleted = this.shouldMarkAsCompleted(milestoneData);
      const completionDate = isCompleted ? new Date().toISOString().split('T')[0] : undefined;

      const updateData: Partial<CoupleMilestone> = {
        milestone_data: milestoneData,
        is_completed: isCompleted,
        completion_date: completionDate,
        completed_by: completedBy,
      };

      const { data, error } = await supabase
        .from('couple_milestones')
        .update(updateData)
        .eq('id', milestoneId)
        .select(`
          *,
          template:milestone_templates(*)
        `)
        .single();

      if (error) {
        logger.error('Error updating milestone:', error);
        throw new Error(`Failed to update milestone: ${error.message}`);
      }

      logger.info(`Updated milestone ${milestoneId}, completed: ${isCompleted}`);
      return data ? {
        ...data,
        milestone_data: (data.milestone_data as Record<string, MilestoneValue>) || {},
        is_completed: data.is_completed ?? false,
        template: data.template ? {
          ...data.template,
          field_schema: (data.template.field_schema as unknown as { fields: MilestoneField[] }) || { fields: [] }
        } : undefined
      } as CoupleMilestone : null;
    } catch (error) {
      logger.error('Error in updateMilestone:', error);
      throw error;
    }
  }

  /**
   * Get milestone progress for a couple
   */
  async getMilestoneProgress(coupleId: string): Promise<MilestoneProgress[]> {
    try {
      const { data, error } = await supabase
        .rpc('get_milestone_progress', { p_couple_id: coupleId });

      if (error) {
        logger.error('Error fetching milestone progress:', error);
        throw new Error(`Failed to fetch milestone progress: ${error.message}`);
      }

      return data || [];
    } catch (error) {
      logger.error('Error in getMilestoneProgress:', error);
      throw error;
    }
  }

  /**
   * Get a specific milestone by template key
   */
  async getMilestoneByKey(coupleId: string, milestoneKey: string): Promise<CoupleMilestone | null> {
    try {
      const { data, error } = await supabase
        .from('couple_milestones')
        .select(`
          *,
          template:milestone_templates(*)
        `)
        .eq('couple_id', coupleId)
        .eq('template.milestone_key', milestoneKey)
        .single();

      if (error && error.code !== 'PGRST116') { // PGRST116 = no rows returned
        logger.error('Error fetching milestone by key:', error);
        throw new Error(`Failed to fetch milestone: ${error.message}`);
      }

      return data ? {
        ...data,
        milestone_data: (data.milestone_data as Record<string, MilestoneValue>) || {},
        is_completed: data.is_completed ?? false,
        template: data.template ? {
          ...data.template,
          field_schema: (data.template.field_schema as unknown as { fields: MilestoneField[] }) || { fields: [] }
        } : undefined
      } as CoupleMilestone : null;
    } catch (error) {
      logger.error('Error in getMilestoneByKey:', error);
      throw error;
    }
  }

  /**
   * Sync origin story data to milestones
   */
  async syncOriginStoryToMilestones(coupleId: string, originStoryData: any): Promise<void> {
    try {
      // Map origin story fields to milestone keys
      const mappings = [
        { originField: 'firstMeeting', milestoneKey: 'how_we_met', dateField: 'firstMeetingDate' },
        { originField: 'firstKiss', milestoneKey: 'first_kiss', dateField: 'firstKissDate' },
        { originField: 'knewILovedYou', milestoneKey: 'first_i_love_you', dateField: 'knewILovedYouDate' },
        // Add more mappings as needed
      ];

      for (const mapping of mappings) {
        const storyText = originStoryData[mapping.originField];
        const storyDate = originStoryData[mapping.dateField];

        if (storyText && storyText.trim()) {
          const milestone = await this.getMilestoneByKey(coupleId, mapping.milestoneKey);

          if (milestone) {
            const milestoneData = {
              ...milestone.milestone_data,
              story: storyText,
              date: storyDate ? new Date(storyDate).toISOString().split('T')[0] : null,
              imported_from_origin_story: true,
            };

            await this.updateMilestone(milestone.id, milestoneData);
          }
        }
      }

      logger.info(`Synced origin story to milestones for couple ${coupleId}`);
    } catch (error) {
      logger.error('Error in syncOriginStoryToMilestones:', error);
      throw error;
    }
  }

  /**
   * Private helper to determine if milestone should be marked as completed
   */
  private shouldMarkAsCompleted(milestoneData: Record<string, MilestoneValue>): boolean {
    // Check if required fields are filled using safe type guards
    const hasStory = typeof milestoneData.story === 'string' && milestoneData.story.trim().length > 0;
    const hasDate = typeof (milestoneData as any).date === 'string' || typeof (milestoneData as any).completion_date === 'string';
    const hasTitle = (typeof (milestoneData as any).title === 'string' && (milestoneData as any).title.trim().length > 0)
      || (typeof (milestoneData as any).custom_title === 'string' && (milestoneData as any).custom_title.trim().length > 0);

    // Basic completion criteria - can be customized per milestone type
    return (hasStory || hasTitle) && hasDate;
  }
}

export const milestoneService = new MilestoneService();
export default milestoneService;
