import * as ImagePicker from 'expo-image-picker';
import { Alert } from 'react-native';
import { logger } from '../../shared/utils/logger';
import { supabase } from '../services/supabase/client';

export interface ImageUploadResult {
  success: boolean;
  url?: string;
  error?: string;
}

export interface StoryPhoto {
  id: string;
  url: string;
  timestamp: number;
  caption?: string;
  localUri?: string; // Keep local URI for immediate display
}

export type ImageBucket = 'story-photos' | 'profile-pictures' | 'scrapbook-photos';

class ImageStorageService {
  /**
   * Request camera and media library permissions
   */
  async requestPermissions(): Promise<boolean> {
    try {
      const [cameraStatus, mediaLibraryStatus] = await Promise.all([
        ImagePicker.requestCameraPermissionsAsync(),
        ImagePicker.requestMediaLibraryPermissionsAsync()
      ]);

      if (cameraStatus.status !== 'granted' || mediaLibraryStatus.status !== 'granted') {
        Alert.alert(
          'Permission Required',
          'We need access to your camera and photo library to add photos to your story.',
          [{ text: 'OK' }]
        );
        return false;
      }

      return true;
    } catch (error) {
      logger.error('Error requesting permissions:', error);
      return false;
    }
  }

  /**
   * Pick image from library
   */
  async pickImageFromLibrary(): Promise<ImagePicker.ImagePickerResult> {
    const hasPermission = await this.requestPermissions();
    if (!hasPermission) {
      return { canceled: true } as ImagePicker.ImagePickerResult;
    }

    try {
      return await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [4, 3],
        quality: 0.8,
        base64: false,
      });
    } catch (error) {
      logger.error('Error picking image from library:', error);
      return { canceled: true } as ImagePicker.ImagePickerResult;
    }
  }

  /**
   * Take photo with camera
   */
  async takePhotoWithCamera(): Promise<ImagePicker.ImagePickerResult> {
    const hasPermission = await this.requestPermissions();
    if (!hasPermission) {
      return { canceled: true } as ImagePicker.ImagePickerResult;
    }

    try {
      return await ImagePicker.launchCameraAsync({
        allowsEditing: true,
        aspect: [4, 3],
        quality: 0.8,
        base64: false,
      });
    } catch (error) {
      logger.error('Error taking photo with camera:', error);
      return { canceled: true } as ImagePicker.ImagePickerResult;
    }
  }

  /**
   * Upload image to Supabase Storage
   */
  async uploadImage(
    localUri: string,
    bucket: ImageBucket,
    userId: string,
    fileName?: string
  ): Promise<ImageUploadResult> {
    try {
      // Generate unique filename if not provided
      const timestamp = Date.now();
      const randomId = Math.random().toString(36).substring(2, 15);
      const finalFileName = fileName || `image_${timestamp}_${randomId}.jpg`;

      // Create file path: userId/filename
      const filePath = `${userId}/${finalFileName}`;

      // Convert local URI to blob
      const response = await fetch(localUri);
      const blob = await response.blob();

      // Upload to Supabase Storage
      const { data: _data, error } = await supabase.storage
        .from(bucket)
        .upload(filePath, blob, {
          contentType: 'image/jpeg',
          upsert: false
        });

      if (error) {
        logger.error('Error uploading image to Supabase:', error);
        return { success: false, error: error.message };
      }

      // Get public URL
      const { data: urlData } = supabase.storage
        .from(bucket)
        .getPublicUrl(filePath);

      return {
        success: true,
        url: urlData.publicUrl
      };

    } catch (error) {
      logger.error('Error in uploadImage:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Delete image from Supabase Storage
   */
  async deleteImage(bucket: ImageBucket, userId: string, fileName: string): Promise<boolean> {
    try {
      const filePath = `${userId}/${fileName}`;

      const { error } = await supabase.storage
        .from(bucket)
        .remove([filePath]);

      if (error) {
        logger.error('Error deleting image from Supabase:', error);
        return false;
      }

      return true;
    } catch (error) {
      logger.error('Error in deleteImage:', error);
      return false;
    }
  }

  /**
   * Create a StoryPhoto object with both local and cloud URLs
   */
  createStoryPhoto(
    localUri: string,
    cloudUrl?: string,
    caption?: string
  ): StoryPhoto {
    return {
      id: `photo_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`,
      url: cloudUrl || localUri, // Use cloud URL if available, fallback to local
      localUri: localUri,
      timestamp: Date.now(),
      caption: caption
    };
  }

  /**
   * Upload image and create StoryPhoto object
   */
  async uploadAndCreateStoryPhoto(
    localUri: string,
    bucket: ImageBucket,
    userId: string,
    caption?: string
  ): Promise<StoryPhoto | null> {
    try {
      // First create the photo object with local URI
      const photo = this.createStoryPhoto(localUri, undefined, caption);

      // Upload to cloud storage
      const uploadResult = await this.uploadImage(localUri, bucket, userId, `${photo.id}.jpg`);

      if (uploadResult.success && uploadResult.url) {
        // Update photo with cloud URL
        photo.url = uploadResult.url;
        return photo;
      } else {
        logger.warn('Failed to upload image, keeping local URI:', uploadResult.error);
        return photo; // Return with local URI as fallback
      }
    } catch (error) {
      logger.error('Error in uploadAndCreateStoryPhoto:', error);
      return null;
    }
  }

  /**
   * Get optimized image URL with transformations
   */
  getOptimizedImageUrl(bucket: ImageBucket, filePath: string, options?: {
    width?: number;
    height?: number;
    quality?: number;
    format?: 'webp' | 'jpeg' | 'png';
  }): string {
    const { data } = supabase.storage
      .from(bucket)
      .getPublicUrl(filePath);

    if (!options) {
      return data.publicUrl;
    }

    // Add transformation parameters
    const params = new URLSearchParams();
    if (options.width) params.append('width', options.width.toString());
    if (options.height) params.append('height', options.height.toString());
    if (options.quality) params.append('quality', options.quality.toString());
    if (options.format) params.append('format', options.format);

    const baseUrl = data.publicUrl;
    const separator = baseUrl.includes('?') ? '&' : '?';

    return `${baseUrl}${separator}${params.toString()}`;
  }

  /**
   * Batch upload multiple images
   */
  async uploadMultipleImages(
    localUris: string[],
    bucket: ImageBucket,
    userId: string
  ): Promise<StoryPhoto[]> {
    const uploadPromises = localUris.map(uri =>
      this.uploadAndCreateStoryPhoto(uri, bucket, userId)
    );

    const results = await Promise.all(uploadPromises);
    return results.filter((photo): photo is StoryPhoto => photo !== null);
  }

  /**
   * Get all images for a user from a bucket
   */
  async getUserImages(bucket: ImageBucket, userId: string): Promise<string[]> {
    try {
      const { data, error } = await supabase.storage
        .from(bucket)
        .list(userId);

      if (error) {
        logger.error('Error listing user images:', error);
        return [];
      }

      return data?.map(file => file.name) || [];
    } catch (error) {
      logger.error('Error in getUserImages:', error);
      return [];
    }
  }

  /**
   * Clean up old local images (optional utility)
   */
  async cleanupLocalImages(photos: StoryPhoto[]): Promise<void> {
    // This could be used to clean up local cache when cloud URLs are available
    // Implementation depends on your caching strategy
    logger.info('Cleanup local images called', { photoCount: photos.length });
  }
}

export const imageStorageService = new ImageStorageService();
