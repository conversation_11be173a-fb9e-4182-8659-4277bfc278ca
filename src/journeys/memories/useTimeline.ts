import { useCallback, useEffect, useMemo, useState } from 'react';
import { useCoupleRealtime } from '../../shared/services/realtime/useCoupleRealtime';
import { logger } from '../../shared/utils/logger';
import { supabase } from '../services/supabase/client';
import type { Database } from '../types/supabase';
import { useCouplePairing } from './useCouplePairing';

export interface TimelineItem {
  id: string;
  title: string;
  description?: string | null;
  eventDate: string; // ISO date
  eventType: string; // e.g., 'origin-story' | 'activity' | 'date-night' | 'reflection' | 'skill' | 'milestone'
  imageUrl?: string | null;
  photos?: Array<{
    id: string;
    url: string;
    caption?: string | null;
    width?: number | null;
    height?: number | null;
  }>;
}

export function useTimeline() {
  const { couple } = useCouplePairing();
  const [items, setItems] = useState<TimelineItem[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  const coupleId = couple?.id ?? null;

  const fetchTimeline = useCallback(async () => {
    if (!coupleId) {
      setItems([]);
      return;
    }
    try {
      setLoading(true);
      setError(null);

      // Fetch latest timeline events for this couple
      const { data: events, error: evErr } = await supabase
        .from('timeline_events')
        .select('*')
        .eq('couple_id', coupleId)
        .order('event_date', { ascending: false });

      if (evErr) throw evErr;

      const eventIds = (events ?? []).map(e => e.id);
      let photosByEvent = new Map<string, Database['public']['Tables']['timeline_photos']['Row'][]>();

      if (eventIds.length > 0) {
        const { data: photos, error: phErr } = await supabase
          .from('timeline_photos')
          .select('*')
          .in('timeline_event_id', eventIds);
        if (phErr) throw phErr;
        photosByEvent = new Map(
          (photos ?? []).reduce((acc: any, p: any) => {
            const arr = acc.get(p.timeline_event_id) ?? [];
            arr.push(p);
            acc.set(p.timeline_event_id, arr);
            return acc;
          }, new Map<string, Database['public']['Tables']['timeline_photos']['Row'][]>())
        );
      }

      const mapped: TimelineItem[] = (events ?? []).map(e => {
        const ph = photosByEvent.get(e.id) ?? [];
        return {
          id: e.id,
          title: e.title,
          description: e.description,
          eventDate: e.event_date,
          eventType: e.event_type,
          imageUrl: ph[0]?.thumbnail_url || ph[0]?.photo_url || null,
          photos: ph.map(p => ({ id: p.id, url: p.thumbnail_url || p.photo_url, caption: p.caption, width: p.width, height: p.height }))
        };
      });

      setItems(mapped);
    } catch (e: any) {
      logger.error('Failed to fetch timeline', e);
      setError(e?.message ?? 'Failed to fetch timeline');
    } finally {
      setLoading(false);
    }
  }, [coupleId]);

  // Initial load
  useEffect(() => { fetchTimeline(); }, [fetchTimeline]);

  // Realtime refresh on couple changes
  useCoupleRealtime(coupleId, fetchTimeline);

  const stats = useMemo(() => {
    const total = items.length;
    return { total };
  }, [items]);

  return { items, loading, error, refresh: fetchTimeline, stats };
}
