import React, { createContext, ReactNode, useContext, useEffect, useState } from 'react';
import { hybridStorageService } from '../../../shared/services/storage/hybridStorageService';
import { auth, type AuthUser } from '../../../shared/types/auth.types';
import { logger } from '../../../shared/utils/logger';
import { logEvent } from '../../services/analytics/eventLogger';
import type { UserProfile } from '../../types/auth';

export interface AuthContextType {
  user: AuthUser | null;
  profile: UserProfile | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  isInitialized: boolean;
  isBiometricEnabled: boolean;
  biometricType: string | null;
  signIn: (_email: string, _password: string) => Promise<void>;
  signUp: (_data: SignUpData) => Promise<void>;
  signOut: () => Promise<void>;
  refreshUser: () => Promise<void>;
  resetPassword: (_email: string) => Promise<void>;
  verifyEmail: (_token: string) => Promise<void>;
  enableBiometric: () => Promise<boolean>;
  disableBiometric: () => Promise<void>;
  signInWithBiometric: () => Promise<void>;
  signInWithGoogle: () => Promise<void>;
  signInWithApple: () => Promise<void>;
}

export interface SignUpData {
  email: string;
  password: string;
  fullName: string;
  confirmPassword?: string;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps { children: ReactNode }

// Secure storage keys
const SECURE_STORAGE_KEYS = {
  BIOMETRIC_ENABLED: 'biometric_enabled',
  USER_CREDENTIALS: 'user_credentials',
  REFRESH_TOKEN: 'refresh_token',
  ACCESS_TOKEN: 'access_token',
} as const;

export function AuthProvider({ children }: AuthProviderProps) {
  const [user, setUser] = useState<AuthContextType['user']>(null);
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isInitialized, setIsInitialized] = useState(false);
  const [isBiometricEnabled, setIsBiometricEnabled] = useState(false);
  const [biometricType, setBiometricType] = useState<string | null>(null);

  // Initialize biometric authentication
  const initializeBiometric = async () => {
    try {
      const hasHardware = await LocalAuthentication.hasHardwareAsync();
      const isEnrolled = await LocalAuthentication.isEnrolledAsync();

      if (hasHardware && isEnrolled) {
        const supportedTypes = await LocalAuthentication.supportedAuthenticationTypesAsync();
        const biometricTypeString = supportedTypes.includes(LocalAuthentication.AuthenticationType.FACIAL_RECOGNITION)
          ? 'Face ID'
          : supportedTypes.includes(LocalAuthentication.AuthenticationType.FINGERPRINT)
          ? 'Touch ID'
          : 'Biometric';

        setBiometricType(biometricTypeString);

        // Check if biometric is enabled in secure storage
        const biometricEnabled = await SecureStore.getItemAsync(SECURE_STORAGE_KEYS.BIOMETRIC_ENABLED);
        setIsBiometricEnabled(biometricEnabled === 'true');
      }
    } catch (error) {
      logger.error('Error initializing biometric authentication:', error);
    }
  };

  useEffect(() => {
    initializeAuth();
    initializeBiometric();

    // Development mode: bypass authentication
    if (__DEV__ && process.env.EXPO_PUBLIC_BYPASS_AUTH === 'true') {
      const mockUser = {
        id: 'dev-user-123',
        email: '<EMAIL>',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      const mockProfile = {
        partner1: { name: 'Alex', icon: '👩🏻‍🦰' },
        partner2: { name: 'Jordan', icon: '👨🏻‍🦱' },
        partner1PhotoUrl: null,
        partner2PhotoUrl: null,
        relationshipStartDate: '2024-01-01',
        isComplete: true,
        createdAt: Date.now(),
        updatedAt: Date.now(),
        coupleId: 'dev-couple-123'
      };

      setUser(mockUser);
      setProfile(mockProfile);
      setIsInitialized(true);
      setIsLoading(false);
      logger.info('Development mode: Using mock authentication');
      return;
    }

    const { data: { subscription } } = auth.onAuthStateChange(async (event, session) => {
      logger.info('Auth state changed:', { event, session: !!session });
      if (event === 'SIGNED_IN' && session) {
        setUser(session.user);
        await loadUserProfile(session.user.id);
        await hybridStorageService.syncAllLocalData();
        await logEvent('sign_in_success');
      } else if (event === 'SIGNED_OUT') {
        await logEvent('sign_out');
        setUser(null);
        setProfile(null);
        await hybridStorageService.clearAllLocalData();
      }
    });
    return () => subscription?.unsubscribe();
  }, []);

  const initializeAuth = async () => {
    try {
      setIsLoading(true);
      const authData = await auth.getCurrent();
      if (authData) {
        setUser(authData.user);
        setProfile(authData.profile ?? null);
        logger.info('User authenticated on app start');
      } else {
        logger.info('No authenticated user found');
      }
    } catch (error) {
      logger.error('Error initializing auth:', error);
    } finally {
      setIsLoading(false);
      setIsInitialized(true);
    }
  };

  const loadUserProfile = async (userId: string) => {
    try {
      const userProfile = await auth.getUserProfile(userId);
      setProfile(userProfile ?? null);
    } catch (error) {
      logger.error('Error loading user profile:', error);
      setProfile(null);
    }
  };

  const signIn = async (email: string, password: string) => {
    try {
      setIsLoading(true);
      const authData = await auth.signIn({ email, password });
      setUser(authData.user);
      setProfile(authData.profile ?? null);
      await hybridStorageService.syncAllLocalData();
    } catch (error) {
      logger.error('Sign in error:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const signUp = async (data: SignUpData) => {
    try {
      setIsLoading(true);

      // Input validation
      if (!data.email || !data.password || !data.fullName) {
        throw new Error('All fields are required');
      }

      if (data.password.length < 8) {
        throw new Error('Password must be at least 8 characters long');
      }

      if (data.confirmPassword && data.password !== data.confirmPassword) {
        throw new Error('Passwords do not match');
      }

      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(data.email)) {
        throw new Error('Please enter a valid email address');
      }

      const authData = await auth.signUp({
        email: data.email,
        password: data.password,
        fullName: data.fullName,
      });

      setUser(authData.user);
      setProfile(authData.profile ?? null);

      await logEvent('sign_up_success', { method: 'email' });
    } catch (error) {
      logger.error('Sign up error:', error);
      await logEvent('sign_up_error', {
        error: error instanceof Error ? error.message : 'Unknown error',
        method: 'email'
      });
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const signOut = async () => {
    try {
      setIsLoading(true);
      await auth.signOut();
      setUser(null);
      setProfile(null);
      await hybridStorageService.clearAllLocalData();
    } catch (error) {
      logger.error('Sign out error:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const refreshUser = async () => {
    try {
      const authData = await auth.getCurrent();
      if (authData) {
        setUser(authData.user);
        setProfile(authData.profile ?? null);
      } else {
        setUser(null);
        setProfile(null);
      }
    } catch (error) {
      logger.error('Error refreshing user:', error);
    }
  };

  // Password reset functionality
  const resetPassword = async (email: string) => {
    try {
      setIsLoading(true);

      if (!email) {
        throw new Error('Email is required');
      }

      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(email)) {
        throw new Error('Please enter a valid email address');
      }

      await auth.resetPassword(email);
      await logEvent('password_reset_requested', { email });
    } catch (error) {
      logger.error('Password reset error:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  // Email verification
  const verifyEmail = async (token: string) => {
    try {
      setIsLoading(true);
      await auth.verifyEmail(token);
      await logEvent('email_verified');
    } catch (error) {
      logger.error('Email verification error:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  // Biometric authentication methods
  const enableBiometric = async (): Promise<boolean> => {
    try {
      const hasHardware = await LocalAuthentication.hasHardwareAsync();
      const isEnrolled = await LocalAuthentication.isEnrolledAsync();

      if (!hasHardware || !isEnrolled) {
        Alert.alert(
          'Biometric Authentication Unavailable',
          'Your device does not support biometric authentication or no biometric data is enrolled.'
        );
        return false;
      }

      const result = await LocalAuthentication.authenticateAsync({
        promptMessage: 'Enable biometric authentication for Nestled',
        cancelLabel: 'Cancel',
        fallbackLabel: 'Use Password',
      });

      if (result.success) {
        await SecureStore.setItemAsync(SECURE_STORAGE_KEYS.BIOMETRIC_ENABLED, 'true');
        setIsBiometricEnabled(true);
        await logEvent('biometric_enabled');
        return true;
      }

      return false;
    } catch (error) {
      logger.error('Error enabling biometric authentication:', error);
      return false;
    }
  };

  const disableBiometric = async () => {
    try {
      await SecureStore.deleteItemAsync(SECURE_STORAGE_KEYS.BIOMETRIC_ENABLED);
      await SecureStore.deleteItemAsync(SECURE_STORAGE_KEYS.USER_CREDENTIALS);
      setIsBiometricEnabled(false);
      await logEvent('biometric_disabled');
    } catch (error) {
      logger.error('Error disabling biometric authentication:', error);
    }
  };

  const signInWithBiometric = async () => {
    try {
      if (!isBiometricEnabled) {
        throw new Error('Biometric authentication is not enabled');
      }

      const result = await LocalAuthentication.authenticateAsync({
        promptMessage: 'Sign in to Nestled',
        cancelLabel: 'Cancel',
        fallbackLabel: 'Use Password',
      });

      if (result.success) {
        const credentials = await SecureStore.getItemAsync(SECURE_STORAGE_KEYS.USER_CREDENTIALS);
        if (credentials) {
          const { email, password } = JSON.parse(credentials);
          await signIn(email, password);
          await logEvent('sign_in_success', { method: 'biometric' });
        } else {
          throw new Error('No stored credentials found');
        }
      }
    } catch (error) {
      logger.error('Biometric sign in error:', error);
      throw error;
    }
  };

  // Social login methods
  const signInWithGoogle = async () => {
    try {
      setIsLoading(true);
      // TODO: Implement Google Sign-In
      // This would typically use @react-native-google-signin/google-signin
      // or expo-auth-session for web-based OAuth flow
      await logEvent('social_sign_in_attempted', { provider: 'google' });
      throw new Error('Google Sign-In not yet implemented');
    } catch (error) {
      logger.error('Google sign in error:', error);
      await logEvent('social_sign_in_error', {
        provider: 'google',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const signInWithApple = async () => {
    try {
      setIsLoading(true);
      // TODO: Implement Apple Sign-In
      // This would typically use expo-apple-authentication
      await logEvent('social_sign_in_attempted', { provider: 'apple' });
      throw new Error('Apple Sign-In not yet implemented');
    } catch (error) {
      logger.error('Apple sign in error:', error);
      await logEvent('social_sign_in_error', {
        provider: 'apple',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const value: AuthContextType = {
    user,
    profile,
    isLoading,
    isAuthenticated: !!user,
    isInitialized,
    isBiometricEnabled,
    biometricType,
    signIn,
    signUp,
    signOut,
    refreshUser,
    resetPassword,
    verifyEmail,
    enableBiometric,
    disableBiometric,
    signInWithBiometric,
    signInWithGoogle,
    signInWithApple,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

export function useAuth() {
  const ctx = useContext(AuthContext);
  if (!ctx) throw new Error('useAuth must be used within an AuthProvider');
  return ctx;
}
