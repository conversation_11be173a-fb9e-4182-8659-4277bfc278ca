import React, { createContext, ReactNode, useContext, useEffect, useState } from 'react';
import { hybridStorageService } from '../../../shared/services/storage/hybridStorageService';
import { auth, type AuthUser } from '../../../shared/types/auth.types';
import { logger } from '../../../shared/utils/logger';
import { logEvent } from '../../services/analytics/eventLogger';
import type { UserProfile } from '../../types/auth';

export interface AuthContextType {
  user: AuthUser | null;
  profile: UserProfile | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  isInitialized: boolean;
  signIn: (_email: string, _password: string) => Promise<void>;
  signUp: (_data: any) => Promise<void>;
  signOut: () => Promise<void>;
  refreshUser: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps { children: ReactNode }

export function AuthProvider({ children }: AuthProviderProps) {
  const [user, setUser] = useState<AuthContextType['user']>(null);
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isInitialized, setIsInitialized] = useState(false);

  useEffect(() => {
    initializeAuth();

    // Development mode: bypass authentication
    if (__DEV__ && process.env.EXPO_PUBLIC_BYPASS_AUTH === 'true') {
      const mockUser = {
        id: 'dev-user-123',
        email: '<EMAIL>',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      const mockProfile = {
        partner1: { name: 'Alex', icon: '👩🏻‍🦰' },
        partner2: { name: 'Jordan', icon: '👨🏻‍🦱' },
        partner1PhotoUrl: null,
        partner2PhotoUrl: null,
        relationshipStartDate: '2024-01-01',
        isComplete: true,
        createdAt: Date.now(),
        updatedAt: Date.now(),
        coupleId: 'dev-couple-123'
      };

      setUser(mockUser);
      setProfile(mockProfile);
      setIsInitialized(true);
      setIsLoading(false);
      logger.info('Development mode: Using mock authentication');
      return;
    }

    const { data: { subscription } } = auth.onAuthStateChange(async (event, session) => {
      logger.info('Auth state changed:', { event, session: !!session });
      if (event === 'SIGNED_IN' && session) {
        setUser(session.user);
        await loadUserProfile(session.user.id);
        await hybridStorageService.syncAllLocalData();
        await logEvent('sign_in_success');
      } else if (event === 'SIGNED_OUT') {
        await logEvent('sign_out');
        setUser(null);
        setProfile(null);
        await hybridStorageService.clearAllLocalData();
      }
    });
    return () => subscription?.unsubscribe();
  }, []);

  const initializeAuth = async () => {
    try {
      setIsLoading(true);
      const authData = await auth.getCurrent();
      if (authData) {
        setUser(authData.user);
        setProfile(authData.profile ?? null);
        logger.info('User authenticated on app start');
      } else {
        logger.info('No authenticated user found');
      }
    } catch (error) {
      logger.error('Error initializing auth:', error);
    } finally {
      setIsLoading(false);
      setIsInitialized(true);
    }
  };

  const loadUserProfile = async (userId: string) => {
    try {
      const userProfile = await auth.getUserProfile(userId);
      setProfile(userProfile ?? null);
    } catch (error) {
      logger.error('Error loading user profile:', error);
      setProfile(null);
    }
  };

  const signIn = async (email: string, password: string) => {
    try {
      setIsLoading(true);
      const authData = await auth.signIn({ email, password });
      setUser(authData.user);
      setProfile(authData.profile ?? null);
      await hybridStorageService.syncAllLocalData();
    } catch (error) {
      logger.error('Sign in error:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const signUp = async (data: Parameters<typeof auth.signUp>[0]) => {
    try {
      setIsLoading(true);
      const authData = await auth.signUp(data);
      setUser(authData.user);
      setProfile(authData.profile ?? null);
    } catch (error) {
      logger.error('Sign up error:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const signOut = async () => {
    try {
      setIsLoading(true);
      await auth.signOut();
      setUser(null);
      setProfile(null);
      await hybridStorageService.clearAllLocalData();
    } catch (error) {
      logger.error('Sign out error:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const refreshUser = async () => {
    try {
      const authData = await auth.getCurrent();
      if (authData) {
        setUser(authData.user);
        setProfile(authData.profile ?? null);
      } else {
        setUser(null);
        setProfile(null);
      }
    } catch (error) {
      logger.error('Error refreshing user:', error);
    }
  };

  const value: AuthContextType = {
    user,
    profile,
    isLoading,
    isAuthenticated: !!user,
    isInitialized,
    signIn,
    signUp,
    signOut,
    refreshUser,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

export function useAuth() {
  const ctx = useContext(AuthContext);
  if (!ctx) throw new Error('useAuth must be used within an AuthProvider');
  return ctx;
}
