/**
 * AuthProvider Tests
 * 
 * Unit tests for the authentication provider with comprehensive
 * coverage of authentication flows and error handling.
 * 
 * @version 3.0.0
 * <AUTHOR> Us Team
 */

import React from 'react';
import { render, act, waitFor } from '@testing-library/react-native';
import { AuthProvider, useAuth } from '../AuthProvider';
import { Text, TouchableOpacity } from 'react-native';

// Mock dependencies
jest.mock('expo-local-authentication', () => ({
  hasHardwareAsync: jest.fn(() => Promise.resolve(true)),
  isEnrolledAsync: jest.fn(() => Promise.resolve(true)),
  supportedAuthenticationTypesAsync: jest.fn(() => Promise.resolve([1])),
  AuthenticationType: {
    FACIAL_RECOGNITION: 1,
    FINGERPRINT: 2,
  },
  authenticateAsync: jest.fn(() => Promise.resolve({ success: true })),
}));

jest.mock('expo-secure-store', () => ({
  getItemAsync: jest.fn(() => Promise.resolve(null)),
  setItemAsync: jest.fn(() => Promise.resolve()),
  deleteItemAsync: jest.fn(() => Promise.resolve()),
}));

jest.mock('../../../../shared/services/storage/hybridStorageService', () => ({
  hybridStorageService: {
    syncAllLocalData: jest.fn(() => Promise.resolve()),
    clearAllLocalData: jest.fn(() => Promise.resolve()),
  },
}));

jest.mock('../../../../shared/types/auth.types', () => ({
  auth: {
    signIn: jest.fn(),
    signUp: jest.fn(),
    signOut: jest.fn(),
    getCurrent: jest.fn(),
    resetPassword: jest.fn(),
    verifyEmail: jest.fn(),
    onAuthStateChange: jest.fn(() => ({
      data: { subscription: { unsubscribe: jest.fn() } },
    })),
  },
}));

jest.mock('../../../../shared/utils/logger', () => ({
  logger: {
    info: jest.fn(),
    error: jest.fn(),
  },
}));

jest.mock('../../../services/analytics/eventLogger', () => ({
  logEvent: jest.fn(() => Promise.resolve()),
}));

// Test component that uses the auth context
const TestComponent = () => {
  const {
    user,
    isAuthenticated,
    isLoading,
    signIn,
    signUp,
    signOut,
    resetPassword,
    verifyEmail,
    enableBiometric,
    disableBiometric,
    signInWithBiometric,
  } = useAuth();

  return (
    <>
      <Text testID="auth-status">
        {isAuthenticated ? 'authenticated' : 'not-authenticated'}
      </Text>
      <Text testID="loading-status">{isLoading ? 'loading' : 'not-loading'}</Text>
      <Text testID="user-email">{user?.email || 'no-user'}</Text>
      <TouchableOpacity testID="sign-in-button" onPress={() => signIn('<EMAIL>', 'password')}>
        <Text>Sign In</Text>
      </TouchableOpacity>
      <TouchableOpacity testID="sign-up-button" onPress={() => signUp({ email: '<EMAIL>', password: 'password', fullName: 'Test User' })}>
        <Text>Sign Up</Text>
      </TouchableOpacity>
      <TouchableOpacity testID="sign-out-button" onPress={() => signOut()}>
        <Text>Sign Out</Text>
      </TouchableOpacity>
      <TouchableOpacity testID="reset-password-button" onPress={() => resetPassword('<EMAIL>')}>
        <Text>Reset Password</Text>
      </TouchableOpacity>
      <TouchableOpacity testID="verify-email-button" onPress={() => verifyEmail('token')}>
        <Text>Verify Email</Text>
      </TouchableOpacity>
      <TouchableOpacity testID="enable-biometric-button" onPress={() => enableBiometric()}>
        <Text>Enable Biometric</Text>
      </TouchableOpacity>
      <TouchableOpacity testID="disable-biometric-button" onPress={() => disableBiometric()}>
        <Text>Disable Biometric</Text>
      </TouchableOpacity>
      <TouchableOpacity testID="biometric-sign-in-button" onPress={() => signInWithBiometric()}>
        <Text>Biometric Sign In</Text>
      </TouchableOpacity>
    </>
  );
};

const renderWithAuthProvider = () => {
  return render(
    <AuthProvider>
      <TestComponent />
    </AuthProvider>
  );
};

describe('AuthProvider', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Reset environment variable
    process.env.EXPO_PUBLIC_BYPASS_AUTH = 'false';
  });

  it('initializes with correct default state', async () => {
    const { getByTestID } = renderWithAuthProvider();

    await waitFor(() => {
      expect(getByTestID('auth-status')).toHaveTextContent('not-authenticated');
      expect(getByTestID('user-email')).toHaveTextContent('no-user');
    });
  });

  it('handles development mode bypass', async () => {
    process.env.EXPO_PUBLIC_BYPASS_AUTH = 'true';
    global.__DEV__ = true;

    const { getByTestID } = renderWithAuthProvider();

    await waitFor(() => {
      expect(getByTestID('auth-status')).toHaveTextContent('authenticated');
      expect(getByTestID('user-email')).toHaveTextContent('<EMAIL>');
    });
  });

  describe('Authentication Methods', () => {
    it('handles successful sign in', async () => {
      const mockAuth = require('../../../../shared/types/auth.types').auth;
      mockAuth.signIn.mockResolvedValueOnce({
        user: { id: '123', email: '<EMAIL>' },
        profile: { partner1: { name: 'Test' } },
      });

      const { getByTestID } = renderWithAuthProvider();

      await act(async () => {
        getByTestID('sign-in-button').props.onPress();
      });

      await waitFor(() => {
        expect(mockAuth.signIn).toHaveBeenCalledWith('<EMAIL>', 'password');
      });
    });

    it('handles sign in error', async () => {
      const mockAuth = require('../../../../shared/types/auth.types').auth;
      mockAuth.signIn.mockRejectedValueOnce(new Error('Invalid credentials'));

      const { getByTestID } = renderWithAuthProvider();

      await act(async () => {
        getByTestID('sign-in-button').props.onPress();
      });

      await waitFor(() => {
        expect(mockAuth.signIn).toHaveBeenCalled();
        // Error should be logged
        expect(require('../../../../shared/utils/logger').logger.error).toHaveBeenCalled();
      });
    });

    it('handles successful sign up', async () => {
      const mockAuth = require('../../../../shared/types/auth.types').auth;
      mockAuth.signUp.mockResolvedValueOnce({
        user: { id: '123', email: '<EMAIL>' },
        profile: null,
      });

      const { getByTestID } = renderWithAuthProvider();

      await act(async () => {
        getByTestID('sign-up-button').props.onPress();
      });

      await waitFor(() => {
        expect(mockAuth.signUp).toHaveBeenCalledWith({
          email: '<EMAIL>',
          password: 'password',
          fullName: 'Test User',
        });
      });
    });

    it('validates sign up input', async () => {
      const { getByTestID } = renderWithAuthProvider();

      // Mock signUp to test validation
      const authContext = require('../AuthProvider');
      const signUpSpy = jest.spyOn(authContext, 'signUp');

      await act(async () => {
        // This should trigger validation errors
        getByTestID('sign-up-button').props.onPress();
      });

      // The actual validation happens inside the component
      // We can't directly test it here without more complex mocking
    });

    it('handles sign out', async () => {
      const mockAuth = require('../../../../shared/types/auth.types').auth;
      mockAuth.signOut.mockResolvedValueOnce(undefined);

      const { getByTestID } = renderWithAuthProvider();

      await act(async () => {
        getByTestID('sign-out-button').props.onPress();
      });

      await waitFor(() => {
        expect(mockAuth.signOut).toHaveBeenCalled();
      });
    });

    it('handles password reset', async () => {
      const mockAuth = require('../../../../shared/types/auth.types').auth;
      mockAuth.resetPassword.mockResolvedValueOnce(undefined);

      const { getByTestID } = renderWithAuthProvider();

      await act(async () => {
        getByTestID('reset-password-button').props.onPress();
      });

      await waitFor(() => {
        expect(mockAuth.resetPassword).toHaveBeenCalledWith('<EMAIL>');
      });
    });

    it('handles email verification', async () => {
      const mockAuth = require('../../../../shared/types/auth.types').auth;
      mockAuth.verifyEmail.mockResolvedValueOnce(undefined);

      const { getByTestID } = renderWithAuthProvider();

      await act(async () => {
        getByTestID('verify-email-button').props.onPress();
      });

      await waitFor(() => {
        expect(mockAuth.verifyEmail).toHaveBeenCalledWith('token');
      });
    });
  });

  describe('Biometric Authentication', () => {
    it('enables biometric authentication', async () => {
      const mockLocalAuth = require('expo-local-authentication');
      const mockSecureStore = require('expo-secure-store');
      
      mockLocalAuth.authenticateAsync.mockResolvedValueOnce({ success: true });

      const { getByTestID } = renderWithAuthProvider();

      await act(async () => {
        getByTestID('enable-biometric-button').props.onPress();
      });

      await waitFor(() => {
        expect(mockLocalAuth.authenticateAsync).toHaveBeenCalled();
        expect(mockSecureStore.setItemAsync).toHaveBeenCalledWith('biometric_enabled', 'true');
      });
    });

    it('disables biometric authentication', async () => {
      const mockSecureStore = require('expo-secure-store');

      const { getByTestID } = renderWithAuthProvider();

      await act(async () => {
        getByTestID('disable-biometric-button').props.onPress();
      });

      await waitFor(() => {
        expect(mockSecureStore.deleteItemAsync).toHaveBeenCalledWith('biometric_enabled');
        expect(mockSecureStore.deleteItemAsync).toHaveBeenCalledWith('user_credentials');
      });
    });

    it('handles biometric sign in', async () => {
      const mockLocalAuth = require('expo-local-authentication');
      const mockSecureStore = require('expo-secure-store');
      
      mockLocalAuth.authenticateAsync.mockResolvedValueOnce({ success: true });
      mockSecureStore.getItemAsync.mockResolvedValueOnce(
        JSON.stringify({ email: '<EMAIL>', password: 'password' })
      );

      const { getByTestID } = renderWithAuthProvider();

      await act(async () => {
        getByTestID('biometric-sign-in-button').props.onPress();
      });

      await waitFor(() => {
        expect(mockLocalAuth.authenticateAsync).toHaveBeenCalled();
        expect(mockSecureStore.getItemAsync).toHaveBeenCalledWith('user_credentials');
      });
    });
  });

  describe('Error Handling', () => {
    it('handles network errors gracefully', async () => {
      const mockAuth = require('../../../../shared/types/auth.types').auth;
      mockAuth.signIn.mockRejectedValueOnce(new Error('Network error'));

      const { getByTestID } = renderWithAuthProvider();

      await act(async () => {
        getByTestID('sign-in-button').props.onPress();
      });

      await waitFor(() => {
        expect(require('../../../../shared/utils/logger').logger.error).toHaveBeenCalledWith(
          'Sign in error:',
          expect.any(Error)
        );
      });
    });

    it('handles biometric hardware unavailable', async () => {
      const mockLocalAuth = require('expo-local-authentication');
      mockLocalAuth.hasHardwareAsync.mockResolvedValueOnce(false);

      const { getByTestID } = renderWithAuthProvider();

      await act(async () => {
        getByTestID('enable-biometric-button').props.onPress();
      });

      // Should handle gracefully without crashing
      await waitFor(() => {
        expect(mockLocalAuth.hasHardwareAsync).toHaveBeenCalled();
      });
    });
  });
});
