/**
 * Authentication and User Management - Index
 *
 * Centralized exports for authentication and user profile functionality:
 * - User authentication and session management
 * - User profile and couple data management
 * - Couple pairing and connection services
 *
 * <AUTHOR> Us Team
 */

// Hooks
export { useAuth } from './useAuth';
export { useCouplePairing } from './useCouplePairing';
export { useCoupleRealtime } from './useCoupleRealtime';
export { useUserProfile } from './useUserProfile';

// Services
export { default as couplePairingService } from './couplePairingService';

// Auth services
export * from './auth/AuthContext';
export * from './auth/AuthProvider';

// Types
// Note: Legacy type exports removed - use types from shared/types instead
