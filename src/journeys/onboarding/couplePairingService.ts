/**
 * Couple Pairing Service
 *
 * Handles partner invitation, code generation, QR codes, and couple management.
 * Provides secure pairing with attempt limiting and proper validation.
 *
 * <AUTHOR> Us Team
 * @version 1.0.0
 */

import QRCode from 'qrcode';
import { logger } from '../../shared/utils/logger';
import { logEvent } from '../services/analytics/eventLogger';
import { supabase } from '../services/supabase/client';

export interface Couple {
  id: string;
  partner1_user_id: string;
  partner2_user_id: string | null;
  couple_code: string;
  qr_code_data: QRCodeData | null;
  status: 'pending' | 'active' | 'expired' | 'cancelled';
  created_at: string;
  expires_at: string;
  updated_at: string;
}

export interface QRCodeData {
  code: string;
  deep_link: string;
  app_url: string;
  created_at: string;
}

export interface PairingAttempt {
  id: string;
  user_id: string;
  attempted_code: string;
  couple_id: string | null;
  success: boolean;
  created_at: string;
}

export interface CreateCoupleResult {
  success: boolean;
  couple?: Couple;
  error?: string;
}

export interface JoinCoupleResult {
  success: boolean;
  couple?: Couple;
  error?: string;
  attempts_remaining?: number;
}

export interface PairingStatus {
  can_attempt: boolean;
  attempts_remaining: number;
  is_blocked: boolean;
}

class CouplePairingService {
  private readonly APP_SCHEME = 'everlastingus';
  private readonly WEB_URL = 'https://everlasting-us.app'; // Update with your actual domain

  /**
   * Create a new couple and generate pairing code
   */
  async createCouple(partner1UserId: string): Promise<CreateCoupleResult> {
    try {
      // Check if user already has an active couple
      const existingCouple = await this.getUserCouple(partner1UserId);
      if (existingCouple && existingCouple.status === 'active') {
        return {
          success: false,
          error: 'User already has an active couple relationship'
        };
      }

      // Generate couple code using database function
      const { data: codeData, error: codeError } = await supabase
        .rpc('generate_couple_code');

      if (codeError || !codeData) {
        logger.error('Failed to generate couple code:', codeError);
        return {
          success: false,
          error: 'Failed to generate pairing code'
        };
      }

      const coupleCode = codeData as string;

      // Create QR code data
      const qrCodeData: QRCodeData = {
        code: coupleCode,
        deep_link: `${this.APP_SCHEME}://join-couple?code=${coupleCode}`,
        app_url: `${this.WEB_URL}/join?code=${coupleCode}`,
        created_at: new Date().toISOString()
      };

      // Insert couple record
      const { data: couple, error: insertError } = await supabase
        .from('couples')
        .insert({
          partner1_user_id: partner1UserId,
          couple_code: coupleCode,
          qr_code_data: JSON.stringify(qrCodeData),
          status: 'pending'
        })
        .select()
        .single();

      if (insertError) {
        logger.error('Failed to create couple:', insertError);
        return {
          success: false,
          error: 'Failed to create couple relationship'
        };
      }

      logger.info('Couple created successfully', {
        coupleId: couple.id,
        code: coupleCode
      });

      // Instrument pairing events
      await logEvent('couple_code_generated', { couple_id: couple.id, code: coupleCode });
      await logEvent('couple_invitation_created', { couple_id: couple.id });

      return {
        success: true,
        couple: couple as Couple
      };

    } catch (error) {
      logger.error('Error creating couple:', error);
      return {
        success: false,
        error: 'An unexpected error occurred'
      };
    }
  }

  /**
   * Join an existing couple using pairing code
   */
  async joinCouple(partner2UserId: string, coupleCode: string): Promise<JoinCoupleResult> {
    try {
      // Check pairing attempt limits
      const pairingStatus = await this.checkPairingStatus(partner2UserId);
      if (!pairingStatus.can_attempt) {
        return {
          success: false,
          error: 'Maximum pairing attempts exceeded',
          attempts_remaining: 0
        };
      }

      // Find the couple by code
      const { data: couple, error: findError } = await supabase
        .from('couples')
        .select('*')
        .eq('couple_code', coupleCode.toUpperCase())
        .eq('status', 'pending')
        .single();

      // Record the attempt
      await this.recordPairingAttempt(
        partner2UserId,
        coupleCode,
        couple?.id || null,
        !!couple
      );

      if (findError || !couple) {
        logger.warn('Invalid pairing code attempted', {
          code: coupleCode,
          userId: partner2UserId
        });
        await logEvent('couple_pairing_attempt_failed', { reason: 'invalid_code' });
        const updatedStatus = await this.checkPairingStatus(partner2UserId);
        return {
          success: false,
          error: 'Invalid pairing code',
          attempts_remaining: updatedStatus.attempts_remaining
        };
      }

      // Check if code has expired
      if (new Date(couple.expires_at) < new Date()) {
        await supabase
          .from('couples')
          .update({ status: 'expired' })
          .eq('id', couple.id);

        return {
          success: false,
          error: 'Pairing code has expired',
          attempts_remaining: pairingStatus.attempts_remaining - 1
        };
      }

      // Check if user is trying to join their own couple
      if (couple.partner1_user_id === partner2UserId) {

      // Instrument invalid pairing attempt
      await logEvent('couple_pairing_attempt_failed', { reason: 'invalid_code' });

        return {
          success: false,
          error: 'Cannot join your own couple invitation',
          attempts_remaining: pairingStatus.attempts_remaining - 1
        };
      }

      // Check if user already has an active couple
      const existingCouple = await this.getUserCouple(partner2UserId);
      if (existingCouple && existingCouple.status === 'active') {
        return {
          success: false,
          error: 'You already have an active couple relationship',
          attempts_remaining: pairingStatus.attempts_remaining - 1
        };
      }

      // Join the couple
      const { data: updatedCouple, error: updateError } = await supabase
        .from('couples')
        .update({
          partner2_user_id: partner2UserId,
          status: 'active',
          updated_at: new Date().toISOString()
        })
        .eq('id', couple.id)
        .select()
        .single();

      if (updateError) {
        logger.error('Failed to join couple:', updateError);
        return {
          success: false,
          error: 'Failed to join couple',
          attempts_remaining: pairingStatus.attempts_remaining - 1
        };
      }

      // Initialize shared couple story
      await this.initializeCoupleStory(updatedCouple.id);

      logger.info('Partner joined couple successfully', {
        coupleId: updatedCouple.id,
        partner1: couple.partner1_user_id,
        partner2: partner2UserId
      });

      // Instrument pairing events
      await logEvent('couple_pairing_success', { couple_id: updatedCouple.id });

      return {
        success: true,
        couple: updatedCouple as Couple
      };

    } catch (error) {
      logger.error('Error joining couple:', error);
      return {
        success: false,
        error: 'An unexpected error occurred'
      };
    }
  }

  /**
   * Get user's current couple relationship
   */
  async getUserCouple(userId: string): Promise<Couple | null> {
    try {
      // Validate userId to prevent injection
      if (!userId || typeof userId !== 'string' || !userId.match(/^[0-9a-f-]{36}$/i)) {
        logger.error('Invalid userId format in getUserCouple');
        return null;
      }

      const { data: couple, error } = await supabase
        .from('couples')
        .select('*')
        .or(`partner1_user_id.eq.${userId},partner2_user_id.eq.${userId}`)
        .in('status', ['pending', 'active'])
        .order('created_at', { ascending: false })
        .limit(1)
        .maybeSingle();

      if (error) {
        logger.error('Error fetching user couple:', error);
        return null;
      }

      return couple as Couple | null;
    } catch (error) {
      logger.error('Error in getUserCouple:', error);
      return null;
    }
  }

  /**
   * Check pairing attempt status for a user
   */
  async checkPairingStatus(userId: string): Promise<PairingStatus> {
    try {
      const { data: canAttempt, error } = await supabase
        .rpc('check_pairing_attempts', { user_uuid: userId });

      if (error) {
        logger.error('Error checking pairing attempts:', error);
        return { can_attempt: false, attempts_remaining: 0, is_blocked: true };
      }

      // Count current failed attempts
      const { data: attempts, error: countError } = await supabase
        .from('pairing_attempts')
        .select('id')
        .eq('user_id', userId)
        .eq('success', false);

      if (countError) {
        logger.error('Error counting pairing attempts:', countError);
        return { can_attempt: false, attempts_remaining: 0, is_blocked: true };
      }

      const failedAttempts = attempts?.length || 0;
      const remaining = Math.max(0, 3 - failedAttempts);

      return {
        can_attempt: canAttempt as boolean,
        attempts_remaining: remaining,
        is_blocked: !canAttempt
      };

    } catch (error) {
      logger.error('Error in checkPairingStatus:', error);
      return { can_attempt: false, attempts_remaining: 0, is_blocked: true };
    }
  }

  /**
   * Record a pairing attempt
   */
  private async recordPairingAttempt(
    userId: string,
    attemptedCode: string,
    coupleId: string | null,
    success: boolean
  ): Promise<void> {
    try {
      await supabase
        .from('pairing_attempts')
        .insert({
          user_id: userId,
          attempted_code: attemptedCode.toUpperCase(),
          couple_id: coupleId,
          success: success
        });
    } catch (error) {
      logger.error('Error recording pairing attempt:', error);
    }
  }

  /**
   * Initialize shared couple story
   */
  private async initializeCoupleStory(coupleId: string): Promise<void> {
    try {
      await supabase
        .from('couple_stories')
        .insert({
          couple_id: coupleId,
          story_data: {},
          completed_sections: []
        });
    } catch (error) {
      logger.error('Error initializing couple story:', error);
    }
  }

  /**
   * Cancel/expire a couple invitation
   */
  async cancelCouple(coupleId: string, userId: string): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('couples')
        .update({
          status: 'cancelled',
          updated_at: new Date().toISOString()
        })
        .eq('id', coupleId)
        .eq('partner1_user_id', userId) // Only creator can cancel
        .eq('status', 'pending');

      if (error) {
        logger.error('Error cancelling couple:', error);
        return false;
      }

      return true;
    } catch (error) {
      logger.error('Error in cancelCouple:', error);
      return false;
    }
  }

  /**
   * Generate shareable invitation text
   */
  generateInvitationText(couple: Couple): string {
    return `Join me on Everlasting Us! 💕

Use this code to connect our journals: ${couple.couple_code}

Or visit: ${couple.qr_code_data?.app_url}

Let's start our journey together! ✨`;
  }

  /**
   * Generate QR code data URL
   */
  async generateQRCodeDataURL(couple: Couple): Promise<string> {
    try {
      if (!couple.qr_code_data?.deep_link) {
        return '';
      }

      const qrDataURL = await QRCode.toDataURL(couple.qr_code_data.deep_link, {
        width: 256,
        margin: 2,
        color: {
          dark: '#393939', // Brand charcoal gray
          light: '#FFFFFF'
        }
      });

      return qrDataURL;
    } catch (error) {
      logger.error('Error generating QR code:', error);
      return '';
    }
  }
}

// Export singleton instance
export const couplePairingService = new CouplePairingService();
export default couplePairingService;
