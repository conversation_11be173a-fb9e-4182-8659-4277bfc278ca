/**
 * Progress Journey - Index
 *
 * Centralized exports for all progress tracking and analytics functionality:
 * - Points system and achievements
 * - User engagement tracking
 * - Performance monitoring
 * - Analytics and reporting
 *
 * <AUTHOR> Us Team
 */

// Hooks
export { useEngagementSystem } from './useEngagementSystem';
export { useHomeScreen } from './useHomeScreen';
export { useDebounce, usePerformanceTracker, useRenderPerformance } from './usePerformance';
export { usePointsSystemSupabase } from './usePointsSystemSupabase';
export { useProgressData } from './useProgressData';
export { useUserEvents } from './useUserEvents';

// Services
export { default as performanceOptimizationService } from './performanceOptimizationService';

// Analytics services (consolidated)
export * from './eventLogger';

// Utils
export * from './userEventUtils';

// Types
export type {
    Achievement
} from '../../shared/services/features/pointsSystemService';

export type {
    MonthlyTrend, ProgressMetrics, UseProgressDataReturn, WeeklyProgress
} from './useProgressData';
