/**
 * useProgressData Hook
 *
 * Comprehensive hook for tracking all types of relationship progress including:
 * - Daily questions completion and streaks
 * - Activities and games progress
 * - Connection metrics and trends
 * - Achievement tracking across all categories
 *
 * <AUTHOR> Us Team
 * @version 1.0.0
 */

import { useCallback, useEffect, useState } from 'react';
import { logger } from '../../shared/utils/logger';
import { useDailyQuestions } from '../daily/useDailyQuestions';
import { useAuth } from '../onboarding/useAuth';

export interface ProgressMetrics {
  questions: {
    total_answered: number;
    current_streak: number;
    longest_streak: number;
    this_month: number;
    this_week: number;
  };
  activities: {
    total_completed: number;
    this_month: number;
    this_week: number;
    favorites: number;
    categories_explored: number;
  };
  games: {
    total_played: number;
    best_score: number;
    this_week: number;
    this_month: number;
    average_score: number;
  };
  connection: {
    score: number;
    weekly_change: number;
    monthly_change: number;
    trend: 'up' | 'down' | 'stable';
    last_updated: string;
  };
  overall: {
    total_interactions: number;
    active_days: number;
    engagement_score: number;
    relationship_health: number;
  };
}

export interface WeeklyProgress {
  day: string;
  completed: boolean;
  isToday?: boolean;
  activities_count: number;
  questions_answered: boolean;
}

export interface MonthlyTrend {
  month: string;
  questions: number;
  activities: number;
  games: number;
  connection_score: number;
}

export interface Achievement {
  id: string;
  title: string;
  description: string;
  emoji: string;
  unlocked: boolean;
  unlockedAt?: string;
  progress?: number;
  maxProgress?: number;
  category: 'questions' | 'activities' | 'games' | 'connection' | 'milestone';
}

export interface UseProgressDataReturn {
  // Core metrics
  metrics: ProgressMetrics | null;
  weeklyProgress: WeeklyProgress[];
  monthlyTrends: MonthlyTrend[];
  achievements: Achievement[];
  
  // Loading states
  isLoading: boolean;
  error: string | null;
  
  // Actions
  refreshData: () => Promise<void>;
  
  // Computed values
  unlockedAchievements: Achievement[];
  lockedAchievements: Achievement[];
  recentAchievements: Achievement[];
}

export const useProgressData = (): UseProgressDataReturn => {
  const { user } = useAuth();
  const { streakData, isLoading: dailyQuestionsLoading } = useDailyQuestions();
  
  // State
  const [metrics, setMetrics] = useState<ProgressMetrics | null>(null);
  const [weeklyProgress, setWeeklyProgress] = useState<WeeklyProgress[]>([]);
  const [monthlyTrends, setMonthlyTrends] = useState<MonthlyTrend[]>([]);
  const [achievements, setAchievements] = useState<Achievement[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  /**
   * Load comprehensive progress data
   */
  const loadProgressData = useCallback(async (): Promise<void> => {
    if (!user?.id) {
      setError('User not authenticated');
      setIsLoading(false);
      return;
    }

    try {
      setIsLoading(true);
      setError(null);

      // For now, using mock data - in production this would fetch from Supabase
      // TODO: Replace with actual API calls to fetch real progress data
      
      const mockMetrics: ProgressMetrics = {
        questions: {
          total_answered: streakData?.total_questions_answered || 89,
          current_streak: streakData?.current_streak || 14,
          longest_streak: streakData?.longest_streak || 21,
          this_month: 33,
          this_week: 5
        },
        activities: {
          total_completed: 24,
          this_month: 8,
          this_week: 3,
          favorites: 5,
          categories_explored: 6
        },
        games: {
          total_played: 15,
          best_score: 95,
          this_week: 3,
          this_month: 7,
          average_score: 78
        },
        connection: {
          score: 92,
          weekly_change: 5,
          monthly_change: 12,
          trend: 'up',
          last_updated: new Date().toISOString()
        },
        overall: {
          total_interactions: 128,
          active_days: 45,
          engagement_score: 88,
          relationship_health: 91
        }
      };

      const mockWeeklyProgress: WeeklyProgress[] = [
        { day: 'Mon', completed: true, activities_count: 2, questions_answered: true },
        { day: 'Tue', completed: true, activities_count: 1, questions_answered: true },
        { day: 'Wed', completed: true, activities_count: 0, questions_answered: true },
        { day: 'Thu', completed: false, activities_count: 0, questions_answered: false },
        { day: 'Fri', completed: false, activities_count: 0, questions_answered: false },
        { day: 'Sat', completed: false, isToday: true, activities_count: 0, questions_answered: false },
        { day: 'Sun', completed: false, activities_count: 0, questions_answered: false },
      ];

      const mockMonthlyTrends: MonthlyTrend[] = [
        { month: 'Jan', questions: 28, activities: 12, games: 4, connection_score: 85 },
        { month: 'Feb', questions: 31, activities: 15, games: 5, connection_score: 88 },
        { month: 'Mar', questions: 29, activities: 18, games: 3, connection_score: 90 },
        { month: 'Apr', questions: 33, activities: 14, games: 7, connection_score: 92 },
      ];

      setMetrics(mockMetrics);
      setWeeklyProgress(mockWeeklyProgress);
      setMonthlyTrends(mockMonthlyTrends);

      logger.debug('Progress data loaded:', { userId: user.id, metrics: mockMetrics });
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load progress data';
      setError(errorMessage);
      logger.error('Error loading progress data:', err);
    } finally {
      setIsLoading(false);
    }
  }, [user?.id, streakData]);

  /**
   * Generate achievements based on current metrics
   */
  const generateAchievements = useCallback((): Achievement[] => {
    if (!metrics) return [];

    const achievements: Achievement[] = [
      // Daily Questions Achievements
      {
        id: 'first_answer',
        title: 'Getting Started',
        description: 'Answer your first daily question',
        emoji: '💬',
        unlocked: metrics.questions.total_answered >= 1,
        category: 'questions'
      },
      {
        id: 'three_day_streak',
        title: 'On Fire',
        description: 'Maintain a 3-day streak',
        emoji: '🔥',
        unlocked: metrics.questions.current_streak >= 3,
        category: 'questions'
      },
      {
        id: 'two_week_streak',
        title: 'Two Week Streak Master',
        description: 'Maintain a 14-day streak',
        emoji: '🏆',
        unlocked: metrics.questions.current_streak >= 14,
        category: 'questions'
      },
      {
        id: 'fifty_questions',
        title: 'Deep Conversationalist',
        description: 'Answer 50+ daily questions',
        emoji: '💬',
        unlocked: metrics.questions.total_answered >= 50,
        progress: Math.min(metrics.questions.total_answered, 50),
        maxProgress: 50,
        category: 'questions'
      },

      // Activities Achievements
      {
        id: 'first_activity',
        title: 'Activity Explorer',
        description: 'Complete your first activity',
        emoji: '🎯',
        unlocked: metrics.activities.total_completed >= 1,
        category: 'activities'
      },
      {
        id: 'twenty_activities',
        title: 'Activity Master',
        description: 'Complete 20+ activities together',
        emoji: '🎯',
        unlocked: metrics.activities.total_completed >= 20,
        progress: Math.min(metrics.activities.total_completed, 20),
        maxProgress: 20,
        category: 'activities'
      },

      // Games Achievements
      {
        id: 'first_game',
        title: 'Game Night Starter',
        description: 'Play your first game together',
        emoji: '🎮',
        unlocked: metrics.games.total_played >= 1,
        category: 'games'
      },
      {
        id: 'high_score',
        title: 'Perfect Match',
        description: 'Score 90% or higher in a game',
        emoji: '🌟',
        unlocked: metrics.games.best_score >= 90,
        category: 'games'
      },

      // Connection Achievements
      {
        id: 'high_connection',
        title: 'Strong Connection',
        description: 'Achieve 90%+ connection score',
        emoji: '💕',
        unlocked: metrics.connection.score >= 90,
        category: 'connection'
      },
      {
        id: 'improving_connection',
        title: 'Growing Together',
        description: 'Improve connection score this week',
        emoji: '📈',
        unlocked: metrics.connection.weekly_change > 0,
        category: 'connection'
      },

      // Milestone Achievements
      {
        id: 'longest_streak',
        title: 'Personal Best',
        description: `Achieve your longest streak: ${metrics.questions.longest_streak} days`,
        emoji: '🏅',
        unlocked: metrics.questions.longest_streak > 0,
        category: 'milestone'
      }
    ];

    return achievements;
  }, [metrics]);

  // Update achievements when metrics change
  useEffect(() => {
    const newAchievements = generateAchievements();
    setAchievements(newAchievements);
  }, [generateAchievements]);

  // Load data on mount and when dependencies change
  useEffect(() => {
    loadProgressData();
  }, [loadProgressData]);

  // Computed values
  const unlockedAchievements = achievements.filter(a => a.unlocked);
  const lockedAchievements = achievements.filter(a => !a.unlocked);
  const recentAchievements = unlockedAchievements
    .filter(a => a.unlockedAt)
    .sort((a, b) => new Date(b.unlockedAt!).getTime() - new Date(a.unlockedAt!).getTime())
    .slice(0, 3);

  return {
    metrics,
    weeklyProgress,
    monthlyTrends,
    achievements,
    isLoading: isLoading || dailyQuestionsLoading,
    error,
    refreshData: loadProgressData,
    unlockedAchievements,
    lockedAchievements,
    recentAchievements
  };
};
