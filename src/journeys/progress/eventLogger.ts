/**
 * Event logging service for analytics and user tracking
 */

import { logger } from '../../shared/utils/logger';

export interface LogEventResponse {
  ok: boolean;
  error?: string;
}

/**
 * Log an event with optional metadata
 * @param eventName - The name of the event to log
 * @param metadata - Optional metadata to include with the event
 * @returns Promise<LogEventResponse> - Response indicating success/failure
 */
export const logEvent = async (
  eventName: string,
  metadata: Record<string, any> = {}
): Promise<LogEventResponse> => {
  try {
    // Log the event for debugging
    logger.info(`Event logged: ${eventName}`, { metadata });

    // In a real implementation, this would send to analytics service
    // For now, we'll just return success
    return { ok: true };
  } catch (error) {
    logger.error(`Failed to log event: ${eventName}`, { error, metadata });
    return {
      ok: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
};

/**
 * Attach a listener for auth flush events
 * @returns Function to detach the listener
 */
export const attachAuthFlushListener = (): (() => void) | undefined => {
  try {
    // In a real implementation, this would attach to auth state changes
    // For now, we'll just return a no-op detach function
    logger.info('Auth flush listener attached');

    return () => {
      logger.info('Auth flush listener detached');
    };
  } catch (error) {
    logger.error('Failed to attach auth flush listener', { error });
    return undefined;
  }
};

/**
 * Legacy export for backward compatibility
 */
export default logEvent;
