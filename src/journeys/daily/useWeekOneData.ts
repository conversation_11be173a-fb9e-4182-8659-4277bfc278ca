import { useEffect, useState } from 'react';
import { logger } from '../../shared/utils/logger';
import { useMatchGame } from '../activities/useMatchGame';
import { secureStorage } from '../utils/secureStorage';

export interface MatchGameResponse {
  question: string;
  playerOneAnswer: string;
  playerTwoGuess: string;
  isRevealed: boolean;
  timestamp: number;
  score: number;
  category: string;
  difficulty: 'easy' | 'medium' | 'hard';
  isCompleted: boolean;
}

export interface MatchGameStats {
  totalScore: number;
  questionsAnswered: number;
  perfectMatches: number;
  currentStreak: number;
  bestStreak: number;
  playerOneScore: number;
  playerTwoScore: number;
}

export interface DateNightPlan {
  when: string;
  where: string;
  completed: boolean;
  timestamp: number;
}

export interface ChatPrompt {
  prompt: string;
  playerOneAnswer: string;
  playerTwoAnswer: string;
  timestamp: number;
}

export interface SoftStartupPractice {
  originalStatement: string;
  practiceStatement: string;
  timestamp: number;
}

export interface WeekOneData {
  matchGameResponses: MatchGameResponse[];
  dateNightPlan: DateNightPlan;
  chatPrompts: ChatPrompt[];
  softStartupPractice: SoftStartupPractice[];
  completedSections: boolean[];
  completedAt?: number;
  matchGameStats: MatchGameStats;
  sectionCompletionDates: {
    matchGame?: number;
    dateNight?: number;
    chatPrompts?: number;
    softStartup?: number;
  };
}

const WEEK_ONE_STORAGE_KEY = 'week_one_data';

export const useWeekOneData = () => {
  const { loadQuestions, gameState } = useMatchGame();

  const [data, setData] = useState<WeekOneData>({
    matchGameResponses: [],
    matchGameStats: {
      totalScore: 0,
      questionsAnswered: 0,
      perfectMatches: 0,
      currentStreak: 0,
      bestStreak: 0,
      playerOneScore: 0,
      playerTwoScore: 0
    },
    dateNightPlan: { when: '', where: '', completed: false, timestamp: Date.now() },
    chatPrompts: [
      { prompt: 'Happiness is _______', playerOneAnswer: '', playerTwoAnswer: '', timestamp: Date.now() },
      { prompt: 'I wish I made more time for _______', playerOneAnswer: '', playerTwoAnswer: '', timestamp: Date.now() },
    ],
    softStartupPractice: [
      { originalStatement: 'You always ignore my suggestions', practiceStatement: '', timestamp: Date.now() },
      { originalStatement: 'You never help with chores', practiceStatement: '', timestamp: Date.now() },
      { originalStatement: 'You\'re always on your phone', practiceStatement: '', timestamp: Date.now() },
    ],
    completedSections: [false, false, false, false],
    sectionCompletionDates: {},
  });

  const [isLoading, setIsLoading] = useState(true);

  // Load data from storage on mount and initialize questions
  useEffect(() => {
    loadData();
    // Load questions for match game
    loadQuestions({ count: 10, balanced: true });
  }, []);

  // Update match game responses when questions are loaded
  useEffect(() => {
    if (gameState.questions.length > 0 && data.matchGameResponses.length === 0) {
      const newResponses = gameState.questions.map((q: any) => ({
        question: q.question_text,
        playerOneAnswer: '',
        playerTwoGuess: '',
        isRevealed: false,
        timestamp: Date.now(),
        score: 0,
        category: q.category,
        difficulty: q.difficulty,
        isCompleted: false
      }));

      setData(prev => ({
        ...prev,
        matchGameResponses: newResponses
      }));
    }
  }, [gameState.questions, data.matchGameResponses.length]);

  const loadData = async () => {
    try {
      const storedData = await secureStorage.getItem<WeekOneData>(WEEK_ONE_STORAGE_KEY);
      if (storedData) {
        setData(storedData);
      }
    } catch (error) {
      logger.error('Error loading Week One data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const saveData = async (newData: WeekOneData) => {
    try {
      await secureStorage.setItem(WEEK_ONE_STORAGE_KEY, newData);
      setData(newData);
    } catch (error) {
      logger.error('Error saving Week One data:', error);
      throw new Error('Failed to save data securely');
    }
  };

  const updateMatchGameResponse = async (index: number, updates: Partial<MatchGameResponse>) => {
    const newData = { ...data };
    newData.matchGameResponses[index] = { ...newData.matchGameResponses[index], ...updates };
    await saveData(newData);
  };

  const updateDateNightPlan = async (updates: Partial<DateNightPlan>) => {
    const newData = { ...data };
    newData.dateNightPlan = { ...newData.dateNightPlan, ...updates };
    await saveData(newData);
  };

  const updateChatPrompt = async (index: number, updates: Partial<ChatPrompt>) => {
    const newData = { ...data };
    newData.chatPrompts[index] = { ...newData.chatPrompts[index], ...updates };
    await saveData(newData);
  };

  const updateSoftStartupPractice = async (index: number, updates: Partial<SoftStartupPractice>) => {
    const newData = { ...data };
    newData.softStartupPractice[index] = { ...newData.softStartupPractice[index], ...updates };
    await saveData(newData);
  };

  const updateCompletedSections = async (sectionIndex: number, completed: boolean) => {
    try {
      const newCompletedSections = [...data.completedSections];
      newCompletedSections[sectionIndex] = completed;

      const newSectionCompletionDates = { ...data.sectionCompletionDates };

      // Set completion date when section is completed
      if (completed) {
        const sectionNames = ['matchGame', 'dateNight', 'chatPrompts', 'softStartup'];
        const sectionName = sectionNames[sectionIndex];
        if (sectionName && !newSectionCompletionDates[sectionName as keyof typeof newSectionCompletionDates]) {
          (newSectionCompletionDates as any)[sectionName] = Date.now();
        }
      }

      const newData = {
        ...data,
        completedSections: newCompletedSections,
        sectionCompletionDates: newSectionCompletionDates,
        completedAt: newCompletedSections.every(Boolean) ? Date.now() : undefined,
      };

      setData(newData);
      await saveData(newData);
    } catch (error) {
      console.error('Error updating completed sections:', error);
      throw error;
    }
  };

  const resetData = () => {
    try {
      if (typeof window !== 'undefined' && window.localStorage) {
        window.localStorage.removeItem(WEEK_ONE_STORAGE_KEY);
      }
      setData({
        matchGameResponses: [
          { question: 'My comfort meal', playerOneAnswer: '', playerTwoGuess: '', isRevealed: false, timestamp: Date.now(), score: 0, category: 'Food', difficulty: 'easy', isCompleted: false },
          { question: 'A movie I could rewatch over and over', playerOneAnswer: '', playerTwoGuess: '', isRevealed: false, timestamp: Date.now(), score: 0, category: 'Entertainment', difficulty: 'easy', isCompleted: false },
          { question: 'My guilty pleasure', playerOneAnswer: '', playerTwoGuess: '', isRevealed: false, timestamp: Date.now(), score: 0, category: 'Personal', difficulty: 'medium', isCompleted: false },
          { question: 'If this song is on, I\'m on the dance floor', playerOneAnswer: '', playerTwoGuess: '', isRevealed: false, timestamp: Date.now(), score: 0, category: 'Music', difficulty: 'easy', isCompleted: false },
          { question: 'My dream vacation', playerOneAnswer: '', playerTwoGuess: '', isRevealed: false, timestamp: Date.now(), score: 0, category: 'Travel', difficulty: 'easy', isCompleted: false },
          { question: 'Which restaurant would I recommend?', playerOneAnswer: '', playerTwoGuess: '', isRevealed: false, timestamp: Date.now(), score: 0, category: 'Food', difficulty: 'medium', isCompleted: false },
          { question: 'My secret talent', playerOneAnswer: '', playerTwoGuess: '', isRevealed: false, timestamp: Date.now(), score: 0, category: 'Personal', difficulty: 'hard', isCompleted: false },
          { question: 'Which game show or reality TV show would I be on?', playerOneAnswer: '', playerTwoGuess: '', isRevealed: false, timestamp: Date.now(), score: 0, category: 'Entertainment', difficulty: 'medium', isCompleted: false },
          { question: 'My dream career', playerOneAnswer: '', playerTwoGuess: '', isRevealed: false, timestamp: Date.now(), score: 0, category: 'Career', difficulty: 'medium', isCompleted: false },
        ],
        matchGameStats: {
          totalScore: 0,
          questionsAnswered: 0,
          perfectMatches: 0,
          currentStreak: 0,
          bestStreak: 0,
          playerOneScore: 0,
          playerTwoScore: 0
        },
        dateNightPlan: { when: '', where: '', completed: false, timestamp: Date.now() },
              chatPrompts: [
        { prompt: 'Happiness is _______', playerOneAnswer: '', playerTwoAnswer: '', timestamp: Date.now() },
        { prompt: 'I wish I made more time for _______', playerOneAnswer: '', playerTwoAnswer: '', timestamp: Date.now() },
      ],
        softStartupPractice: [
          { originalStatement: 'You always ignore my suggestions', practiceStatement: '', timestamp: Date.now() },
          { originalStatement: 'You never help with chores', practiceStatement: '', timestamp: Date.now() },
          { originalStatement: 'You\'re always on your phone', practiceStatement: '', timestamp: Date.now() },
        ],
        completedSections: [false, false, false, false],
        sectionCompletionDates: {},
      });
    } catch (error) {
      console.error('Error resetting Week One data:', error);
    }
  };

  return {
    data,
    isLoading,
    updateMatchGameResponse,
    updateDateNightPlan,
    updateChatPrompt,
    updateSoftStartupPractice,
    updateCompletedSections,
    resetData,
  };
};
