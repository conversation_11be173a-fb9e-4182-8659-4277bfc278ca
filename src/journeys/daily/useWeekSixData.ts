import { useEffect, useState } from 'react';
import { logger } from '../../shared/utils/logger';
import { secureStorage } from '../utils/secureStorage';

export interface ChildhoodMemory {
  memory: string;
  partnerGuess: string;
  timestamp: number;
}

export interface MemoryLaneDate {
  when: string;
  where: string;
  sharedStories: string[];
  completed: boolean;
  timestamp: number;
}

export interface ChatPrompt {
  prompt: string;
  playerOneAnswer: string;
  playerTwoAnswer: string;
  timestamp: number;
}

export interface ValidationPractice {
  originalStatement: string;
  validatingPhrase: string;
  timestamp: number;
}

export interface WeekSixData {
  childhoodMemories: ChildhoodMemory[];
  memoryLaneDate: MemoryLaneDate;
  chatPrompts: ChatPrompt[];
  validationPractice: ValidationPractice[];
  completedSections: boolean[];
  completedAt?: number;
}

const WEEK_SIX_STORAGE_KEY = 'week_six_data';

export const useWeekSixData = () => {
  const [data, setData] = useState<WeekSixData>({
    childhoodMemories: [
      { memory: '', partnerGuess: '', timestamp: Date.now() },
      { memory: '', partnerGuess: '', timestamp: Date.now() },
    ],
    memoryLaneDate: {
      when: '',
      where: '',
      sharedStories: [],
      completed: false,
      timestamp: Date.now()
    },
    chatPrompts: [
      { prompt: 'What do you say when you describe me to others?', playerOneAnswer: '', playerTwoAnswer: '', timestamp: Date.now() },
      { prompt: 'What is my most attractive quality?', playerOneAnswer: '', playerTwoAnswer: '', timestamp: Date.now() },
    ],
    validationPractice: [
      { originalStatement: 'You never listen to me', validatingPhrase: '', timestamp: Date.now() },
      { originalStatement: 'You always interrupt me', validatingPhrase: '', timestamp: Date.now() },
      { originalStatement: 'You don\'t care about my feelings', validatingPhrase: '', timestamp: Date.now() },
    ],
    completedSections: [false, false, false, false],
  });

  const [isLoading, setIsLoading] = useState(true);

  // Load data from storage on mount
  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      const storedData = await secureStorage.getItem<WeekSixData>(WEEK_SIX_STORAGE_KEY);
      if (storedData) {
        setData(storedData);
      }
    } catch (error) {
      logger.error('Error loading Week Six data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const saveData = async (newData: WeekSixData) => {
    try {
      await secureStorage.setItem(WEEK_SIX_STORAGE_KEY, newData);
      setData(newData);
    } catch (error) {
      logger.error('Error saving Week Six data:', error);
      throw new Error('Failed to save data securely');
    }
  };

  const updateChildhoodMemory = async (index: number, updates: Partial<ChildhoodMemory>) => {
    const newData = { ...data };
    newData.childhoodMemories[index] = { ...newData.childhoodMemories[index], ...updates };
    await saveData(newData);
  };

  const updateMemoryLaneDate = async (updates: Partial<MemoryLaneDate>) => {
    const newData = { ...data };
    newData.memoryLaneDate = { ...newData.memoryLaneDate, ...updates };
    await saveData(newData);
  };

  const updateChatPrompt = async (index: number, updates: Partial<ChatPrompt>) => {
    const newData = { ...data };
    newData.chatPrompts[index] = { ...newData.chatPrompts[index], ...updates };
    await saveData(newData);
  };

  const updateValidationPractice = async (index: number, updates: Partial<ValidationPractice>) => {
    const newData = { ...data };
    newData.validationPractice[index] = { ...newData.validationPractice[index], ...updates };
    await saveData(newData);
  };

  const updateCompletedSections = async (newSections: boolean[]) => {
    const newData = { ...data };
    newData.completedSections = newSections;
    if (newSections.every(section => section)) {
      newData.completedAt = Date.now();
    }
    await saveData(newData);
  };

  const resetData = async () => {
    const initialData: WeekSixData = {
      childhoodMemories: [
        { memory: '', partnerGuess: '', timestamp: Date.now() },
        { memory: '', partnerGuess: '', timestamp: Date.now() },
      ],
      memoryLaneDate: {
        when: '',
        where: '',
        sharedStories: [],
        completed: false,
        timestamp: Date.now()
      },
      chatPrompts: [
        { prompt: 'What do you say when you describe me to others?', playerOneAnswer: '', playerTwoAnswer: '', timestamp: Date.now() },
        { prompt: 'What is my most attractive quality?', playerOneAnswer: '', playerTwoAnswer: '', timestamp: Date.now() },
      ],
      validationPractice: [
        { originalStatement: 'You never listen to me', validatingPhrase: '', timestamp: Date.now() },
        { originalStatement: 'You always interrupt me', validatingPhrase: '', timestamp: Date.now() },
        { originalStatement: 'You don\'t care about my feelings', validatingPhrase: '', timestamp: Date.now() },
      ],
      completedSections: [false, false, false, false],
    };
    await saveData(initialData);
  };

  return {
    data,
    isLoading,
    updateChildhoodMemory,
    updateMemoryLaneDate,
    updateChatPrompt,
    updateValidationPractice,
    updateCompletedSections,
    resetData,
  };
};
