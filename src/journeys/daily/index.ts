/**
 * Daily Journey - Index
 *
 * Centralized exports for all daily engagement functionality:
 * - Daily questions and responses
 * - Streak tracking and achievements
 * - Daily challenges and activities
 * - Weekly progress data
 *
 * <AUTHOR> Us Team
 */

// Hooks
export { useDailyChallenges } from './useDailyChallenges';
export { useDailyQuestions } from './useDailyQuestions';
export { useDailyQuestionsNotifications } from './useDailyQuestionsNotifications';
export { useStreakData } from './useStreakData';

// Weekly data hooks
export { useGenericWeekData } from './useGenericWeekData';
export { useWeekEightData } from './useWeekEightData';
export { useWeekElevenData } from './useWeekElevenData';
export { useWeekFiveData } from './useWeekFiveData';
export { useWeekFourData } from './useWeekFourData';
export { useWeekNineData } from './useWeekNineData';
export { useWeekOneData } from './useWeekOneData';
export { useWeekSevenData } from './useWeekSevenData';
export { useWeekSixData } from './useWeekSixData';
export { useWeekTenData } from './useWeekTenData';
export { useWeekThreeData } from './useWeekThreeData';
export { useWeekTwelveData } from './useWeekTwelveData';
export { useWeekTwoData } from './useWeekTwoData';

// Services
export { dailyQuestionsNotificationService } from './dailyQuestionsNotificationService';
export { dailyQuestionsService } from './dailyQuestionsService';
export { streakEventService } from './streakEventService';

// TODO: Define missing types in daily.types.ts
// Types
// export type {
//   DailyQuestion,
//   QuestionResponse,
//   StreakData,
//   CoupleResponseStatus,
//   WeeklyData,
//   DailyChallenge
// } from './daily.types';
