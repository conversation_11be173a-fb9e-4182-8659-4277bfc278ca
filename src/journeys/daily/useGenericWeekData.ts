/**
 * Generic Week Data Hook
 *
 * Consolidates common patterns across all week data hooks to reduce duplication.
 * Provides a standardized interface for week-based data management.
 *
 * <AUTHOR> Us Team
 * @version 1.0.0
 */

import { useCallback, useEffect, useState } from 'react';
import { logger } from '../../shared/utils/logger';
import { secureStorage } from '../utils/secureStorage';

/**
 * Generic interface for week data
 */
export interface WeekDataBase {
  completedSections: boolean[];
  completedAt?: number;
}

/**
 * Options for the generic week data hook
 */
export interface UseGenericWeekDataOptions<T extends WeekDataBase> {
  storageKey: string;
  defaultData: T;
  weekNumber: number;
}

/**
 * Return type for the generic week data hook
 */
export interface UseGenericWeekDataReturn<T extends WeekDataBase> {
  data: T;
  isLoading: boolean;
  error: string | null;
  updateData: (_updates: Partial<T>) => Promise<void>;
  updateCompletedSections: (_sections: boolean[]) => Promise<void>;
  markSectionComplete: (_sectionIndex: number) => Promise<void>;
  resetData: () => Promise<void>;
  saveData: (_newData: T) => Promise<void>;
  getCompletionPercentage: () => number;
  isComplete: boolean;
}

/**
 * Generic hook for managing week data with consistent patterns
 *
 * @param options - Configuration options for the hook
 * @returns Week data management functions and state
 */
export function useGenericWeekData<T extends WeekDataBase>(
  options: UseGenericWeekDataOptions<T>
): UseGenericWeekDataReturn<T> {
  const { storageKey, defaultData, weekNumber } = options;

  const [data, setData] = useState<T>(defaultData);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Load data from storage on mount
  useEffect(() => {
    loadData();
  }, []);

  const loadData = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);

      const storedData = await secureStorage.getItem<T>(storageKey);
      if (storedData) {
        setData(storedData);
        logger.info(`Week ${weekNumber} data loaded successfully`);
      } else {
        logger.info(`No stored data found for Week ${weekNumber}, using defaults`);
      }
    } catch (error) {
      const errorMessage = `Error loading Week ${weekNumber} data`;
      logger.error(errorMessage, error);
      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  }, [storageKey, weekNumber]);

  const saveData = useCallback(async (newData: T) => {
    try {
      const dataToSave = {
        ...newData,
        completedAt: newData.completedSections.every(Boolean) ? Date.now() : undefined,
      };

      await secureStorage.setItem(storageKey, dataToSave);
      setData(dataToSave);
      logger.info(`Week ${weekNumber} data saved successfully`);
    } catch (error) {
      const errorMessage = `Error saving Week ${weekNumber} data`;
      logger.error(errorMessage, error);
      setError(errorMessage);
      throw new Error('Failed to save data securely');
    }
  }, [storageKey, weekNumber]);

  const updateData = useCallback(async (updates: Partial<T>) => {
    try {
      const newData = { ...data, ...updates };
      await saveData(newData);
    } catch (error) {
      logger.error(`Error updating Week ${weekNumber} data:`, error);
      throw error;
    }
  }, [data, saveData, weekNumber]);

  const updateCompletedSections = useCallback(async (sections: boolean[]) => {
    await updateData({ completedSections: sections } as Partial<T>);
  }, [updateData]);

  const markSectionComplete = useCallback(async (sectionIndex: number) => {
    const newSections = [...data.completedSections];
    newSections[sectionIndex] = true;
    await updateCompletedSections(newSections);
  }, [data.completedSections, updateCompletedSections]);

  const resetData = useCallback(async () => {
    try {
      await secureStorage.removeItem(storageKey);
      setData(defaultData);
      setError(null);
      logger.info(`Week ${weekNumber} data reset successfully`);
    } catch (error) {
      const errorMessage = `Error resetting Week ${weekNumber} data`;
      logger.error(errorMessage, error);
      setError(errorMessage);
      throw error;
    }
  }, [storageKey, defaultData, weekNumber]);

  const getCompletionPercentage = useCallback((): number => {
    if (data.completedSections.length === 0) return 0;
    const completedCount = data.completedSections.filter(Boolean).length;
    return Math.round((completedCount / data.completedSections.length) * 100);
  }, [data.completedSections]);

  const isComplete = data.completedSections.every(Boolean);

  return {
    data,
    isLoading,
    error,
    updateData,
    updateCompletedSections,
    markSectionComplete,
    resetData,
    saveData,
    getCompletionPercentage,
    isComplete,
  };
}
