/**
 * Daily Questions Service
 *
 * Handles daily couple bonding questions, responses, and partner interactions.
 * Provides question delivery, response management, and engagement tracking.
 *
 * <AUTHOR> Us Team
 * @version 1.0.0
 */

import { supabase } from '../../shared/services/supabase/client';
import { logger } from '../../shared/utils/logger';
import { logEvent } from '../services/analytics/eventLogger';
import { STREAK_EVENT_CATEGORIES, STREAK_EVENT_TYPES, streakEventService } from './streakEventService';

// Type definitions for daily questions
export interface DailyQuestion {
  id: string;
  question_id: string;
  question_text: string;
  category: string;
  tone?: string;
  difficulty?: string;
  is_active: boolean;
  created_at: string;
}

export interface DailyQuestionResponse {
  id: string;
  user_id: string | null;
  couple_id: string | null;
  question_id: string | null;
  response_text: string;
  question_date: string;
  is_visible_to_partner: boolean | null;
  answered_at: string | null;
}

export interface DailyQuestionReaction {
  id: string;
  response_id: string;
  user_id: string;
  reaction_type: string;
  created_at: string;
}

export interface DailyQuestionComment {
  id: string;
  response_id: string;
  user_id: string;
  comment_text: string;
  created_at: string;
}

export interface CoupleResponseStatus {
  user_id: string;
  has_responded: boolean;
  response_text?: string;
  responded_at?: string;
}

export interface QuestionHistory {
  question_date: string;
  question: DailyQuestion;
  user_response?: DailyQuestionResponse;
  partner_response?: DailyQuestionResponse;
}

export interface StreakData {
  current_streak: number;
  longest_streak: number;
  last_activity_date?: string;
  total_responses: number;
}

class DailyQuestionsService {
  /**
   * Get today's question for a couple
   */
  async getTodaysQuestion(coupleId: string): Promise<DailyQuestion | null> {
    try {
      console.log('🔍 DailyQuestionsService: getTodaysQuestion called with coupleId:', coupleId);
      // Get today's date
      const today = new Date().toISOString().split('T')[0];
      console.log('📅 Today\'s date:', today);

      // First, try to get today's scheduled question
      const { data: scheduledData, error: scheduleError } = await (supabase as any)
        .from('daily_question_schedule')
        .select(`
          question_date,
          daily_questions!inner(
            id,
            question_id,
            question_text,
            category,
            tone,
            difficulty,
            is_active,
            created_at
          )
        `)
        .eq('question_date', today)
        .eq('is_active', true)
        .single();

      if (!scheduleError && scheduledData?.daily_questions) {
        logger.info('Found scheduled question for today');
        return {
          id: scheduledData.daily_questions.id,
          question_id: scheduledData.daily_questions.question_id,
          question_text: scheduledData.daily_questions.question_text,
          category: scheduledData.daily_questions.category,
          tone: scheduledData.daily_questions.tone,
          difficulty: scheduledData.daily_questions.difficulty,
          is_active: scheduledData.daily_questions.is_active,
          created_at: scheduledData.daily_questions.created_at
        } as DailyQuestion;
      }

      // Fallback: get any active question if no scheduled question exists
      logger.info('No scheduled question found, getting random active question');
      const { data, error } = await (supabase as any)
        .from('daily_questions')
        .select('*')
        .eq('is_active', true)
        .order('created_at', { ascending: false })
        .limit(1);

      if (error) {
        console.log('❌ Supabase error getting fallback question:', error);
        logger.error('Failed to get fallback question:', error);
        throw new Error(`Failed to get today's question: ${error.message}`);
      }

      if (!data || data.length === 0) {
        logger.warn('No questions available in database, using sample question');
        // Return a sample question for development/testing
        return {
          id: 'sample-question-1',
          question_id: 'sample-question-1',
          question_text: 'What is one thing you appreciate most about our relationship today?',
          category: 'gratitude',
          tone: 'warm',
          difficulty: 'easy',
          is_active: true,
          created_at: new Date().toISOString(),
          question_date: new Date().toISOString().split('T')[0]
        } as DailyQuestion;
      }

      logger.info('Found fallback question:', data[0]);
      return data[0] as unknown as DailyQuestion;
    } catch (error) {
      logger.error('Error getting today\'s question:', error);
      throw error instanceof Error ? error : new Error('Failed to get today\'s question');
    }
  }

  /**
   * Get couple's response status for today
   */
  async getCoupleResponseStatus(coupleId: string, questionDate?: string): Promise<CoupleResponseStatus[]> {
    try {
      const date = questionDate || new Date().toISOString().split('T')[0];

      const { data, error } = await (supabase as any)
        .from('daily_question_responses')
        .select('*')
        .eq('couple_id', coupleId)
        .eq('question_date', date);

      if (error) {
        logger.error('Failed to get couple response status:', error);
        return [];
      }

      return (data || []) as unknown as CoupleResponseStatus[];
    } catch (error) {
      logger.error('Error getting couple response status:', error);
      return [];
    }
  }

  /**
   * Submit a response to today's question
   */
  async submitResponse(
    userId: string,
    coupleId: string,
    questionId: string,
    responseText: string,
    questionDate?: string
  ): Promise<DailyQuestionResponse | null> {
    try {
      const date = questionDate || new Date().toISOString().split('T')[0];

      // Check if user already answered today
      const existingResponse = await this.getUserResponse(userId, date);
      if (existingResponse) {
        logger.warn('User already answered today\'s question');
        return null;
      }

      const { data, error } = await (supabase as any)
        .from('daily_question_responses')
        .insert({
          user_id: userId,
          couple_id: coupleId,
          question_id: questionId,
          response_text: responseText,
          question_date: date,
          is_visible_to_partner: true
        })
        .select();

      if (error) {
        logger.error('Failed to submit response:', error);
        return null;
      }

      // Log analytics event
      await logEvent('daily_question_answered', {
        question_id: questionId,
        response_length: responseText.length,
        question_date: date
      });

      // Track streak event
      const streakTracked = await streakEventService.trackEvent({
        user_id: userId,
        couple_id: coupleId,
        event_name: 'Daily Question Answered',
        event_category: STREAK_EVENT_CATEGORIES.DAILY_QUESTIONS,
        event_type: STREAK_EVENT_TYPES.DAILY_QUESTION_ANSWERED,
        streak_eligible: true,
        points_awarded: 10,
        metadata: {
          question_id: questionId,
          response_length: responseText.length,
          question_date: date
        }
      });

      if (!streakTracked) {
        logger.warn('Failed to track streak event for daily question');
      }

      return (data && data.length > 0 ? data[0] : null) as unknown as DailyQuestionResponse;
    } catch (error) {
      logger.error('Error submitting response:', error);
      return null;
    }
  }

  /**
   * Get user's response for a specific date
   */
  async getUserResponse(userId: string, questionDate: string): Promise<DailyQuestionResponse | null> {
    try {
      const { data, error } = await (supabase as any)
        .from('daily_question_responses')
        .select('*')
        .eq('user_id', userId)
        .eq('question_date', questionDate)
        .maybeSingle();

      if (error && error.code !== 'PGRST116') { // PGRST116 = no rows returned
        logger.error('Failed to get user response:', error);
        return null;
      }

      return data as unknown as DailyQuestionResponse | null;
    } catch (error) {
      logger.error('Error getting user response:', error);
      return null;
    }
  }

  /**
   * Update user's response (within edit window)
   */
  async updateResponse(
    responseId: string,
    responseText: string,
    userId: string
  ): Promise<DailyQuestionResponse | null> {
    try {
      // Check if response is within edit window (30 minutes)
      const { data: existingResponse, error: fetchError } = await (supabase as any)
        .from('daily_question_responses')
        .select('answered_at, user_id')
        .eq('id', responseId)
        .maybeSingle();

      if (fetchError) {
        logger.error('Failed to fetch existing response:', fetchError);
        return null;
      }

      if (!existingResponse || (existingResponse as any).user_id !== userId) {
        logger.error('User not authorized to update this response');
        return null;
      }

      const answeredAt = new Date((existingResponse as any).answered_at);
      const now = new Date();
      const editWindow = 30 * 60 * 1000; // 30 minutes in milliseconds

      if (now.getTime() - answeredAt.getTime() > editWindow) {
        logger.warn('Response edit window has expired');
        return null;
      }

      const { data, error } = await supabase
        .from('daily_question_responses')
        .update({ response_text: responseText })
        .eq('id', responseId)
        .select()
        .single();

      if (error) {
        logger.error('Failed to update response:', error);
        return null;
      }

      return data as DailyQuestionResponse;
    } catch (error) {
      logger.error('Error updating response:', error);
      return null;
    }
  }

  /**
   * Add a reaction to a response
   */
  async addReaction(
    _responseId: string,
    _userId: string,
    _reactionType: 'heart' | 'laugh' | 'surprise' | 'love'
  ): Promise<DailyQuestionReaction | null> {
    try {
      // Note: daily_question_reactions table is not available in current schema
      logger.warn('addReaction called but daily_question_reactions table is not available');
      return null;
    } catch (error) {
      logger.error('Error adding reaction:', error);
      return null;
    }
  }

  /**
   * Add a comment to a response
   */
  async addComment(
    _responseId: string,
    _userId: string,
    _commentText: string
  ): Promise<DailyQuestionComment | null> {
    try {
      // Note: daily_question_comments table is not available in current schema
      logger.warn('addComment called but daily_question_comments table is not available');
      return null;
    } catch (error) {
      logger.error('Error adding comment:', error);
      return null;
    }
  }

  /**
   * Get question history for a couple
   */
  async getQuestionHistory(
    coupleId: string,
    userId: string,
    limit: number = 30,
    offset: number = 0
  ): Promise<QuestionHistory[]> {
    try {
      const { data, error } = await supabase
        .from('daily_question_schedule')
        .select(`
          question_date,
          daily_questions!inner(
            question_id,
            question_text,
            category,
            tone,
            difficulty
          )
        `)
        .eq('is_active', true)
        .order('question_date', { ascending: false })
        .range(offset, offset + limit - 1);

      if (error) {
        logger.error('Failed to get question history:', error);
        return [];
      }

      const history: QuestionHistory[] = [];

      for (const item of data) {
        const questionDate = item.question_date;

        // Get responses for this date
        const { data: responses } = await supabase
          .from('daily_question_responses')
          .select('*')
          .eq('couple_id', coupleId)
          .eq('question_date', questionDate);

        const userResponseRaw = responses?.find(r => r.user_id === userId);
        const partnerResponseRaw = responses?.find(r => r.user_id !== userId);

        // Transform database response to match interface
        const userResponse: DailyQuestionResponse | undefined = userResponseRaw ? {
          id: userResponseRaw.id,
          user_id: userResponseRaw.user_id || '',
          couple_id: userResponseRaw.couple_id || '',
          question_id: userResponseRaw.question_id || '',
          response_text: userResponseRaw.response_text,
          question_date: userResponseRaw.question_date,
          is_visible_to_partner: userResponseRaw.is_visible_to_partner || false,
          answered_at: userResponseRaw.answered_at || new Date().toISOString()
        } : undefined;

        const partnerResponse: DailyQuestionResponse | undefined = partnerResponseRaw ? {
          id: partnerResponseRaw.id,
          user_id: partnerResponseRaw.user_id || '',
          couple_id: partnerResponseRaw.couple_id || '',
          question_id: partnerResponseRaw.question_id || '',
          response_text: partnerResponseRaw.response_text,
          question_date: partnerResponseRaw.question_date,
          is_visible_to_partner: partnerResponseRaw.is_visible_to_partner || false,
          answered_at: partnerResponseRaw.answered_at || new Date().toISOString()
        } : undefined;

        // Get reactions and comments
        const _responseIds = responses?.map(r => r.id) || [];

        // Reactions and comments functionality not implemented yet
        const _reactions: DailyQuestionReaction[] = [];
        const _comments: any[] = [];

        history.push({
          question_date: questionDate,
          question: {
            id: item.daily_questions.question_id,
            question_id: item.daily_questions.question_id,
            question_text: item.daily_questions.question_text,
            category: item.daily_questions.category,
            tone: item.daily_questions.tone || undefined,
            difficulty: item.daily_questions.difficulty || undefined,
            is_active: true,
            created_at: new Date().toISOString()
          },
          user_response: userResponse,
          partner_response: partnerResponse
        });
      }

      return history;
    } catch (error) {
      logger.error('Error getting question history:', error);
      return [];
    }
  }

  /**
   * Get user's streak data
   */
  async getStreakData(userId: string): Promise<StreakData> {
    try {
      // Get all user responses ordered by date
      const { data: responses, error } = await supabase
        .from('daily_question_responses')
        .select('question_date')
        .eq('user_id', userId)
        .order('question_date', { ascending: false });

      if (error) {
        logger.error('Failed to get streak data:', error);
        return {
          current_streak: 0,
          longest_streak: 0,
          total_responses: 0,
          last_activity_date: undefined
        };
      }

      if (!responses || responses.length === 0) {
        return {
          current_streak: 0,
          longest_streak: 0,
          total_responses: 0,
          last_activity_date: undefined
        };
      }

      const dates = responses.map(r => new Date(r.question_date));
      const totalAnswered = responses.length;
      const lastAnsweredDate = dates[0].toISOString().split('T')[0];

      // Calculate current streak
      let currentStreak = 0;
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      for (let i = 0; i < dates.length; i++) {
        const responseDate = new Date(dates[i]);
        responseDate.setHours(0, 0, 0, 0);

        const expectedDate = new Date(today);
        expectedDate.setDate(today.getDate() - i);

        if (responseDate.getTime() === expectedDate.getTime()) {
          currentStreak++;
        } else {
          break;
        }
      }

      // Calculate longest streak
      let longestStreak = 0;
      let tempStreak = 1;

      for (let i = 1; i < dates.length; i++) {
        const prevDate = new Date(dates[i - 1]);
        const currDate = new Date(dates[i]);

        const dayDiff = Math.floor((prevDate.getTime() - currDate.getTime()) / (1000 * 60 * 60 * 24));

        if (dayDiff === 1) {
          tempStreak++;
        } else {
          longestStreak = Math.max(longestStreak, tempStreak);
          tempStreak = 1;
        }
      }

      longestStreak = Math.max(longestStreak, tempStreak);

      return {
        current_streak: currentStreak,
        longest_streak: longestStreak,
        total_responses: totalAnswered,
        last_activity_date: lastAnsweredDate
      };
    } catch (error) {
      logger.error('Error getting streak data:', error);
      return {
        current_streak: 0,
        longest_streak: 0,
        total_responses: 0,
        last_activity_date: undefined
      };
    }
  }

  /**
   * Get questions by category
   */
  async getQuestionsByCategory(
    category: 'deep' | 'fun' | 'funny' | 'memories' | 'dreams' | 'gratitude',
    limit: number = 10
  ): Promise<DailyQuestion[]> {
    try {
      const { data, error } = await supabase
        .from('daily_questions')
        .select('*')
        .eq('category', category)
        .eq('is_active', true)
        .order('created_at', { ascending: false })
        .limit(limit);

      if (error) {
        logger.error('Failed to get questions by category:', error);
        return [];
      }

      return data.map(q => ({
        id: q.question_id, // Use question_id as the id
        question_id: q.question_id,
        question_text: q.question_text,
        category: q.category,
        tone: q.tone || undefined, // Convert null to undefined
        difficulty: q.difficulty || undefined, // Convert null to undefined
        is_active: true, // Default to active
        created_at: new Date().toISOString(), // Current timestamp
        question_date: new Date().toISOString().split('T')[0] // Current date as placeholder
      }));
    } catch (error) {
      logger.error('Error getting questions by category:', error);
      return [];
    }
  }

  /**
   * Skip today's question (limited uses per month)
   */
  async skipTodaysQuestion(userId: string, coupleId: string): Promise<boolean> {
    try {
      // Check if user has skip attempts remaining (implement skip limit logic)
      // For now, we'll just log the skip event

      await logEvent('daily_question_skipped', {
        user_id: userId,
        couple_id: coupleId,
        skip_date: new Date().toISOString().split('T')[0]
      });

      return true;
    } catch (error) {
      logger.error('Error skipping question:', error);
      return false;
    }
  }
}

export const dailyQuestionsService = new DailyQuestionsService();
