/**
 * Match Game Helper Utilities
 * Pure utility functions for match game functionality
 */

import {
    MatchGameCategory,
    MatchGameDifficulty,
    MatchGameGameState,
    MatchGamePlayer,
    MatchGameQuestion
} from '../../shared/types/matchGame.types';

/**
 * Shuffle an array using Fisher-Yates algorithm
 */
export function shuffleArray<T>(array: T[]): T[] {
  const shuffled = [...array];
  for (let i = shuffled.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
  }
  return shuffled;
}

/**
 * Get random questions from an array
 */
export function getRandomQuestionsFromArray(
  questions: MatchGameQuestion[],
  count: number = 10
): MatchGameQuestion[] {
  if (questions.length <= count) {
    return shuffleArray(questions);
  }
  return shuffleArray(questions).slice(0, count);
}

/**
 * Filter questions by category
 */
export function filterQuestionsByCategory(
  questions: MatchGameQuestion[],
  category: MatchGameCategory
): MatchGameQuestion[] {
  return questions.filter(q => q.category === category);
}

/**
 * Filter questions by difficulty
 */
export function filterQuestionsByDifficulty(
  questions: MatchGameQuestion[],
  difficulty: MatchGameDifficulty
): MatchGameQuestion[] {
  return questions.filter(q => q.difficulty === difficulty);
}

/**
 * Get balanced questions (40% easy, 40% medium, 20% hard)
 */
export function getBalancedQuestionsFromArray(
  questions: MatchGameQuestion[],
  count: number = 10
): MatchGameQuestion[] {
  const easyCount = Math.ceil(count * 0.4);
  const mediumCount = Math.ceil(count * 0.4);
  const hardCount = count - easyCount - mediumCount;

  const easyQuestions = shuffleArray(filterQuestionsByDifficulty(questions, 'easy'));
  const mediumQuestions = shuffleArray(filterQuestionsByDifficulty(questions, 'medium'));
  const hardQuestions = shuffleArray(filterQuestionsByDifficulty(questions, 'hard'));

  const selectedQuestions = [
    ...easyQuestions.slice(0, easyCount),
    ...mediumQuestions.slice(0, mediumCount),
    ...hardQuestions.slice(0, hardCount)
  ];

  return shuffleArray(selectedQuestions);
}

/**
 * Calculate similarity between two answers (basic string similarity)
 */
export function calculateAnswerSimilarity(answer1: string, answer2: string): number {
  if (!answer1 || !answer2) return 0;

  const normalize = (str: string) => str.toLowerCase().trim().replace(/[^\w\s]/g, '');
  const normalized1 = normalize(answer1);
  const normalized2 = normalize(answer2);

  if (normalized1 === normalized2) return 100;

  // Simple word-based similarity
  const words1 = normalized1.split(/\s+/);
  const words2 = normalized2.split(/\s+/);

  const commonWords = words1.filter(word => words2.includes(word));
  const totalWords = new Set([...words1, ...words2]).size;

  return totalWords > 0 ? (commonWords.length / totalWords) * 100 : 0;
}

/**
 * Check if two answers match (with fuzzy matching)
 */
export function doAnswersMatch(
  answer1: string,
  answer2: string,
  threshold: number = 80
): boolean {
  return calculateAnswerSimilarity(answer1, answer2) >= threshold;
}

/**
 * Calculate game score
 */
export function calculateGameScore(
  correctAnswers: number,
  totalQuestions: number
): number {
  if (totalQuestions === 0) return 0;
  return Math.round((correctAnswers / totalQuestions) * 100);
}

/**
 * Get game score description
 */
export function getScoreDescription(score: number): string {
  if (score >= 90) return 'Perfect Match! 🎉';
  if (score >= 80) return 'Excellent! 🌟';
  if (score >= 70) return 'Great Job! 👏';
  if (score >= 60) return 'Good Match! 👍';
  if (score >= 50) return 'Not Bad! 😊';
  return 'Keep Trying! 💪';
}

/**
 * Get difficulty color for UI
 */
export function getDifficultyColor(difficulty: MatchGameDifficulty): string {
  switch (difficulty) {
    case 'easy': return '#10B981'; // Green
    case 'medium': return '#F59E0B'; // Yellow
    case 'hard': return '#EF4444'; // Red
    default: return '#6B7280'; // Gray
  }
}

/**
 * Get category color for UI
 */
export function getCategoryColor(category: MatchGameCategory): string {
  const colors: Record<MatchGameCategory, string> = {
    'Food': '#F59E0B', // Amber
    'Entertainment': '#8B5CF6', // Purple
    'Personal': '#EC4899', // Pink
    'Music': '#06B6D4', // Cyan
    'Travel': '#10B981', // Emerald
    'Relationship': '#EF4444', // Red
    'Work': '#6366F1', // Indigo
    'Hobbies': '#84CC16', // Lime
    'Family': '#F97316', // Orange
    'Values': '#6B7280' // Gray
  };

  return colors[category] || '#6B7280';
}

/**
 * Get category icon for UI
 */
export function getCategoryIcon(category: MatchGameCategory): string {
  const icons: Record<MatchGameCategory, string> = {
    'Food': '🍽️',
    'Entertainment': '🎬',
    'Personal': '👤',
    'Music': '🎵',
    'Travel': '✈️',
    'Relationship': '💕',
    'Work': '💼',
    'Hobbies': '🎨',
    'Family': '👨‍👩‍👧‍👦',
    'Values': '⭐'
  };

  return icons[category] || '❓';
}

/**
 * Format time duration
 */
export function formatDuration(seconds: number): string {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;

  if (minutes > 0) {
    return `${minutes}m ${remainingSeconds}s`;
  }
  return `${remainingSeconds}s`;
}

/**
 * Generate game session ID
 */
export function generateSessionId(): string {
  return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * Validate game state
 */
export function validateGameState(state: MatchGameGameState): boolean {
  return !!(
    state.questions &&
    state.questions.length > 0 &&
    state.player1 &&
    state.player2 &&
    state.current_question_index >= 0 &&
    state.current_question_index < state.questions.length
  );
}

/**
 * Get next question index
 */
export function getNextQuestionIndex(
  currentIndex: number,
  totalQuestions: number
): number | null {
  const nextIndex = currentIndex + 1;
  return nextIndex < totalQuestions ? nextIndex : null;
}

/**
 * Check if game is complete
 */
export function isGameComplete(
  currentIndex: number,
  totalQuestions: number
): boolean {
  return currentIndex >= totalQuestions - 1;
}

/**
 * Calculate player statistics
 */
export function calculatePlayerStats(player: MatchGamePlayer): {
  accuracy: number;
  average_score: number;
} {
  const accuracy = player.total_answers > 0
    ? (player.correct_answers / player.total_answers) * 100
    : 0;

  return {
    accuracy: Math.round(accuracy),
    average_score: Math.round(accuracy)
  };
}

/**
 * Get game phase description
 */
export function getGamePhaseDescription(phase: MatchGameGameState['phase']): string {
  switch (phase) {
    case 'setup': return 'Setting up game...';
    case 'answering': return 'Answering questions...';
    case 'guessing': return 'Guessing partner\'s answers...';
    case 'revealing': return 'Revealing answers...';
    case 'completed': return 'Game completed!';
    default: return 'Unknown phase';
  }
}

/**
 * Get question difficulty multiplier for scoring
 */
export function getDifficultyMultiplier(difficulty: MatchGameDifficulty): number {
  switch (difficulty) {
    case 'easy': return 1.0;
    case 'medium': return 1.5;
    case 'hard': return 2.0;
    default: return 1.0;
  }
}

/**
 * Calculate weighted score based on difficulty
 */
export function calculateWeightedScore(
  correctAnswers: number,
  totalQuestions: number,
  questions: MatchGameQuestion[]
): number {
  if (totalQuestions === 0) return 0;

  let weightedScore = 0;
  let totalWeight = 0;

  questions.forEach(question => {
    const weight = getDifficultyMultiplier(question.difficulty);
    totalWeight += weight;
    // This would need to be updated with actual correct/incorrect data
    // For now, just use equal distribution
    weightedScore += weight * (1 / totalQuestions);
  });

  return Math.round((weightedScore / totalWeight) * 100);
}

/**
 * Filter out answered questions
 */
export function filterUnansweredQuestions(
  questions: MatchGameQuestion[],
  answeredQuestionIds: string[]
): MatchGameQuestion[] {
  return questions.filter(question => !answeredQuestionIds.includes(question.question_id));
}

/**
 * Get questions by multiple categories
 */
export function getQuestionsByCategories(
  questions: MatchGameQuestion[],
  categories: MatchGameCategory[]
): MatchGameQuestion[] {
  return questions.filter(question => categories.includes(question.category as MatchGameCategory));
}

/**
 * Sort questions by difficulty
 */
export function sortQuestionsByDifficulty(
  questions: MatchGameQuestion[],
  order: 'asc' | 'desc' = 'asc'
): MatchGameQuestion[] {
  const difficultyOrder = { easy: 1, medium: 2, hard: 3 };

  return [...questions].sort((a, b) => {
    const aOrder = difficultyOrder[a.difficulty as keyof typeof difficultyOrder];
    const bOrder = difficultyOrder[b.difficulty as keyof typeof difficultyOrder];
    return order === 'asc' ? aOrder - bOrder : bOrder - aOrder;
  });
}

/**
 * Get random question from array
 */
export function getRandomQuestion(questions: MatchGameQuestion[]): MatchGameQuestion | null {
  if (questions.length === 0) return null;
  const randomIndex = Math.floor(Math.random() * questions.length);
  return questions[randomIndex];
}

/**
 * Check if question is answered by user
 */
export function isQuestionAnswered(
  questionId: string,
  userAnswers: string[]
): boolean {
  return userAnswers.includes(questionId);
}

/**
 * Get game progress percentage
 */
export function getGameProgress(
  currentQuestionIndex: number,
  totalQuestions: number
): number {
  if (totalQuestions === 0) return 0;
  return Math.round(((currentQuestionIndex + 1) / totalQuestions) * 100);
}

/**
 * Format score for display
 */
export function formatScore(score: number): string {
  return `${score}%`;
}

/**
 * Get performance level based on score
 */
export function getPerformanceLevel(score: number): 'beginner' | 'intermediate' | 'advanced' | 'expert' {
  if (score >= 90) return 'expert';
  if (score >= 75) return 'advanced';
  if (score >= 50) return 'intermediate';
  return 'beginner';
}

/**
 * Get performance level color
 */
export function getPerformanceLevelColor(level: ReturnType<typeof getPerformanceLevel>): string {
  switch (level) {
    case 'expert': return '#10B981'; // Green
    case 'advanced': return '#3B82F6'; // Blue
    case 'intermediate': return '#F59E0B'; // Yellow
    case 'beginner': return '#EF4444'; // Red
    default: return '#6B7280'; // Gray
  }
}
