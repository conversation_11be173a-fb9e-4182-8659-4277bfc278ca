import React, { useCallback, useEffect, useMemo, useState } from 'react';
import {
    ActivityIndicator,
    Alert,
    Dimensions,
    FlatList,
    Modal,
    ScrollView,
    StyleSheet,
    Text,
    TouchableOpacity,
    View
} from 'react-native';

import {
    Calendar,
    CheckCircle,
    EyeOff,
    Heart,
    Plus,
    Shuffle,
    Star,
    Trash2,
    X
} from 'lucide-react-native';
import { HeartToggle } from '../../shared/components/common/HeartToggle';

import { useFavorites } from '../../shared/hooks/useFavorites';
import { supabase } from '../../shared/services/supabase/client';
import { dateNightIdeasService } from './dateNightIdeasService';
import { useDateNightIdeasSupabase } from './useDateNightIdeasSupabase';

import {
    CommonModal,
    EmptyState,
    SearchBar,
    StatCard,
    StyledInput
} from '../../shared/components/common/CommonComponents';

import type { DateNightIdeasByWeek, SurpriseItem as SurpriseItemType } from '../../shared/types';
import { colors } from '../../shared/utils/colors';
import { useAuth } from '../onboarding/useAuth';
import type { DateNightIdea } from './dateNightIdeasService';


import { DSButton } from '../../components/shared';
import TopTabs from '../../shared/components/common/TopTabs';
import Meals from './Meals';

const { width } = Dimensions.get('window');

// Always render all 12 weeks
const WEEKS = Array.from({ length: 12 }, (_, i) => i + 1);

type TabType = 'discover' | 'my-ideas' | 'meals';

// Types are defined in shared/types - no local interfaces needed

export default function DateNight() {
  // Performance monitoring
  // useRenderPerformance('DateNight');

  // State for date night ideas
  const [allIdeas, setAllIdeas] = useState<DateNightIdea[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const { user } = useAuth();
  // const { hideIdea, filterHiddenIdeas } = useUserPreferences();

  // Use shared favorites hook
  const {
    favoriteStatuses: _favoriteStatuses,
    loadingStates: favoriteLoadingStates,
    debouncedToggleFavorite: _debouncedToggleFavorite,
    isFavorited: _isFavoritedByKey,
    getFavoriteCount: _getFavoriteCount
  } = useFavorites();

  // Use date night ideas hook for creating user ideas
  const { createUserDateNightIdea } = useDateNightIdeasSupabase();

  // Apply comprehensive migration if needed
  const _ensureComprehensiveData = useCallback(async () => {
    try {
      const { data } = await supabase
        .from('date_night_ideas_global')
        .select('id', { count: 'exact' });

      const currentCount = data?.length || 0;

      if (currentCount < 30) { // If less than 30, add comprehensive data

        // Comprehensive date night activities - 30+ activities across categories
        const comprehensiveActivities = [
          // Romantic Activities (10)
          { title: 'Candlelit Bath Together', description: 'Draw a warm bath with candles and essential oils', category: 'romantic', emoji: '🛁', difficulty: 'easy' as const, estimated_duration: 60, cost: 'low' as const, indoor_outdoor: 'indoor' as const },
          { title: 'Love Letter Writing', description: 'Write heartfelt letters to each other and exchange them', category: 'romantic', emoji: '💌', difficulty: 'easy' as const, estimated_duration: 45, cost: 'free' as const, indoor_outdoor: 'indoor' as const },
          { title: 'Sunrise Breakfast Date', description: 'Wake up early to watch the sunrise with breakfast', category: 'romantic', emoji: '🌅', difficulty: 'medium' as const, estimated_duration: 90, cost: 'low' as const, indoor_outdoor: 'outdoor' as const },
          { title: 'Couples Massage', description: 'Give each other relaxing massages at home', category: 'romantic', emoji: '💆', difficulty: 'easy' as const, estimated_duration: 60, cost: 'free' as const, indoor_outdoor: 'indoor' as const },
          { title: 'Memory Lane Drive', description: 'Drive to places that are special to your relationship', category: 'romantic', emoji: '🚗', difficulty: 'easy' as const, estimated_duration: 120, cost: 'low' as const, indoor_outdoor: 'outdoor' as const },
          { title: 'Slow Dancing', description: 'Dance slowly to your favorite songs in the living room', category: 'romantic', emoji: '💃', difficulty: 'easy' as const, estimated_duration: 45, cost: 'free' as const, indoor_outdoor: 'indoor' as const },
          { title: 'Moonlight Walk', description: 'Take a peaceful walk under the moonlight', category: 'romantic', emoji: '🌙', difficulty: 'easy' as const, estimated_duration: 60, cost: 'free' as const, indoor_outdoor: 'outdoor' as const },
          { title: 'Romantic Picnic Indoors', description: 'Set up a picnic on the living room floor', category: 'romantic', emoji: '🧺', difficulty: 'easy' as const, estimated_duration: 90, cost: 'low' as const, indoor_outdoor: 'indoor' as const },
          { title: 'Star Map Creation', description: 'Create a map of the stars from your first date', category: 'romantic', emoji: '⭐', difficulty: 'medium' as const, estimated_duration: 120, cost: 'low' as const, indoor_outdoor: 'indoor' as const },
          { title: 'Love Coupon Book', description: 'Create a book of romantic coupons for each other', category: 'romantic', emoji: '🎟️', difficulty: 'easy' as const, estimated_duration: 90, cost: 'low' as const, indoor_outdoor: 'indoor' as const },

          // Adventure Activities (8)
          { title: 'Rock Climbing', description: 'Try indoor or outdoor rock climbing together', category: 'adventure', emoji: '🧗', difficulty: 'hard' as const, estimated_duration: 180, cost: 'medium' as const, indoor_outdoor: 'both' as const },
          { title: 'Kayaking Adventure', description: 'Paddle together on a lake or calm river', category: 'adventure', emoji: '🛶', difficulty: 'medium' as const, estimated_duration: 240, cost: 'medium' as const, indoor_outdoor: 'outdoor' as const },
          { title: 'Geocaching Hunt', description: 'Use GPS to find hidden treasures in your area', category: 'adventure', emoji: '📍', difficulty: 'medium' as const, estimated_duration: 120, cost: 'free' as const, indoor_outdoor: 'outdoor' as const },
          { title: 'Zip Lining', description: 'Soar through the trees on a zip line course', category: 'adventure', emoji: '🌲', difficulty: 'hard' as const, estimated_duration: 180, cost: 'high' as const, indoor_outdoor: 'outdoor' as const },
          { title: 'Hot Air Balloon', description: 'Take a romantic hot air balloon ride', category: 'adventure', emoji: '🎈', difficulty: 'easy' as const, estimated_duration: 180, cost: 'high' as const, indoor_outdoor: 'outdoor' as const },
          { title: 'Camping Under Stars', description: 'Set up camp and sleep under the stars', category: 'adventure', emoji: '⛺', difficulty: 'medium' as const, estimated_duration: 720, cost: 'medium' as const, indoor_outdoor: 'outdoor' as const },
          { title: 'Mountain Biking', description: 'Explore trails on mountain bikes', category: 'adventure', emoji: '🚵', difficulty: 'medium' as const, estimated_duration: 180, cost: 'medium' as const, indoor_outdoor: 'outdoor' as const },
          { title: 'Horseback Riding', description: 'Ride horses through scenic trails', category: 'adventure', emoji: '🐎', difficulty: 'medium' as const, estimated_duration: 180, cost: 'medium' as const, indoor_outdoor: 'outdoor' as const },

          // Creative Activities (6)
          { title: 'Pottery Making', description: 'Create ceramic pieces together at a pottery studio', category: 'creative', emoji: '🏺', difficulty: 'medium' as const, estimated_duration: 180, cost: 'medium' as const, indoor_outdoor: 'indoor' as const },
          { title: 'Painting Class', description: 'Take a couples painting class or paint at home', category: 'creative', emoji: '🎨', difficulty: 'easy' as const, estimated_duration: 120, cost: 'medium' as const, indoor_outdoor: 'indoor' as const },
          { title: 'Jewelry Making', description: 'Design and create jewelry pieces for each other', category: 'creative', emoji: '💍', difficulty: 'medium' as const, estimated_duration: 150, cost: 'medium' as const, indoor_outdoor: 'indoor' as const },
          { title: 'Photography Walk', description: 'Explore your city and capture beautiful moments', category: 'creative', emoji: '📸', difficulty: 'easy' as const, estimated_duration: 120, cost: 'free' as const, indoor_outdoor: 'outdoor' as const },
          { title: 'Candle Making', description: 'Create custom scented candles together', category: 'creative', emoji: '🕯️', difficulty: 'easy' as const, estimated_duration: 90, cost: 'low' as const, indoor_outdoor: 'indoor' as const },
          { title: 'Scrapbook Creation', description: 'Create a scrapbook of your memories together', category: 'creative', emoji: '📖', difficulty: 'easy' as const, estimated_duration: 120, cost: 'low' as const, indoor_outdoor: 'indoor' as const },

          // Fun & Games Activities (6)
          { title: 'Escape Room Challenge', description: 'Work together to solve puzzles and escape', category: 'fun', emoji: '🔐', difficulty: 'medium' as const, estimated_duration: 90, cost: 'medium' as const, indoor_outdoor: 'indoor' as const },
          { title: 'Bowling Night', description: 'Strike up some fun at the bowling alley', category: 'fun', emoji: '🎳', difficulty: 'easy' as const, estimated_duration: 120, cost: 'low' as const, indoor_outdoor: 'indoor' as const },
          { title: 'Karaoke Night', description: 'Sing your hearts out at karaoke', category: 'fun', emoji: '🎤', difficulty: 'easy' as const, estimated_duration: 120, cost: 'medium' as const, indoor_outdoor: 'indoor' as const },
          { title: 'Mini Golf', description: 'Play mini golf followed by ice cream treats', category: 'fun', emoji: '⛳', difficulty: 'easy' as const, estimated_duration: 90, cost: 'low' as const, indoor_outdoor: 'outdoor' as const },
          { title: 'Arcade Games', description: 'Compete in classic arcade games', category: 'fun', emoji: '🕹️', difficulty: 'easy' as const, estimated_duration: 90, cost: 'low' as const, indoor_outdoor: 'indoor' as const },
          { title: 'Board Game Tournament', description: 'Have a tournament with your favorite board games', category: 'fun', emoji: '🎲', difficulty: 'easy' as const, estimated_duration: 180, cost: 'free' as const, indoor_outdoor: 'indoor' as const }
        ];

        // Insert activities in batches to avoid overwhelming the database
        const batchSize = 10;
        let _totalInserted = 0;

        for (let i = 0; i < comprehensiveActivities.length; i += batchSize) {
          const batch = comprehensiveActivities.slice(i, i + batchSize);
          const { error: insertError } = await supabase
            .from('date_night_ideas_global')
            .insert(batch);

          if (insertError && !insertError.message.includes('duplicate')) {
            console.error('Error inserting batch:', insertError);
          } else {
            _totalInserted += batch.length;
          }
        }
      }
    } catch (err) {
      console.error('❌ Error in ensureComprehensiveData:', err);
    }
  }, []);

  // Load date night ideas with composite IDs
  const loadIdeas = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Temporarily disabled comprehensive data insertion for debugging
      // await ensureComprehensiveData();

      // Test direct database query first
      const { data: _directData, error: _directError } = await supabase
        .from('date_night_ideas_global')
        .select('*')
        .order('category, title');


      const ideas = await dateNightIdeasService.getAllIdeas(user?.id);


      // Filter out hidden ideas and ensure proper typing
      const visibleIdeas = ideas; // user?.id ? filterHiddenIdeas(ideas) : ideas;
      // Ensure category is always a string and handle null values for type compatibility
      const typedIdeas: DateNightIdea[] = visibleIdeas.map(idea => ({
        ...idea,
        category: idea.category || 'Date Night',
        difficulty: idea.difficulty || undefined,
        emoji: idea.emoji || undefined,
        estimatedDuration: idea.estimatedDuration || undefined,
        costLevel: idea.costLevel || undefined,
        indoorOutdoor: idea.indoorOutdoor || undefined,
        user_id: idea.user_id || undefined,
        weekNumber: idea.weekNumber || undefined,
        isHidden: idea.isHidden || undefined,
        tags: idea.tags || undefined,
        composite_id: idea.composite_id,
        source: idea.source || undefined,
        created_at: idea.created_at || undefined,
        updated_at: idea.updated_at || undefined
      }));
      setAllIdeas(typedIdeas);

      // Favorites are now handled by the shared useFavorites hook

      // // Load favorite states for all ideas
      // if (user?.id && ideas.length > 0) {
      //   try {
      //     const favorites = await favoritesService.getUserFavorites(user.id, 'date_night_idea');
      //     console.log(`✅ Loaded ${favorites.length} favorites for user`);

      //     const favoriteIds = new Set(favorites.map(f => f.item_id));
      //     const newFavoriteStates: Record<string, boolean> = {};
      //     ideas.forEach(idea => {
      //       if (idea.composite_id) {
      //         newFavoriteStates[idea.composite_id] = favoriteIds.has(idea.composite_id);
      //       }
      //     });
      //     setFavoriteStates(newFavoriteStates);
      //   } catch (e) {
      //     console.warn('Failed to load favorites; defaulting to empty', e);
      //     setFavoriteStates({});
      //   }
      // }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load date night ideas');
      console.error('Error loading date night ideas:', err);
    } finally {
      setIsLoading(false);
    }
  }, [user?.id]);

  // Load ideas on mount
  useEffect(() => {
    loadIdeas();
  }, [loadIdeas]);

  // Expose a simple refresh handler for retry button
  const refreshData = useCallback(() => {
    loadIdeas();
  }, [loadIdeas]);

  // Toggle favorite function with composite ID support
  const toggleFavorite = useCallback(async (compositeId: string, metadata?: Record<string, unknown>) => {
    if (!user?.id) {
      Alert.alert('Error', 'Please log in to favorite ideas');
      return;
    }

    try {
      await _debouncedToggleFavorite(compositeId, 'date_night_idea', undefined, metadata);
    } catch (error) {
      console.error('❌ Failed to toggle date night favorite:', error);
      Alert.alert('Error', 'Failed to update favorite. Please try again.');
    }
  }, [user?.id, _debouncedToggleFavorite]);

  // Check if an idea is favorited using shared hook
  const isFavorited = useCallback((compositeId: string): boolean => {
    return _isFavoritedByKey(compositeId, 'date_night_idea');
  }, [_isFavoritedByKey]);

  // SurpriseMe handlers
  const handleSurpriseDateNights = useCallback(async () => {
    try {
      // Get 3 random ideas from all available ideas
      const shuffled = [...(allIdeas || [])].sort(() => 0.5 - Math.random());
      const randomIdeas = shuffled.slice(0, 3);

      // Convert to SurpriseItem format
      const surpriseItems = randomIdeas
        .filter(idea => idea.composite_id) // Filter out ideas without composite_id
        .map(idea => ({
          id: idea.composite_id!,
          title: idea.title,
          description: idea.description,
          emoji: idea.emoji || '💕',
          category: idea.category || 'Date Night',
          type: 'activity' as const
        }));

      setSurpriseIdeas(surpriseItems);
      setShowSurpriseModal(true);
    } catch (error) {
      console.error('Error getting surprise date night ideas:', error);
      Alert.alert('Error', 'Failed to get surprise date night ideas. Please try again.');
    }
  }, [allIdeas]);


  // Removed unused handlers - functionality is handled directly in UI components

  // State management
  const [activeTab, setActiveTab] = useState<TabType>('discover');
  const [showAddModal, setShowAddModal] = useState(false);
  const [showFilterModal, setShowFilterModal] = useState(false);
  const [showSurpriseModal, setShowSurpriseModal] = useState(false);
  const [selectedFilter, setSelectedFilter] = useState<'all' | 'weekly' | 'favorites' | 'completed'>('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [randomIdeas, setRandomIdeas] = useState<DateNightIdea[]>([]);
  const [surpriseIdeas, setSurpriseIdeas] = useState<SurpriseItemType[]>([]);

  // Debounce search query to prevent excessive filtering
  // const debouncedSearchQuery = useDebounce(searchQuery, 300);
  const debouncedSearchQuery = searchQuery;

  const [newDateNight, setNewDateNight] = useState({
    title: '',
    description: '',
    emoji: '',
    category: 'Custom'
  });

  // Computed values
  const totalIdeas = allIdeas.length;
  const favoriteIdeas = allIdeas.filter(idea => idea.composite_id && _isFavoritedByKey(idea.composite_id, 'date_night_idea'));
  const favoriteCount = favoriteIdeas.length;

  const filteredIdeas = useMemo(() => {
    let filtered = allIdeas || [];

    // Apply source filter
    if (selectedFilter === 'weekly') {
      // For now, show all ideas - we can add week filtering later
      filtered = allIdeas || [];
    } else if (selectedFilter === 'favorites') {
      filtered = favoriteIdeas;
    } else if (selectedFilter === 'completed') {
      // For now, show empty - we can add completed status later
      filtered = [];
    }

    // Apply search filter with debounced query
    if (debouncedSearchQuery.trim()) {
      filtered = filtered.filter(idea =>
        idea.title.toLowerCase().includes(debouncedSearchQuery.toLowerCase()) ||
        idea.description.toLowerCase().includes(debouncedSearchQuery.toLowerCase()) ||
        idea.category?.toLowerCase().includes(debouncedSearchQuery.toLowerCase())
      );
    }

    return filtered;
  }, [allIdeas, favoriteIdeas, selectedFilter, debouncedSearchQuery]);

  // Group ideas by week number
  const ideasByWeek = useMemo((): DateNightIdeasByWeek => {
    const grouped: DateNightIdeasByWeek = {};

    // Initialize all weeks with empty arrays
    WEEKS.forEach(week => {
      grouped[week] = [];
    });

    // Group filtered ideas by week
    (filteredIdeas || []).forEach(idea => {
      const week = idea.weekNumber || 1; // Default to week 1 if no week specified
      if (week >= 1 && week <= 12) {
        // Convert service idea to shared type format
        const sharedIdea: import('../../shared/types').DateNightIdea = {
          ...idea,
          source: idea.source === 'global' || idea.source === 'user' ? idea.source : 'global'
        };
        grouped[week].push(sharedIdea);
      }
    });

    // Sort ideas within each week by title
    WEEKS.forEach(week => {
      grouped[week].sort((a, b) => a.title.localeCompare(b.title));
    });

    return grouped;
  }, [filteredIdeas]);

  // Flatten all ideas for FlatList rendering
  const flattenedIdeas = useMemo(() => {
    const flattened: DateNightIdea[] = [];

    // Add all ideas from all weeks
    WEEKS.forEach(week => {
      const weekIdeas = ideasByWeek[week] || [];
      flattened.push(...weekIdeas);
    });

    return flattened;
  }, [ideasByWeek]);

  // Render functions - moved here to avoid hoisting issues
  const renderIdeaCard = useCallback((idea: DateNightIdea) => {
    const isIdeaFavorited = isFavorited(idea.composite_id);
    const isLoading = favoriteLoadingStates[idea.composite_id];

    // Use consistent brand color for all date night cards
    const cardBackgroundColor = colors.sageGreen;

    return (
      <TouchableOpacity key={idea.id} style={styles.ideaCard} activeOpacity={0.8}>
        <View style={[styles.cardSurface, { backgroundColor: cardBackgroundColor }]} >
          {/* Top section with heart, hide button, and category */}
          <View style={styles.cardTopSection}>
            <View style={styles.categoryContainer}>
              {idea.category && (
                <Text style={styles.categoryText}>{idea.category}</Text>
              )}
            </View>
            <View style={styles.cardActions}>
              <TouchableOpacity
                onPress={() => handleHideIdea(idea.composite_id, idea.title)}
                style={styles.hideButton}
                disabled={isLoading}
                accessibilityLabel="Hide this idea"
              >
                <EyeOff size={18} color={colors.white} />
              </TouchableOpacity>
              <HeartToggle
                isFavorited={isIdeaFavorited}
                onToggle={async (_newFavoritedState: boolean) => {
                  try {
                    await toggleFavorite(idea.composite_id, {
                      user_action: 'manual_toggle',
                      source: 'date_night_page',
                      timestamp: Date.now(),
                    });
                  } catch (error) {
                    console.error('❌ [DATENIGHT] Failed to toggle date night favorite:', error);
                    Alert.alert('Error', 'Failed to update favorite. Please try again.');
                  }
                }}
                size={20}
                style={styles.heartToggle}
                disabled={isLoading}
                accessibilityLabel={`${isIdeaFavorited ? 'Remove from' : 'Add to'} favorites`}
              />
            </View>
          </View>

          {/* Title and emoji */}
          <View style={styles.cardTitleSection}>
            {idea.emoji && <Text style={styles.emoji}>{idea.emoji}</Text>}
            <Text style={styles.cardTitle}>{idea.title}</Text>
          </View>

          {/* Description */}
          <Text style={styles.cardDescription} numberOfLines={4} ellipsizeMode="tail">
            {idea.description}
          </Text>

          {/* Remove favorite button for favorited items */}
          {isIdeaFavorited && (
            <TouchableOpacity
              onPress={() => handleRemoveIdea(idea.composite_id)}
              style={styles.deleteButton}
              disabled={isLoading}
            >
              <Trash2 size={16} color={colors.white} />
            </TouchableOpacity>
          )}

          {/* Completed indicator - feature coming soon */}
          {/* {false && (
            <View style={styles.completedInfo}>
              <CheckCircle size={16} color={colors.success} />
              <Text style={styles.completedText}>Completed!</Text>
            </View>
          )} */}
        </View>
      </TouchableOpacity>
    );
  }, [isFavorited, favoriteLoadingStates, toggleFavorite, handleHideIdea, handleRemoveIdea]);

  // Render item for FlatList with performance optimization
  const renderFlatListItem = useCallback(({ item }: { item: DateNightIdea }) => {
    return renderIdeaCard(item);
  }, [renderIdeaCard]);

  // Key extractor for FlatList with stable keys
  const keyExtractor = useCallback((item: DateNightIdea) => {
    return item.composite_id || item.id || `${item.title}-${item.category}`;
  }, []);

  // Removed unused getItemLayout - FlatList works fine without it for this use case

  // Event handlers
  const handleAddDateNight = useCallback(async () => {
    if (!newDateNight.title.trim() || !newDateNight.description.trim()) {
      Alert.alert('Missing Information', 'Please provide both a title and description for your Date Night idea.');
      return;
    }

    try {

      const success = await createUserDateNightIdea({
        title: newDateNight.title.trim(),
        description: newDateNight.description.trim(),
        category: newDateNight.category || 'Custom',
        emoji: newDateNight.emoji || '💕',
        difficulty: 'easy',
        cost: 'medium',
        indoor_outdoor: 'both'
      });


      if (success) {
        Alert.alert('Success!', 'Your date night idea has been added successfully!');
        setNewDateNight({ title: '', description: '', emoji: '', category: 'Custom' });
        setShowAddModal(false);

        // Add a small delay to ensure database consistency
        await new Promise(resolve => setTimeout(resolve, 500));

        // Reload ideas to show the new one
        await loadIdeas();
      } else {
        Alert.alert('Error', 'Failed to add your date night idea. Please try again.');
      }
    } catch (error) {
      console.error('Error adding date night idea:', error);
      Alert.alert('Error', 'Failed to add your date night idea. Please try again.');
    }
  }, [newDateNight, createUserDateNightIdea, loadIdeas]);

  // Removed unused error handler - errors are handled inline where they occur

  const handleSaveIdea = useCallback(async (ideaId: string, status: 'favorite' | 'planned' | 'completed') => {
    try {
      // Find the idea by ID and use composite ID for favoriting
      const idea = allIdeas?.find(i => i.id === ideaId || i.composite_id === ideaId);
      if (idea && idea.composite_id && status === 'favorite') {
        await toggleFavorite(idea.composite_id, {
          user_action: 'save_idea',
          source: 'date_night_page',
          timestamp: Date.now(),
        });
        Alert.alert('Saved!', `Idea has been saved to your ${status} list!`);
      } else {
        Alert.alert('Info', `${status === 'planned' ? 'Planning' : 'Completion'} features coming soon!`);
      }
    } catch {
      Alert.alert('Error', 'Failed to save idea. Please try again.');
    }
  }, [allIdeas, toggleFavorite]);

  const handleRemoveIdea = useCallback(async (ideaId: string) => {
    Alert.alert(
      'Remove Favorite',
      'Are you sure you want to remove this idea from your favorites?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Remove',
          style: 'destructive',
          onPress: async () => {
            try {
              // Find the idea by ID and use composite ID for unfavoriting
              const idea = allIdeas?.find(i => i.id === ideaId || i.composite_id === ideaId);
              if (idea && idea.composite_id) {
                await toggleFavorite(idea.composite_id, {
                  user_action: 'remove_favorite',
                  source: 'date_night_page',
                  timestamp: Date.now(),
                });
                Alert.alert('Removed', 'Idea has been removed from your favorites.');
              } else {
                Alert.alert('Error', 'Could not find the date night idea.');
              }
            } catch {
              Alert.alert('Error', 'Failed to remove idea. Please try again.');
            }
          }
        }
      ]
    );
  }, [allIdeas, toggleFavorite]);

  const handleHideIdea = useCallback(async (compositeId: string, title: string) => {
    try {
      Alert.alert(
        'Hide Idea',
        `Are you sure you want to hide "${title}"? You can unhide it later in your preferences.`,
        [
          { text: 'Cancel', style: 'cancel' },
          {
            text: 'Hide',
            style: 'destructive',
            onPress: async () => {
              try {
                // await hideIdea(compositeId);
                // Reload ideas to reflect the change
                await loadIdeas();
                Alert.alert('Hidden', 'Idea has been hidden from your feed.');
              } catch (error) {
                console.error('Failed to hide idea:', error);
                Alert.alert('Error', 'Failed to hide idea. Please try again.');
              }
            }
          }
        ]
      );
    } catch (error) {
      console.error('Error in handleHideIdea:', error);
    }
  }, [loadIdeas]);

  const _handlePlanDateNight = useCallback((idea: DateNightIdea) => {
    const currentDate = new Date();
    const suggestedDate = new Date(currentDate.getTime() + 7 * 24 * 60 * 60 * 1000);

    Alert.alert(
      'Plan Date Night ✨',
      `Planning "${idea.title}" for ${suggestedDate.toLocaleDateString()}!`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Plan It!',
          onPress: () => {
            handleSaveIdea(idea.id, 'planned');
            Alert.alert(
              'Date Night Planned! 📅',
              `"${idea.title}" has been added to your planned date nights!\n\nYou'll both receive reminders as the date approaches!`,
              [{ text: 'Perfect! 💕' }]
            );
          }
        }
      ]
    );
  }, [handleSaveIdea]);

  // Removed unused plan handler - functionality is handled directly in UI


  // Removed unused difficulty color function

  const renderDiscoverTab = () => (
    <ScrollView style={styles.tabContent} showsVerticalScrollIndicator={false}>
      <View style={styles.headerSection}>
        <Text style={styles.tabTitle}>Discover Date Nights ✨</Text>
        <Text style={styles.tabSubtitle}>Find your next perfect evening together</Text>
      </View>

      <SearchBar
        value={searchQuery}
        onChangeText={setSearchQuery}
        placeholder="Search date night ideas..."
        onFilterPress={() => setShowFilterModal(true)}
        showFilter={true}
      />

      <View style={styles.quickActions}>
        <View style={{ width: '48%' }}>
          <DSButton title="Surprise Us!" onPress={handleSurpriseDateNights} leftIcon={<Shuffle size={20} color={colors.white} />} />
        </View>
        <View style={{ width: '48%' }}>
          <DSButton title="Add Idea" onPress={() => setShowAddModal(true)} leftIcon={<Plus size={20} color={colors.white} />} />
        </View>
      </View>

      {(randomIdeas?.length || 0) > 0 && (
        <View style={styles.randomIdeasContainer}>
          <View style={styles.randomIdeasHeader}>
            <Text style={styles.randomIdeasTitle}>🎲 Random Picks</Text>
            <TouchableOpacity onPress={() => setRandomIdeas([])}>
              <X size={20} color={colors.textSecondary} />
            </TouchableOpacity>
          </View>
          {randomIdeas?.map(renderIdeaCard) || []}
        </View>
      )}

      {/* Render all ideas in a 2-column FlatList with performance optimizations */}
      <FlatList
        data={flattenedIdeas}
        renderItem={renderFlatListItem}
        keyExtractor={keyExtractor}
        numColumns={2}
        columnWrapperStyle={styles.flatListRow}
        contentContainerStyle={styles.flatListContainer}
        showsVerticalScrollIndicator={false}
        scrollEnabled={false} // Disable FlatList scrolling since it's inside ScrollView
        removeClippedSubviews={true} // Performance optimization
        maxToRenderPerBatch={10} // Render 10 items per batch
        windowSize={10} // Keep 10 screens worth of items in memory
        initialNumToRender={8} // Render 8 items initially (4 rows)
        updateCellsBatchingPeriod={50} // Batch updates every 50ms
        ListEmptyComponent={
          <View style={styles.emptyState}>
            <Text style={styles.emptyStateText}>No date night ideas found</Text>
            <Text style={styles.emptyStateSubtext}>Try adjusting your filters or search terms</Text>
          </View>
        }
      />
    </ScrollView>
  );

  const renderMyIdeasTab = () => (
    <ScrollView style={styles.tabContent} showsVerticalScrollIndicator={false}>
      <View style={styles.headerSection}>
        <Text style={styles.tabTitle}>My Date Nights 💕</Text>
        <Text style={styles.tabSubtitle}>Your saved and planned date nights</Text>
      </View>

      <View style={styles.statsContainer}>
        <StatCard
          number={totalIdeas}
          label="Total Ideas"
          icon={<Calendar size={20} color={colors.surface} />}
        />
        <StatCard
          number={favoriteCount}
          label="Favorites"
          icon={<Heart size={20} color={colors.surface} />}
        />
        <StatCard
          number={0}
          label="Completed"
          icon={<CheckCircle size={20} color={colors.surface} />}
        />
      </View>

      {(favoriteIdeas.length || 0) === 0 ? (
        <EmptyState
          icon={<Star size={48} color={colors.textTertiary} />}
          title="No saved date night ideas yet"
          subtitle="Start exploring and saving ideas you love!"
        />
      ) : (
        favoriteIdeas.map(idea => renderIdeaCard(idea))
      )}
    </ScrollView>
  );

  const renderMealsTab = () => (
    <Meals />
  );


  const renderCurrentTab = () => {
    switch (activeTab) {
      case 'discover':
        return renderDiscoverTab();
      case 'my-ideas':
        return renderMyIdeasTab();
      case 'meals':
        return renderMealsTab();
      default:
        return renderDiscoverTab();
    }
  };

  // Loading state
  if (isLoading) {
    return (
      <View style={styles.container}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <Text style={styles.loadingText}>Loading date night ideas...</Text>
        </View>
      </View>
    );
  }

  // Error state
  if (error) {
    return (
      <View style={styles.container}>
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>Error loading date night ideas</Text>
          <View style={{ alignSelf: 'stretch' }}>
            <DSButton title="Retry" onPress={refreshData} />
          </View>
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.tabContainer}>
        <TopTabs
          backgroundColor={colors.secondary}
          tabs={[
            { key: 'discover', label: 'Discover' },
            { key: 'my-ideas', label: 'Our Ideas' },
            { key: 'meals', label: 'Meals' },
          ]}
          activeKey={activeTab}
          onChange={(key: any) => setActiveTab(key as TabType)}
        />
      </View>

      <View style={styles.contentContainer}>
        {renderCurrentTab()}
      </View>

      <CommonModal
        visible={showAddModal}
        onClose={() => setShowAddModal(false)}
        title="Add Date Night Idea"
      >
        <View style={styles.inputGroup}>
          <Text style={styles.inputLabel}>Title *</Text>
          <StyledInput
            value={newDateNight.title}
            onChangeText={(text: any) => setNewDateNight(prev => ({ ...prev, title: text }))}
            placeholder="e.g., Coffee Shop Crawl"
          />
        </View>

        <View style={styles.inputGroup}>
          <Text style={styles.inputLabel}>Description *</Text>
          <StyledInput
            value={newDateNight.description}
            onChangeText={(text: any) => setNewDateNight(prev => ({ ...prev, description: text }))}
            placeholder="Describe your date night idea..."
            multiline
            numberOfLines={4}
          />
        </View>

        <View style={styles.inputGroup}>
          <Text style={styles.inputLabel}>Emoji (Optional)</Text>
          <StyledInput
            value={newDateNight.emoji}
            onChangeText={(text: any) => setNewDateNight(prev => ({ ...prev, emoji: text }))}
            placeholder="🎭 🎨 🍕"
          />
        </View>

        <View style={styles.modalFooter}>
          <View style={{ flex: 1 }}>
            <DSButton title="Cancel" onPress={() => setShowAddModal(false)} variant="outline" />
          </View>
          <View style={{ flex: 1 }}>
            <DSButton title="Add Idea" onPress={handleAddDateNight} variant="primary" />
          </View>
        </View>
      </CommonModal>

      <Modal
        visible={showFilterModal}
        animationType="slide"
        transparent={true}
        onRequestClose={() => setShowFilterModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Filter Date Nights</Text>
              <TouchableOpacity onPress={() => setShowFilterModal(false)}>
                <X size={24} color={colors.textSecondary} />
              </TouchableOpacity>
            </View>

            <View style={styles.modalBody}>
              {[
                { key: 'all', label: 'All Ideas', count: totalIdeas },
                { key: 'weekly', label: 'Weekly Challenges', count: totalIdeas },
                { key: 'favorites', label: 'Your Favorites', count: favoriteCount },
                { key: 'completed', label: 'Completed', count: 0 }
              ].map((filter: any) => (
                <TouchableOpacity
                  key={filter.key}
                  style={[
                    styles.filterOption,
                    selectedFilter === filter.key && styles.filterOptionSelected
                  ]}
                  onPress={() => {
                    setSelectedFilter(filter.key as any);
                    setShowFilterModal(false);
                  }}
                >
                  <Text style={[
                    styles.filterOptionText,
                    selectedFilter === filter.key && styles.filterOptionTextSelected
                  ]}>
                    {filter.label}
                  </Text>
                  <View style={styles.filterOptionCount}>
                    <Text style={[
                      styles.filterOptionCountText,
                      selectedFilter === filter.key && styles.filterOptionCountTextSelected
                    ]}>
                      {filter.count}
                    </Text>
                  </View>
                </TouchableOpacity>
              ))}
            </View>
          </View>
        </View>
      </Modal>

      {/* Surprise Modal */}
      <Modal
        visible={showSurpriseModal}
        animationType="slide"
        transparent={true}
        onRequestClose={() => setShowSurpriseModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>🎲 Your Surprise Date Nights!</Text>
              <TouchableOpacity onPress={() => setShowSurpriseModal(false)}>
                <X size={24} color={colors.textSecondary} />
              </TouchableOpacity>
            </View>

            <ScrollView style={styles.modalBody}>
              {surpriseIdeas?.map((item: any) => {
                const idea = allIdeas?.find(i => i.composite_id === item.id || i.id === item.id);
                if (!idea) return null;

                const isIdeaFavorited = idea.composite_id ? isFavorited(idea.composite_id) : false;
                const isLoading = idea.composite_id ? favoriteLoadingStates[idea.composite_id] : false;

                return (
                  <View key={item.id} style={styles.surpriseIdeaCard}>
                    <View
                      style={[styles.surpriseCardSurface, { backgroundColor: colors.sageGreen }]}
                    >
                      {/* Top section with heart and category */}
                      <View style={styles.surpriseCardTopSection}>
                        <View style={styles.surpriseCategoryContainer}>
                          {idea.category && (
                            <Text style={styles.surpriseCategoryText}>{idea.category}</Text>
                          )}
                        </View>
                        <HeartToggle
                          isFavorited={isIdeaFavorited}
                          onToggle={async (newFavoritedState: any) => {
                            try {
                              if (idea.composite_id) {
                                await toggleFavorite(idea.composite_id, {
                                  user_action: 'manual_toggle',
                                  source: 'surprise_modal',
                                  timestamp: Date.now(),
                                });
                                console.log(`Surprise date night idea ${idea.composite_id} ${newFavoritedState ? 'favorited' : 'unfavorited'}`);
                              }
                            } catch (error) {
                              console.error('Failed to toggle surprise date night favorite:', error);
                              Alert.alert('Error', 'Failed to update favorite. Please try again.');
                            }
                          }}
                          size={20}
                          style={styles.surpriseHeartToggle}
                          disabled={isLoading}
                          accessibilityLabel={`${isIdeaFavorited ? 'Remove from' : 'Add to'} favorites`}
                        />
                      </View>

                      {/* Title and emoji */}
                      <View style={styles.surpriseCardTitleSection}>
                        {idea.emoji && <Text style={styles.surpriseEmoji}>{idea.emoji}</Text>}
                        <Text style={styles.surpriseCardTitle}>{idea.title}</Text>
                      </View>

                      {/* Description */}
                      <Text style={styles.surpriseCardDescription}>{idea.description}</Text>
                    </View>
                  </View>
                );
              })}
            </ScrollView>
          </View>
        </View>
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.backgroundPrimary,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  loadingText: {
    fontSize: 18,
    color: colors.textSecondary,
    marginTop: 16,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    fontSize: 18,
    color: colors.error,
    marginBottom: 16,
  },

  tabContainer: {
    paddingTop: 20,
    paddingHorizontal: 20,
    paddingBottom: 10,
  },
  // TopTabs uses shared styles; local tab styles removed to avoid duplication
  contentContainer: {
    flex: 1,
  },
  tabContent: {
    flex: 1,
    paddingTop: 24, // More top padding for better spacing
    paddingHorizontal: 16, // Slightly less horizontal padding to give cards more space
    paddingBottom: 20,
  },
  headerSection: {
    alignItems: 'center',
    marginBottom: 30,
  },
  tabTitle: {
    fontSize: 24,
    fontWeight: '700',
    color: colors.textPrimary,
    marginBottom: 8,
    textAlign: 'center',
  },
  tabSubtitle: {
    fontSize: 16,
    color: colors.textSecondary,
    textAlign: 'center',
  },
  searchFilterBar: {
    flexDirection: 'row',
    marginBottom: 20,
    gap: 12,
  },
  searchContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.backgroundSecondary,
    borderRadius: 12,
    paddingHorizontal: 16,
    borderWidth: 1,
    borderColor: colors.borderLight,
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    paddingVertical: 12,
    fontSize: 16,
    color: colors.textPrimary,
  },
  filterButton: {
    backgroundColor: colors.backgroundSecondary,
    borderRadius: 12,
    padding: 12,
    borderWidth: 1,
    borderColor: colors.borderLight,
  },
  quickActions: {
    flexDirection: 'row',
    marginBottom: 20,
    justifyContent: 'space-between',
  },
  quickActionButton: {
    width: '48%', // Same width as idea cards
    borderRadius: 12,
    overflow: 'hidden',
  },
  quickActionButtonContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 14,
    gap: 8,
  },
  quickActionButtonText: {
    color: colors.white,
    fontSize: 16,
    fontWeight: '600',
  },
  randomizeButton: {
    flex: 1,
    borderRadius: 12,
    overflow: 'hidden',
  },
  randomizeButtonGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 14,
    gap: 8,
  },
  randomizeButtonText: {
    color: colors.white,
    fontSize: 16,
    fontWeight: '600',
  },
  addButton: {
    flex: 1,
    borderRadius: 12,
    overflow: 'hidden',
  },
  addButtonGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 14,
    gap: 8,
  },
  addButtonText: {
    color: colors.white,
    fontSize: 16,
    fontWeight: '600',
  },
  randomIdeasContainer: {
    marginBottom: 20,
  },
  randomIdeasHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  randomIdeasTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.textPrimary,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: colors.textPrimary,
    marginBottom: 16,
  },
  ideasGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    gap: 12,
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: 40,
  },
  emptyStateText: {
    fontSize: 16,
    color: colors.textSecondary,
    textAlign: 'center',
    marginTop: 16,
    marginBottom: 8,
  },
  emptyStateSubtext: {
    fontSize: 14,
    color: colors.textTertiary,
    textAlign: 'center',
  },
  ideaCard: {
    width: '48%', // Ensures 2 columns with gap
    minHeight: 180, // Increased height for better content display
    marginBottom: 20, // Increased spacing between rows
    marginHorizontal: 4, // Small horizontal margin for better spacing
    borderRadius: 20, // Increased border radius for modern look
    overflow: 'hidden',
    elevation: 4, // Increased elevation for better depth
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.15, // Slightly stronger shadow
    shadowRadius: 8, // Larger shadow radius for softer effect
    backgroundColor: 'rgba(255, 255, 255, 0.95)', // Glass-morphism background
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)', // Subtle glass border
  },
  cardSurface: {
    flex: 1,
    padding: 16, // Increased padding for better spacing
    position: 'relative',
    justifyContent: 'space-between',
    borderRadius: 20, // Match parent border radius
    // Glass-morphism effect with subtle gradient overlay
    backgroundColor: 'transparent', // Let the parent handle the background
  },
  cardTopSection: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  categoryContainer: {
    flex: 1,
  },
  categoryText: {
    fontSize: 11,
    fontWeight: '600', // Bolder for better visibility
    color: 'rgba(255, 255, 255, 0.95)',
    textTransform: 'uppercase',
    letterSpacing: 0.8, // Increased letter spacing for better readability
    backgroundColor: 'rgba(255, 255, 255, 0.15)', // Subtle background for better contrast
    paddingHorizontal: 8,
    paddingVertical: 3,
    borderRadius: 12, // Rounded pill shape
    alignSelf: 'flex-start', // Don't stretch to full width
    overflow: 'hidden',
  },
  cardActions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  hideButton: {
    padding: 4,
    borderRadius: 12,
    backgroundColor: 'rgba(0, 0, 0, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  heartToggle: {
    position: 'relative',
    top: 0,
    right: 0,
    zIndex: 1,
    padding: 2,
  },
  cardTitleSection: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
    marginBottom: 6,
  },
  emoji: {
    fontSize: 20,
  },
  cardTitle: {
    fontSize: 17, // Slightly larger for better readability
    fontWeight: '700', // Bolder weight for better hierarchy
    color: colors.white,
    flex: 1,
    lineHeight: 22, // Better line height for readability
    textShadowColor: 'rgba(0, 0, 0, 0.3)', // Subtle text shadow for better contrast
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },
  deleteButton: {
    padding: 4,
  },
  cardDescription: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.95)', // Slightly transparent for hierarchy
    lineHeight: 21, // Better line height for readability
    marginBottom: 12,
    flex: 1,
    textShadowColor: 'rgba(0, 0, 0, 0.2)', // Subtle text shadow
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 1,
  },
  cardFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  sourceIndicator: {
    backgroundColor: colors.white,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  sourceText: {
    fontSize: 12,
    fontWeight: '500',
    color: colors.primary,
  },
  difficultyIndicator: {
    backgroundColor: colors.white,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  difficultyText: {
    fontSize: 12,
    fontWeight: '500',
    color: colors.primary,
  },
  savedButton: {
    backgroundColor: colors.success,
  },
  completedInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginTop: 12,
  },
  completedText: {
    fontSize: 14,
    color: colors.white,
  },
  statsContainer: {
    flexDirection: 'row',
    marginBottom: 24,
    gap: 12,
  },
  statCard: {
    flex: 1,
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  statNumber: {
    fontSize: 24,
    fontWeight: '700',
    color: colors.primary,
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    color: colors.textSecondary,
    fontWeight: '500',
  },
  surpriseSection: {
    backgroundColor: colors.white,
    borderRadius: 16,
    padding: 24,
    marginBottom: 24,
    alignItems: 'center',
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  surpriseButton: {
    backgroundColor: colors.accent3,
    paddingVertical: 16,
    paddingHorizontal: 32,
    borderRadius: 12,
    marginBottom: 12,
  },
  surpriseButtonText: {
    color: colors.white,
    fontSize: 18,
    fontWeight: '600',
  },
  surpriseSubtext: {
    fontSize: 14,
    color: colors.textSecondary,
    textAlign: 'center',
    fontStyle: 'italic',
  },
  horizontalScroll: {
    flexDirection: 'row',
  },
  horizontalCard: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
    marginRight: 12,
    minWidth: 120,
    alignItems: 'center',
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  horizontalEmoji: {
    fontSize: 24,
    marginBottom: 8,
  },
  horizontalTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.textPrimary,
    textAlign: 'center',
    marginBottom: 12,
    lineHeight: 18,
  },
  smallButton: {
    backgroundColor: colors.primary,
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 6,
  },
  smallButtonText: {
    color: colors.white,
    fontSize: 12,
    fontWeight: '600',
  },
  comingSoonText: {
    fontSize: 16,
    color: colors.textSecondary,
    textAlign: 'center',
    fontStyle: 'italic',
    padding: 20,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: colors.background,
    borderRadius: 20,
    width: width - 40,
    maxHeight: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: colors.borderLight,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: colors.textPrimary,
  },
  modalBody: {
    padding: 20,
  },
  inputGroup: {
    marginBottom: 20,
  },
  inputLabel: {
    fontSize: 16,
    fontWeight: '500',
    color: colors.textPrimary,
    marginBottom: 8,
  },
  textInput: {
    backgroundColor: colors.backgroundSecondary,
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    color: colors.textPrimary,
    borderWidth: 1,
    borderColor: colors.borderLight,
  },
  textArea: {
    backgroundColor: colors.backgroundSecondary,
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    color: colors.textPrimary,
    borderWidth: 1,
    borderColor: colors.borderLight,
    textAlignVertical: 'top',
  },
  modalFooter: {
    flexDirection: 'row',
    padding: 20,
    gap: 12,
    borderTopWidth: 1,
    borderTopColor: colors.borderLight,
  },
  cancelButton: {
    flex: 1,
    backgroundColor: colors.backgroundSecondary,
    paddingVertical: 14,
    borderRadius: 12,
    alignItems: 'center',
  },
  cancelButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.textSecondary,
  },
  saveButton: {
    flex: 1,
    borderRadius: 12,
    overflow: 'hidden',
  },
  saveButtonGradient: {
    paddingVertical: 14,
    alignItems: 'center',
  },
  saveButtonText: {
    color: colors.white,
    fontSize: 16,
    fontWeight: '600',
  },
  filterOption: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 16,
    paddingHorizontal: 20,
    borderRadius: 12,
    marginBottom: 8,
  },
  filterOptionSelected: {
    backgroundColor: colors.primary,
  },
  filterOptionText: {
    fontSize: 16,
    color: colors.textPrimary,
  },
  filterOptionTextSelected: {
    color: colors.white,
  },
  filterOptionCount: {
    backgroundColor: colors.backgroundSecondary,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  filterOptionCountText: {
    fontSize: 12,
    fontWeight: '600',
    color: colors.textSecondary,
  },
  filterOptionCountTextSelected: {
    color: colors.white,
  },
  // Surprise modal styles
  surpriseIdeaCard: {
    marginBottom: 16,
    borderRadius: 16,
    overflow: 'hidden',
    elevation: 3,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
  },
  surpriseCardSurface: {
    padding: 16,
    position: 'relative',
  },
  surpriseCardTopSection: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  surpriseCategoryContainer: {
    flex: 1,
  },
  surpriseCategoryText: {
    fontSize: 12,
    fontWeight: '500',
    color: colors.white,
    opacity: 0.9,
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },
  surpriseHeartToggle: {
    position: 'relative',
    top: 0,
    right: 0,
    zIndex: 1,
    padding: 2,
  },
  surpriseCardTitleSection: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginBottom: 8,
  },
  surpriseEmoji: {
    fontSize: 24,
  },
  surpriseCardTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.white,
    flex: 1,
  },
  surpriseCardDescription: {
    fontSize: 14,
    color: colors.white,
    lineHeight: 20,
    marginBottom: 12,
  },
  // FlatList styles for 2-column layout
  flatListContainer: {
    paddingHorizontal: 8, // Better padding for card spacing
    paddingBottom: 20, // Bottom padding for better scrolling experience
  },
  flatListRow: {
    justifyContent: 'space-between',
    paddingHorizontal: 4,
    marginBottom: 4, // Small margin between rows
  },
});
