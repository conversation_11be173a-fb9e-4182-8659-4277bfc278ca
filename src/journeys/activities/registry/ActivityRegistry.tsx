/**
 * Activity Registry
 * Replaces the hardcoded allActivities array with a dynamic registry system
 * Preserves exact same interfaces and functionality as the original
 */

import {
    Award,
    BookOpen,
    Calendar,
    Heart,
    MessageCircle,
    Puzzle,
    Star,
    Target,
    Users,
    Zap
} from 'lucide-react-native';
import React from 'react';

import { colors } from '../../../utils/colors';

// Preserve exact same interfaces from Activities.tsx
export interface DualEntryPrompt {
  id: string;
  question: string;
  type: 'text' | 'list' | 'guess';
  category?: string;
  difficulty?: 'easy' | 'medium' | 'hard';
}

export interface Activity {
  id: string;
  title: string;
  description: string;
  category: 'getting-to-know' | 'communication' | 'fun' | 'reflection' | 'skills' | 'planning';
  difficulty: 'easy' | 'medium' | 'hard';
  duration: string;
  players: number;
  icon: React.ReactNode;
  route: string;
  points: number;
  isAvailable: boolean;
  prompts?: DualEntryPrompt[];
  theme?: 'default' | 'romantic' | 'playful' | 'reflective';
}

class ActivityRegistryService {
  private activities: Map<string, Activity> = new Map();
  private categories: Map<Activity['category'], Activity[]> = new Map();

  constructor() {
    this.initializeCategories();
    this.registerAllActivities();
  }

  private initializeCategories() {
    const categories: Activity['category'][] = [
      'getting-to-know',
      'communication',
      'fun',
      'reflection',
      'skills',
      'planning'
    ];

    categories.forEach(category => {
      this.categories.set(category, []);
    });
  }

  private registerAllActivities() {
    // Register all activities from the original hardcoded array
    const allActivities: Activity[] = [
      // Daily Questions Activity
      {
        id: 'daily-questions',
        title: 'Daily Questions',
        description: 'Answer thoughtful questions together every day to strengthen your bond.',
        category: 'getting-to-know',
        difficulty: 'easy',
        duration: '5-10 min',
        players: 2,
        icon: <MessageCircle size={24} color={colors.white} />,
        route: '/daily-questions',
        points: 10,
        isAvailable: true,
        theme: 'reflective',
      },
      // Getting to Know You Activities
      {
        id: 'match-activity',
        title: 'The Match Activity',
        description: 'How well do you know each other? Guess your partner\'s answers to fun questions.',
        category: 'getting-to-know',
        difficulty: 'easy',
        duration: '15-20 min',
        players: 2,
        icon: <Users size={24} color={colors.white} />,
        route: '/week-one?section=0',
        points: 25,
        isAvailable: true,
        theme: 'playful',
        prompts: [
          {
            id: 'match-1',
            question: 'What is your partner\'s favorite type of music?',
            type: 'text',
            category: 'preferences',
            difficulty: 'easy'
          },
          {
            id: 'match-2',
            question: 'What would your partner choose: beach vacation or mountain retreat?',
            type: 'text',
            category: 'preferences',
            difficulty: 'easy'
          },
          {
            id: 'match-3',
            question: 'What is your partner\'s biggest fear?',
            type: 'text',
            category: 'personal',
            difficulty: 'medium'
          },
          {
            id: 'match-4',
            question: 'What is your partner\'s dream job?',
            type: 'text',
            category: 'aspirations',
            difficulty: 'medium'
          },
          {
            id: 'match-5',
            question: 'What is your partner\'s most embarrassing moment?',
            type: 'text',
            category: 'personal',
            difficulty: 'hard'
          }
        ]
      },
      {
        id: 'would-you-rather',
        title: 'Would You Rather?',
        description: 'Fun dilemmas that reveal preferences and spark interesting conversations.',
        category: 'getting-to-know',
        difficulty: 'easy',
        duration: '10-15 min',
        players: 2,
        icon: <Target size={24} color={colors.white} />,
        route: '/week-three?section=0',
        points: 25,
        isAvailable: true
      },
      {
        id: 'strengths-bingo',
        title: 'Strengths Bingo',
        description: 'Discover and celebrate each other\'s unique strengths and talents.',
        category: 'getting-to-know',
        difficulty: 'easy',
        duration: '20-25 min',
        players: 2,
        icon: <Star size={24} color={colors.white} />,
        route: '/week-two?section=0',
        points: 25,
        isAvailable: true
      },
      {
        id: 'dream-vacation',
        title: 'Dream Vacation',
        description: 'Plan your perfect getaway together using creative adjectives.',
        category: 'getting-to-know',
        difficulty: 'medium',
        duration: '15-20 min',
        players: 2,
        icon: <Calendar size={24} color={colors.white} />,
        route: '/week-five?section=0',
        points: 25,
        isAvailable: true
      },
      {
        id: 'playlist-creation',
        title: 'Create a Playlist',
        description: 'Build a shared music collection that represents your relationship.',
        category: 'getting-to-know',
        difficulty: 'easy',
        duration: '20-30 min',
        players: 2,
        icon: <Zap size={24} color={colors.white} />,
        route: '/week-four?section=0',
        points: 25,
        isAvailable: true
      },
      {
        id: 'sharing-memories',
        title: 'Sharing Memories',
        description: 'Exchange favorite memories and create new ones together.',
        category: 'getting-to-know',
        difficulty: 'medium',
        duration: '25-35 min',
        players: 2,
        icon: <Heart size={24} color={colors.white} />,
        route: '/week-six?section=0',
        points: 25,
        isAvailable: true
      },
      {
        id: 'superhero-chart',
        title: 'Superhero Duo Chart',
        description: 'Create your superhero personas and discover your combined powers.',
        category: 'getting-to-know',
        difficulty: 'medium',
        duration: '20-25 min',
        players: 2,
        icon: <Zap size={24} color={colors.white} />,
        route: '/week-seven?section=0',
        points: 25,
        isAvailable: true
      },
      {
        id: 'perfect-saturday',
        title: 'Perfect Saturday Activity',
        description: 'Design your ideal Saturday and blend your preferences together.',
        category: 'getting-to-know',
        difficulty: 'easy',
        duration: '15-20 min',
        players: 2,
        icon: <Calendar size={24} color={colors.white} />,
        route: '/week-eight?section=0',
        points: 25,
        isAvailable: true
      },
      {
        id: 'create-crest',
        title: 'Create a Crest',
        description: 'Design a family crest that represents your shared values and dreams.',
        category: 'getting-to-know',
        difficulty: 'medium',
        duration: '30-40 min',
        players: 2,
        icon: <Puzzle size={24} color={colors.white} />,
        route: '/week-nine?section=0',
        points: 25,
        isAvailable: true
      },
      {
        id: 'dream-bank',
        title: 'Dream Bank ($5M)',
        description: 'Imagine what you\'d do with $5 million and share your dreams.',
        category: 'getting-to-know',
        difficulty: 'easy',
        duration: '15-20 min',
        players: 2,
        icon: <Award size={24} color={colors.white} />,
        route: '/week-ten?section=0',
        points: 25,
        isAvailable: true
      },
      {
        id: 'love-language-quiz',
        title: 'Love Language Quiz',
        description: 'Discover your love languages and how to express love better.',
        category: 'getting-to-know',
        difficulty: 'medium',
        duration: '20-25 min',
        players: 2,
        icon: <Heart size={24} color={colors.white} />,
        route: '/week-eleven?section=0',
        points: 25,
        isAvailable: true
      },
      {
        id: 'build-story',
        title: 'Build-a-Story Activity',
        description: 'Create a collaborative story together, one sentence at a time.',
        category: 'getting-to-know',
        difficulty: 'easy',
        duration: '15-25 min',
        players: 2,
        icon: <BookOpen size={24} color={colors.white} />,
        route: '/week-twelve?section=0',
        points: 25,
        isAvailable: true
      },

      // Communication Activities
      {
        id: 'chat-prompts',
        title: 'Deep Chat Prompts',
        description: 'Meaningful conversation starters for deeper connection.',
        category: 'communication',
        difficulty: 'easy',
        duration: '20-30 min',
        players: 2,
        icon: <MessageCircle size={24} color={colors.white} />,
        route: '/week-one?section=2',
        points: 15,
        isAvailable: true
      },
      {
        id: 'soft-startup',
        title: 'Soft Start-Up Practice',
        description: 'Learn to communicate concerns using "I" statements.',
        category: 'communication',
        difficulty: 'hard',
        duration: '25-35 min',
        players: 2,
        icon: <MessageCircle size={24} color={colors.white} />,
        route: '/week-one?section=3',
        points: 30,
        isAvailable: true
      },
      {
        id: 'five-to-one-ratio',
        title: '5:1 Ratio Practice',
        description: 'Practice positive interactions to build a strong foundation.',
        category: 'communication',
        difficulty: 'medium',
        duration: '20-25 min',
        players: 2,
        icon: <MessageCircle size={24} color={colors.white} />,
        route: '/week-two?section=3',
        points: 20,
        isAvailable: true
      },
      {
        id: 'being-curious',
        title: 'Being Curious',
        description: 'Develop curiosity about your partner\'s thoughts and feelings.',
        category: 'communication',
        difficulty: 'medium',
        duration: '20-25 min',
        players: 2,
        icon: <MessageCircle size={24} color={colors.white} />,
        route: '/week-three?section=3',
        points: 30,
        isAvailable: true
      },
      {
        id: 'emotional-regulation',
        title: 'Emotional Regulation',
        description: 'Practice managing emotions during difficult conversations.',
        category: 'communication',
        difficulty: 'hard',
        duration: '30-40 min',
        players: 2,
        icon: <MessageCircle size={24} color={colors.white} />,
        route: '/week-four?section=3',
        points: 30,
        isAvailable: true
      },
      {
        id: 'conflict-style',
        title: 'Conflict Style Discovery',
        description: 'Understand your conflict resolution styles and alternatives.',
        category: 'communication',
        difficulty: 'hard',
        duration: '25-35 min',
        players: 2,
        icon: <MessageCircle size={24} color={colors.white} />,
        route: '/week-five?section=3',
        points: 30,
        isAvailable: true
      },
      {
        id: 'validation-toolkit',
        title: 'Validation Toolkit',
        description: 'Learn to validate each other\'s feelings and experiences.',
        category: 'communication',
        difficulty: 'medium',
        duration: '25-30 min',
        players: 2,
        icon: <MessageCircle size={24} color={colors.white} />,
        route: '/week-six?section=3',
        points: 30,
        isAvailable: true
      },
      {
        id: 'turning-toward',
        title: 'Turning Toward',
        description: 'Practice responding positively to your partner\'s bids for attention.',
        category: 'communication',
        difficulty: 'medium',
        duration: '20-25 min',
        players: 2,
        icon: <MessageCircle size={24} color={colors.white} />,
        route: '/week-seven?section=3',
        points: 30,
        isAvailable: true
      },
      {
        id: 'conflict-mapping',
        title: 'Conflict Mapping',
        description: 'Map out conflicts to find underlying issues and solutions.',
        category: 'communication',
        difficulty: 'hard',
        duration: '30-40 min',
        players: 2,
        icon: <MessageCircle size={24} color={colors.white} />,
        route: '/week-eight?section=3',
        points: 30,
        isAvailable: true
      },
      {
        id: 'shared-values',
        title: 'Shared Values Discovery',
        description: 'Explore and align your core values and beliefs.',
        category: 'communication',
        difficulty: 'medium',
        duration: '25-35 min',
        players: 2,
        icon: <MessageCircle size={24} color={colors.white} />,
        route: '/week-nine?section=3',
        points: 30,
        isAvailable: true
      },
      {
        id: 'money-talk',
        title: 'Money Talk',
        description: 'Have open conversations about financial goals and values.',
        category: 'communication',
        difficulty: 'hard',
        duration: '30-45 min',
        players: 2,
        icon: <MessageCircle size={24} color={colors.white} />,
        route: '/week-ten?section=3',
        points: 30,
        isAvailable: true
      },
      {
        id: 'love-language-practice',
        title: 'Love Language Practice',
        description: 'Practice expressing love in your partner\'s preferred language.',
        category: 'communication',
        difficulty: 'medium',
        duration: '20-30 min',
        players: 2,
        icon: <MessageCircle size={24} color={colors.white} />,
        route: '/week-eleven?section=3',
        points: 30,
        isAvailable: true
      },
      {
        id: 'sensate-focus',
        title: 'Sensate Focus',
        description: 'Practice mindful touch and physical connection.',
        category: 'communication',
        difficulty: 'hard',
        duration: '25-35 min',
        players: 2,
        icon: <MessageCircle size={24} color={colors.white} />,
        route: '/week-twelve?section=3',
        points: 30,
        isAvailable: true
      },

      // Fun & Active Activities
      {
        id: 'thrift-shop-showdown',
        title: 'Thrift Shop Showdown',
        description: 'Each partner gets $20 to find the most interesting item for the other.',
        category: 'fun',
        difficulty: 'easy',
        duration: '45-60 min',
        players: 2,
        icon: <Zap size={24} color={colors.white} />,
        route: '/week-seven?section=1',
        points: 20,
        isAvailable: true
      },
      {
        id: 'mini-olympics',
        title: 'Mini-Olympics',
        description: 'Create your own Olympic activities with silly challenges.',
        category: 'fun',
        difficulty: 'easy',
        duration: '30-45 min',
        players: 2,
        icon: <Target size={24} color={colors.white} />,
        route: '/week-eleven?section=1',
        points: 20,
        isAvailable: true
      },
      {
        id: 'get-active-date',
        title: 'Get Active Date',
        description: 'Try a new physical activity together - hiking, climbing, or dancing.',
        category: 'fun',
        difficulty: 'medium',
        duration: '60-90 min',
        players: 2,
        icon: <Zap size={24} color={colors.white} />,
        route: '/week-twelve?section=1',
        points: 20,
        isAvailable: true
      }
    ];

    // Register all activities
    allActivities.forEach(activity => {
      this.registerActivity(activity);
    });
  }

  private registerActivity(activity: Activity) {
    this.activities.set(activity.id, activity);

    const categoryActivities = this.categories.get(activity.category) || [];
    categoryActivities.push(activity);
    this.categories.set(activity.category, categoryActivities);
  }

  // Public API - matches the original allActivities array behavior
  getAllActivities(): Activity[] {
    return Array.from(this.activities.values());
  }

  getActivity(id: string): Activity | undefined {
    return this.activities.get(id);
  }

  getActivitiesByCategory(category: Activity['category']): Activity[] {
    return this.categories.get(category) || [];
  }

  getAvailableActivities(): Activity[] {
    return this.getAllActivities().filter(activity => activity.isAvailable);
  }

  getCategoryStats() {
    const stats = {
      'getting-to-know': this.getActivitiesByCategory('getting-to-know').length,
      'communication': this.getActivitiesByCategory('communication').length,
      'fun': this.getActivitiesByCategory('fun').length,
      'reflection': this.getActivitiesByCategory('reflection').length,
      'skills': this.getActivitiesByCategory('skills').length,
      'planning': this.getActivitiesByCategory('planning').length,
    };
    return stats;
  }
}

// Create singleton instance
export const activityRegistry = new ActivityRegistryService();

// Export the class for testing
export { ActivityRegistryService };
