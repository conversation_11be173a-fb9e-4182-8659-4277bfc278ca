import { useCallback, useEffect, useState } from 'react';
import {
    DateNightFilters
} from '../../shared/types';
import {
    DateNightIdeaGlobal,
    DateNightIdeaUser,
    DateNightIdeaUserInsert,
    DateNightIdeaUserUpdate,
    IdeaSource
} from '../../shared/types/supabase.types';
import { logger } from '../../shared/utils/logger';
import { dateNightApi } from './dateNightApi';

// Simple cache implementation
interface CacheEntry<T> {
  data: T;
  timestamp: number;
  ttl: number; // time to live in milliseconds
}

class SimpleCache {
  private cache = new Map<string, CacheEntry<any>>();
  private readonly DEFAULT_TTL = 5 * 60 * 1000; // 5 minutes

  set<T>(key: string, data: T, ttl: number = this.DEFAULT_TTL): void {
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl
    });
  }

  get<T>(key: string): T | null {
    const entry = this.cache.get(key);
    if (!entry) return null;

    if (Date.now() - entry.timestamp > entry.ttl) {
      this.cache.delete(key);
      return null;
    }

    return entry.data;
  }

  delete(key: string): void {
    this.cache.delete(key);
  }

  clear(): void {
    this.cache.clear();
  }

  invalidatePattern(pattern: string): void {
    const regex = new RegExp(pattern);
    for (const key of Array.from(this.cache.keys())) {
      if (regex.test(key)) {
        this.cache.delete(key);
      }
    }
  }
}

const cache = new SimpleCache();

// Hook state interfaces
interface UseQueryState<T> {
  data: T | null;
  isLoading: boolean;
  error: string | null;
  isRefreshing: boolean;
}

interface UseMutationState {
  isLoading: boolean;
  error: string | null;
}

// Global Ideas Hook
export function useGlobalIdeas(
  filters: DateNightFilters & { source?: IdeaSource } = {},
  options: { enabled?: boolean; refetchOnMount?: boolean } = {}
) {
  const [state, setState] = useState<UseQueryState<DateNightIdeaGlobal[]>>({
    data: null,
    isLoading: true,
    error: null,
    isRefreshing: false
  });

  const { enabled = true, refetchOnMount = true } = options;
  const cacheKey = `global-ideas-${JSON.stringify(filters)}`;

  const fetchData = useCallback(async (isRefresh = false) => {
    if (!enabled) return;

    try {
      setState(prev => ({
        ...prev,
        isLoading: !isRefresh,
        isRefreshing: isRefresh,
        error: null
      }));

      // Check cache first
      const cachedData = cache.get<DateNightIdeaGlobal[]>(cacheKey);
      if (cachedData && !isRefresh) {
        setState(prev => ({
          ...prev,
          data: cachedData,
          isLoading: false,
          isRefreshing: false
        }));
        return;
      }

      const result = await dateNightApi.listGlobalIdeas(filters);
      const data = result.data;

      // Cache the result
      cache.set(cacheKey, data);

      setState(prev => ({
        ...prev,
        data,
        isLoading: false,
        isRefreshing: false
      }));

      logger.info('Global ideas loaded successfully');
    } catch (error) {
      logger.error('Error loading global ideas:', error);
      setState(prev => ({
        ...prev,
        isLoading: false,
        isRefreshing: false,
        error: error instanceof Error ? error.message : 'Failed to load ideas'
      }));
    }
  }, [filters, enabled, cacheKey]);

  const refetch = useCallback(() => {
    cache.delete(cacheKey);
    fetchData(true);
  }, [fetchData, cacheKey]);

  useEffect(() => {
    if (refetchOnMount) {
      fetchData();
    }
  }, [fetchData, refetchOnMount]);

  return {
    ...state,
    refetch
  };
}

// User Ideas Hook
export function useUserIdeas(
  userId: string | null,
  filters: DateNightFilters = {},
  options: { enabled?: boolean; refetchOnMount?: boolean } = {}
) {
  const [state, setState] = useState<UseQueryState<DateNightIdeaUser[]>>({
    data: null,
    isLoading: true,
    error: null,
    isRefreshing: false
  });

  const { enabled = true, refetchOnMount = true } = options;
  const cacheKey = `user-ideas-${userId}-${JSON.stringify(filters)}`;

  const fetchData = useCallback(async (isRefresh = false) => {
    if (!enabled || !userId) return;

    try {
      setState(prev => ({
        ...prev,
        isLoading: !isRefresh,
        isRefreshing: isRefresh,
        error: null
      }));

      // Check cache first
      const cachedData = cache.get<DateNightIdeaUser[]>(cacheKey);
      if (cachedData && !isRefresh) {
        setState(prev => ({
          ...prev,
          data: cachedData,
          isLoading: false,
          isRefreshing: false
        }));
        return;
      }

      const result = await dateNightApi.listUserIdeas(userId, filters);
      const data = result.data;

      // Cache the result
      cache.set(cacheKey, data);

      setState(prev => ({
        ...prev,
        data,
        isLoading: false,
        isRefreshing: false
      }));

      logger.info('User ideas loaded successfully');
    } catch (error) {
      logger.error('Error loading user ideas:', error);
      setState(prev => ({
        ...prev,
        isLoading: false,
        isRefreshing: false,
        error: error instanceof Error ? error.message : 'Failed to load user ideas'
      }));
    }
  }, [userId, filters, enabled, cacheKey]);

  const refetch = useCallback(() => {
    cache.delete(cacheKey);
    fetchData(true);
  }, [fetchData, cacheKey]);

  useEffect(() => {
    if (refetchOnMount && userId) {
      fetchData();
    }
  }, [fetchData, refetchOnMount, userId]);

  return {
    ...state,
    refetch
  };
}

// Create User Idea Hook
export function useCreateUserIdea(userId: string | null) {
  const [state, setState] = useState<UseMutationState>({
    isLoading: false,
    error: null
  });

  const mutate = useCallback(async (payload: Omit<DateNightIdeaUserInsert, 'user_id'>) => {
    if (!userId) {
      setState(prev => ({ ...prev, error: 'User not authenticated' }));
      return null;
    }

    try {
      setState(prev => ({ ...prev, isLoading: true, error: null }));

      const result = await dateNightApi.createUserIdea(userId, payload);

      // Invalidate user ideas cache
      cache.invalidatePattern(`user-ideas-${userId}`);

      setState(prev => ({ ...prev, isLoading: false }));
      logger.info('User idea created successfully');
      return result;
    } catch (error) {
      logger.error('Error creating user idea:', error);
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to create idea'
      }));
      return null;
    }
  }, [userId]);

  return {
    ...state,
    mutate
  };
}

// Update User Idea Hook
export function useUpdateUserIdea(userId: string | null) {
  const [state, setState] = useState<UseMutationState>({
    isLoading: false,
    error: null
  });

  const mutate = useCallback(async (id: string, updates: DateNightIdeaUserUpdate) => {
    if (!userId) {
      setState(prev => ({ ...prev, error: 'User not authenticated' }));
      return null;
    }

    try {
      setState(prev => ({ ...prev, isLoading: true, error: null }));

      const result = await dateNightApi.updateUserIdea(id, userId, updates);

      // Invalidate user ideas cache
      cache.invalidatePattern(`user-ideas-${userId}`);

      setState(prev => ({ ...prev, isLoading: false }));
      logger.info('User idea updated successfully');
      return result;
    } catch (error) {
      logger.error('Error updating user idea:', error);
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to update idea'
      }));
      return null;
    }
  }, [userId]);

  return {
    ...state,
    mutate
  };
}

// Delete User Idea Hook
export function useDeleteUserIdea(userId: string | null) {
  const [state, setState] = useState<UseMutationState>({
    isLoading: false,
    error: null
  });

  const mutate = useCallback(async (id: string) => {
    if (!userId) {
      setState(prev => ({ ...prev, error: 'User not authenticated' }));
      return false;
    }

    try {
      setState(prev => ({ ...prev, isLoading: true, error: null }));

      await dateNightApi.deleteUserIdea(id, userId);

      // Invalidate user ideas cache
      cache.invalidatePattern(`user-ideas-${userId}`);

      setState(prev => ({ ...prev, isLoading: false }));
      logger.info('User idea deleted successfully');
      return true;
    } catch (error) {
      logger.error('Error deleting user idea:', error);
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to delete idea'
      }));
      return false;
    }
  }, [userId]);

  return {
    ...state,
    mutate
  };
}

// Random Ideas Hook
export function useRandomIdeas(count: number = 5) {
  const [state, setState] = useState<UseQueryState<DateNightIdeaGlobal[]>>({
    data: null,
    isLoading: false,
    error: null,
    isRefreshing: false
  });

  const fetchRandom = useCallback(async () => {
    try {
      setState(prev => ({ ...prev, isLoading: true, error: null }));

      const data = await dateNightApi.getRandomGlobalIdeas(count);

      setState(prev => ({ ...prev, data, isLoading: false }));
      logger.info('Random ideas loaded successfully');
    } catch (error) {
      logger.error('Error loading random ideas:', error);
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to load random ideas'
      }));
    }
  }, [count]);

  return {
    ...state,
    fetchRandom
  };
}

// Categories Hook
export function useCategories() {
  const [state, setState] = useState<UseQueryState<string[]>>({
    data: null,
    isLoading: true,
    error: null,
    isRefreshing: false
  });

  const fetchCategories = useCallback(async () => {
    try {
      setState(prev => ({ ...prev, isLoading: true, error: null }));

      // Check cache first
      const cachedData = cache.get<string[]>('categories');
      if (cachedData) {
        setState(prev => ({ ...prev, data: cachedData, isLoading: false }));
        return;
      }

      const data = await dateNightApi.getCategories();

      // Cache the result for 10 minutes
      cache.set('categories', data, 10 * 60 * 1000);

      setState(prev => ({ ...prev, data, isLoading: false }));
      logger.info('Categories loaded successfully');
    } catch (error) {
      logger.error('Error loading categories:', error);
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to load categories'
      }));
    }
  }, []);

  useEffect(() => {
    fetchCategories();
  }, [fetchCategories]);

  return {
    ...state,
    refetch: fetchCategories
  };
}

// Weekly Ideas Hook
export function useWeeklyIdeas() {
  const [state, setState] = useState<UseQueryState<DateNightIdeaGlobal[]>>({
    data: null,
    isLoading: true,
    error: null,
    isRefreshing: false
  });

  const fetchWeekly = useCallback(async () => {
    try {
      setState(prev => ({ ...prev, isLoading: true, error: null }));

      // Check cache first
      const cachedData = cache.get<DateNightIdeaGlobal[]>('weekly-ideas');
      if (cachedData) {
        setState(prev => ({ ...prev, data: cachedData, isLoading: false }));
        return;
      }

      const data = await dateNightApi.getWeeklyIdeas();

      // Cache the result for 5 minutes
      cache.set('weekly-ideas', data);

      setState(prev => ({ ...prev, data, isLoading: false }));
      logger.info('Weekly ideas loaded successfully');
    } catch (error) {
      logger.error('Error loading weekly ideas:', error);
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to load weekly ideas'
      }));
    }
  }, []);

  useEffect(() => {
    fetchWeekly();
  }, [fetchWeekly]);

  return {
    ...state,
    refetch: fetchWeekly
  };
}

// Utility hook to clear all caches
export function useClearCache() {
  return useCallback(() => {
    cache.clear();
    logger.info('Date night cache cleared');
  }, []);
}
