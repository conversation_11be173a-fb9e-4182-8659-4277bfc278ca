/**
 * Match Game Sessions Service
 * Handles all database operations related to game sessions
 */

import { supabase } from '../../../shared/services/supabase/client';
import { logger } from '../../../shared/utils/logger';
import {
    CreateSessionRequest,
    MatchGameError,
    MatchGameResult,
    MatchGameSession,
    MatchGameSessionsService,
    MatchGameSessionWithResults
} from '../types/matchGame.types';

export class MatchGameSessionsServiceImpl implements MatchGameSessionsService {

  /**
   * Create a new game session
   */
  async createSession(session: CreateSessionRequest): Promise<MatchGameSession> {
    try {
      const { data, error } = await supabase
        .from('match_game_sessions')
        .insert({
          couple_id: session.couple_id,
          session_type: session.session_type || 'weekly',
          total_questions: session.question_count || 0
        })
        .select()
        .single();

      if (error) {
        logger.error('Error creating session:', error);
        throw new MatchGameError({
          code: 'CREATE_SESSION_ERROR',
          message: 'Failed to create game session',
          details: error
        });
      }

      if (!data) {
        throw new MatchGameError({
          code: 'CREATE_SESSION_ERROR',
          message: 'Failed to create session - no data returned',
          details: 'Database insert returned null'
        });
      }

      return {
        id: data.id,
        couple_id: data.couple_id || '',
        total_questions: data.total_questions || 0,
        correct_matches: data.correct_matches || 0,
        completed: data.completed || false,
        session_date: data.session_date || new Date().toISOString(),
        created_at: data.created_at || new Date().toISOString()
      };
    } catch (error) {
      logger.error('Error in createSession:', error);
      throw error;
    }
  }

  /**
   * Get a specific session with results and questions
   */
  async getSession(session_id: string): Promise<MatchGameSessionWithResults | null> {
    try {
      // Get session data
      const { data: sessionData, error: sessionError } = await supabase
        .from('match_game_sessions')
        .select('*')
        .eq('id', session_id)
        .single();

      if (sessionError) {
        if (sessionError.code === 'PGRST116') {
          // No rows returned
          return null;
        }
        logger.error('Error fetching session:', sessionError);
        throw new MatchGameError({
          code: 'FETCH_SESSION_ERROR',
          message: `Failed to fetch session: ${session_id}`,
          details: sessionError
        });
      }

      // Get session results
      const { data: resultsData, error: resultsError } = await supabase
        .from('match_game_results')
        .select('*')
        .eq('session_id', session_id)
        .order('answered_at', { ascending: true });

      if (resultsError) {
        logger.error('Error fetching session results:', resultsError);
        throw new MatchGameError({
          code: 'FETCH_SESSION_RESULTS_ERROR',
          message: `Failed to fetch results for session: ${session_id}`,
          details: resultsError
        });
      }

      // Get questions for this session
      const questionIds = resultsData?.map(result => result.question_id).filter((id): id is string => id !== null) || [];
      let questions: any[] = [];

      if (questionIds.length > 0) {
        const { data: questionsData, error: questionsError } = await supabase
          .from('match_game_questions')
          .select('*')
          .in('question_id', questionIds);

        if (questionsError) {
          logger.error('Error fetching session questions:', questionsError);
          throw new MatchGameError({
            code: 'FETCH_SESSION_QUESTIONS_ERROR',
            message: `Failed to fetch questions for session: ${session_id}`,
            details: questionsError
          });
        }

        questions = questionsData || [];
      }

      return {
        id: sessionData.id,
        couple_id: sessionData.couple_id || '',
        total_questions: sessionData.total_questions || 0,
        correct_matches: sessionData.correct_matches || 0,
        completed: sessionData.completed || false,
        session_date: sessionData.session_date || new Date().toISOString(),
        created_at: sessionData.created_at || new Date().toISOString(),
        results: (resultsData || []).filter(result =>
          result.session_id !== null && result.question_id !== null
        ) as MatchGameResult[],
        questions
      };
    } catch (error) {
      logger.error('Error in getSession:', error);
      throw error;
    }
  }

  /**
   * Get user's sessions (as part of a couple)
   */
  async getUserSessions(user_id: string, limit: number = 10): Promise<MatchGameSession[]> {
    try {
      const { data, error } = await supabase
        .from('match_game_sessions')
        .select(`
          *,
          couples!inner(
            partner1_id,
            partner2_id
          )
        `)
        .or(`couples.partner1_id.eq.${user_id},couples.partner2_id.eq.${user_id}`)
        .order('created_at', { ascending: false })
        .limit(limit);

      if (error) {
        logger.error('Error fetching user sessions:', error);
        throw new MatchGameError({
          code: 'FETCH_USER_SESSIONS_ERROR',
          message: `Failed to fetch sessions for user: ${user_id}`,
          details: error
        });
      }

      return (data || []).map(session => ({
        id: session.id,
        couple_id: session.couple_id || '',
        total_questions: session.total_questions || 0,
        correct_matches: session.correct_matches || 0,
        completed: session.completed || false,
        session_date: session.session_date || new Date().toISOString(),
        created_at: session.created_at || new Date().toISOString()
      }));
    } catch (error) {
      logger.error('Error in getUserSessions:', error);
      throw error;
    }
  }

  /**
   * Update session data
   */
  async updateSession(session_id: string, updates: Partial<MatchGameSession>): Promise<MatchGameSession> {
    try {
      const { data, error } = await supabase
        .from('match_game_sessions')
        .update(updates)
        .eq('id', session_id)
        .select()
        .single();

      if (error) {
        logger.error('Error updating session:', error);
        throw new MatchGameError({
          code: 'UPDATE_SESSION_ERROR',
          message: `Failed to update session: ${session_id}`,
          details: error
        });
      }

      if (!data) {
        throw new MatchGameError({
          code: 'CREATE_SESSION_ERROR',
          message: 'Failed to create session - no data returned',
          details: 'Database insert returned null'
        });
      }

      return {
        id: data.id,
        couple_id: data.couple_id || '',
        total_questions: data.total_questions || 0,
        correct_matches: data.correct_matches || 0,
        completed: data.completed || false,
        session_date: data.session_date || new Date().toISOString(),
        created_at: data.created_at || new Date().toISOString()
      };
    } catch (error) {
      logger.error('Error in updateSession:', error);
      throw error;
    }
  }

  /**
   * Mark session as completed
   */
  async completeSession(session_id: string): Promise<MatchGameSession> {
    try {
      const { data, error } = await supabase
        .from('match_game_sessions')
        .update({
          completed: true,
          updated_at: new Date().toISOString()
        })
        .eq('id', session_id)
        .select()
        .single();

      if (error) {
        logger.error('Error completing session:', error);
        throw new MatchGameError({
          code: 'COMPLETE_SESSION_ERROR',
          message: `Failed to complete session: ${session_id}`,
          details: error
        });
      }

      if (!data) {
        throw new MatchGameError({
          code: 'CREATE_SESSION_ERROR',
          message: 'Failed to create session - no data returned',
          details: 'Database insert returned null'
        });
      }

      return {
        id: data.id,
        couple_id: data.couple_id || '',
        total_questions: data.total_questions || 0,
        correct_matches: data.correct_matches || 0,
        completed: data.completed || false,
        session_date: data.session_date || new Date().toISOString(),
        created_at: data.created_at || new Date().toISOString()
      };
    } catch (error) {
      logger.error('Error in completeSession:', error);
      throw error;
    }
  }

  /**
   * Delete a session
   */
  async deleteSession(session_id: string): Promise<void> {
    try {
      const { error } = await supabase
        .from('match_game_sessions')
        .delete()
        .eq('id', session_id);

      if (error) {
        logger.error('Error deleting session:', error);
        throw new MatchGameError({
          code: 'DELETE_SESSION_ERROR',
          message: `Failed to delete session: ${session_id}`,
          details: error
        });
      }
    } catch (error) {
      logger.error('Error in deleteSession:', error);
      throw error;
    }
  }

  /**
   * Get active session for a couple (not completed)
   */
  async getActiveSession(couple_id: string): Promise<MatchGameSession | null> {
    try {
      const { data, error } = await supabase
        .from('match_game_sessions')
        .select('*')
        .eq('couple_id', couple_id)
        .eq('completed', false)
        .order('created_at', { ascending: false })
        .limit(1)
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          // No rows returned
          return null;
        }
        logger.error('Error fetching active session:', error);
        throw new MatchGameError({
          code: 'FETCH_ACTIVE_SESSION_ERROR',
          message: `Failed to fetch active session for couple: ${couple_id}`,
          details: error
        });
      }

      if (!data) {
        throw new MatchGameError({
          code: 'CREATE_SESSION_ERROR',
          message: 'Failed to create session - no data returned',
          details: 'Database insert returned null'
        });
      }

      return {
        id: data.id,
        couple_id: data.couple_id || '',
        total_questions: data.total_questions || 0,
        correct_matches: data.correct_matches || 0,
        completed: data.completed || false,
        session_date: data.session_date || new Date().toISOString(),
        created_at: data.created_at || new Date().toISOString()
      };
    } catch (error) {
      logger.error('Error in getActiveSession:', error);
      throw error;
    }
  }

  /**
   * Get session statistics for a couple
   */
  async getCoupleSessionStats(couple_id: string): Promise<{
    total_sessions: number;
    completed_sessions: number;
    total_questions_answered: number;
    total_correct_matches: number;
    average_score: number;
    best_score: number;
    last_session_date: string | null;
  }> {
    try {
      const { data, error } = await supabase
        .from('match_game_sessions')
        .select('*')
        .eq('couple_id', couple_id);

      if (error) {
        logger.error('Error fetching couple session stats:', error);
        throw new MatchGameError({
          code: 'FETCH_COUPLE_SESSION_STATS_ERROR',
          message: `Failed to fetch session stats for couple: ${couple_id}`,
          details: error
        });
      }

      const sessions = data || [];
      const completedSessions = sessions.filter(s => s.completed);

      const stats = {
        total_sessions: sessions.length,
        completed_sessions: completedSessions.length,
        total_questions_answered: sessions.reduce((sum, s) => sum + (s.total_questions || 0), 0),
        total_correct_matches: sessions.reduce((sum, s) => sum + (s.correct_matches || 0), 0),
        average_score: 0,
        best_score: 0,
        last_session_date: sessions.length > 0 ? sessions[0].created_at : null
      };

      if (completedSessions.length > 0) {
        const scores = completedSessions.map(s =>
          (s.total_questions && s.total_questions > 0) && (s.correct_matches !== null)
            ? (s.correct_matches / s.total_questions) * 100
            : 0
        );
        stats.average_score = scores.reduce((sum, score) => sum + score, 0) / scores.length;
        stats.best_score = Math.max(...scores);
      }

      return stats;
    } catch (error) {
      logger.error('Error in getCoupleSessionStats:', error);
      throw error;
    }
  }

  /**
   * Get recent sessions for a couple
   */
  async getRecentSessions(couple_id: string, limit: number = 5): Promise<MatchGameSession[]> {
    try {
      const { data, error } = await supabase
        .from('match_game_sessions')
        .select('*')
        .eq('couple_id', couple_id)
        .order('created_at', { ascending: false })
        .limit(limit);

      if (error) {
        logger.error('Error fetching recent sessions:', error);
        throw new MatchGameError({
          code: 'FETCH_RECENT_SESSIONS_ERROR',
          message: `Failed to fetch recent sessions for couple: ${couple_id}`,
          details: error
        });
      }

      return (data || []).map(session => ({
        id: session.id,
        couple_id: session.couple_id || '',
        total_questions: session.total_questions || 0,
        correct_matches: session.correct_matches || 0,
        completed: session.completed || false,
        session_date: session.session_date || new Date().toISOString(),
        created_at: session.created_at || new Date().toISOString()
      }));
    } catch (error) {
      logger.error('Error in getRecentSessions:', error);
      throw error;
    }
  }
}

// Export singleton instance
export const matchGameSessionsService = new MatchGameSessionsServiceImpl();
