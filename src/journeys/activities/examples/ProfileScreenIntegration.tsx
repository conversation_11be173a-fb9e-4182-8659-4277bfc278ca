/**
 * Profile Screen Integration Example
 * Demonstrates how to embed activities in profile context with user-specific data
 */

import { router } from 'expo-router';
import { Award, Calendar, Target } from 'lucide-react-native';
import React, { memo, useCallback, useState } from 'react';
import {
    Dimensions,
    Platform,
    ScrollView,
    StyleSheet,
    Text,
    TouchableOpacity,
    View
} from 'react-native';

import { colors } from '../../../shared/utils/colors';
import UniversalActivityContainer from '../containers/UniversalActivityContainer';

const { width: _width } = Dimensions.get('window');

interface ProfileScreenIntegrationProps {
  userId: string;
  coupleId: string;
  userProfile?: {
    name: string;
    avatar?: string;
    totalPoints: number;
    activitiesCompleted: number;
    currentStreak: number;
  };
  onActivityComplete?: (_result: any) => void;
  onActivityError?: (_error: any) => void;
}

const ProfileScreenIntegration = memo<ProfileScreenIntegrationProps>(({
  userId,
  coupleId,
  userProfile,
  onActivityComplete,
  onActivityError
}) => {
  const [recommendedActivity, _setRecommendedActivity] = useState<string | null>('love-language-quiz');
  const [recentActivities] = useState([
    { id: 'match-activity', title: 'Match Activity', completedAt: '2 hours ago', points: 10 },
    { id: 'daily-questions', title: 'Daily Questions', completedAt: '1 day ago', points: 5 },
    { id: 'would-you-rather', title: 'Would You Rather', completedAt: '3 days ago', points: 10 },
  ]);

  const handleActivityComplete = useCallback((result: any) => {
    console.log('Profile screen activity completed:', result);
    onActivityComplete?.(result);

    // Update user profile data
    // You could trigger a profile refresh here
  }, [onActivityComplete]);

  const handleActivityError = useCallback((error: any) => {
    console.error('Profile screen activity error:', error);
    onActivityError?.(error);
  }, [onActivityError]);

  const handleStartRecommendedActivity = useCallback(() => {
    if (recommendedActivity) {
      router.push(`/activities/${recommendedActivity}` as any);
    }
  }, [recommendedActivity]);

  const handleViewActivityHistory = useCallback(() => {
    router.push('/activities/history' as any);
  }, []);

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      {/* User Profile Header */}
      <View style={styles.profileHeader}>
        <View style={styles.avatarContainer}>
          <Text style={styles.avatarText}>
            {userProfile?.name?.charAt(0) || 'U'}
          </Text>
        </View>
        <Text style={styles.userName}>{userProfile?.name || 'User'}</Text>
        <Text style={styles.userStats}>
          {userProfile?.totalPoints || 0} points • {userProfile?.activitiesCompleted || 0} activities
        </Text>
      </View>

      {/* Recommended Activity Section */}
      <View style={styles.section}>
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>Recommended for You</Text>
          <Target size={20} color={colors.accent1} />
        </View>

        {recommendedActivity && (
          <View style={styles.recommendedActivityContainer}>
            <UniversalActivityContainer
              activityId={recommendedActivity}
              userId={userId}
              coupleId={coupleId}
              onComplete={handleActivityComplete}
              onError={handleActivityError}
              showHeader={true}
              showProgress={true}
              showExitButton={true}
            />
            <TouchableOpacity
              style={styles.startButton}
              onPress={handleStartRecommendedActivity}
              activeOpacity={0.7}
              accessibilityLabel="Start recommended activity"
              accessibilityRole="button"
            >
              <Text style={styles.startButtonText}>Start Activity</Text>
            </TouchableOpacity>
          </View>
        )}
      </View>

      {/* Recent Activities Section */}
      <View style={styles.section}>
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>Recent Activities</Text>
          <TouchableOpacity
            style={styles.viewAllButton}
            onPress={handleViewActivityHistory}
            activeOpacity={0.7}
            accessibilityLabel="View activity history"
            accessibilityRole="button"
          >
            <Text style={styles.viewAllText}>View All</Text>
          </TouchableOpacity>
        </View>

        <View style={styles.recentActivitiesList}>
          {recentActivities.map((activity, _index) => (
            <View key={activity.id} style={styles.recentActivityItem}>
              <View style={styles.activityIcon}>
                <Award size={20} color={colors.primary} />
              </View>
              <View style={styles.activityInfo}>
                <Text style={styles.activityTitle}>{activity.title}</Text>
                <Text style={styles.activityDate}>{activity.completedAt}</Text>
              </View>
              <View style={styles.pointsBadge}>
                <Text style={styles.pointsText}>+{activity.points}</Text>
              </View>
            </View>
          ))}
        </View>
      </View>

      {/* Achievement Section */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Achievements</Text>
        <View style={styles.achievementsGrid}>
          <View style={styles.achievementCard}>
            <Calendar size={24} color={colors.accent1} />
            <Text style={styles.achievementTitle}>Streak Master</Text>
            <Text style={styles.achievementDescription}>
              {userProfile?.currentStreak || 0} day streak
            </Text>
          </View>
          <View style={styles.achievementCard}>
            <Award size={24} color={colors.accent1} />
            <Text style={styles.achievementTitle}>Activity Explorer</Text>
            <Text style={styles.achievementDescription}>
              {userProfile?.activitiesCompleted || 0} activities completed
            </Text>
          </View>
        </View>
      </View>
    </ScrollView>
  );
});

ProfileScreenIntegration.displayName = 'ProfileScreenIntegration';

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  profileHeader: {
    alignItems: 'center',
    paddingVertical: 24,
    paddingHorizontal: 16,
    backgroundColor: colors.surface,
    marginBottom: 16,
  },
  avatarContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 12,
  },
  avatarText: {
    fontSize: 32,
    fontWeight: '700',
    color: colors.white,
  },
  userName: {
    fontSize: 24,
    fontWeight: '600',
    color: colors.textPrimary,
    marginBottom: 4,
  },
  userStats: {
    fontSize: 14,
    color: colors.textSecondary,
  },
  section: {
    marginBottom: 24,
    paddingHorizontal: 16,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: colors.textPrimary,
  },
  viewAllButton: {
    paddingVertical: 4,
    paddingHorizontal: 8,
  },
  viewAllText: {
    fontSize: 14,
    color: colors.primary,
    fontWeight: '500',
  },
  recommendedActivityContainer: {
    backgroundColor: colors.surface,
    borderRadius: 12,
    padding: 16,
    ...Platform.select({
      ios: {
        shadowColor: colors.shadow,
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 8,
      },
      android: {
        elevation: 4,
      },
    }),
  },
  startButton: {
    backgroundColor: colors.primary,
    borderRadius: 8,
    paddingVertical: 12,
    alignItems: 'center',
    marginTop: 16,
  },
  startButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.white,
  },
  recentActivitiesList: {
    gap: 12,
  },
  recentActivityItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.surface,
    borderRadius: 12,
    padding: 16,
    ...Platform.select({
      ios: {
        shadowColor: colors.shadow,
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
      },
      android: {
        elevation: 2,
      },
    }),
  },
  activityIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.primary + '20',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  activityInfo: {
    flex: 1,
  },
  activityTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: colors.textPrimary,
    marginBottom: 2,
  },
  activityDate: {
    fontSize: 14,
    color: colors.textSecondary,
  },
  pointsBadge: {
    backgroundColor: colors.accent1 + '20',
    borderRadius: 12,
    paddingHorizontal: 8,
    paddingVertical: 4,
  },
  pointsText: {
    fontSize: 12,
    fontWeight: '600',
    color: colors.accent1,
  },
  achievementsGrid: {
    flexDirection: 'row',
    gap: 12,
  },
  achievementCard: {
    flex: 1,
    backgroundColor: colors.surface,
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
    ...Platform.select({
      ios: {
        shadowColor: colors.shadow,
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
      },
      android: {
        elevation: 2,
      },
    }),
  },
  achievementTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.textPrimary,
    marginTop: 8,
    marginBottom: 4,
    textAlign: 'center',
  },
  achievementDescription: {
    fontSize: 12,
    color: colors.textSecondary,
    textAlign: 'center',
  },
});

export default ProfileScreenIntegration;
