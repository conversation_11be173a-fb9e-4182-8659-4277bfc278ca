/**
 * Home Screen Example
 * Demonstrates how to use the universal activity container on any screen
 * Shows how activities are now portable and can be embedded anywhere
 */

import { ArrowRight, Heart, Play } from 'lucide-react-native';
import React, { useState } from 'react';
import { Alert, StyleSheet, Text, TouchableOpacity, View } from 'react-native';

import { colors } from '../../../utils/colors';
import UniversalActivityContainer from '../containers/UniversalActivityContainer';

interface HomeScreenExampleProps {
  userId: string;
  coupleId: string;
}

export function HomeScreenExample({ userId, coupleId }: HomeScreenExampleProps) {
  const [showFeaturedActivity, setShowFeaturedActivity] = useState(false);
  const [featuredActivityId, setFeaturedActivityId] = useState('match-activity');

  // Handle activity completion
  const handleActivityComplete = (result: any) => {
    Alert.alert(
      'Activity Completed! 🎉',
      `Great job! You earned ${result.points} points!`,
      [
        {
          text: 'Continue',
          onPress: () => {
            setShowFeaturedActivity(false);
          }
        }
      ]
    );
  };

  // Handle activity exit
  const handleActivityExit = () => {
    setShowFeaturedActivity(false);
  };

  // Handle activity error
  const handleActivityError = (error: any) => {
    Alert.alert('Activity Error', error.error || 'Something went wrong');
    setShowFeaturedActivity(false);
  };

  // If showing featured activity, render the universal container
  if (showFeaturedActivity) {
    return (
      <UniversalActivityContainer
        activityId={featuredActivityId}
        userId={userId}
        coupleId={coupleId}
        onComplete={handleActivityComplete}
        onExit={handleActivityExit}
        onError={handleActivityError}
        showHeader={true}
        showProgress={true}
        showExitButton={true}
      />
    );
  }

  // Regular home screen content
  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Welcome Home</Text>
        <Text style={styles.headerSubtitle}>Ready for some fun together?</Text>
      </View>

      <View style={styles.content}>
        {/* Featured Activity Card */}
        <View style={styles.featuredCard}>
          <View style={styles.featuredHeader}>
            <Heart size={24} color={colors.white} />
            <Text style={styles.featuredTitle}>Featured Activity</Text>
          </View>

          <Text style={styles.featuredDescription}>
            Test how well you know each other with our fun Match Activity!
          </Text>

          <TouchableOpacity
            style={styles.playButton}
            onPress={() => setShowFeaturedActivity(true)}
          >
            <Play size={20} color={colors.white} />
            <Text style={styles.playButtonText}>Play Now</Text>
            <ArrowRight size={20} color={colors.white} />
          </TouchableOpacity>
        </View>

        {/* Quick Activity Options */}
        <View style={styles.quickActivities}>
          <Text style={styles.sectionTitle}>Quick Activities</Text>

          <TouchableOpacity
            style={styles.quickActivityButton}
            onPress={() => {
              setFeaturedActivityId('would-you-rather');
              setShowFeaturedActivity(true);
            }}
          >
            <Text style={styles.quickActivityText}>Would You Rather?</Text>
            <ArrowRight size={16} color={colors.primary} />
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.quickActivityButton}
            onPress={() => {
              setFeaturedActivityId('love-language-quiz');
              setShowFeaturedActivity(true);
            }}
          >
            <Text style={styles.quickActivityText}>Love Language Quiz</Text>
            <ArrowRight size={16} color={colors.primary} />
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.quickActivityButton}
            onPress={() => {
              setFeaturedActivityId('daily-questions');
              setShowFeaturedActivity(true);
            }}
          >
            <Text style={styles.quickActivityText}>Daily Questions</Text>
            <ArrowRight size={16} color={colors.primary} />
          </TouchableOpacity>
        </View>

        {/* Recent Activity */}
        <View style={styles.recentActivity}>
          <Text style={styles.sectionTitle}>Recent Activity</Text>
          <Text style={styles.recentText}>You completed 3 activities this week!</Text>
          <Text style={styles.recentSubtext}>Keep up the great work! 🎉</Text>
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  header: {
    padding: 20,
    paddingTop: 60,
    backgroundColor: colors.primary,
  },
  headerTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: colors.white,
    marginBottom: 8,
  },
  headerSubtitle: {
    fontSize: 16,
    color: colors.white,
    opacity: 0.9,
  },
  content: {
    flex: 1,
    padding: 20,
    gap: 24,
  },
  featuredCard: {
    backgroundColor: colors.solidColors.modulePink[0],
    borderRadius: 16,
    padding: 20,
    elevation: 3,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
  },
  featuredHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginBottom: 12,
  },
  featuredTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.white,
  },
  featuredDescription: {
    fontSize: 14,
    color: colors.white,
    lineHeight: 20,
    marginBottom: 20,
  },
  playButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 12,
    gap: 8,
  },
  playButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.white,
  },
  quickActivities: {
    backgroundColor: colors.backgroundSecondary,
    borderRadius: 12,
    padding: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.textPrimary,
    marginBottom: 16,
  },
  quickActivityButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: colors.background,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    marginBottom: 8,
    borderWidth: 1,
    borderColor: colors.borderLight,
  },
  quickActivityText: {
    fontSize: 16,
    color: colors.textPrimary,
  },
  recentActivity: {
    backgroundColor: colors.backgroundSecondary,
    borderRadius: 12,
    padding: 16,
  },
  recentText: {
    fontSize: 16,
    color: colors.textPrimary,
    marginBottom: 4,
  },
  recentSubtext: {
    fontSize: 14,
    color: colors.textSecondary,
  },
});
