/**
 * Universal Activity Container
 * Can render any activity on any screen with consistent behavior
 * Preserves exact same functionality as the original Activities.tsx
 */

import { router } from 'expo-router';
import React, { memo, useCallback, useEffect, useMemo, useState } from 'react';
import {
    ActivityIndicator,
    Dimensions,
    Platform,
    StyleSheet,
    Text,
    TouchableOpacity,
    View
} from 'react-native';

import { ActivityErrorBoundary } from '../../../shared/components/activity/ErrorBoundary';
import { colors } from '../../../shared/utils/colors';
import { MatchActivity } from '../activities/MatchActivity/MatchActivity';
import { Activity, activityRegistry } from '../registry/ActivityRegistry';

const { width, height } = Dimensions.get('window');

interface UniversalActivityContainerProps {
  activityId: string;
  userId: string;
  coupleId: string;
  onComplete?: (_result: any) => void;
  onExit?: () => void;
  onError?: (_error: any) => void;
  showHeader?: boolean;
  showProgress?: boolean;
  showExitButton?: boolean;
}

const UniversalActivityContainer = memo<UniversalActivityContainerProps>(({
  activityId,
  userId,
  coupleId,
  onComplete,
  onExit,
  onError,
  showHeader: _showHeader = true,
  showProgress: _showProgress = true,
  showExitButton: _showExitButton = true
}) => {
  const [isLoading, setIsLoading] = useState(true);
  const [loadingError, setLoadingError] = useState<string | null>(null);
  const [_activityState, _setActivityState] = useState<any>(null);

  // Memoize activity lookup for performance
  const activity = useMemo(() => {
    return activityRegistry.getActivity(activityId);
  }, [activityId]);

  // Handle loading state with proper cleanup
  useEffect(() => {
    let isMounted = true;

    const loadActivity = async () => {
      try {
        setIsLoading(true);
        setLoadingError(null);

        // Simulate loading time for better UX
        await new Promise(resolve => setTimeout(resolve, 300));

        if (isMounted) {
          setIsLoading(false);
        }
      } catch (error) {
        if (isMounted) {
          setLoadingError(error instanceof Error ? error.message : 'Failed to load activity');
          setIsLoading(false);
        }
      }
    };

    loadActivity();

    return () => {
      isMounted = false;
    };
  }, [activityId]);

  // Handle activity completion - matches original logic with memoization
  const handleComplete = useCallback((result: any) => {
    try {
      // Track completion analytics
      console.log(`Activity ${activityId} completed by user ${userId}`, result);
      onComplete?.(result);
    } catch (error) {
      console.error('Error handling activity completion:', error);
      onError?.(error);
    }
  }, [activityId, userId, onComplete, onError]);

  // Handle activity exit - matches original logic with memoization
  const handleExit = useCallback(() => {
    try {
      console.log(`Activity ${activityId} exited by user ${userId}`);
      onExit?.();
    } catch (error) {
      console.error('Error handling activity exit:', error);
      onError?.(error);
    }
  }, [activityId, userId, onExit, onError]);

  // Handle activity error - matches original logic with memoization
  const handleError = useCallback((error: any) => {
    console.error(`Activity ${activityId} error:`, error);
    onError?.(error);
  }, [activityId, onError]);

  // Start activity - exact same logic as original startActivity function
  const startActivity = useCallback((activity: Activity) => {
    if (activity.prompts && activity.prompts.length > 0) {
      // This activity has prompts - render inline component
      return renderInlineActivity(activity);
    } else {
      // This activity uses routing - navigate to route
      router.push(activity.route as any);
      return null;
    }
  }, []);

  // Render inline activity component
  const renderInlineActivity = (activity: Activity) => {
    switch (activity.id) {
      case 'match-activity':
        return (
          <ActivityErrorBoundary
            onError={(error, errorInfo) => {
              console.error('Activity error:', error, errorInfo);
              handleError({
                activityId: activity.id,
                userId,
                error: error.message,
                code: 'ACTIVITY_RUNTIME_ERROR',
                timestamp: new Date().toISOString(),
                context: { errorInfo }
              });
            }}
          >
            <MatchActivity
              activityId={activity.id}
              userId={userId}
              coupleId={coupleId}
              onComplete={handleComplete}
              onExit={handleExit}
              onError={handleError}
              theme={activity.theme}
            />
          </ActivityErrorBoundary>
        );

      // Add more inline activities here as they are extracted
      default:
        // Fallback to routing for activities not yet converted to inline components
        router.push(activity.route as any);
        return null;
    }
  };

  // Show loading state with proper mobile styling
  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator
          size="large"
          color={colors.primary}
          style={styles.loadingSpinner}
        />
        <Text style={styles.loadingText}>Loading Activity...</Text>
        {loadingError && (
          <Text style={styles.loadingErrorText}>{loadingError}</Text>
        )}
      </View>
    );
  }

  // If no activity found, show error with proper mobile styling
  if (!activity) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorTitle}>Activity Not Found</Text>
        <Text style={styles.errorMessage}>
          The activity "{activityId}" could not be found.
        </Text>
        <TouchableOpacity
          style={styles.retryButton}
          onPress={handleExit}
          activeOpacity={0.7}
          accessibilityLabel="Go back to previous screen"
          accessibilityRole="button"
        >
          <Text style={styles.retryButtonText}>Go Back</Text>
        </TouchableOpacity>
      </View>
    );
  }

  // If activity is not available, show error with proper mobile styling
  if (!activity.isAvailable) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorTitle}>Activity Not Available</Text>
        <Text style={styles.errorMessage}>
          This activity will be available in a future update!
        </Text>
        <TouchableOpacity
          style={styles.retryButton}
          onPress={handleExit}
          activeOpacity={0.7}
          accessibilityLabel="Go back to previous screen"
          accessibilityRole="button"
        >
          <Text style={styles.retryButtonText}>Go Back</Text>
        </TouchableOpacity>
      </View>
    );
  }

  // Start the activity with error boundary
  return (
    <ActivityErrorBoundary
      onError={(error, errorInfo) => {
        console.error('Activity error:', error, errorInfo);
        handleError({
          activityId,
          userId,
          error: error.message,
          code: 'ACTIVITY_RUNTIME_ERROR',
          timestamp: new Date().toISOString(),
          context: { errorInfo }
        });
      }}
    >
      {startActivity(activity)}
    </ActivityErrorBoundary>
  );
});

UniversalActivityContainer.displayName = 'UniversalActivityContainer';

const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.background,
    padding: 20,
    minHeight: height * 0.3,
  },
  loadingSpinner: {
    marginBottom: 16,
  },
  loadingText: {
    fontSize: 16,
    color: colors.textSecondary,
    textAlign: 'center',
    marginBottom: 8,
  },
  loadingErrorText: {
    fontSize: 14,
    color: colors.error,
    textAlign: 'center',
    marginTop: 8,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.background,
    padding: 20,
    minHeight: height * 0.4,
  },
  errorTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: colors.textPrimary,
    marginBottom: 8,
    textAlign: 'center',
  },
  errorMessage: {
    fontSize: 16,
    color: colors.textSecondary,
    textAlign: 'center',
    marginBottom: 20,
    lineHeight: 22,
    maxWidth: width * 0.8,
  },
  retryButton: {
    backgroundColor: colors.primary,
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    minWidth: 120,
    alignItems: 'center',
    ...Platform.select({
      ios: {
        shadowColor: colors.shadow,
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
      },
      android: {
        elevation: 3,
      },
    }),
  },
  retryButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.white,
  },
});

// Export for easy use on any screen
export default UniversalActivityContainer;
