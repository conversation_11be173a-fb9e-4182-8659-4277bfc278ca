/**
 * Activity Registry Tests
 * Unit tests for the activity registry system
 */

// Mock the registry to avoid JSX parsing issues in tests
const mockActivities = [
  {
    id: 'match-activity',
    title: 'Match Activity',
    description: 'Test your compatibility with fun questions',
    category: 'getting-to-know',
    difficulty: 'easy',
    duration: '10-15 min',
    players: 2,
    points: 10,
    isAvailable: true,
    route: '/match-activity'
  },
  {
    id: 'daily-questions',
    title: 'Daily Questions',
    description: 'Daily relationship questions',
    category: 'communication',
    difficulty: 'easy',
    duration: '5-10 min',
    players: 2,
    points: 5,
    isAvailable: true,
    route: '/daily-questions'
  }
];

class MockActivityRegistryService {
  getAllActivities() {
    return mockActivities;
  }

  getActivity(id: string) {
    return mockActivities.find(activity => activity.id === id);
  }

  getActivitiesByCategory(category: string) {
    return mockActivities.filter(activity => activity.category === category);
  }

  getAvailableActivities() {
    return mockActivities.filter(activity => activity.isAvailable);
  }

  getCategoryStats() {
    const stats: Record<string, number> = {};
    mockActivities.forEach(activity => {
      stats[activity.category] = (stats[activity.category] || 0) + 1;
    });
    return stats;
  }
}

describe('ActivityRegistryService', () => {
  let registry: MockActivityRegistryService;

  beforeEach(() => {
    registry = new MockActivityRegistryService();
  });

  describe('getAllActivities', () => {
    it('should return all registered activities', () => {
      const activities = registry.getAllActivities();
      expect(activities).toBeDefined();
      expect(Array.isArray(activities)).toBe(true);
      expect(activities.length).toBeGreaterThan(0);
    });

    it('should return activities with correct structure', () => {
      const activities = registry.getAllActivities();
      const firstActivity = activities[0];
      
      expect(firstActivity).toHaveProperty('id');
      expect(firstActivity).toHaveProperty('title');
      expect(firstActivity).toHaveProperty('description');
      expect(firstActivity).toHaveProperty('category');
      expect(firstActivity).toHaveProperty('difficulty');
      expect(firstActivity).toHaveProperty('duration');
      expect(firstActivity).toHaveProperty('players');
      expect(firstActivity).toHaveProperty('route');
      expect(firstActivity).toHaveProperty('points');
      expect(firstActivity).toHaveProperty('isAvailable');
    });
  });

  describe('getActivity', () => {
    it('should return activity by id', () => {
      const activity = registry.getActivity('match-activity');
      expect(activity).toBeDefined();
      expect(activity?.id).toBe('match-activity');
    });

    it('should return undefined for non-existent activity', () => {
      const activity = registry.getActivity('non-existent-activity');
      expect(activity).toBeUndefined();
    });
  });

  describe('getActivitiesByCategory', () => {
    it('should return activities for valid category', () => {
      const activities = registry.getActivitiesByCategory('getting-to-know');
      expect(activities).toBeDefined();
      expect(Array.isArray(activities)).toBe(true);
      expect(activities.length).toBeGreaterThan(0);
      
      // All activities should be in the correct category
      activities.forEach(activity => {
        expect(activity.category).toBe('getting-to-know');
      });
    });

    it('should return empty array for non-existent category', () => {
      const activities = registry.getActivitiesByCategory('non-existent');
      expect(activities).toEqual([]);
    });
  });

  describe('getAvailableActivities', () => {
    it('should return only available activities', () => {
      const activities = registry.getAvailableActivities();
      expect(activities).toBeDefined();
      expect(Array.isArray(activities)).toBe(true);
      
      // All activities should be available
      activities.forEach(activity => {
        expect(activity.isAvailable).toBe(true);
      });
    });
  });

  describe('getCategoryStats', () => {
    it('should return category statistics', () => {
      const stats = registry.getCategoryStats();
      expect(stats).toBeDefined();
      expect(stats).toHaveProperty('getting-to-know');
      expect(stats).toHaveProperty('communication');
      
      // All stats should be numbers
      Object.values(stats).forEach(count => {
        expect(typeof count).toBe('number');
        expect(count).toBeGreaterThanOrEqual(0);
      });
    });
  });
});
