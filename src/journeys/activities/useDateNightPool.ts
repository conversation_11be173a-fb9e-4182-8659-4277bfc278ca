import { useEffect, useState } from 'react';
import { logger } from '../../shared/utils/logger';
import { secureStorage } from '../utils/secureStorage';

export interface DateNightIdea {
  id: string;
  title: string;
  description: string;
  source: 'user' | 'weekly';
  weekNumber?: number; // For weekly Date Nights
  dateAdded: number;
  status: 'planned' | 'completed';
  completedAt?: number;
  image?: string;
  emoji?: string;
  partner1Confirmed?: boolean;
  partner2Confirmed?: boolean;
  partner1Reaction?: string;
  partner2Reaction?: string;
}

export interface DateNightPoolData {
  ideas: DateNightIdea[];
  lastUpdated: number;
}

const DATE_NIGHT_POOL_KEY = 'date_night_pool';

export const useDateNightPool = () => {
  const [ideas, setIdeas] = useState<DateNightIdea[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Load ideas from storage on mount
  useEffect(() => {
    loadIdeas();
  }, []);

  const loadIdeas = async () => {
    try {
      const stored = await secureStorage.getItem<DateNightPoolData>(DATE_NIGHT_POOL_KEY);
      if (stored && stored.ideas) {
        setIdeas(stored.ideas);
      } else {
        // Initialize with preloaded weekly Date Night ideas
        await initializeWeeklyDateNights();
      }
    } catch (error) {
      logger.error('Error loading Date Night pool:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const saveIdeas = async (newIdeas: DateNightIdea[]) => {
    try {
      const dataToSave: DateNightPoolData = {
        ideas: newIdeas,
        lastUpdated: Date.now()
      };
      await secureStorage.setItem(DATE_NIGHT_POOL_KEY, dataToSave);
      setIdeas(newIdeas);
    } catch (error) {
      logger.error('Error saving Date Night pool:', error);
      throw new Error('Failed to save Date Night ideas securely');
    }
  };

  const initializeWeeklyDateNights = async () => {
    const weeklyDateNights: DateNightIdea[] = [
      {
        id: 'week1-game-night',
        title: 'Game Night – The Bet Is On',
        description: 'Play your favorite board game with a twist: loser of each round makes the winner a drink, grabs a snack, or takes on a playful dare.',
        source: 'weekly',
        weekNumber: 1,
        dateAdded: Date.now(),
        status: 'planned'
      },
      {
        id: 'week2-strengths-date',
        title: 'Strengths Celebration Date',
        description: 'Plan a date that celebrates your partner\'s top strengths. Focus on activities that highlight what makes them amazing.',
        source: 'weekly',
        weekNumber: 2,
        dateAdded: Date.now(),
        status: 'planned'
      },
      {
        id: 'week3-alphabet-date',
        title: 'Alphabet Date Night',
        description: 'Plan a date where each activity starts with a different letter of the alphabet. Get creative with A-Z adventures!',
        source: 'weekly',
        weekNumber: 3,
        dateAdded: Date.now(),
        status: 'planned'
      },
      {
        id: 'week4-movie-theme',
        title: 'Movie Theme Night',
        description: 'Choose a movie theme (romance, adventure, comedy) and plan your entire evening around that vibe - food, drinks, and activities included!',
        source: 'weekly',
        weekNumber: 4,
        dateAdded: Date.now(),
        status: 'planned'
      },
      {
        id: 'week5-vacation-planning',
        title: 'Dream Vacation Planning Date',
        description: 'Spend an evening planning your dream vacation together. Research destinations, activities, and create a vision board.',
        source: 'weekly',
        weekNumber: 5,
        dateAdded: Date.now(),
        status: 'planned'
      },
      {
        id: 'week6-memory-lane',
        title: 'Memory Lane Date',
        description: 'Recreate one of your favorite dates or memories together. Visit the same places, eat the same foods, relive the magic.',
        source: 'weekly',
        weekNumber: 6,
        dateAdded: Date.now(),
        status: 'planned'
      },
      {
        id: 'week7-thrift-shop',
        title: 'Thrift Shop Showdown',
        description: 'Each partner gets $20 to find the most interesting item for the other. Share why you chose what you did!',
        source: 'weekly',
        weekNumber: 7,
        dateAdded: Date.now(),
        status: 'planned'
      },
      {
        id: 'week8-blended-day',
        title: 'Blended Perfect Day',
        description: 'Combine your perfect Saturday morning with your partner\'s perfect Saturday evening. Create a full day of shared joy.',
        source: 'weekly',
        weekNumber: 8,
        dateAdded: Date.now(),
        status: 'planned'
      },
      {
        id: 'week9-live-show',
        title: 'Live Show Date',
        description: 'Find a local live performance - music, comedy, theater, or even an open mic night. Experience something new together.',
        source: 'weekly',
        weekNumber: 9,
        dateAdded: Date.now(),
        status: 'planned'
      },
      {
        id: 'week10-get-artsy',
        title: 'Get Artsy Date',
        description: 'Try a creative activity together - painting, pottery, cooking class, or DIY project. No experience required, just fun!',
        source: 'weekly',
        weekNumber: 10,
        dateAdded: Date.now(),
        status: 'planned'
      },
      {
        id: 'week11-mini-olympics',
        title: 'Mini-Olympics Date',
        description: 'Create your own Olympic games with silly challenges - who can balance a book on their head longest, fastest to fold laundry, etc.',
        source: 'weekly',
        weekNumber: 11,
        dateAdded: Date.now(),
        status: 'planned'
      },
      {
        id: 'week12-get-active',
        title: 'Get Active Date',
        description: 'Try a new physical activity together - hiking, rock climbing, dance class, or even a fun workout. Get those endorphins flowing!',
        source: 'weekly',
        weekNumber: 12,
        dateAdded: Date.now(),
        status: 'planned'
      }
    ];

    await saveIdeas(weeklyDateNights);
  };

  const addUserDateNight = async (idea: Omit<DateNightIdea, 'id' | 'dateAdded' | 'status' | 'source'>) => {
    const newIdea: DateNightIdea = {
      ...idea,
      id: `user_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      dateAdded: Date.now(),
      status: 'planned',
      source: 'user'
    };

    const updatedIdeas = [...ideas, newIdea];
    await saveIdeas(updatedIdeas);
    return newIdea;
  };

  const updateDateNightStatus = async (id: string, status: 'planned' | 'completed', completedAt?: number) => {
    const updatedIdeas = ideas.map(idea =>
      idea.id === id
        ? {
            ...idea,
            status,
            completedAt: status === 'completed' ? completedAt || Date.now() : undefined
          }
        : idea
    );
    await saveIdeas(updatedIdeas);
  };

  const confirmDateNight = async (id: string, partnerNumber: 1 | 2) => {
    const updatedIdeas = ideas.map(idea =>
      idea.id === id
        ? {
            ...idea,
            [`partner${partnerNumber}Confirmed`]: true
          }
        : idea
    );
    await saveIdeas(updatedIdeas);
  };

  const addReaction = async (id: string, partnerNumber: 1 | 2, reaction: string) => {
    const updatedIdeas = ideas.map(idea =>
      idea.id === id
        ? {
            ...idea,
            [`partner${partnerNumber}Reaction`]: reaction
          }
        : idea
    );
    await saveIdeas(updatedIdeas);
  };

  const getRandomDateNight = (): DateNightIdea | null => {
    const availableIdeas = ideas.filter(idea => idea.status === 'planned');
    if (availableIdeas.length === 0) return null;

    const randomIndex = Math.floor(Math.random() * availableIdeas.length);
    return availableIdeas[randomIndex];
  };

  const getFilteredIdeas = (filters: {
    source?: 'user' | 'weekly';
    status?: 'planned' | 'completed';
    weekNumber?: number;
  }) => {
    return ideas.filter(idea => {
      if (filters.source && idea.source !== filters.source) return false;
      if (filters.status && idea.status !== filters.status) return false;
      if (filters.weekNumber && idea.weekNumber !== filters.weekNumber) return false;
      return true;
    });
  };

  const getUserCreatedIdeas = () => {
    return ideas.filter(idea => idea.source === 'user');
  };

  const getWeeklyIdeas = () => {
    return ideas.filter(idea => idea.source === 'weekly');
  };

  const getCompletedIdeas = () => {
    return ideas.filter(idea => idea.status === 'completed');
  };

  const getPlannedIdeas = () => {
    return ideas.filter(idea => idea.status === 'planned');
  };

  const deleteUserDateNight = async (id: string) => {
    const idea = ideas.find(i => i.id === id);
    if (idea && idea.source === 'user') {
      const updatedIdeas = ideas.filter(idea => idea.id !== id);
      await saveIdeas(updatedIdeas);
    }
  };

  const resetPool = async () => {
    await saveIdeas([]);
    await initializeWeeklyDateNights();
  };

  return {
    ideas,
    isLoading,
    addUserDateNight,
    updateDateNightStatus,
    confirmDateNight,
    addReaction,
    getRandomDateNight,
    getFilteredIdeas,
    getUserCreatedIdeas,
    getWeeklyIdeas,
    getCompletedIdeas,
    getPlannedIdeas,
    deleteUserDateNight,
    resetPool,
    refresh: loadIdeas
  };
};
