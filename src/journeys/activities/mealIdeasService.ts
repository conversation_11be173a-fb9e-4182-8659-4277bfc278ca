import { supabase } from '../../shared/services/supabase/client';
import { logger } from '../../shared/utils/logger';

export interface MealIdea {
  id: string;
  title: string;
  description?: string | null;
  category: string;
  emoji?: string | null;
  difficulty: 'easy' | 'medium' | 'hard';
  prepTime?: number | null;
  cookTime?: number | null;
  servings: number;
  ingredients: string[];
  instructions: string[];
  tags: string[];
  source: 'global' | 'user';
  weekNumber?: number | null;
  composite_id?: string; // For unified favorites system
  createdAt: string;
  updatedAt: string;
}

export interface GlobalMealIdea {
  id: string;
  title: string;
  description?: string | null;
  category: string;
  emoji?: string | null;
  difficulty?: 'easy' | 'medium' | 'hard' | null;
  prepTime?: number | null;
  cookTime?: number | null;
  servings?: number | null;
  ingredients?: string[] | null;
  instructions?: string[] | null;
  tags?: string[] | null;
  weekNumber?: number | null;
  composite_id?: string; // For unified favorites system
  createdAt?: string | null;
  updatedAt?: string | null;
}

export interface UserMealIdea {
  id: string;
  userId: string;
  ideaId?: string | null; // reference to global idea
  title: string;
  description?: string | null;
  category: string;
  emoji?: string | null;
  difficulty?: 'easy' | 'medium' | 'hard' | null;
  prepTime?: number | null;
  cookTime?: number | null;
  servings?: number | null;
  ingredients?: string[] | null;
  instructions?: string[] | null;
  tags?: string[] | null;
  source?: string | null;
  weekNumber?: number | null;
  composite_id?: string; // For unified favorites system
  createdAt?: string | null;
  updatedAt?: string | null;
}

class MealIdeasService {
  /**
   * Create composite ID for favorites system
   * Format: "global:{id}" or "user:{id}"
   */
  private createCompositeId(id: string, source: 'global' | 'user'): string {
    return `${source}:${id}`;
  }

  /**
   * Get all global meal ideas
   */
  async getGlobalMealIdeas(category?: string, weekNumber?: number): Promise<GlobalMealIdea[]> {
    try {
      console.log('🔍 [MEALS] Starting getGlobalMealIdeas...');

      // Test direct database query first
      const { data: testData, error: testError } = await supabase
        .from('meal_ideas_global')
        .select('*', { count: 'exact' });

      console.log('🔍 [MEALS] Direct database test:', {
        count: testData?.length || 0,
        error: testError?.message || null,
        errorCode: testError?.code || null,
        errorDetails: testError?.details || null,
        hasData: !!testData && testData.length > 0,
        sample: testData?.slice(0, 3).map(d => ({ id: d.id, title: d.title, category: d.category })) || []
      });

      // If there's an error, let's try a simpler query
      if (testError) {
        console.log('🔍 [MEALS] Testing simple query due to error...');
        const { data: simpleData, error: simpleError } = await supabase
          .from('meal_ideas_global')
          .select('id, title')
          .limit(5);

        console.log('🔍 [MEALS] Simple query result:', {
          count: simpleData?.length || 0,
          error: simpleError?.message || null,
          data: simpleData || []
        });
      }

      let query = supabase
        .from('meal_ideas_global')
        .select('*')
        .order('created_at', { ascending: false });

      if (category) {
        console.log('🔍 [MEALS] Filtering by category:', category);
        query = query.eq('category', category);
      }

      if (weekNumber) {
        console.log('🔍 [MEALS] Filtering by week:', weekNumber);
        query = query.eq('week_number', weekNumber);
      }

      const { data, error } = await query;
      console.log('🔍 [MEALS] Query result:', {
        count: data?.length || 0,
        error: error?.message || null,
        errorDetails: error,
        hasData: !!data && data.length > 0,
        sample: data?.slice(0, 2).map(d => ({ id: d.id, title: d.title })) || []
      });

      if (error) {
        console.error('❌ [MEALS] Database error:', error);
        logger.error('Error fetching global meal ideas:', error);
        return [];
      }

      if (!data || data.length === 0) {
        console.log('⚠️ [MEALS] No meal ideas found in database');
        logger.warn('No global meal ideas found in database');
        return [];
      }

      console.log(`✅ [MEALS] Found ${data.length} meal ideas, starting transformation...`);
      logger.info('Found global meal ideas:', data.length);

      // Transform database fields to interface format
      const transformedIdeas = (data || []).map(idea => ({
        id: idea.id,
        title: idea.title,
        description: idea.description,
        category: idea.category,
        emoji: idea.emoji,
        difficulty: idea.difficulty as 'easy' | 'medium' | 'hard' | null,
        prepTime: idea.prep_time,
        cookTime: idea.cook_time,
        servings: idea.servings,
        ingredients: idea.ingredients,
        instructions: idea.instructions,
        tags: idea.tags,
        weekNumber: idea.week_number,
        composite_id: this.createCompositeId(idea.id, 'global'), // Add composite ID for favorites
        createdAt: idea.created_at,
        updatedAt: idea.updated_at
      })) as GlobalMealIdea[];

      console.log(`✅ [MEALS] Transformation complete: ${transformedIdeas.length} meal ideas ready`);
      return transformedIdeas;
    } catch (error) {
      console.error('❌ [MEALS] Error in getGlobalMealIdeas:', error);
      logger.error('Error in getGlobalMealIdeas:', error);
      return [];
    }
  }

  /**
   * Get user's meal ideas
   */
  async getUserMealIdeas(userId: string, category?: string): Promise<UserMealIdea[]> {
    try {
      let query = supabase
        .from('meal_ideas_users')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false });

      if (category) {
        query = query.eq('category', category);
      }

      const { data, error } = await query;

      if (error) {
        logger.error('Error fetching user meal ideas:', error);
        return [];
      }

      // Transform database fields to interface format
      return (data || []).map(idea => ({
        id: idea.id,
        userId: idea.user_id,
        ideaId: idea.idea_id,
        title: idea.title,
        description: idea.description,
        category: idea.category,
        emoji: idea.emoji,
        difficulty: idea.difficulty as 'easy' | 'medium' | 'hard' | null,
        prepTime: idea.prep_time,
        cookTime: idea.cook_time,
        servings: idea.servings,
        ingredients: idea.ingredients,
        instructions: idea.instructions,
        tags: idea.tags,
        source: idea.source,
        weekNumber: idea.week_number,
        composite_id: this.createCompositeId(idea.id, 'user'), // Add composite ID for favorites
        createdAt: idea.created_at,
        updatedAt: idea.updated_at
      })) as UserMealIdea[];
    } catch (error) {
      logger.error('Error in getUserMealIdeas:', error);
      return [];
    }
  }

  /**
   * Get combined meal ideas (global + user's)
   */
  async getCombinedMealIdeas(userId: string, category?: string): Promise<MealIdea[]> {
    try {
      const [globalIdeas, userIdeas] = await Promise.all([
        this.getGlobalMealIdeas(category),
        this.getUserMealIdeas(userId, category)
      ]);

      // Convert global ideas to MealIdea format
      const globalMealIdeas: MealIdea[] = (globalIdeas || []).map(idea => ({
        id: idea.id,
        title: idea.title,
        description: idea.description,
        category: idea.category,
        emoji: idea.emoji,
        difficulty: idea.difficulty || 'easy',
        prepTime: idea.prepTime,
        cookTime: idea.cookTime,
        servings: idea.servings || 1,
        ingredients: idea.ingredients || [],
        instructions: idea.instructions || [],
        tags: idea.tags || [],
        source: 'global' as const,
        weekNumber: idea.weekNumber,
        composite_id: idea.composite_id, // Use existing composite_id from global ideas
        createdAt: idea.createdAt || new Date().toISOString(),
        updatedAt: idea.updatedAt || new Date().toISOString()
      }));

      // Convert user ideas to MealIdea format
      const userMealIdeas: MealIdea[] = (userIdeas || []).map(idea => ({
        id: idea.id,
        title: idea.title,
        description: idea.description,
        category: idea.category,
        emoji: idea.emoji,
        difficulty: idea.difficulty || 'easy',
        prepTime: idea.prepTime,
        cookTime: idea.cookTime,
        servings: idea.servings || 1,
        ingredients: idea.ingredients || [],
        instructions: idea.instructions || [],
        tags: idea.tags || [],
        source: 'user' as const,
        weekNumber: idea.weekNumber,
        composite_id: idea.composite_id, // Use existing composite_id from user ideas
        createdAt: idea.createdAt || new Date().toISOString(),
        updatedAt: idea.updatedAt || new Date().toISOString()
      }));

      // Combine and sort by creation date
      return [...userMealIdeas, ...globalMealIdeas].sort(
        (a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
      );
    } catch (error) {
      logger.error('Error in getCombinedMealIdeas:', error);
      return [];
    }
  }

  /**
   * Create a new user meal idea
   */
  async createUserMealIdea(userId: string, mealIdea: Omit<UserMealIdea, 'id' | 'userId' | 'createdAt' | 'updatedAt'>): Promise<UserMealIdea | null> {
    try {
      const { data, error } = await supabase
        .from('meal_ideas_users')
        .insert({
          user_id: userId,
          title: mealIdea.title,
          description: mealIdea.description,
          category: mealIdea.category,
          emoji: mealIdea.emoji,
          difficulty: mealIdea.difficulty,
          prep_time: mealIdea.prepTime,
          cook_time: mealIdea.cookTime,
          servings: mealIdea.servings,
          ingredients: mealIdea.ingredients,
          instructions: mealIdea.instructions,
          tags: mealIdea.tags,
          source: mealIdea.source,
          week_number: mealIdea.weekNumber
        })
        .select()
        .single();

      if (error) {
        logger.error('Error creating user meal idea:', error);
        return null;
      }

      // Transform the returned data to match UserMealIdea interface
      return {
        id: data.id,
        userId: data.user_id,
        ideaId: data.idea_id,
        title: data.title,
        description: data.description,
        category: data.category,
        emoji: data.emoji,
        difficulty: data.difficulty as 'easy' | 'medium' | 'hard' | null,
        prepTime: data.prep_time,
        cookTime: data.cook_time,
        servings: data.servings,
        ingredients: data.ingredients,
        instructions: data.instructions,
        tags: data.tags,
        source: data.source,
        weekNumber: data.week_number,
        composite_id: this.createCompositeId(data.id, 'user'), // Add composite ID
        createdAt: data.created_at,
        updatedAt: data.updated_at
      };
    } catch (error) {
      logger.error('Error in createUserMealIdea:', error);
      return null;
    }
  }

  /**
   * Update a user meal idea
   */
  async updateUserMealIdea(userId: string, ideaId: string, updates: Partial<UserMealIdea>): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('meal_ideas_users')
        .update({
          title: updates.title,
          description: updates.description,
          category: updates.category,
          emoji: updates.emoji,
          difficulty: updates.difficulty,
          prep_time: updates.prepTime,
          cook_time: updates.cookTime,
          servings: updates.servings,
          ingredients: updates.ingredients,
          instructions: updates.instructions,
          tags: updates.tags,
          source: updates.source,
          week_number: updates.weekNumber
        })
        .eq('id', ideaId)
        .eq('user_id', userId);

      if (error) {
        logger.error('Error updating user meal idea:', error);
        return false;
      }

      return true;
    } catch (error) {
      logger.error('Error in updateUserMealIdea:', error);
      return false;
    }
  }

  /**
   * Delete a user meal idea
   */
  async deleteUserMealIdea(userId: string, ideaId: string): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('meal_ideas_users')
        .delete()
        .eq('id', ideaId)
        .eq('user_id', userId);

      if (error) {
        logger.error('Error deleting user meal idea:', error);
        return false;
      }

      return true;
    } catch (error) {
      logger.error('Error in deleteUserMealIdea:', error);
      return false;
    }
  }


  /**
   * Mark a meal idea as completed
   */
  async markAsCompleted(userId: string, ideaId: string): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('meal_ideas_users')
        .update({
          is_completed: true,
          completed_at: new Date().toISOString()
        })
        .eq('id', ideaId)
        .eq('user_id', userId);

      if (error) {
        logger.error('Error marking meal as completed:', error);
        return false;
      }

      return true;
    } catch (error) {
      logger.error('Error in markAsCompleted:', error);
      return false;
    }
  }

  /**
   * Get meal categories
   */
  async getMealCategories(): Promise<string[]> {
    try {
      const { data, error } = await supabase
        .from('meal_ideas_global')
        .select('category')
        .not('category', 'is', null);

      if (error) {
        logger.error('Error fetching meal categories:', error);
        return [];
      }

      // Get unique categories
      const categories = [...new Set((data || []).map(item => item.category))];
      return categories.sort();
    } catch (error) {
      logger.error('Error in getMealCategories:', error);
      return [];
    }
  }

  /**
   * Search meal ideas
   */
  async searchMealIdeas(userId: string, searchTerm: string, category?: string): Promise<MealIdea[]> {
    try {
      const allIdeas = await this.getCombinedMealIdeas(userId, category);

      const searchLower = searchTerm.toLowerCase();
      return allIdeas.filter(idea =>
        idea.title.toLowerCase().includes(searchLower) ||
        idea.description?.toLowerCase().includes(searchLower) ||
        idea.ingredients.some(ingredient => ingredient.toLowerCase().includes(searchLower)) ||
        idea.tags.some(tag => tag.toLowerCase().includes(searchLower))
      );
    } catch (error) {
      logger.error('Error in searchMealIdeas:', error);
      return [];
    }
  }
}

export const mealIdeasService = new MealIdeasService();
