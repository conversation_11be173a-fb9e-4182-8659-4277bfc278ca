import { useCallback, useEffect, useState } from 'react';
import { performanceMonitor } from '../../shared/services/performanceMonitor';
import { cacheManager } from '../../shared/services/storage/cacheManager';
import { subscriptionManager } from '../../shared/services/system/subscriptionManager';
import { logger } from '../../shared/utils/logger';
import { performanceOptimizationService } from '../progress/performanceOptimizationService';
import { DateNightIdea, dateNightIdeasService, UserDateNightIdea } from '../services/dateNightIdeasService';
import { useAuth } from './useAuth';

export interface DateNightIdeasState {
  allIdeas: DateNightIdea[];
  weeklyIdeas: DateNightIdea[];
  userIdeas: UserDateNightIdea[];
  categories: string[];
  isLoading: boolean;
  error: string | null;
}

export const useDateNightIdeasSupabase = () => {
  const [state, setState] = useState<DateNightIdeasState>({
    allIdeas: [],
    weeklyIdeas: [],
    userIdeas: [],
    categories: [],
    isLoading: true,
    error: null,
  });

  const { user } = useAuth();

  // Performance tracking
  const componentTracker = performanceMonitor.trackComponent('useDateNightIdeasSupabase');

  // Load all data on mount with caching and optimization
  useEffect(() => {
    loadAllData();

    // Setup real-time subscriptions for data updates
    if (user) {
      setupRealtimeSubscriptions();
    }

    return () => {
      // Cleanup subscriptions on unmount
      cleanupSubscriptions();
    };
  }, [user]);

  const loadAllData = useCallback(async () => {
    componentTracker.startRender();
    const networkTracker = performanceMonitor.trackNetworkRequest('/api/date-night-ideas');
    networkTracker.start();

    try {
      setState(prev => ({ ...prev, isLoading: true, error: null }));

      // Check cache first
      const cacheKey = `date-night-data-${user?.id || 'anonymous'}`;
      const cachedData = await cacheManager.get<DateNightIdeasState>(cacheKey);

      if (cachedData && !cachedData.error) {
        setState(cachedData);
        networkTracker.end(true, 0); // Cache hit
        componentTracker.endRender();
        logger.info('Date night ideas loaded from cache');
        return;
      }

      // Use optimized batch loading to prevent N+1 queries
      const _optimizedData = await performanceOptimizationService.fetchRelatedData(
        'date_night_ideas_global',
        user ? [user.id] : [],
        [
          { table: 'date_night_ideas_global', foreignKey: 'id', select: '*' },
          { table: 'date_night_ideas_user', foreignKey: 'user_id', select: '*' },
          { table: 'favorites', foreignKey: 'user_id', select: 'item_id, type' }
        ]
      );

      // Fallback to individual queries if batch fails
      const [allIdeas, weeklyIdeas, categories, userIdeas] = await Promise.all([
        dateNightIdeasService.getAllIdeas(),
        dateNightIdeasService.getWeeklyIdeas(),
        dateNightIdeasService.getCategories(),
        user ? dateNightIdeasService.getUserIdeas(user.id) : Promise.resolve([]),
      ]);

      const newState = {
        allIdeas,
        weeklyIdeas,
        userIdeas,
        categories,
        isLoading: false,
        error: null,
      };

      setState(newState);

      // Cache the results
      await cacheManager.set(cacheKey, newState, {
        ttl: 300000, // 5 minutes
        tags: ['date-night', user?.id || 'anonymous']
      });

      networkTracker.end(true, JSON.stringify(newState).length);
      logger.info('Date night ideas loaded successfully');
    } catch (error) {
      logger.error('Error loading date night ideas:', error);
      const errorState = {
        ...state,
        isLoading: false,
        error: 'Failed to load date night ideas',
      };
      setState(errorState);
      networkTracker.end(false);
    } finally {
      componentTracker.endRender();
    }
  }, [user, componentTracker]);

  const getRandomIdeas = async (count: number = 5): Promise<DateNightIdea[]> => {
    try {
      return await dateNightIdeasService.getRandomIdeas(count);
    } catch (error) {
      logger.error('Error getting random ideas:', error);
      return [];
    }
  };

  const getIdeasByCategory = async (category: string): Promise<DateNightIdea[]> => {
    try {
      return await dateNightIdeasService.getIdeasByCategory(category);
    } catch (error) {
      logger.error('Error getting ideas by category:', error);
      return [];
    }
  };

  const getIdeasByDifficulty = async (difficulty: 'easy' | 'medium' | 'hard'): Promise<DateNightIdea[]> => {
    try {
      return await dateNightIdeasService.getIdeasByDifficulty(difficulty);
    } catch (error) {
      logger.error('Error getting ideas by difficulty:', error);
      return [];
    }
  };

  const searchIdeas = async (query: string): Promise<DateNightIdea[]> => {
    try {
      return await dateNightIdeasService.searchIdeas(query);
    } catch (error) {
      logger.error('Error searching ideas:', error);
      return [];
    }
  };

  const saveUserIdea = async (ideaId: string, status: 'planned' | 'completed' | 'favorite', notes?: string, rating?: number): Promise<boolean> => {
    if (!user) {
      logger.warn('No user logged in, cannot save idea');
      return false;
    }

    try {
      const success = await dateNightIdeasService.saveUserIdea(user.id, ideaId, status, notes, rating);

      if (success) {
        // Reload user ideas to update the state
        const updatedUserIdeas = await dateNightIdeasService.getUserIdeas(user.id);
        setState(prev => ({ ...prev, userIdeas: updatedUserIdeas }));
      }

      return success;
    } catch (error) {
      logger.error('Error saving user idea:', error);
      return false;
    }
  };

  const createUserDateNightIdea = async (ideaData: {
    title: string;
    description?: string;
    category: string;
    emoji?: string;
    difficulty?: 'easy' | 'medium' | 'hard';
    estimated_duration?: number;
    cost?: 'free' | 'low' | 'medium' | 'high';
    indoor_outdoor?: 'indoor' | 'outdoor' | 'both';
    week_number?: number;
    tags?: string[];
  }): Promise<boolean> => {
    if (!user) {
      logger.warn('No user logged in, cannot create date night idea');
      return false;
    }

    try {
      const result = await dateNightIdeasService.createUserDateNightIdea(user.id, ideaData);

      if (result) {
        // Reload user ideas to update the state
        const updatedUserIdeas = await dateNightIdeasService.getUserIdeas(user.id);
        setState(prev => ({ ...prev, userIdeas: updatedUserIdeas }));
      }

      return !!result;
    } catch (error) {
      logger.error('Error creating date night idea:', error);
      return false;
    }
  };

  const removeUserIdea = async (ideaId: string): Promise<boolean> => {
    if (!user) {
      logger.warn('No user logged in, cannot remove idea');
      return false;
    }

    try {
      const success = await dateNightIdeasService.removeUserIdea(user.id, ideaId);

      if (success) {
        // Reload user ideas to update the state
        const updatedUserIdeas = await dateNightIdeasService.getUserIdeas(user.id);
        setState(prev => ({ ...prev, userIdeas: updatedUserIdeas }));
      }

      return success;
    } catch (error) {
      logger.error('Error removing user idea:', error);
      return false;
    }
  };

  const getUserIdeasByStatus = (_status: 'planned' | 'completed' | 'favorite'): UserDateNightIdea[] => {
    // Note: The current schema doesn't support status-based filtering
    // This would need to be implemented using the favorites table
    return [];
  };

  const getCompletedIdeas = (): UserDateNightIdea[] => {
    return getUserIdeasByStatus('completed');
  };

  const getFavoriteIdeas = (): UserDateNightIdea[] => {
    return getUserIdeasByStatus('favorite');
  };

  const getPlannedIdeas = (): UserDateNightIdea[] => {
    return getUserIdeasByStatus('planned');
  };

  const isIdeaSaved = (ideaId: string): boolean => {
    return state.userIdeas.some(idea => idea.id === ideaId);
  };

  const getUserIdeaStatus = (_ideaId: string): 'planned' | 'completed' | 'favorite' | null => {
    // Note: The current schema doesn't support status tracking
    // This would need to be implemented using the favorites table
    return null;
  };

  // Real-time subscription management
  const setupRealtimeSubscriptions = useCallback(() => {
    if (!user) return;

    // Subscribe to user's date night ideas changes
    subscriptionManager.subscribe({
      table: 'user_date_night_ideas',
      event: '*',
      filter: `user_id=eq.${user.id}`,
      callback: (payload: any) => {
        logger.info('Real-time update for user date night ideas:', payload);
        // Invalidate cache and reload data
        cacheManager.invalidate(`date-night-data-${user.id}`);
        loadAllData();
      },
      onError: (error: any) => {
        logger.error('Subscription error for user date night ideas:', error);
      }
    });

    // Subscribe to global ideas changes (less frequent updates)
    subscriptionManager.subscribe({
      table: 'date_night_ideas_global',
      event: 'INSERT',
      callback: (payload: any) => {
        logger.info('New global date night idea added:', payload);
        // Invalidate cache for all users
        cacheManager.invalidate('date-night', true);
        loadAllData();
      }
    });
  }, [user, loadAllData]);

  const cleanupSubscriptions = useCallback(() => {
    // Subscriptions are automatically cleaned up by the subscription manager
    // when component unmounts, but we can force cleanup here if needed
    logger.info('Cleaning up date night ideas subscriptions');
  }, []);

  const refreshData = useCallback(() => {
    // Invalidate cache and reload
    if (user) {
      cacheManager.invalidate(`date-night-data-${user.id}`);
    }
    loadAllData();
  }, [user, loadAllData]);

  return {
    // State
    ...state,

    // Actions
    createUserDateNightIdea,
    getRandomIdeas,
    getIdeasByCategory,
    getIdeasByDifficulty,
    searchIdeas,
    saveUserIdea,
    removeUserIdea,
    refreshData,

    // Computed values
    getUserIdeasByStatus,
    getCompletedIdeas,
    getFavoriteIdeas,
    getPlannedIdeas,
    isIdeaSaved,
    getUserIdeaStatus,
  };
};
