# Screens Directory

This directory is for shared screen components and screen-related utilities that are not part of the Expo Router file-based routing system.

## Structure

- **components/**: Reusable screen-level components
- **hooks/**: Screen-specific custom hooks  
- **types/**: Screen-related TypeScript types
- **utils/**: Screen utility functions

## Note

The main application screens are located in the `app/` directory following Expo Router conventions. This `src/screens/` directory is for shared screen components and utilities that can be reused across multiple routes.
