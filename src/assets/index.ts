/**
 * Assets Index - Centralized asset management
 * 
 * This file provides a centralized way to import and reference all app assets.
 * Following React Native best practices for asset organization.
 */

// =============================================================================
// ICONS
// =============================================================================

export const icons = {
  favicon: require('./images/icons/favicon.png'),
  // Add more icons here as they're created
  // appIcon: require('./images/icons/app-icon.png'),
  // appIconAndroid: require('./images/icons/app-icon-android.png'),
} as const;

// =============================================================================
// SPLASH SCREENS
// =============================================================================

export const splash = {
  // Add splash screen assets here
  // default: require('./images/splash/splash.png'),
  // ios: require('./images/splash/splash-ios.png'),
  // android: require('./images/splash/splash-android.png'),
} as const;

// =============================================================================
// APP STORE ASSETS
// =============================================================================

export const appStore = {
  // iOS App Store assets
  // icon1024: require('./images/app-store/app-icon-1024.png'),
  // screenshots: {
  //   iphone67: [
  //     require('./images/app-store/screenshots-iphone-67/screenshot-1.png'),
  //     // Add more screenshots
  //   ],
  //   ipad: [
  //     require('./images/app-store/screenshots-ipad/screenshot-1.png'),
  //     // Add more screenshots
  //   ],
  // },
} as const;

// =============================================================================
// PLAY STORE ASSETS
// =============================================================================

export const playStore = {
  // Google Play Store assets
  // icon512: require('./images/play-store/app-icon-512.png'),
  // featureGraphic: require('./images/play-store/feature-graphic.png'),
  // screenshots: {
  //   phone: [
  //     require('./images/play-store/screenshots-phone/screenshot-1.png'),
  //     // Add more screenshots
  //   ],
  //   tablet: [
  //     require('./images/play-store/screenshots-tablet/screenshot-1.png'),
  //     // Add more screenshots
  //   ],
  // },
} as const;

// =============================================================================
// ASSET HELPERS
// =============================================================================

/**
 * Get platform-specific app icon
 */
export const getAppIcon = () => {
  // Return appropriate icon based on platform
  return icons.favicon; // Fallback until proper icons are added
};

/**
 * Get platform-specific splash screen
 */
export const getSplashScreen = () => {
  // Return appropriate splash screen based on platform
  return null; // Return null until splash screens are added
};

// =============================================================================
// TYPE DEFINITIONS
// =============================================================================

export type IconKey = keyof typeof icons;
export type SplashKey = keyof typeof splash;
export type AppStoreKey = keyof typeof appStore;
export type PlayStoreKey = keyof typeof playStore;

// =============================================================================
// DEFAULT EXPORT
// =============================================================================

export default {
  icons,
  splash,
  appStore,
  playStore,
  getAppIcon,
  getSplashScreen,
};
