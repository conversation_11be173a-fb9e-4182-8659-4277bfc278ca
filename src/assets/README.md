# Assets Directory

## 📁 **Structure**

```
src/assets/
├── index.ts                    # 📋 Centralized asset exports
└── images/
    ├── icons/                  # 🎯 App icons and favicons
    │   └── favicon.png         # Web favicon
    ├── splash/                 # 🌟 Splash screen images
    ├── app-store/             # 🍎 iOS App Store assets
    └── play-store/            # 🤖 Google Play Store assets
```

## 🎯 **Usage**

### **Import Assets**
```typescript
// Import all assets
import assets from './assets';

// Import specific categories
import { icons, splash, getAppIcon } from './assets';

// Use in components
<Image source={icons.favicon} style={styles.icon} />
```

### **Adding New Assets**
1. **Place files** in appropriate subfolder
2. **Update index.ts** to export the new asset
3. **Use TypeScript** for type safety

## 📝 **Guidelines**

### **File Naming**
- Use **kebab-case**: `app-icon-1024.png`
- Be **descriptive**: `screenshot-home-screen.png`
- Include **dimensions** when relevant: `icon-512x512.png`

### **Organization**
- **icons/** - App icons, favicons, UI icons
- **splash/** - Splash screens and loading images
- **app-store/** - iOS App Store marketing assets
- **play-store/** - Google Play Store marketing assets

### **Optimization**
- **Compress images** before adding
- **Use appropriate formats** (PNG for icons, JPG for photos)
- **Provide multiple sizes** when needed

## 🔗 **Related Documentation**
- **[Assets Guide](../../docs/assets-guide.md)** - Complete asset requirements and guidelines

---
*All assets centrally managed through index.ts for easy imports and type safety*
