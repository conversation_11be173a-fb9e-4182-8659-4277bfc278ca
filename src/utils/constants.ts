/**
 * Application Constants
 * Centralized constants used throughout the application
 */

// App Information
export const APP_INFO = {
  NAME: 'Everlasting Us',
  VERSION: '1.0.0',
  TAGLINE: 'Couples Crave Closeness, Not Complexity',
  DESCRIPTION: 'A couples app focused on building deeper connections through daily questions and shared experiences'
} as const;

// App Constants
export const APP_CONSTANTS = {
  MAX_FILE_SIZE: 10 * 1024 * 1024, // 10MB
  MAX_QUESTIONS_PER_DAY: 5,
  MAX_ACTIVITIES_PER_WEEK: 7,
  DEFAULT_TIMEOUT: 30000, // 30 seconds
  RETRY_ATTEMPTS: 3,
  CACHE_DURATION: 24 * 60 * 60 * 1000, // 24 hours
  SUPPORTED_IMAGE_FORMATS: ['jpg', 'jpeg', 'png', 'gif', 'webp'],
  SUPPORTED_VIDEO_FORMATS: ['mp4', 'mov', 'avi'],
  MAX_DAILY_QUESTIONS: 3,
  MAX_WEEKLY_ACTIVITIES: 5,
  STORAGE_KEYS: {
    USER_PREFERENCES: 'user_preferences',
    DAILY_QUESTIONS_SETTINGS: 'daily_questions_settings',
    LAST_SYNC: 'last_sync',
    WEEKLY_RITUAL: 'weekly_ritual',
    JOURNAL_ICON_PREFERENCE: 'journal_icon_preference'
  }
} as const;

// Content Constants
export const CONTENT_CONSTANTS = {
  APP_NAME: 'Everlasting Us',
  APP_TAGLINE: 'Couples Crave Closeness, Not Complexity',

  DAILY_QUESTIONS: {
    DEFAULT_CATEGORIES: ['relationship', 'personal', 'fun', 'deep'],
    MAX_QUESTIONS_PER_DAY: 3
  }
} as const;

// Brand Colors (from user preferences - no gradients, solid colors only)
export const COLORS = {
  PRIMARY: {
    SAGE: '#9CAF88',           // Primary Green Sage
    SAGE_LIGHT: '#B8C9A6',
    SAGE_DARK: '#7A8B6A'
  },
  SECONDARY: {
    LAVENDER: '#CBC3E3',       // Secondary Lavender Purple
    LAVENDER_LIGHT: '#E0D9F0',
    LAVENDER_DARK: '#A89BC7'
  },
  ACCENT: {
    PINK: '#F3E0DA',           // Accent Pink
    PINK_LIGHT: '#F8EDE8',
    PINK_DARK: '#E6C7BD'
  },
  TERTIARY: {
    DARK_PINK: '#F7D0D6',      // Darker Pink
    DARK_PINK_LIGHT: '#FADDE2',
    DARK_PINK_DARK: '#E8B8C1'
  },
  NEUTRAL: {
    CHARCOAL: '#393939',       // Text Charcoal Gray
    CHARCOAL_LIGHT: '#5A5A5A',
    CHARCOAL_DARK: '#2A2A2A',
    WHITE: '#FFFFFF',
    GRAY_100: '#F5F5F5',
    GRAY_200: '#E5E5E5',
    GRAY_300: '#D4D4D4',
    GRAY_400: '#A3A3A3',
    GRAY_500: '#737373',
    GRAY_600: '#525252',
    GRAY_700: '#404040',
    GRAY_800: '#262626',
    GRAY_900: '#171717'
  },
  SEMANTIC: {
    SUCCESS: '#10B981',
    WARNING: '#F59E0B',
    ERROR: '#EF4444',
    INFO: '#3B82F6'
  }
} as const;

// Spacing Constants
export const SPACING = {
  XS: 4,
  SM: 8,
  MD: 16,
  LG: 24,
  XL: 32,
  XXL: 48
} as const;

// Typography Constants
export const TYPOGRAPHY = {
  FONT_FAMILY: {
    PRIMARY: 'Inter, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
    SECONDARY: 'Georgia, "Times New Roman", serif'
  },
  FONT_SIZE: {
    XS: 12,
    SM: 14,
    BASE: 16,
    LG: 18,
    XL: 20,
    XXL: 24,
    XXXL: 30
  },
  FONT_WEIGHT: {
    LIGHT: '300',
    NORMAL: '400',
    MEDIUM: '500',
    SEMIBOLD: '600',
    BOLD: '700'
  }
} as const;

// Animation Constants
export const ANIMATION = {
  DURATION: {
    FAST: 150,
    NORMAL: 300,
    SLOW: 500
  },
  EASING: {
    EASE_IN: 'cubic-bezier(0.4, 0, 1, 1)',
    EASE_OUT: 'cubic-bezier(0, 0, 0.2, 1)',
    EASE_IN_OUT: 'cubic-bezier(0.4, 0, 0.2, 1)'
  }
} as const;

// Screen Dimensions
export const SCREEN = {
  BREAKPOINTS: {
    XS: 320,
    SM: 640,
    MD: 768,
    LG: 1024,
    XL: 1280
  }
} as const;

// API Constants
export const API = {
  BASE_URL: process.env.REACT_APP_API_URL || 'http://localhost:3000/api',
  TIMEOUT: 10000,
  RETRY_ATTEMPTS: 3
} as const;

// Storage Keys
export const STORAGE_KEYS = {
  USER_TOKEN: 'user_token',
  USER_PROFILE: 'user_profile',
  PARTNER_PROFILE: 'partner_profile',
  THEME_PREFERENCE: 'theme_preference',
  NOTIFICATION_SETTINGS: 'notification_settings',
  DAILY_QUESTIONS_SETTINGS: 'daily_questions_settings',
  LAST_SYNC: 'last_sync'
} as const;

// Route Names
export const ROUTES = {
  // Auth
  LANDING: 'Landing',
  LOGIN: 'Login',
  SIGNUP: 'Signup',

  // Main App
  HOME: 'Home',
  OUR_STORY: 'OurStory',
  DAILY_QUESTIONS: 'DailyQuestions',
  MODULES: 'Modules',
  PROFILE: 'Profile',

  // Daily Questions
  DAILY_QUESTIONS_ACHIEVEMENTS: 'DailyQuestionsAchievements',
  DAILY_QUESTIONS_SETTINGS: 'DailyQuestionsSettings',

  // Couple Features
  JOIN_COUPLE: 'JoinCouple',
  COUPLE_PAIRING: 'CouplePairing',

  // Settings
  SETTINGS: 'Settings',
  NOTIFICATIONS: 'Notifications',
  PRIVACY: 'Privacy',
  HELP: 'Help',

  // Tabs
  TABS: {
    HOME: 'Home',
    OUR_STORY: 'OurStory',
    DAILY_QUESTIONS: 'DailyQuestions',
    MODULES: 'Modules',
    PROFILE: 'Profile'
  }
} as const;

// Question Categories
export const QUESTION_CATEGORIES = {
  RELATIONSHIP: 'relationship',
  PERSONAL: 'personal',
  FUN: 'fun',
  DEEP: 'deep',
  FUTURE: 'future',
  MEMORIES: 'memories',
  VALUES: 'values',
  DREAMS: 'dreams'
} as const;

// Question Difficulties
export const QUESTION_DIFFICULTIES = {
  EASY: 'easy',
  MEDIUM: 'medium',
  HARD: 'hard'
} as const;

// User Event Types
export const USER_EVENT_TYPES = {
  PROFILE_CREATED: 'profile_created',
  PARTNER_CONNECTED: 'partner_connected',
  DAILY_QUESTION_ANSWERED: 'daily_question_answered',
  DAILY_QUESTION_SKIPPED: 'daily_question_skipped',
  ACHIEVEMENT_UNLOCKED: 'achievement_unlocked',
  STREAK_MILESTONE: 'streak_milestone',
  MODULE_COMPLETED: 'module_completed',
  SETTINGS_UPDATED: 'settings_updated'
} as const;

// Achievement Types
export const ACHIEVEMENT_TYPES = {
  STREAK: 'streak',
  QUESTIONS_ANSWERED: 'questions_answered',
  MODULES_COMPLETED: 'modules_completed',
  PROFILE_COMPLETION: 'profile_completion',
  PARTNER_CONNECTION: 'partner_connection'
} as const;

// Notification Types
export const NOTIFICATION_TYPES = {
  DAILY_QUESTION: 'daily_question',
  PARTNER_ACTIVITY: 'partner_activity',
  ACHIEVEMENT: 'achievement',
  REMINDER: 'reminder',
  SYSTEM: 'system'
} as const;

// Error Messages
export const ERROR_MESSAGES = {
  NETWORK_ERROR: 'Network connection error. Please check your internet connection.',
  SERVER_ERROR: 'Server error. Please try again later.',
  VALIDATION_ERROR: 'Please check your input and try again.',
  AUTH_ERROR: 'Authentication failed. Please log in again.',
  PERMISSION_ERROR: 'You do not have permission to perform this action.',
  NOT_FOUND: 'The requested resource was not found.',
  GENERIC_ERROR: 'Something went wrong. Please try again.'
} as const;

// Success Messages
export const SUCCESS_MESSAGES = {
  PROFILE_UPDATED: 'Profile updated successfully!',
  PARTNER_CONNECTED: 'Successfully connected with your partner!',
  QUESTION_ANSWERED: 'Question answered successfully!',
  SETTINGS_SAVED: 'Settings saved successfully!',
  ACHIEVEMENT_UNLOCKED: 'Achievement unlocked!'
} as const;

// Validation Rules
export const VALIDATION = {
  PASSWORD_MIN_LENGTH: 8,
  NAME_MIN_LENGTH: 2,
  NAME_MAX_LENGTH: 50,
  EMAIL_REGEX: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  PHONE_REGEX: /^\+?[\d\s\-\(\)]+$/
} as const;

// Feature Flags
export const FEATURES = {
  DAILY_QUESTIONS: true,
  ACHIEVEMENTS: true,
  PARTNER_PAIRING: true,
  PUSH_NOTIFICATIONS: true,
  ANALYTICS: true,
  DARK_MODE: true
} as const;

// Legacy alias for backward compatibility
export const FEATURE_FLAGS = {
  ENABLE_DAILY_QUESTIONS: FEATURES.DAILY_QUESTIONS,
  ENABLE_MATCH_GAME: true,
  ENABLE_DATE_NIGHT_IDEAS: true,
  ENABLE_ACHIEVEMENTS: FEATURES.ACHIEVEMENTS,
  ENABLE_PARTNER_PAIRING: FEATURES.PARTNER_PAIRING,
  ENABLE_PUSH_NOTIFICATIONS: FEATURES.PUSH_NOTIFICATIONS,
  ENABLE_ANALYTICS: FEATURES.ANALYTICS,
  ENABLE_DARK_MODE: FEATURES.DARK_MODE,
  ENABLE_DEBUG_LOGGING: true,
  ENABLE_AUTHENTICATION: true, // Enabled for production-ready auth system
} as const;

// UI Constants
export const UI_CONSTANTS = {
  DIMENSIONS: {
    ICON_SIZE_HERO: 48,
    ICON_SIZE_LARGE: 32,
    ICON_SIZE_MEDIUM: 24,
    ICON_SIZE_SMALL: 16,
    BUTTON_HEIGHT: 48,
    INPUT_HEIGHT: 44,
    HEADER_HEIGHT: 56,
  },
  ANIMATIONS: {
    DURATION_SHORT: 200,
    DURATION_MEDIUM: 300,
    DURATION_LONG: 500,

  },
  SPACING: {
    XS: 4,
    SM: 8,
    MD: 16,
    LG: 24,
    XL: 32,
  },
} as const;

// Default Settings
export const DEFAULT_SETTINGS = {
  NOTIFICATIONS: {
    DAILY_QUESTIONS: true,
    PARTNER_ACTIVITY: true,
    ACHIEVEMENTS: true,
    REMINDERS: true
  },
  PRIVACY: {
    PROFILE_VISIBILITY: 'partner_only',
    DATA_SHARING: false,
    ANALYTICS: true
  },
  PREFERENCES: {
    THEME: 'light',
    LANGUAGE: 'en',
    TIME_FORMAT: '12h',
    DATE_FORMAT: 'MM/DD/YYYY'
  }
} as const;
