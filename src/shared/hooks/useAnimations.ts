import { useRef } from 'react';
import { Animated } from 'react-native';
import { animationConfig } from '../utils/sharedStyles';

// Hook for fade animations
export const useFadeAnimation = (initialValue: number = 1) => {
  const animation = useRef(new Animated.Value(initialValue)).current;

  const fadeIn = (duration: keyof typeof animationConfig = 'medium') => {
    Animated.timing(animation, {
      toValue: 1,
      ...animationConfig[duration]
    }).start();
  };

  const fadeOut = (duration: keyof typeof animationConfig = 'medium') => {
    Animated.timing(animation, {
      toValue: 0,
      ...animationConfig[duration]
    }).start();
  };

  const fadeToggle = (duration: keyof typeof animationConfig = 'medium') => {
    Animated.sequence([
      Animated.timing(animation, {
        toValue: 0,
        ...animationConfig[duration]
      }),
      Animated.timing(animation, {
        toValue: 1,
        ...animationConfig[duration]
      })
    ]).start();
  };

  return { animation, fadeIn, fadeOut, fadeToggle };
};

// Hook for slide animations
export const useSlideAnimation = (initialValue: number = 0) => {
  const animation = useRef(new Animated.Value(initialValue)).current;

  const slideIn = (toValue: number = 0, duration: keyof typeof animationConfig = 'slow') => {
    Animated.timing(animation, {
      toValue,
      ...animationConfig[duration]
    }).start();
  };

  const slideOut = (toValue: number = 100, duration: keyof typeof animationConfig = 'medium') => {
    Animated.timing(animation, {
      toValue,
      ...animationConfig[duration]
    }).start();
  };

  return { animation, slideIn, slideOut };
};

// Hook for scale animations
export const useScaleAnimation = (initialValue: number = 1) => {
  const animation = useRef(new Animated.Value(initialValue)).current;

  const scaleIn = (duration: keyof typeof animationConfig = 'medium') => {
    Animated.timing(animation, {
      toValue: 1,
      ...animationConfig[duration]
    }).start();
  };

  const scaleOut = (duration: keyof typeof animationConfig = 'medium') => {
    Animated.timing(animation, {
      toValue: 0.8,
      ...animationConfig[duration]
    }).start();
  };

  const pulse = (duration: keyof typeof animationConfig = 'fast') => {
    Animated.sequence([
      Animated.timing(animation, {
        toValue: 1.1,
        ...animationConfig[duration]
      }),
      Animated.timing(animation, {
        toValue: 1,
        ...animationConfig[duration]
      })
    ]).start();
  };

  return { animation, scaleIn, scaleOut, pulse };
};

// Hook for confetti animation
export const useConfettiAnimation = () => {
  const animation = useRef(new Animated.Value(0)).current;

  const trigger = () => {
    Animated.sequence([
      Animated.timing(animation, {
        toValue: 1,
        ...animationConfig.fast
      }),
      Animated.timing(animation, {
        toValue: 0,
        ...animationConfig.fast
      })
    ]).start();
  };

  return { animation, trigger };
};

// Hook for sequence animations
export const useSequenceAnimation = () => {
  const createSequence = (animations: Array<{
    animation: Animated.Value;
    toValue: number;
    duration: keyof typeof animationConfig;
  }>) => {
    const sequence = animations.map(({ animation, toValue, duration }) =>
      Animated.timing(animation, {
        toValue,
        ...animationConfig[duration]
      })
    );

    return Animated.sequence(sequence);
  };

  return { createSequence };
};
