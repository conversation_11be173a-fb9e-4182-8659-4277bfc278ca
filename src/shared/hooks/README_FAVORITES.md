# Unified Favorites System

## Overview

The unified favorites system provides consistent favorite functionality across all content types in the Nestled app. It uses a centralized database table (`user_favorites`) and provides reusable hooks and components for easy integration.

## Architecture

### Database Schema
```sql
CREATE TABLE user_favorites (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  item_id TEXT NOT NULL,
  item_type TEXT NOT NULL,
  source_table TEXT,
  metadata JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id, item_id, item_type)
);
```

### Supported Content Types
- `date_night_idea` - Date night activities
- `meal_idea` - Meal suggestions and recipes
- `daily_question` - Daily relationship questions
- `activity` - General activities
- `memory` - Shared memories

## Core Components

### 1. useFavorites Hook
The main hook that provides all favorites functionality:

```typescript
const {
  favoriteStatuses,        // Record<string, boolean> - Current favorite states
  loadingStates,          // Record<string, boolean> - Per-item loading states
  toggleFavorite,         // Function to toggle favorites
  debouncedToggleFavorite, // Debounced version to prevent race conditions
  isFavorited,            // Check if item is favorited
  getFavoritesByType,     // Get all favorites of a specific type
  getFavoriteCount,       // Get count of favorites by type
  refreshFavorites,       // Manually refresh favorites
  isLoading,              // Global loading state
  error                   // Error state
} = useFavorites();
```

### 2. FavoriteButton Component
Reusable button component for consistent UI:

```typescript
<FavoriteButton
  compositeId="global:123"
  contentType="meal_idea"
  size="medium"
  onToggle={(isFavorited, compositeId) => {
    console.log(`Item ${compositeId} is now ${isFavorited ? 'favorited' : 'unfavorited'}`);
  }}
/>
```

### 3. Composite ID Pattern
Items use a composite ID format to handle different sources:
- `"global:123"` - Global/shared content
- `"user:456"` - User-generated content

## Implementation Guide

### Step 1: Add Composite ID to Your Data Interface

```typescript
interface YourContentType {
  id: string;
  title: string;
  // ... other fields
  composite_id: string; // Add this field
}
```

### Step 2: Generate Composite IDs in Your Service

```typescript
// In your service file
private createCompositeId(id: string, source: 'global' | 'user'): string {
  return `${source}:${id}`;
}

// When transforming data
const transformedItem = {
  ...rawItem,
  composite_id: this.createCompositeId(rawItem.id, 'global')
};
```

### Step 3: Use the Hook in Your Component

```typescript
import { useFavorites } from '../../shared/hooks/useFavorites';

export default function YourComponent() {
  const { 
    debouncedToggleFavorite,
    isFavorited,
    loadingStates 
  } = useFavorites();

  const handleToggle = useCallback(async (compositeId: string) => {
    try {
      await debouncedToggleFavorite(compositeId, 'your_content_type');
    } catch (error) {
      Alert.alert('Error', 'Failed to update favorite');
    }
  }, [debouncedToggleFavorite]);

  const isItemFavorited = useCallback((compositeId: string) => {
    return isFavorited(compositeId, 'your_content_type');
  }, [isFavorited]);

  // In your render method
  return (
    <TouchableOpacity onPress={() => handleToggle(item.composite_id)}>
      <Heart 
        color={isItemFavorited(item.composite_id) ? colors.accent : colors.textSecondary}
        fill={isItemFavorited(item.composite_id) ? colors.accent : 'transparent'}
      />
    </TouchableOpacity>
  );
}
```

### Step 4: Or Use the FavoriteButton Component

```typescript
import { FavoriteButton } from '../../shared/components/common/FavoriteButton';

// In your render method
<FavoriteButton
  compositeId={item.composite_id}
  contentType="your_content_type"
  size="medium"
  metadata={{ title: item.title, category: item.category }}
  onToggle={(isFavorited, compositeId) => {
    // Optional callback for additional actions
    console.log(`${compositeId} toggled to ${isFavorited}`);
  }}
/>
```

## Performance Features

### Optimistic Updates
The system immediately updates the UI when a user clicks favorite, then syncs with the database. If the database operation fails, the UI reverts to the previous state.

### Debouncing
Rapid clicks are debounced to prevent race conditions and excessive API calls.

### Memoization
Expensive computations like favorite counts and filtered lists are memoized for optimal performance.

### Loading States
Per-item loading states provide smooth UX feedback during operations.

## Error Handling

The system includes comprehensive error handling:
- Network failures are caught and user-friendly messages are shown
- Optimistic updates are reverted on failure
- Loading states are properly cleared
- Errors are logged for debugging

## Best Practices

1. **Always use composite_id**: Never use raw IDs for favorites
2. **Use debounced toggle**: Prevents race conditions from rapid clicking
3. **Handle loading states**: Show appropriate UI feedback
4. **Provide metadata**: Store useful context with favorites
5. **Use the FavoriteButton component**: Ensures consistent UI/UX
6. **Test persistence**: Verify favorites survive app restarts and navigation

## Migration from Old System

If you have existing favorites code:

1. Replace direct service calls with the `useFavorites` hook
2. Update item IDs to use the composite_id format
3. Replace custom heart components with `FavoriteButton`
4. Remove duplicate state management code
5. Test thoroughly to ensure data migration works correctly

## Troubleshooting

### Favorites not persisting
- Check that composite_id format is correct
- Verify user authentication
- Check database permissions and RLS policies

### Performance issues
- Ensure you're using `useCallback` and `useMemo` appropriately
- Check that debouncing is enabled
- Monitor for excessive re-renders

### UI inconsistencies
- Use the `FavoriteButton` component for consistency
- Check that loading states are properly handled
- Verify color schemes match the design system
