/**
 * useAppTheme Hook
 *
 * Provides theme functionality throughout the app.
 * Now fully integrated with the consolidated theme system.
 */

import { useGlobalTheme } from '../components/common/ThemeProvider';

export const useAppTheme = () => {
  const { currentTheme, isDarkMode, themeMode, completeTheme } = useGlobalTheme();

  return {
    theme: currentTheme,
    completeTheme,
    isDarkMode,
    mode: themeMode,
    // Legacy compatibility
    colors: currentTheme,
    tokens: completeTheme.tokens,
  };
};

export default useAppTheme;
