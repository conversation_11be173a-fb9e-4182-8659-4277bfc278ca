/**
 * Toast Hook and Context
 *
 * Provides a simple API for showing toast notifications throughout the app.
 * Manages toast state and provides methods to show/dismiss toasts.
 *
 * @version 3.0.0
 * <AUTHOR> Us Team
 */

import React, { createContext, ReactNode, useCallback, useContext, useState } from 'react';
import { useGlobalTheme } from '../components/common/ThemeProvider';
import { ToastData, ToastManager, ToastType } from '../components/common/Toast';

// =============================================================================
// TYPES
// =============================================================================

interface ToastContextValue {
  showToast: (_type: ToastType, _title: string, _message?: string, _duration?: number) => string;
  showSuccess: (_title: string, _message?: string, _duration?: number) => string;
  showError: (_title: string, _message?: string, _duration?: number) => string;
  showWarning: (_title: string, _message?: string, _duration?: number) => string;
  showInfo: (_title: string, _message?: string, _duration?: number) => string;
  dismissToast: (_id: string) => void;
  dismissAll: () => void;
}

// =============================================================================
// CONTEXT
// =============================================================================

const ToastContext = createContext<ToastContextValue | undefined>(undefined);

// =============================================================================
// PROVIDER
// =============================================================================

interface ToastProviderProps {
  children: ReactNode;
}

export const ToastProvider: React.FC<ToastProviderProps> = ({ children }) => {
  const [toasts, setToasts] = useState<ToastData[]>([]);
  const { currentTheme } = useGlobalTheme();

  const generateId = useCallback(() => {
    return `toast_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }, []);

  const showToast = useCallback((
    type: ToastType,
    title: string,
    message?: string,
    duration?: number
  ): string => {
    const id = generateId();
    const newToast: ToastData = {
      id,
      type,
      title,
      message,
      duration,
    };

    setToasts(prev => [...prev, newToast]);
    return id;
  }, [generateId]);

  const showSuccess = useCallback((
    title: string,
    message?: string,
    duration?: number
  ): string => {
    return showToast('success', title, message, duration);
  }, [showToast]);

  const showError = useCallback((
    title: string,
    message?: string,
    duration?: number
  ): string => {
    return showToast('error', title, message, duration);
  }, [showToast]);

  const showWarning = useCallback((
    title: string,
    message?: string,
    duration?: number
  ): string => {
    return showToast('warning', title, message, duration);
  }, [showToast]);

  const showInfo = useCallback((
    title: string,
    message?: string,
    duration?: number
  ): string => {
    return showToast('info', title, message, duration);
  }, [showToast]);

  const dismissToast = useCallback((id: string) => {
    setToasts(prev => prev.filter(toast => toast.id !== id));
  }, []);

  const dismissAll = useCallback(() => {
    setToasts([]);
  }, []);

  const contextValue: ToastContextValue = {
    showToast,
    showSuccess,
    showError,
    showWarning,
    showInfo,
    dismissToast,
    dismissAll,
  };

  return (
    <ToastContext.Provider value={contextValue}>
      {children}
      <ToastManager
        toasts={toasts}
        onDismiss={dismissToast}
        theme={currentTheme}
      />
    </ToastContext.Provider>
  );
};

// =============================================================================
// HOOK
// =============================================================================

export const useToast = (): ToastContextValue => {
  const context = useContext(ToastContext);
  if (!context) {
    throw new Error('useToast must be used within a ToastProvider');
  }
  return context;
};

// =============================================================================
// CONVENIENCE HOOKS
// =============================================================================

/**
 * Hook for showing success messages
 */
export const useSuccessToast = () => {
  const { showSuccess } = useToast();
  return showSuccess;
};

/**
 * Hook for showing error messages
 */
export const useErrorToast = () => {
  const { showError } = useToast();
  return showError;
};

/**
 * Hook for showing warning messages
 */
export const useWarningToast = () => {
  const { showWarning } = useToast();
  return showWarning;
};

/**
 * Hook for showing info messages
 */
export const useInfoToast = () => {
  const { showInfo } = useToast();
  return showInfo;
};

// =============================================================================
// UTILITY FUNCTIONS
// =============================================================================

/**
 * Show error toast from Error object
 */
export const showErrorFromException = (
  showError: (_title: string, _message?: string, _duration?: number) => string,
  error: Error | unknown,
  fallbackTitle: string = 'Something went wrong'
) => {
  if (error instanceof Error) {
    return showError(fallbackTitle, error.message);
  } else {
    return showError(fallbackTitle, 'An unexpected error occurred');
  }
};

/**
 * Show network error toast
 */
export const showNetworkError = (
  showError: (_title: string, _message?: string, _duration?: number) => string
) => {
  return showError(
    'Network Error',
    'Please check your internet connection and try again'
  );
};

/**
 * Show validation error toast
 */
export const showValidationError = (
  showError: (_title: string, _message?: string, _duration?: number) => string,
  field: string,
  message: string
) => {
  return showError(
    `Invalid ${field}`,
    message
  );
};

export default useToast;
