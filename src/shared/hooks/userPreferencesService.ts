import { supabase } from '../services/supabase/client';
import { logger } from '../utils/logger';

export interface UserPreferences {
  id: string;
  user_id: string;
  hidden_date_night_ideas: string[];
  hidden_meal_ideas: string[]; // Add support for hiding meal ideas
  favorite_categories: string[];
  preferred_difficulty?: 'easy' | 'medium' | 'hard';
  preferred_cost?: 'free' | 'low' | 'medium' | 'high';
  preferred_duration_min?: number;
  preferred_duration_max?: number;
  notification_preferences: Record<string, any>;
  privacy_settings: Record<string, any>;
  ui_preferences: Record<string, any>;
  created_at: string;
  updated_at: string;
}

class UserPreferencesService {
  /**
   * Get user preferences, creating default ones if they don't exist
   */
  async getUserPreferences(userId: string): Promise<UserPreferences | null> {
    try {
      const { data, error } = await supabase
        .rpc('get_user_preferences', { user_uuid: userId });

      if (error) {
        logger.error('Error getting user preferences:', error);
        console.log(`❌ [USER_PREFERENCES] Get preferences error:`, {
          code: error.code,
          message: error.message,
          details: error.details,
          hint: error.hint
        });
        throw new Error(`Failed to get user preferences: ${error.message}`);
      }

      // Transform the data to match the UserPreferences interface
      return {
        ...data,
        preferred_difficulty: data.preferred_difficulty === null ? undefined : data.preferred_difficulty as 'easy' | 'medium' | 'hard',
        preferred_cost: data.preferred_cost === null ? undefined : data.preferred_cost as 'free' | 'low' | 'medium' | 'high',
        preferred_duration_min: data.preferred_duration_min === null ? undefined : data.preferred_duration_min,
        preferred_duration_max: data.preferred_duration_max === null ? undefined : data.preferred_duration_max,
        notification_preferences: (data.notification_preferences && typeof data.notification_preferences === 'object' && !Array.isArray(data.notification_preferences)) ? data.notification_preferences as Record<string, any> : {},
        privacy_settings: (data.privacy_settings && typeof data.privacy_settings === 'object' && !Array.isArray(data.privacy_settings)) ? data.privacy_settings as Record<string, any> : {},
        ui_preferences: (data.ui_preferences && typeof data.ui_preferences === 'object' && !Array.isArray(data.ui_preferences)) ? data.ui_preferences as Record<string, any> : {},
        hidden_meal_ideas: [],
      };
    } catch (error) {
      logger.error('Error in getUserPreferences:', error);
      return null;
    }
  }

  /**
   * Hide a date night idea for the user
   */
  async hideDateNightIdea(userId: string, compositeId: string): Promise<boolean> {
    try {
      const { data, error } = await supabase
        .rpc('hide_date_night_idea', {
          user_uuid: userId,
          idea_composite_id: compositeId
        });

      if (error) {
        logger.error('Error hiding date night idea:', error);
        throw new Error(`Failed to hide idea: ${error.message}`);
      }

      logger.info(`Date night idea ${compositeId} hidden for user ${userId}`);
      return data;
    } catch (error) {
      logger.error('Error in hideDateNightIdea:', error);
      throw error;
    }
  }

  /**
   * Hide a meal idea for the user
   */
  async hideMealIdea(userId: string, compositeId: string): Promise<boolean> {
    try {
      // Get current preferences
      const preferences = await this.getUserPreferences(userId);
      if (!preferences) {
        throw new Error('User preferences not found');
      }

      // Add to hidden meal ideas if not already hidden
      const hiddenMealIdeas = [...(preferences.hidden_meal_ideas || [])];
      if (!hiddenMealIdeas.includes(compositeId)) {
        hiddenMealIdeas.push(compositeId);
      }

      // Update preferences
      const success = await this.updatePreferences(userId, {
        hidden_meal_ideas: hiddenMealIdeas
      });

      if (success) {
        logger.info(`Meal idea ${compositeId} hidden for user ${userId}`);
      }

      return success;
    } catch (error) {
      logger.error('Error in hideMealIdea:', error);
      throw error;
    }
  }

  /**
   * Unhide a date night idea for the user
   */
  async unhideDateNightIdea(userId: string, compositeId: string): Promise<boolean> {
    try {
      const { data, error } = await supabase
        .rpc('unhide_date_night_idea', {
          user_uuid: userId,
          idea_composite_id: compositeId
        });

      if (error) {
        logger.error('Error unhiding date night idea:', error);
        throw new Error(`Failed to unhide idea: ${error.message}`);
      }

      logger.info(`Date night idea ${compositeId} unhidden for user ${userId}`);
      return data;
    } catch (error) {
      logger.error('Error in unhideDateNightIdea:', error);
      throw error;
    }
  }

  /**
   * Unhide a meal idea for the user
   */
  async unhideMealIdea(userId: string, compositeId: string): Promise<boolean> {
    try {
      // Get current preferences
      const preferences = await this.getUserPreferences(userId);
      if (!preferences) {
        throw new Error('User preferences not found');
      }

      // Remove from hidden meal ideas
      const hiddenMealIdeas = (preferences.hidden_meal_ideas || []).filter(id => id !== compositeId);

      // Update preferences
      const success = await this.updatePreferences(userId, {
        hidden_meal_ideas: hiddenMealIdeas
      });

      if (success) {
        logger.info(`Meal idea ${compositeId} unhidden for user ${userId}`);
      }

      return success;
    } catch (error) {
      logger.error('Error in unhideMealIdea:', error);
      throw error;
    }
  }

  /**
   * Check if a date night idea is hidden for the user
   */
  async isIdeaHidden(userId: string, compositeId: string): Promise<boolean> {
    try {
      const { data, error } = await supabase
        .rpc('is_idea_hidden', {
          user_uuid: userId,
          idea_composite_id: compositeId
        });

      if (error) {
        logger.error('Error checking if idea is hidden:', error);
        return false;
      }

      return data || false;
    } catch (error) {
      logger.error('Error in isIdeaHidden:', error);
      return false;
    }
  }

  /**
   * Check if a meal idea is hidden for the user
   */
  async isMealIdeaHidden(userId: string, compositeId: string): Promise<boolean> {
    try {
      const preferences = await this.getUserPreferences(userId);
      return preferences?.hidden_meal_ideas?.includes(compositeId) || false;
    } catch (error) {
      logger.error('Error checking if meal idea is hidden:', error);
      return false;
    }
  }

  /**
   * Generic method to hide any type of idea
   */
  async hideIdea(userId: string, compositeId: string, type: 'date_night' | 'meal'): Promise<boolean> {
    if (type === 'date_night') {
      return this.hideDateNightIdea(userId, compositeId);
    } else if (type === 'meal') {
      return this.hideMealIdea(userId, compositeId);
    } else {
      throw new Error(`Unsupported idea type: ${type}`);
    }
  }

  /**
   * Generic method to unhide any type of idea
   */
  async unhideIdea(userId: string, compositeId: string, type: 'date_night' | 'meal'): Promise<boolean> {
    if (type === 'date_night') {
      return this.unhideDateNightIdea(userId, compositeId);
    } else if (type === 'meal') {
      return this.unhideMealIdea(userId, compositeId);
    } else {
      throw new Error(`Unsupported idea type: ${type}`);
    }
  }

  /**
   * Get all hidden date night ideas for the user
   */
  async getHiddenIdeas(userId: string): Promise<string[]> {
    try {
      const preferences = await this.getUserPreferences(userId);
      return preferences?.hidden_date_night_ideas || [];
    } catch (error) {
      logger.error('Error getting hidden ideas:', error);
      return [];
    }
  }

  /**
   * Get all hidden meal ideas for the user
   */
  async getHiddenMealIdeas(userId: string): Promise<string[]> {
    try {
      const preferences = await this.getUserPreferences(userId);
      return preferences?.hidden_meal_ideas || [];
    } catch (error) {
      logger.error('Error getting hidden meal ideas:', error);
      return [];
    }
  }

  /**
   * Get hidden ideas by type
   */
  async getHiddenIdeasByType(userId: string, type: 'date_night' | 'meal'): Promise<string[]> {
    if (type === 'date_night') {
      return this.getHiddenIdeas(userId);
    } else if (type === 'meal') {
      return this.getHiddenMealIdeas(userId);
    } else {
      throw new Error(`Unsupported idea type: ${type}`);
    }
  }

  /**
   * Update user preferences
   */
  async updatePreferences(
    userId: string,
    updates: Partial<Omit<UserPreferences, 'id' | 'user_id' | 'created_at' | 'updated_at'>>
  ): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('user_preferences')
        .upsert({
          user_id: userId,
          ...updates,
          updated_at: new Date().toISOString()
        }, {
          onConflict: 'user_id'
        });

      if (error) {
        logger.error('Error updating user preferences:', error);
        throw new Error(`Failed to update preferences: ${error.message}`);
      }

      logger.info(`User preferences updated for user ${userId}`);
      return true;
    } catch (error) {
      logger.error('Error in updatePreferences:', error);
      throw error;
    }
  }

  /**
   * Set favorite categories for the user
   */
  async setFavoriteCategories(userId: string, categories: string[]): Promise<boolean> {
    return this.updatePreferences(userId, { favorite_categories: categories });
  }

  /**
   * Set preferred difficulty for the user
   */
  async setPreferredDifficulty(userId: string, difficulty: 'easy' | 'medium' | 'hard'): Promise<boolean> {
    return this.updatePreferences(userId, { preferred_difficulty: difficulty });
  }

  /**
   * Set preferred cost level for the user
   */
  async setPreferredCost(userId: string, cost: 'free' | 'low' | 'medium' | 'high'): Promise<boolean> {
    return this.updatePreferences(userId, { preferred_cost: cost });
  }

  /**
   * Set preferred duration range for the user
   */
  async setPreferredDuration(userId: string, minMinutes: number, maxMinutes: number): Promise<boolean> {
    return this.updatePreferences(userId, {
      preferred_duration_min: minMinutes,
      preferred_duration_max: maxMinutes
    });
  }

  /**
   * Filter out hidden ideas from a list of date night ideas
   */
  async filterHiddenIdeas<T extends { composite_id: string }>(
    userId: string,
    ideas: T[]
  ): Promise<T[]> {
    try {
      const hiddenIds = await this.getHiddenIdeas(userId);
      return ideas.filter(idea => !hiddenIds.includes(idea.composite_id));
    } catch (error) {
      logger.error('Error filtering hidden ideas:', error);
      // Return original list if filtering fails
      return ideas;
    }
  }

  /**
   * Filter out hidden meal ideas from a list of meal ideas
   */
  async filterHiddenMealIdeas<T extends { composite_id: string }>(
    userId: string,
    ideas: T[]
  ): Promise<T[]> {
    try {
      const hiddenIds = await this.getHiddenMealIdeas(userId);
      return ideas.filter(idea => !hiddenIds.includes(idea.composite_id));
    } catch (error) {
      logger.error('Error filtering hidden meal ideas:', error);
      // Return original list if filtering fails
      return ideas;
    }
  }

  /**
   * Filter out hidden ideas by type
   */
  async filterHiddenIdeasByType<T extends { composite_id: string }>(
    userId: string,
    ideas: T[],
    type: 'date_night' | 'meal'
  ): Promise<T[]> {
    if (type === 'date_night') {
      return this.filterHiddenIdeas(userId, ideas);
    } else if (type === 'meal') {
      return this.filterHiddenMealIdeas(userId, ideas);
    } else {
      throw new Error(`Unsupported idea type: ${type}`);
    }
  }
}

export const userPreferencesService = new UserPreferencesService();
