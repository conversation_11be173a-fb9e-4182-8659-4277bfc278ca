/**
 * Unified Favorites Hook
 * Provides consistent favorite functionality across all features
 */

import { useCallback, useEffect, useRef, useState } from 'react';
import { useAuth } from '../../journeys/onboarding/useAuth';
import {
    FavoriteItem,
    FavoriteItemType,
    FavoriteMetadata,
    favoritesService
} from '../services/favoritesService';
import { logger } from '../utils/logger';

export interface UseFavoritesReturn {
  favorites: FavoriteItem[];
  favoriteStatuses: Record<string, boolean>;
  loadingStates: Record<string, boolean>; // Per-item loading states
  isLoading: boolean;
  error: string | null;

  // Actions
  addFavorite: (_itemId: string, _itemType: FavoriteItemType, _sourceTable?: string, _metadata?: FavoriteMetadata) => Promise<boolean>;
  removeFavorite: (_itemId: string, _itemType: FavoriteItemType) => Promise<boolean>;
  toggleFavorite: (_itemId: string, _itemType: FavoriteItemType, _sourceTable?: string, _metadata?: FavoriteMetadata) => Promise<boolean>;
  debouncedToggleFavorite: (_itemId: string, _itemType: FavoriteItemType, _sourceTable?: string, _metadata?: FavoriteMetadata, _debounceMs?: number) => Promise<boolean>;
  isFavorited: (_itemId: string, _itemType: FavoriteItemType) => boolean;
  getFavoritesByType: (_itemType: FavoriteItemType) => FavoriteItem[];
  getFavoriteCount: (_itemType: FavoriteItemType) => number;
  refreshFavorites: () => Promise<void>;
}

export function useFavorites(): UseFavoritesReturn {
  const { user } = useAuth();
  const [favorites, setFavorites] = useState<FavoriteItem[]>([]);
  const [favoriteStatuses, setFavoriteStatuses] = useState<Record<string, boolean>>({});
  const [loadingStates, setLoadingStates] = useState<Record<string, boolean>>({});
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Debouncing refs to prevent race conditions
  const debounceTimeouts = useRef<Record<string, NodeJS.Timeout>>({});

  // Load all favorites for the user
  const loadFavorites = useCallback(async () => {
    if (!user?.id) {
      setFavorites([]);
      setFavoriteStatuses({});
      return;
    }

    try {
      setIsLoading(true);
      setError(null);

      const userFavorites = await favoritesService.getAllFavorites(user.id);
      setFavorites(userFavorites);

      // Create status lookup for quick access
      const statuses: Record<string, boolean> = {};
      userFavorites.forEach(fav => {
        const key = `${fav.item_id}:${fav.item_type}`;
        statuses[key] = true;
      });
      setFavoriteStatuses(statuses);

      logger.info('Favorites loaded:', { count: userFavorites.length });
    } catch (error) {
      logger.error('Error loading favorites:', error);
      setError('Failed to load favorites');
    } finally {
      setIsLoading(false);
    }
  }, [user?.id]);

  // Load favorites when user changes
  useEffect(() => {
    loadFavorites();
  }, [loadFavorites]);

  // Add favorite
  const addFavorite = useCallback(async (
    itemId: string,
    itemType: FavoriteItemType,
    sourceTable?: string,
    metadata?: FavoriteMetadata
  ): Promise<boolean> => {
    if (!user?.id) {
      logger.warn('Cannot add favorite: no user logged in');
      return false;
    }

    try {
      const success = await favoritesService.addFavorite(user.id, itemId, itemType, sourceTable, metadata);

      if (success) {
        // Update local state optimistically
        const key = `${itemId}:${itemType}`;
        setFavoriteStatuses(prev => ({ ...prev, [key]: true }));

        // Refresh favorites to get the complete data
        await loadFavorites();
      }

      return success;
    } catch (error) {
      logger.error('Error adding favorite:', error);
      return false;
    }
  }, [user?.id, loadFavorites]);

  // Remove favorite
  const removeFavorite = useCallback(async (
    itemId: string,
    itemType: FavoriteItemType
  ): Promise<boolean> => {
    if (!user?.id) {
      logger.warn('Cannot remove favorite: no user logged in');
      return false;
    }

    try {
      const success = await favoritesService.removeFavorite(user.id, itemId, itemType);

      if (success) {
        // Update local state optimistically
        const key = `${itemId}:${itemType}`;
        setFavoriteStatuses(prev => ({ ...prev, [key]: false }));

        // Refresh favorites to get the complete data
        await loadFavorites();
      }

      return success;
    } catch (error) {
      logger.error('Error removing favorite:', error);
      return false;
    }
  }, [user?.id, loadFavorites]);

  // Toggle favorite with optimistic updates
  const toggleFavorite = useCallback(async (
    itemId: string,
    itemType: FavoriteItemType,
    sourceTable?: string,
    metadata?: FavoriteMetadata
  ): Promise<boolean> => {
    if (!user?.id) {
      logger.warn('Cannot toggle favorite: no user logged in');
      return false;
    }

    const key = `${itemId}:${itemType}`;
    const currentlyFavorited = favoriteStatuses[key] || false;
    const newFavoriteState = !currentlyFavorited;

    try {
      // Set loading state
      setLoadingStates(prev => ({ ...prev, [key]: true }));
      setError(null);

      // Optimistic update
      setFavoriteStatuses(prev => ({ ...prev, [key]: newFavoriteState }));

      // Perform database operation
      const success = await favoritesService.toggleFavorite(user.id, itemId, itemType, sourceTable, metadata);

      if (success) {
        // Refresh favorites to get the complete updated state
        await loadFavorites();
        logger.info(`Successfully ${newFavoriteState ? 'favorited' : 'unfavorited'} ${itemType}:`, itemId);
      } else {
        // Revert optimistic update on failure
        setFavoriteStatuses(prev => ({ ...prev, [key]: currentlyFavorited }));
        logger.error('Failed to toggle favorite - reverting optimistic update');
      }

      return success;
    } catch (error) {
      // Revert optimistic update on error
      setFavoriteStatuses(prev => ({ ...prev, [key]: currentlyFavorited }));
      logger.error('Error toggling favorite:', error);
      setError(`Failed to ${newFavoriteState ? 'add' : 'remove'} favorite`);
      return false;
    } finally {
      // Clear loading state
      setLoadingStates(prev => ({ ...prev, [key]: false }));
    }
  }, [user?.id, favoriteStatuses, loadFavorites]);

  // Debounced toggle to prevent race conditions from rapid clicking
  const debouncedToggleFavorite = useCallback((
    itemId: string,
    itemType: FavoriteItemType,
    sourceTable?: string,
    metadata?: FavoriteMetadata,
    debounceMs: number = 300
  ): Promise<boolean> => {
    return new Promise((resolve) => {
      const key = `${itemId}:${itemType}`;

      // Clear existing timeout for this item
      if (debounceTimeouts.current[key]) {
        clearTimeout(debounceTimeouts.current[key]);
      }

      // Set new timeout
      debounceTimeouts.current[key] = setTimeout(async () => {
        const result = await toggleFavorite(itemId, itemType, sourceTable, metadata);
        delete debounceTimeouts.current[key];
        resolve(result);
      }, debounceMs);
    });
  }, [toggleFavorite]);

  // Check if item is favorited
  const isFavorited = useCallback((itemId: string, itemType: FavoriteItemType): boolean => {
    const key = `${itemId}:${itemType}`;
    return favoriteStatuses[key] || false;
  }, [favoriteStatuses]);

  // Get favorites by type
  const getFavoritesByType = useCallback((itemType: FavoriteItemType): FavoriteItem[] => {
    return favorites.filter(fav => fav.item_type === itemType);
  }, [favorites]);

  // Get favorite count by type
  const getFavoriteCount = useCallback((itemType: FavoriteItemType): number => {
    return favorites.filter(fav => fav.item_type === itemType).length;
  }, [favorites]);

  // Refresh favorites
  const refreshFavorites = useCallback(async () => {
    await loadFavorites();
  }, [loadFavorites]);

  return {
    favorites,
    favoriteStatuses,
    loadingStates,
    isLoading,
    error,
    addFavorite,
    removeFavorite,
    toggleFavorite,
    debouncedToggleFavorite,
    isFavorited,
    getFavoritesByType,
    getFavoriteCount,
    refreshFavorites
  };
}
