/**
 * Would You Rather Activity Component
 *
 * Fun dilemma-based activity that reveals preferences
 * and sparks interesting conversations between partners.
 */

import { ArrowRight, CheckCircle, Users } from 'lucide-react-native';
import React, { useCallback, useState } from 'react';
import {
    ScrollView,
    StyleSheet,
    Text,
    TouchableOpacity,
    View,
} from 'react-native';

import { DSBadge } from '../../../components/shared';
import { colors } from '../../utils/colors';
import { ActivityCompletionData, BaseActivityProps } from './types';

interface WouldYouRatherData {
  currentQuestionIndex: number;
  responses: Record<number, { choice: 'A' | 'B'; reason?: string }>;
  completed: boolean;
}

const questions = [
  {
    id: 1,
    question: "Would you rather...",
    optionA: "Have the ability to fly",
    optionB: "Have the ability to read minds",
    category: "Superpowers"
  },
  {
    id: 2,
    question: "Would you rather...",
    optionA: "Always be 10 minutes late",
    optionB: "Always be 20 minutes early",
    category: "Time"
  },
  {
    id: 3,
    question: "Would you rather...",
    optionA: "Live in a world without music",
    optionB: "Live in a world without movies",
    category: "Entertainment"
  },
  {
    id: 4,
    question: "Would you rather...",
    optionA: "Have dinner with your favorite celebrity",
    optionB: "Have dinner with a historical figure",
    category: "People"
  },
  {
    id: 5,
    question: "Would you rather...",
    optionA: "Always have to tell the truth",
    optionB: "Always have to lie",
    category: "Honesty"
  },
];

/**
 * Would You Rather Activity Component
 */
export const WouldYouRatherActivity: React.FC<BaseActivityProps> = ({
  onComplete,
  onError,
  initialData,
  isActive,
  weekNumber,
  stepIndex,
}) => {
  const [activityData, setActivityData] = useState<WouldYouRatherData>(
    initialData?.wouldYouRather || {
      currentQuestionIndex: 0,
      responses: {},
      completed: false,
    }
  );
  const [isCompleting, setIsCompleting] = useState(false);

  const currentQuestion = questions[activityData.currentQuestionIndex];
  const isLastQuestion = activityData.currentQuestionIndex === questions.length - 1;

  const handleChoiceSelect = useCallback((choice: 'A' | 'B') => {
    const newResponses = {
      ...activityData.responses,
      [activityData.currentQuestionIndex]: { choice },
    };

    setActivityData(prev => ({
      ...prev,
      responses: newResponses,
    }));
  }, [activityData.responses, activityData.currentQuestionIndex]);

  const handleNext = useCallback(() => {
    if (isLastQuestion) {
      handleComplete();
    } else {
      setActivityData(prev => ({
        ...prev,
        currentQuestionIndex: prev.currentQuestionIndex + 1,
      }));
    }
  }, [isLastQuestion]);

  const handleComplete = useCallback(async () => {
    try {
      setIsCompleting(true);

      const completionData: ActivityCompletionData = {
        activityType: 'would-you-rather',
        points: 15,
        data: {
          ...activityData,
          completed: true,
          weekNumber,
          stepIndex,
          totalQuestions: questions.length,
          completedQuestions: Object.keys(activityData.responses).length,
        },
        completedAt: Date.now(),
      };

      onComplete(completionData);
    } catch (error) {
      onError(error instanceof Error ? error : new Error('Failed to complete Would You Rather activity'));
    } finally {
      setIsCompleting(false);
    }
  }, [activityData, onComplete, onError, weekNumber, stepIndex]);

  if (!isActive) {
    return null;
  }

  const currentResponse = activityData.responses[activityData.currentQuestionIndex];
  const canProceed = currentResponse !== undefined;

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      <View style={styles.header}>
        <Text style={styles.title}>Would You Rather?</Text>
        <Text style={styles.subtitle}>
          Question {activityData.currentQuestionIndex + 1} of {questions.length}
        </Text>
      </View>

      <View style={styles.progressContainer}>
        <View style={styles.progressBar}>
          <View
            style={[
              styles.progressFill,
              { width: `${((activityData.currentQuestionIndex + 1) / questions.length) * 100}%` }
            ]}
          />
        </View>
      </View>

      <View style={styles.questionCard}>
        <Text style={styles.questionText}>{currentQuestion.question}</Text>

        <TouchableOpacity
          style={[
            styles.optionButton,
            currentResponse?.choice === 'A' && styles.optionButtonSelected
          ]}
          onPress={() => handleChoiceSelect('A')}
        >
          <View style={styles.optionContent}>
            <View style={styles.optionLabel}>
              <Text style={styles.optionLetter}>A</Text>
            </View>
            <Text style={[
              styles.optionText,
              currentResponse?.choice === 'A' && styles.optionTextSelected
            ]}>
              {currentQuestion.optionA}
            </Text>
            {currentResponse?.choice === 'A' && (
              <CheckCircle size={20} color={colors.white} />
            )}
          </View>
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.optionButton,
            currentResponse?.choice === 'B' && styles.optionButtonSelected
          ]}
          onPress={() => handleChoiceSelect('B')}
        >
          <View style={styles.optionContent}>
            <View style={styles.optionLabel}>
              <Text style={styles.optionLetter}>B</Text>
            </View>
            <Text style={[
              styles.optionText,
              currentResponse?.choice === 'B' && styles.optionTextSelected
            ]}>
              {currentQuestion.optionB}
            </Text>
            {currentResponse?.choice === 'B' && (
              <CheckCircle size={20} color={colors.white} />
            )}
          </View>
        </TouchableOpacity>
      </View>

      {canProceed && (
        <TouchableOpacity
          style={[styles.nextButton, isCompleting && styles.nextButtonDisabled]}
          onPress={handleNext}
          disabled={isCompleting}
        >
          <Text style={styles.nextButtonText}>
            {isLastQuestion
              ? (isCompleting ? 'Completing...' : 'Complete Activity')
              : 'Next Question'
            }
          </Text>
          {!isLastQuestion && <ArrowRight size={20} color={colors.white} />}
        </TouchableOpacity>
      )}

      {activityData.completed && (
        <DSBadge
          variant="success"
          style={styles.completedBadge}
        >
          Activity Complete
        </DSBadge>
      )}
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    marginBottom: 20,
    paddingHorizontal: 20,
    alignItems: 'center',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: colors.textPrimary,
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: colors.textSecondary,
  },
  progressContainer: {
    paddingHorizontal: 20,
    marginBottom: 30,
  },
  progressBar: {
    height: 6,
    backgroundColor: colors.border,
    borderRadius: 3,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    backgroundColor: colors.primary,
    borderRadius: 3,
  },
  questionCard: {
    backgroundColor: colors.white,
    borderRadius: 16,
    padding: 20,
    marginHorizontal: 20,
    marginBottom: 20,
    shadowColor: colors.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  questionText: {
    fontSize: 20,
    fontWeight: '600',
    color: colors.textPrimary,
    textAlign: 'center',
    marginBottom: 30,
  },
  optionButton: {
    backgroundColor: colors.backgroundSecondary,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    borderWidth: 2,
    borderColor: colors.border,
  },
  optionButtonSelected: {
    backgroundColor: colors.primary,
    borderColor: colors.primary,
  },
  optionContent: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  optionLabel: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: colors.white,
    alignItems: 'center',
    justifyContent: 'center',
  },
  optionLetter: {
    fontSize: 16,
    fontWeight: 'bold',
    color: colors.textPrimary,
  },
  optionText: {
    flex: 1,
    fontSize: 16,
    color: colors.textPrimary,
    fontWeight: '500',
  },
  optionTextSelected: {
    color: colors.white,
  },
  nextButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 8,
    backgroundColor: colors.success,
    borderRadius: 12,
    padding: 16,
    marginHorizontal: 20,
    marginBottom: 20,
  },
  nextButtonDisabled: {
    opacity: 0.6,
  },
  nextButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.white,
  },
  completedBadge: {
    alignSelf: 'center',
    marginBottom: 20,
  },
});

// Register this activity
export const wouldYouRatherConfig = {
  id: 'would-you-rather',
  title: 'Would You Rather',
  description: 'Fun dilemmas that reveal preferences and spark conversations',
  icon: <Users size={24} color={colors.white} />,
  defaultPoints: 15,
  category: 'games' as const,
  estimatedDuration: 10,
  requiresPartner: false,
  component: WouldYouRatherActivity,
};
