/**
 * Week Configurations
 *
 * Data-driven configuration system that defines which activities
 * are included in each week and their presentation.
 */

import { colors } from '../../utils/colors';
import { WeekConfig } from './types';

/**
 * Configuration for all 12 weeks
 * Each week defines its activities, theme, and presentation
 */
export const weekConfigurations: WeekConfig[] = [
  {
    weekNumber: 1,
    title: 'Getting to Know You',
    description: 'Discover new things about each other',
    headerColor: colors.primary,
    activityIds: ['match-game', 'date-night-plan', 'chat-prompts', 'soft-startup'],
    stepTitles: ['The Match Activity', 'Date Night Plan', 'Chat Prompts', 'Soft Startup'],
    pointsMultiplier: 1,
    requiresPreviousCompletion: false,
  },
  {
    weekNumber: 2,
    title: 'Celebrating Strengths',
    description: 'Appreciate what makes each other unique',
    headerColor: colors.secondary,
    activityIds: ['strengths-bingo', 'date-night-plan', 'chat-prompts', 'five-to-one'],
    stepTitles: ['Strengths Bingo', 'Date Night Plan', 'Chat Prompts', '5:1 Ratio'],
    pointsMultiplier: 1,
    requiresPreviousCompletion: false,
  },
  {
    weekNumber: 3,
    title: 'Would You Rather',
    description: 'Fun choices and preferences',
    headerColor: colors.accent1,
    activityIds: ['would-you-rather', 'date-night-plan', 'chat-prompts', 'emotional-bank'],
    stepTitles: ['Would You Rather', 'Date Night Plan', 'Chat Prompts', 'Emotional Bank'],
    pointsMultiplier: 1,
    requiresPreviousCompletion: false,
  },
  {
    weekNumber: 4,
    title: 'Love Languages',
    description: 'Understanding how you each feel loved',
    headerColor: colors.warning,
    activityIds: ['love-languages', 'date-night-plan', 'chat-prompts', 'appreciation'],
    stepTitles: ['Love Languages', 'Date Night Plan', 'Chat Prompts', 'Appreciation'],
    pointsMultiplier: 1,
    requiresPreviousCompletion: false,
  },
  {
    weekNumber: 5,
    title: 'Dream Together',
    description: 'Exploring your shared future',
    headerColor: colors.info,
    activityIds: ['dream-vacation', 'date-night-plan', 'chat-prompts', 'conflict-style'],
    stepTitles: ['Dream Vacation', 'Date Night Plan', 'Chat Prompts', 'Conflict Style'],
    pointsMultiplier: 1,
    requiresPreviousCompletion: false,
  },
  {
    weekNumber: 6,
    title: 'Sharing Memories',
    description: 'Create a memory lane together',
    headerColor: colors.error,
    activityIds: ['memory-sharing', 'memory-lane-date', 'chat-prompts', 'validation-toolkit'],
    stepTitles: ['Sharing Memories', 'Memory Lane Date', 'Chat Prompts', 'Validation Toolkit'],
    pointsMultiplier: 1,
    requiresPreviousCompletion: false,
  },
  {
    weekNumber: 7,
    title: 'Superhero Duo',
    description: 'Discover your combined powers',
    headerColor: colors.purple,
    activityIds: ['superhero-chart', 'thrift-showdown', 'chat-prompts', 'turning-toward'],
    stepTitles: ['Superhero Duo Chart', 'Thrift Shop Showdown', 'Chat Prompts', 'Turning Toward'],
    pointsMultiplier: 1,
    requiresPreviousCompletion: false,
  },
  {
    weekNumber: 8,
    title: 'Perfect Saturday',
    description: 'Design your ideal day together',
    headerColor: colors.success,
    activityIds: ['perfect-saturday', 'blended-day', 'chat-prompts', 'conflict-mapping'],
    stepTitles: ['Perfect Saturday Game', 'Blended Perfect Day', 'Chat Prompts', 'Conflict Mapping'],
    pointsMultiplier: 1,
    requiresPreviousCompletion: false,
  },
  {
    weekNumber: 9,
    title: 'Relationship Rituals',
    description: 'Building meaningful traditions',
    headerColor: colors.primary,
    activityIds: ['ritual-design', 'ritual-date', 'chat-prompts', 'stress-reducing'],
    stepTitles: ['Ritual Design', 'Ritual Date Night', 'Chat Prompts', 'Stress-Reducing'],
    pointsMultiplier: 1,
    requiresPreviousCompletion: false,
  },
  {
    weekNumber: 10,
    title: 'Adventure Planning',
    description: 'Plan exciting experiences together',
    headerColor: colors.warning,
    activityIds: ['adventure-bucket', 'adventure-date', 'chat-prompts', 'repair-attempts'],
    stepTitles: ['Adventure Bucket List', 'Adventure Date', 'Chat Prompts', 'Repair Attempts'],
    pointsMultiplier: 1,
    requiresPreviousCompletion: false,
  },
  {
    weekNumber: 11,
    title: 'Gratitude & Growth',
    description: 'Appreciating your journey together',
    headerColor: colors.success,
    activityIds: ['gratitude-jar', 'growth-date', 'chat-prompts', 'meaning-making'],
    stepTitles: ['Gratitude Jar', 'Growth Date Night', 'Chat Prompts', 'Meaning Making'],
    pointsMultiplier: 1,
    requiresPreviousCompletion: false,
  },
  {
    weekNumber: 12,
    title: 'Playful Tales',
    description: 'Create stories together and explore intimate connection',
    headerColor: colors.primary,
    activityIds: ['build-story', 'active-date', 'chat-prompts', 'sensate-focus'],
    stepTitles: ['Build-a-Story Game', 'Get Active Date', 'Chat Prompts', 'Sensate Focus'],
    pointsMultiplier: 1.2, // Bonus points for final week
    requiresPreviousCompletion: false,
  },
];

/**
 * Get configuration for a specific week
 */
export function getWeekConfig(weekNumber: number): WeekConfig | undefined {
  return weekConfigurations.find(config => config.weekNumber === weekNumber);
}

/**
 * Get all week configurations
 */
export function getAllWeekConfigs(): WeekConfig[] {
  return [...weekConfigurations];
}

/**
 * Validate that all activities referenced in week configs exist
 */
export function validateWeekConfigurations(activityRegistry: any): {
  valid: boolean;
  errors: string[];
} {
  const errors: string[] = [];

  for (const weekConfig of weekConfigurations) {
    for (const activityId of weekConfig.activityIds) {
      if (!activityRegistry.hasActivity(activityId)) {
        errors.push(`Week ${weekConfig.weekNumber}: Activity "${activityId}" not found in registry`);
      }
    }

    // Validate step titles match activity count
    if (weekConfig.stepTitles && weekConfig.stepTitles.length !== weekConfig.activityIds.length) {
      errors.push(`Week ${weekConfig.weekNumber}: Step titles count (${weekConfig.stepTitles.length}) doesn't match activities count (${weekConfig.activityIds.length})`);
    }
  }

  return {
    valid: errors.length === 0,
    errors,
  };
}

/**
 * Get weeks that are unlocked based on completion status
 */
export function getUnlockedWeeks(completedWeeks: number[]): WeekConfig[] {
  return weekConfigurations.filter(config => {
    if (!config.requiresPreviousCompletion) {
      return true;
    }

    // Check if previous week is completed
    const previousWeek = config.weekNumber - 1;
    return previousWeek === 0 || completedWeeks.includes(previousWeek);
  });
}

/**
 * Get next recommended week based on completion
 */
export function getNextRecommendedWeek(completedWeeks: number[]): WeekConfig | undefined {
  const unlockedWeeks = getUnlockedWeeks(completedWeeks);
  return unlockedWeeks.find(config => !completedWeeks.includes(config.weekNumber));
}

/**
 * Calculate total possible points across all weeks
 */
export function getTotalPossiblePoints(activityRegistry: any): number {
  let total = 0;

  for (const weekConfig of weekConfigurations) {
    let weekPoints = 0;

    for (const activityId of weekConfig.activityIds) {
      const activity = activityRegistry.getActivity(activityId);
      if (activity) {
        weekPoints += activity.defaultPoints;
      }
    }

    total += weekPoints * (weekConfig.pointsMultiplier || 1);
  }

  return Math.round(total);
}
