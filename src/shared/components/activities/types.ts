/**
 * Activity System Types
 *
 * Standardized interfaces for the modular activity system.
 * All activity components must implement these interfaces.
 */

import { ReactNode } from 'react';

/**
 * Base interface that all activities must implement
 */
export interface BaseActivityProps {
  /** Called when activity is completed successfully */
  onComplete: (_data: ActivityCompletionData) => void;
  /** Called when an error occurs in the activity */
  onError: (_error: Error) => void;
  /** Initial data to populate the activity */
  initialData?: any;
  /** Whether this activity is currently active/visible */
  isActive: boolean;
  /** Week number this activity is being used in */
  weekNumber: number;
  /** Step index within the week */
  stepIndex: number;
}

/**
 * Data returned when an activity is completed
 */
export interface ActivityCompletionData {
  /** Unique identifier for the activity type */
  activityType: string;
  /** Points earned from completing this activity */
  points: number;
  /** Any data collected during the activity */
  data: Record<string, any>;
  /** Timestamp when completed */
  completedAt: number;
  /** Whether this completion should trigger navigation */
  shouldNavigate?: boolean;
}

/**
 * Configuration for registering an activity
 */
export interface ActivityConfig {
  /** Unique identifier for this activity */
  id: string;
  /** Display name for the activity */
  title: string;
  /** Brief description of what the activity does */
  description: string;
  /** Icon to display for this activity */
  icon: ReactNode;
  /** Default points awarded for completion */
  defaultPoints: number;
  /** Category this activity belongs to */
  category: ActivityCategory;
  /** Estimated duration in minutes */
  estimatedDuration: number;
  /** React component that renders this activity */
  component: React.ComponentType<BaseActivityProps>;
  /** Whether this activity requires partner interaction */
  requiresPartner?: boolean;
  /** Minimum data required to start this activity */
  requiredData?: string[];
}

/**
 * Categories for organizing activities
 */
export type ActivityCategory =
  | 'games'
  | 'planning'
  | 'communication'
  | 'reflection'
  | 'creative'
  | 'physical';

/**
 * Week configuration that defines which activities to show
 */
export interface WeekConfig {
  /** Week number (1-12) */
  weekNumber: number;
  /** Display title for the week */
  title: string;
  /** Brief description of the week's theme */
  description: string;
  /** Background color for the week header */
  headerColor: string;
  /** Array of activity IDs to include in this week */
  activityIds: string[];
  /** Custom step titles (optional - will use activity titles if not provided) */
  stepTitles?: string[];
  /** Points multiplier for this week (default: 1) */
  pointsMultiplier?: number;
  /** Whether this week is locked until previous weeks are complete */
  requiresPreviousCompletion?: boolean;
}

/**
 * Registry for managing all available activities
 */
export interface ActivityRegistry {
  /** Register a new activity */
  register: (_config: ActivityConfig) => void;
  /** Get activity configuration by ID */
  getActivity: (_id: string) => ActivityConfig | undefined;
  /** Get all activities in a category */
  getActivitiesByCategory: (_category: ActivityCategory) => ActivityConfig[];
  /** Get all registered activities */
  getAllActivities: () => ActivityConfig[];
  /** Check if an activity is registered */
  hasActivity: (_id: string) => boolean;
}

/**
 * Props for the enhanced WeekScreenLayout
 */
export interface EnhancedWeekScreenLayoutProps {
  /** Week configuration */
  weekConfig: WeekConfig;
  /** Current step index */
  currentStep: number;
  /** Function to update current step */
  setCurrentStep: (_step: number) => void;
  /** Completion status for each step */
  completedSections: boolean[];
  /** Function to handle activity completion */
  onActivityComplete: (_stepIndex: number, _data: ActivityCompletionData) => Promise<void>;
  /** Function to handle errors */
  onError: (_error: Error) => void;
  /** Function to handle navigation back */
  onNavigateBack: () => void;
  /** Initial data for activities */
  initialData?: Record<string, any>;
}
