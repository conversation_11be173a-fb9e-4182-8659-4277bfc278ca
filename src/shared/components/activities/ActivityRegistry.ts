/**
 * Activity Registry
 * 
 * Central registry for managing all available activities.
 * Provides registration, lookup, and organization of activity components.
 */

import { ActivityConfig, ActivityRegistry, ActivityCategory } from './types';

/**
 * Implementation of the activity registry
 */
class ActivityRegistryImpl implements ActivityRegistry {
  private activities: Map<string, ActivityConfig> = new Map();

  /**
   * Register a new activity in the registry
   */
  register(config: ActivityConfig): void {
    if (this.activities.has(config.id)) {
      console.warn(`Activity with ID "${config.id}" is already registered. Overwriting.`);
    }
    
    this.activities.set(config.id, config);
  }

  /**
   * Get activity configuration by ID
   */
  getActivity(id: string): ActivityConfig | undefined {
    return this.activities.get(id);
  }

  /**
   * Get all activities in a specific category
   */
  getActivitiesByCategory(category: ActivityCategory): ActivityConfig[] {
    return Array.from(this.activities.values()).filter(
      activity => activity.category === category
    );
  }

  /**
   * Get all registered activities
   */
  getAllActivities(): ActivityConfig[] {
    return Array.from(this.activities.values());
  }

  /**
   * Check if an activity is registered
   */
  hasActivity(id: string): boolean {
    return this.activities.has(id);
  }

  /**
   * Unregister an activity (useful for testing)
   */
  unregister(id: string): boolean {
    return this.activities.delete(id);
  }

  /**
   * Clear all registered activities (useful for testing)
   */
  clear(): void {
    this.activities.clear();
  }

  /**
   * Get activity count
   */
  getCount(): number {
    return this.activities.size;
  }

  /**
   * Get activities that require partner interaction
   */
  getPartnerActivities(): ActivityConfig[] {
    return Array.from(this.activities.values()).filter(
      activity => activity.requiresPartner === true
    );
  }

  /**
   * Get activities by estimated duration range
   */
  getActivitiesByDuration(minMinutes: number, maxMinutes: number): ActivityConfig[] {
    return Array.from(this.activities.values()).filter(
      activity => activity.estimatedDuration >= minMinutes && activity.estimatedDuration <= maxMinutes
    );
  }

  /**
   * Validate that all required activities exist for a week configuration
   */
  validateWeekActivities(activityIds: string[]): { valid: boolean; missing: string[] } {
    const missing: string[] = [];
    
    for (const id of activityIds) {
      if (!this.hasActivity(id)) {
        missing.push(id);
      }
    }

    return {
      valid: missing.length === 0,
      missing
    };
  }
}

// Create singleton instance
export const activityRegistry = new ActivityRegistryImpl();

/**
 * Decorator function to register activities
 * Usage: @registerActivity(config)
 */
export function registerActivity(config: Omit<ActivityConfig, 'component'>) {
  return function <T extends React.ComponentType<any>>(component: T): T {
    activityRegistry.register({
      ...config,
      component: component as React.ComponentType<any>
    });
    return component;
  };
}

/**
 * Hook to get activity registry (for React components)
 */
export function useActivityRegistry(): ActivityRegistry {
  return activityRegistry;
}

/**
 * Utility function to bulk register activities
 */
export function registerActivities(configs: ActivityConfig[]): void {
  configs.forEach(config => activityRegistry.register(config));
}

/**
 * Get activity statistics
 */
export function getActivityStats() {
  const activities = activityRegistry.getAllActivities();
  const categories = activities.reduce((acc, activity) => {
    acc[activity.category] = (acc[activity.category] || 0) + 1;
    return acc;
  }, {} as Record<ActivityCategory, number>);

  const avgDuration = activities.reduce((sum, activity) => sum + activity.estimatedDuration, 0) / activities.length;
  const partnerActivities = activities.filter(a => a.requiresPartner).length;

  return {
    total: activities.length,
    categories,
    averageDuration: Math.round(avgDuration),
    partnerActivities,
    soloActivities: activities.length - partnerActivities
  };
}
