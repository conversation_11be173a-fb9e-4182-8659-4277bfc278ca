/**
 * Activity Components Index
 *
 * Central export point for all activity components and configurations.
 */

// Types
export * from './types';

// Registry
export * from './ActivityRegistry';

// Week Configurations
export * from './WeekConfigurations';

// Activity Components
export { ChatPromptsActivity, chatPromptsConfig } from './ChatPromptsActivity';
export { DateNightPlanActivity, dateNightPlanConfig } from './DateNightPlanActivity';
export { MatchGameActivity, matchGameConfig } from './MatchGameActivity';
export { SoftStartupActivity, softStartupConfig } from './SoftStartupActivity';
export { WouldYouRatherActivity, wouldYouRatherConfig } from './WouldYouRatherActivity';

// Auto-register all activities
import { activityRegistry } from './ActivityRegistry';
import { chatPromptsConfig } from './ChatPromptsActivity';
import { dateNightPlanConfig } from './DateNightPlanActivity';
import { matchGameConfig } from './MatchGameActivity';
import { softStartupConfig } from './SoftStartupActivity';
import { wouldYouRatherConfig } from './WouldYouRatherActivity';

// Register all activities on module load
activityRegistry.register(matchGameConfig);
activityRegistry.register(dateNightPlanConfig);
activityRegistry.register(chatPromptsConfig);
activityRegistry.register(softStartupConfig);
activityRegistry.register(wouldYouRatherConfig);

// Export registry for external use
export { activityRegistry };
