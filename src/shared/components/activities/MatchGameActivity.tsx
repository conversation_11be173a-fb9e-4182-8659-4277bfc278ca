/**
 * Match Game Activity Component
 *
 * Interactive compatibility game where partners answer questions
 * and try to match each other's responses.
 */

import { router } from 'expo-router';
import { Play, Users } from 'lucide-react-native';
import React, { useCallback, useState } from 'react';
import {
    StyleSheet,
    Text,
    TouchableOpacity,
    View
} from 'react-native';

import { DSBadge } from '../../../components/shared';
import { colors } from '../../utils/colors';
import { ActivityCompletionData, BaseActivityProps } from './types';

/**
 * Match Game Activity Component
 */
export const MatchGameActivity: React.FC<BaseActivityProps> = ({
  onComplete,
  onError,
  initialData,
  isActive,
  weekNumber,
  stepIndex,
}) => {
  const [isStarting, setIsStarting] = useState(false);

  const handleStartActivity = useCallback(async () => {
    try {
      setIsStarting(true);

      // Navigate to the relationship activities module
      // This integrates with the existing game system
      router.push('/(tabs)/activities');

      // Complete the activity with navigation flag
      const completionData: ActivityCompletionData = {
        activityType: 'match-game',
        points: 25,
        data: {
          startedAt: Date.now(),
          weekNumber,
          stepIndex,
        },
        completedAt: Date.now(),
        shouldNavigate: true,
      };

      onComplete(completionData);
    } catch (error) {
      onError(error instanceof Error ? error : new Error('Failed to start match game'));
    } finally {
      setIsStarting(false);
    }
  }, [onComplete, onError, weekNumber, stepIndex]);

  if (!isActive) {
    return null;
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>The Match Game</Text>
        <Text style={styles.subtitle}>How well do you know each other?</Text>
      </View>

      <View style={styles.instructions}>
        <Text style={styles.instructionText}>
          Both partners answer independently, then reveal together!
        </Text>
        <Text style={styles.instructionText}>
          One partner writes their answer, the other guesses what they said
        </Text>
      </View>

      <TouchableOpacity
        style={[styles.startButton, isStarting && styles.startButtonDisabled]}
        onPress={handleStartActivity}
        disabled={isStarting}
      >
        <View style={styles.startButtonContent}>
          <Play size={20} color={colors.white} />
          <Text style={styles.startButtonText}>
            {isStarting ? 'Starting...' : 'Start Match Game'}
          </Text>
        </View>
      </TouchableOpacity>

      {initialData?.completed && (
        <DSBadge
          variant="success"
          style={styles.completedBadge}
        >
          Completed
        </DSBadge>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.white,
    borderRadius: 16,
    padding: 20,
    marginHorizontal: 20,
    marginVertical: 10,
    shadowColor: colors.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  header: {
    marginBottom: 16,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: colors.textPrimary,
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: colors.textSecondary,
    lineHeight: 22,
  },
  instructions: {
    backgroundColor: colors.backgroundSecondary,
    borderRadius: 12,
    padding: 16,
    marginBottom: 20,
  },
  instructionText: {
    fontSize: 14,
    color: colors.textSecondary,
    lineHeight: 20,
    marginBottom: 8,
  },
  startButton: {
    backgroundColor: colors.primary,
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },
  startButtonDisabled: {
    opacity: 0.6,
  },
  startButtonContent: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  startButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.white,
  },
  completedBadge: {
    alignSelf: 'center',
    marginTop: 12,
  },
});

// Register this activity
export const matchGameConfig = {
  id: 'match-game',
  title: 'The Match Game',
  description: 'Test your compatibility with fun questions',
  icon: <Users size={24} color={colors.white} />,
  defaultPoints: 25,
  category: 'games' as const,
  estimatedDuration: 15,
  requiresPartner: true,
  component: MatchGameActivity,
};
