/**
 * Soft Startup Activity Component
 *
 * Teaches couples how to approach difficult conversations
 * with gentleness and understanding.
 */

import { <PERSON><PERSON><PERSON>, CheckCircle, Heart } from 'lucide-react-native';
import React, { useCallback, useState } from 'react';
import {
    ScrollView,
    StyleSheet,
    Text,
    TextInput,
    TouchableOpacity,
    View,
} from 'react-native';

import { DSBadge } from '../../../components/shared';
import { colors } from '../../utils/colors';
import { ActivityCompletionData, BaseActivityProps } from './types';

interface SoftStartupData {
  selectedScenario?: number;
  practiceResponse?: string;
  reflection?: string;
  completed?: boolean;
}

const scenarios = [
  {
    title: "Household Chores",
    situation: "You feel like you're doing more than your fair share of housework.",
    harshStart: "You never help with anything around here!",
    softStart: "I've been feeling overwhelmed with the housework lately. Could we talk about how to share it more evenly?"
  },
  {
    title: "Quality Time",
    situation: "You feel like your partner is always on their phone when you're together.",
    harshStart: "You care more about your phone than me!",
    softStart: "I really value our time together. Could we try putting our phones away during dinner?"
  },
  {
    title: "Financial Decisions",
    situation: "Your partner made a large purchase without discussing it with you.",
    harshStart: "You're so irresponsible with money!",
    softStart: "I felt surprised about the purchase. Can we talk about how we make financial decisions together?"
  },
];

/**
 * Soft Startup Activity Component
 */
export const SoftStartupActivity: React.FC<BaseActivityProps> = ({
  onComplete,
  onError,
  initialData,
  isActive,
  weekNumber,
  stepIndex,
}) => {
  const [activityData, setActivityData] = useState<SoftStartupData>(
    initialData?.softStartup || {}
  );
  const [isCompleting, setIsCompleting] = useState(false);

  const handleScenarioSelect = useCallback((scenarioIndex: number) => {
    setActivityData(prev => ({
      ...prev,
      selectedScenario: scenarioIndex,
    }));
  }, []);

  const handlePracticeResponseChange = useCallback((response: string) => {
    setActivityData(prev => ({
      ...prev,
      practiceResponse: response,
    }));
  }, []);

  const handleReflectionChange = useCallback((reflection: string) => {
    setActivityData(prev => ({
      ...prev,
      reflection: reflection,
    }));
  }, []);

  const handleComplete = useCallback(async () => {
    try {
      setIsCompleting(true);

      if (activityData.selectedScenario === undefined) {
        throw new Error('Please select a scenario to practice');
      }

      if (!activityData.practiceResponse?.trim()) {
        throw new Error('Please write your soft startup response');
      }

      const completionData: ActivityCompletionData = {
        activityType: 'soft-startup',
        points: 20,
        data: {
          ...activityData,
          weekNumber,
          stepIndex,
          scenarioTitle: scenarios[activityData.selectedScenario].title,
        },
        completedAt: Date.now(),
      };

      onComplete(completionData);
    } catch (error) {
      onError(error instanceof Error ? error : new Error('Failed to complete soft startup practice'));
    } finally {
      setIsCompleting(false);
    }
  }, [activityData, onComplete, onError, weekNumber, stepIndex]);

  if (!isActive) {
    return null;
  }

  const selectedScenario = activityData.selectedScenario !== undefined
    ? scenarios[activityData.selectedScenario]
    : null;

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      <View style={styles.header}>
        <Text style={styles.title}>Soft Startup Practice</Text>
        <Text style={styles.subtitle}>Learn to approach difficult conversations gently</Text>
      </View>

      <View style={styles.infoCard}>
        <View style={styles.infoHeader}>
          <BookOpen size={20} color={colors.primary} />
          <Text style={styles.infoTitle}>What is a Soft Startup?</Text>
        </View>
        <Text style={styles.infoText}>
          A soft startup begins a conversation without blame or criticism.
          It focuses on your feelings and needs rather than your partner's faults.
        </Text>
      </View>

      <View style={styles.scenarioSection}>
        <Text style={styles.sectionTitle}>Choose a Scenario to Practice</Text>

        {scenarios.map((scenario, index) => (
          <TouchableOpacity
            key={index}
            style={[
              styles.scenarioCard,
              activityData.selectedScenario === index && styles.scenarioCardSelected
            ]}
            onPress={() => handleScenarioSelect(index)}
          >
            <View style={styles.scenarioHeader}>
              <Text style={styles.scenarioTitle}>{scenario.title}</Text>
              {activityData.selectedScenario === index && (
                <CheckCircle size={20} color={colors.primary} />
              )}
            </View>
            <Text style={styles.scenarioSituation}>{scenario.situation}</Text>
          </TouchableOpacity>
        ))}
      </View>

      {selectedScenario && (
        <View style={styles.practiceSection}>
          <Text style={styles.sectionTitle}>Practice Your Soft Startup</Text>

          <View style={styles.exampleCard}>
            <Text style={styles.exampleLabel}>❌ Harsh Startup:</Text>
            <Text style={styles.harshExample}>"{selectedScenario.harshStart}"</Text>

            <Text style={styles.exampleLabel}>✅ Soft Startup:</Text>
            <Text style={styles.softExample}>"{selectedScenario.softStart}"</Text>
          </View>

          <Text style={styles.practiceLabel}>Now write your own soft startup:</Text>
          <TextInput
            style={styles.practiceInput}
            placeholder="I feel... because... Could we..."
            value={activityData.practiceResponse || ''}
            onChangeText={handlePracticeResponseChange}
            multiline
            numberOfLines={4}
            textAlignVertical="top"
          />

          <Text style={styles.reflectionLabel}>Reflection (Optional):</Text>
          <TextInput
            style={styles.reflectionInput}
            placeholder="How did it feel to practice this approach?"
            value={activityData.reflection || ''}
            onChangeText={handleReflectionChange}
            multiline
            numberOfLines={3}
            textAlignVertical="top"
          />
        </View>
      )}

      {activityData.selectedScenario !== undefined && activityData.practiceResponse?.trim() && (
        <TouchableOpacity
          style={[styles.completeButton, isCompleting && styles.completeButtonDisabled]}
          onPress={handleComplete}
          disabled={isCompleting}
        >
          <Heart size={20} color={colors.white} />
          <Text style={styles.completeButtonText}>
            {isCompleting ? 'Saving Practice...' : 'Complete Soft Startup Practice'}
          </Text>
        </TouchableOpacity>
      )}

      {initialData?.completed && (
        <DSBadge
          variant="success"
          style={styles.completedBadge}
        >
          Practice Complete
        </DSBadge>
      )}
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    marginBottom: 20,
    paddingHorizontal: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: colors.textPrimary,
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: colors.textSecondary,
  },
  infoCard: {
    backgroundColor: colors.backgroundSecondary,
    borderRadius: 12,
    padding: 16,
    marginHorizontal: 20,
    marginBottom: 20,
  },
  infoHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginBottom: 8,
  },
  infoTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.textPrimary,
  },
  infoText: {
    fontSize: 14,
    color: colors.textSecondary,
    lineHeight: 20,
  },
  scenarioSection: {
    paddingHorizontal: 20,
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.textPrimary,
    marginBottom: 16,
  },
  scenarioCard: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    borderWidth: 2,
    borderColor: colors.border,
  },
  scenarioCardSelected: {
    borderColor: colors.primary,
    backgroundColor: colors.backgroundSecondary,
  },
  scenarioHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  scenarioTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.textPrimary,
  },
  scenarioSituation: {
    fontSize: 14,
    color: colors.textSecondary,
    lineHeight: 20,
  },
  practiceSection: {
    paddingHorizontal: 20,
    marginBottom: 20,
  },
  exampleCard: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
  },
  exampleLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.textPrimary,
    marginBottom: 4,
    marginTop: 8,
  },
  harshExample: {
    fontSize: 14,
    color: colors.error,
    fontStyle: 'italic',
    marginBottom: 8,
  },
  softExample: {
    fontSize: 14,
    color: colors.success,
    fontStyle: 'italic',
  },
  practiceLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.textPrimary,
    marginBottom: 8,
  },
  practiceInput: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    color: colors.textPrimary,
    borderWidth: 1,
    borderColor: colors.border,
    minHeight: 100,
    marginBottom: 16,
  },
  reflectionLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.textPrimary,
    marginBottom: 8,
  },
  reflectionInput: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
    fontSize: 14,
    color: colors.textPrimary,
    borderWidth: 1,
    borderColor: colors.border,
    minHeight: 80,
  },
  completeButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 8,
    backgroundColor: colors.success,
    borderRadius: 12,
    padding: 16,
    marginHorizontal: 20,
    marginBottom: 20,
  },
  completeButtonDisabled: {
    opacity: 0.6,
  },
  completeButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.white,
  },
  completedBadge: {
    alignSelf: 'center',
    marginBottom: 20,
  },
});

// Register this activity
export const softStartupConfig = {
  id: 'soft-startup',
  title: 'Soft Startup',
  description: 'Practice gentle conversation starters',
  icon: <Heart size={24} color={colors.white} />,
  defaultPoints: 20,
  category: 'communication' as const,
  estimatedDuration: 15,
  requiresPartner: false,
  component: SoftStartupActivity,
};
