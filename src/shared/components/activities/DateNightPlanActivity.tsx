/**
 * Date Night Plan Activity Component
 *
 * Provides curated date night ideas and planning suggestions
 * for couples to enjoy together.
 */

import { Calendar, CheckCircle, Heart } from 'lucide-react-native';
import React, { useCallback, useState } from 'react';
import {
    ScrollView,
    StyleSheet,
    Text,
    TextInput,
    TouchableOpacity,
    View,
} from 'react-native';

import { DSBadge } from '../../../components/shared';
import { colors } from '../../utils/colors';
import { ActivityCompletionData, BaseActivityProps } from './types';

interface DateNightPlanData {
  selectedPlan?: string;
  customNotes?: string;
  scheduledDate?: string;
  completed?: boolean;
}

/**
 * Date Night Plan Activity Component
 */
export const DateNightPlanActivity: React.FC<BaseActivityProps> = ({
  onComplete,
  onError,
  initialData,
  isActive,
  weekNumber,
  stepIndex,
}) => {
  const [planData, setPlanData] = useState<DateNightPlanData>(
    initialData?.dateNightPlan || {}
  );
  const [isCompleting, setIsCompleting] = useState(false);

  // Default plan based on week number - can be customized per week
  const getDefaultPlan = useCallback(() => {
    const plans = {
      1: {
        title: 'Game Night – The Bet Is On',
        description: 'Play your favorite board game with a twist: loser of each round makes the winner a drink, grabs a snack, or takes on a playful dare.',
      },
      2: {
        title: 'Strengths Celebration Dinner',
        description: 'Cook a meal together while sharing what you appreciate most about each other\'s unique strengths.',
      },
      5: {
        title: 'Dream Vacation Planning',
        description: 'Research and plan your dream vacation together, creating a vision board of your ideal getaway.',
      },
      // Add more week-specific plans as needed
    };

    return plans[weekNumber as keyof typeof plans] || {
      title: 'Custom Date Night',
      description: 'Plan a special evening together doing something you both enjoy.',
    };
  }, [weekNumber]);

  const defaultPlan = getDefaultPlan();

  const handlePlanSelection = useCallback((planType: 'default' | 'custom') => {
    setPlanData(prev => ({
      ...prev,
      selectedPlan: planType,
    }));
  }, []);

  const handleNotesChange = useCallback((notes: string) => {
    setPlanData(prev => ({
      ...prev,
      customNotes: notes,
    }));
  }, []);

  const handleComplete = useCallback(async () => {
    try {
      setIsCompleting(true);

      if (!planData.selectedPlan) {
        throw new Error('Please select a date night plan');
      }

      const completionData: ActivityCompletionData = {
        activityType: 'date-night-plan',
        points: 20,
        data: {
          ...planData,
          weekNumber,
          stepIndex,
          planTitle: defaultPlan.title,
        },
        completedAt: Date.now(),
      };

      onComplete(completionData);
    } catch (error) {
      onError(error instanceof Error ? error : new Error('Failed to complete date night planning'));
    } finally {
      setIsCompleting(false);
    }
  }, [planData, onComplete, onError, weekNumber, stepIndex, defaultPlan.title]);

  if (!isActive) {
    return null;
  }

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      <View style={styles.header}>
        <Text style={styles.title}>Date Night Plan</Text>
        <Text style={styles.subtitle}>{defaultPlan.title}</Text>
      </View>

      <View style={styles.planCard}>
        <Text style={styles.planDescription}>
          {defaultPlan.description}
        </Text>

        <TouchableOpacity
          style={[
            styles.planOption,
            planData.selectedPlan === 'default' && styles.planOptionSelected
          ]}
          onPress={() => handlePlanSelection('default')}
        >
          <View style={styles.planOptionContent}>
            <Calendar size={20} color={planData.selectedPlan === 'default' ? colors.white : colors.primary} />
            <Text style={[
              styles.planOptionText,
              planData.selectedPlan === 'default' && styles.planOptionTextSelected
            ]}>
              Use This Plan
            </Text>
          </View>
          {planData.selectedPlan === 'default' && (
            <CheckCircle size={20} color={colors.white} />
          )}
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.planOption,
            planData.selectedPlan === 'custom' && styles.planOptionSelected
          ]}
          onPress={() => handlePlanSelection('custom')}
        >
          <View style={styles.planOptionContent}>
            <Heart size={20} color={planData.selectedPlan === 'custom' ? colors.white : colors.primary} />
            <Text style={[
              styles.planOptionText,
              planData.selectedPlan === 'custom' && styles.planOptionTextSelected
            ]}>
              Create Custom Plan
            </Text>
          </View>
          {planData.selectedPlan === 'custom' && (
            <CheckCircle size={20} color={colors.white} />
          )}
        </TouchableOpacity>
      </View>

      {planData.selectedPlan && (
        <View style={styles.notesSection}>
          <Text style={styles.notesLabel}>Additional Notes (Optional)</Text>
          <TextInput
            style={styles.notesInput}
            placeholder="Add any special touches, timing, or preferences..."
            value={planData.customNotes || ''}
            onChangeText={handleNotesChange}
            multiline
            numberOfLines={3}
            textAlignVertical="top"
          />
        </View>
      )}

      {planData.selectedPlan && (
        <TouchableOpacity
          style={[styles.completeButton, isCompleting && styles.completeButtonDisabled]}
          onPress={handleComplete}
          disabled={isCompleting}
        >
          <Text style={styles.completeButtonText}>
            {isCompleting ? 'Saving Plan...' : 'Save Date Night Plan'}
          </Text>
        </TouchableOpacity>
      )}

      {initialData?.completed && (
        <DSBadge
          variant="success"
          style={styles.completedBadge}
        >
          Plan Saved
        </DSBadge>
      )}
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    marginBottom: 20,
    paddingHorizontal: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: colors.textPrimary,
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 18,
    color: colors.textSecondary,
    fontWeight: '600',
  },
  planCard: {
    backgroundColor: colors.white,
    borderRadius: 16,
    padding: 20,
    marginHorizontal: 20,
    marginBottom: 20,
    shadowColor: colors.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  planDescription: {
    fontSize: 16,
    color: colors.textSecondary,
    lineHeight: 24,
    marginBottom: 20,
  },
  planOption: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: colors.primary,
    marginBottom: 12,
  },
  planOptionSelected: {
    backgroundColor: colors.primary,
    borderColor: colors.primary,
  },
  planOptionContent: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  planOptionText: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.primary,
  },
  planOptionTextSelected: {
    color: colors.white,
  },
  notesSection: {
    paddingHorizontal: 20,
    marginBottom: 20,
  },
  notesLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.textPrimary,
    marginBottom: 8,
  },
  notesInput: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    color: colors.textPrimary,
    borderWidth: 1,
    borderColor: colors.border,
    minHeight: 80,
  },
  completeButton: {
    backgroundColor: colors.success,
    borderRadius: 12,
    padding: 16,
    marginHorizontal: 20,
    marginBottom: 20,
    alignItems: 'center',
  },
  completeButtonDisabled: {
    opacity: 0.6,
  },
  completeButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.white,
  },
  completedBadge: {
    alignSelf: 'center',
    marginBottom: 20,
  },
});

// Register this activity
export const dateNightPlanConfig = {
  id: 'date-night-plan',
  title: 'Date Night Plan',
  description: 'Plan a special evening together',
  icon: <Calendar size={24} color={colors.white} />,
  defaultPoints: 20,
  category: 'planning' as const,
  estimatedDuration: 10,
  requiresPartner: true,
  component: DateNightPlanActivity,
};
