/**
 * Chat Prompts Activity Component
 *
 * Provides conversation starters and deep questions
 * for meaningful discussions between partners.
 */

import { ArrowLeft, ArrowRight, CheckCircle, MessageCircle } from 'lucide-react-native';
import React, { useCallback, useMemo, useState } from 'react';
import {
    ScrollView,
    StyleSheet,
    Text,
    TextInput,
    TouchableOpacity,
    View,
} from 'react-native';

import { DSBadge } from '../../../components/shared';
import { colors } from '../../utils/colors';
import { ActivityCompletionData, BaseActivityProps } from './types';

interface ChatPromptData {
  currentPromptIndex: number;
  responses: Record<number, { partner1?: string; partner2?: string }>;
  completed: boolean;
}

/**
 * Chat Prompts Activity Component
 */
export const ChatPromptsActivity: React.FC<BaseActivityProps> = ({
  onComplete,
  onError,
  initialData,
  isActive,
  weekNumber,
  stepIndex,
}) => {
  const [promptData, setPromptData] = useState<ChatPromptData>(
    initialData?.chatPrompts || {
      currentPromptIndex: 0,
      responses: {},
      completed: false,
    }
  );
  const [currentResponse, setCurrentResponse] = useState('');
  const [isCompleting, setIsCompleting] = useState(false);

  // Week-specific prompts - can be customized per week
  const prompts = useMemo(() => {
    const basePrompts = [
      "What's one thing you've learned about yourself this week?",
      "If you could relive one moment from our relationship, what would it be?",
      "What's something you're looking forward to doing together?",
      "How do you feel most loved and appreciated?",
      "What's a goal we could work on together?",
    ];

    // Week-specific prompts can be added here
    const weekSpecificPrompts = {
      1: [
        "What first attracted you to me?",
        "What's your favorite memory of us from our early days?",
        "How do you think we complement each other?",
      ],
      2: [
        "What strength of mine do you admire most?",
        "How can we better support each other's goals?",
        "What's something positive you've noticed about us lately?",
      ],
      5: [
        "Where would you love to travel together and why?",
        "What's your ideal way to spend a weekend together?",
        "How do you envision our future together?",
      ],
    };

    return weekSpecificPrompts[weekNumber as keyof typeof weekSpecificPrompts] || basePrompts;
  }, [weekNumber]);

  const currentPrompt = prompts[promptData.currentPromptIndex];
  const isLastPrompt = promptData.currentPromptIndex === prompts.length - 1;
  const hasResponse = currentResponse.trim().length > 0;

  const handleSaveResponse = useCallback(() => {
    if (!hasResponse) return;

    setPromptData(prev => ({
      ...prev,
      responses: {
        ...prev.responses,
        [prev.currentPromptIndex]: {
          ...prev.responses[prev.currentPromptIndex],
          partner1: currentResponse, // In a real app, this would be dynamic based on current user
        },
      },
    }));

    setCurrentResponse('');
  }, [currentResponse, hasResponse]);

  const handleNextPrompt = useCallback(() => {
    if (promptData.currentPromptIndex < prompts.length - 1) {
      setPromptData(prev => ({
        ...prev,
        currentPromptIndex: prev.currentPromptIndex + 1,
      }));
    }
  }, [promptData.currentPromptIndex, prompts.length]);

  const handlePreviousPrompt = useCallback(() => {
    if (promptData.currentPromptIndex > 0) {
      setPromptData(prev => ({
        ...prev,
        currentPromptIndex: prev.currentPromptIndex - 1,
      }));
    }
  }, [promptData.currentPromptIndex]);

  const handleComplete = useCallback(async () => {
    try {
      setIsCompleting(true);

      const responseCount = Object.keys(promptData.responses).length;
      const completionPercentage = (responseCount / prompts.length) * 100;

      const completionData: ActivityCompletionData = {
        activityType: 'chat-prompts',
        points: Math.round(15 * (completionPercentage / 100)), // Points based on completion
        data: {
          ...promptData,
          weekNumber,
          stepIndex,
          responseCount,
          completionPercentage,
        },
        completedAt: Date.now(),
      };

      onComplete(completionData);
    } catch (error) {
      onError(error instanceof Error ? error : new Error('Failed to complete chat prompts'));
    } finally {
      setIsCompleting(false);
    }
  }, [promptData, prompts.length, onComplete, onError, weekNumber, stepIndex]);

  if (!isActive) {
    return null;
  }

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      <View style={styles.header}>
        <Text style={styles.title}>Date Night Chat Prompts</Text>
        <Text style={styles.subtitle}>Deep conversations over dinner</Text>
      </View>

      <View style={styles.promptCard}>
        <View style={styles.promptHeader}>
          <Text style={styles.promptCounter}>
            {promptData.currentPromptIndex + 1} of {prompts.length}
          </Text>
          <View style={styles.progressBar}>
            <View
              style={[
                styles.progressFill,
                { width: `${((promptData.currentPromptIndex + 1) / prompts.length) * 100}%` }
              ]}
            />
          </View>
        </View>

        <Text style={styles.promptText}>{currentPrompt}</Text>

        <TextInput
          style={styles.responseInput}
          placeholder="Share your thoughts..."
          value={currentResponse}
          onChangeText={setCurrentResponse}
          multiline
          numberOfLines={4}
          textAlignVertical="top"
        />

        <View style={styles.promptActions}>
          <TouchableOpacity
            style={[styles.actionButton, promptData.currentPromptIndex === 0 && styles.actionButtonDisabled]}
            onPress={handlePreviousPrompt}
            disabled={promptData.currentPromptIndex === 0}
          >
            <ArrowLeft size={20} color={promptData.currentPromptIndex === 0 ? colors.textTertiary : colors.primary} />
            <Text style={[
              styles.actionButtonText,
              promptData.currentPromptIndex === 0 && styles.actionButtonTextDisabled
            ]}>
              Previous
            </Text>
          </TouchableOpacity>

          {hasResponse && (
            <TouchableOpacity
              style={styles.saveButton}
              onPress={handleSaveResponse}
            >
              <CheckCircle size={20} color={colors.white} />
              <Text style={styles.saveButtonText}>Save</Text>
            </TouchableOpacity>
          )}

          <TouchableOpacity
            style={[styles.actionButton, isLastPrompt && styles.actionButtonDisabled]}
            onPress={handleNextPrompt}
            disabled={isLastPrompt}
          >
            <Text style={[
              styles.actionButtonText,
              isLastPrompt && styles.actionButtonTextDisabled
            ]}>
              Next
            </Text>
            <ArrowRight size={20} color={isLastPrompt ? colors.textTertiary : colors.primary} />
          </TouchableOpacity>
        </View>
      </View>

      <View style={styles.instructions}>
        <Text style={styles.instructionText}>
          💡 Take turns answering each prompt, then discuss your responses together
        </Text>
      </View>

      <TouchableOpacity
        style={[styles.completeButton, isCompleting && styles.completeButtonDisabled]}
        onPress={handleComplete}
        disabled={isCompleting}
      >
        <Text style={styles.completeButtonText}>
          {isCompleting ? 'Saving Responses...' : 'Complete Chat Prompts'}
        </Text>
      </TouchableOpacity>

      {promptData.completed && (
        <DSBadge
          variant="success"
          style={styles.completedBadge}
        >
          Completed
        </DSBadge>
      )}
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    marginBottom: 20,
    paddingHorizontal: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: colors.textPrimary,
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: colors.textSecondary,
  },
  promptCard: {
    backgroundColor: colors.white,
    borderRadius: 16,
    padding: 20,
    marginHorizontal: 20,
    marginBottom: 20,
    shadowColor: colors.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  promptHeader: {
    marginBottom: 20,
  },
  promptCounter: {
    fontSize: 14,
    color: colors.textSecondary,
    marginBottom: 8,
  },
  progressBar: {
    height: 4,
    backgroundColor: colors.backgroundSecondary,
    borderRadius: 2,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    backgroundColor: colors.primary,
    borderRadius: 2,
  },
  promptText: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.textPrimary,
    lineHeight: 26,
    marginBottom: 20,
  },
  responseInput: {
    backgroundColor: colors.backgroundSecondary,
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    color: colors.textPrimary,
    minHeight: 100,
    marginBottom: 20,
  },
  promptActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    padding: 12,
  },
  actionButtonDisabled: {
    opacity: 0.4,
  },
  actionButtonText: {
    fontSize: 16,
    color: colors.primary,
    fontWeight: '500',
  },
  actionButtonTextDisabled: {
    color: colors.textTertiary,
  },
  saveButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    backgroundColor: colors.success,
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 8,
  },
  saveButtonText: {
    fontSize: 16,
    color: colors.white,
    fontWeight: '600',
  },
  instructions: {
    backgroundColor: colors.backgroundSecondary,
    borderRadius: 12,
    padding: 16,
    marginHorizontal: 20,
    marginBottom: 20,
  },
  instructionText: {
    fontSize: 14,
    color: colors.textSecondary,
    lineHeight: 20,
    textAlign: 'center',
  },
  completeButton: {
    backgroundColor: colors.primary,
    borderRadius: 12,
    padding: 16,
    marginHorizontal: 20,
    marginBottom: 20,
    alignItems: 'center',
  },
  completeButtonDisabled: {
    opacity: 0.6,
  },
  completeButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.white,
  },
  completedBadge: {
    alignSelf: 'center',
    marginBottom: 20,
  },
});

// Register this activity
export const chatPromptsConfig = {
  id: 'chat-prompts',
  title: 'Chat Prompts',
  description: 'Meaningful conversation starters',
  icon: <MessageCircle size={24} color={colors.white} />,
  defaultPoints: 15,
  category: 'communication' as const,
  estimatedDuration: 20,
  requiresPartner: true,
  component: ChatPromptsActivity,
};
