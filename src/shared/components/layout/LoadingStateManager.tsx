/**
 * Enterprise Loading State Management System
 * Unified loading states with skeleton screens, progress indicators, and smart caching
 */

import React, { createContext, ReactNode, useContext, useEffect, useReducer } from 'react';
import { Animated, Dimensions, StyleSheet, Text, View } from 'react-native';
import { useAppTheme } from '../../hooks/useAppTheme';
import { logger } from '../../utils/logger';

// =============================================================================
// LOADING TYPES & INTERFACES
// =============================================================================

export enum LoadingState {
  _IDLE = 'idle',
  _LOADING = 'loading',
  _SUCCESS = 'success',
  _ERROR = 'error',
  _REFRESHING = 'refreshing',
  _LOADING_MORE = 'loading_more'
}

export enum LoadingType {
  _SPINNER = 'spinner',
  _SKELETON = 'skeleton',
  _PROGRESS = 'progress',
  _SHIMMER = 'shimmer',
  _PULSE = 'pulse',
  _DOTS = 'dots'
}

export interface LoadingConfig {
  type: LoadingType;
  message?: string;
  progress?: number;
  timeout?: number;
  showProgress?: boolean;
  enableCaching?: boolean;
  cacheKey?: string;
  minDisplayTime?: number;
  overlay?: boolean;
  blockInteraction?: boolean;
}

export interface LoadingStateData {
  state: LoadingState;
  config: LoadingConfig;
  startTime: number;
  progress: number;
  error?: Error;
  data?: unknown;
}

interface LoadingContextType {
  loadingStates: Map<string, LoadingStateData>;
  setLoading: (_key: string, _config: LoadingConfig) => void;
  setSuccess: (_key: string, _data?: unknown) => void;
  setError: (_key: string, _error: Error) => void;
  setProgress: (_key: string, _progress: number) => void;
  clearLoading: (_key: string) => void;
  isLoading: (_key: string) => boolean;
  getLoadingState: (_key: string) => LoadingStateData | undefined;
}

// =============================================================================
// LOADING STATE REDUCER
// =============================================================================

type LoadingAction =
  | { type: 'SET_LOADING'; key: string; config: LoadingConfig }
  | { type: 'SET_SUCCESS'; key: string; data?: unknown }
  | { type: 'SET_ERROR'; key: string; error: Error }
  | { type: 'SET_PROGRESS'; key: string; progress: number }
  | { type: 'CLEAR_LOADING'; key: string }
  | { type: 'CLEANUP_EXPIRED' };

function loadingReducer(
  state: Map<string, LoadingStateData>,
  action: LoadingAction
): Map<string, LoadingStateData> {
  const newState = new Map(state);

  switch (action.type) {
    case 'SET_LOADING':
      newState.set(action.key, {
        state: LoadingState._LOADING,
        config: action.config,
        startTime: Date.now(),
        progress: 0
      });
      break;

    case 'SET_SUCCESS':
      const successData = newState.get(action.key);
      if (successData) {
        newState.set(action.key, {
          ...successData,
          state: LoadingState._SUCCESS,
          data: action.data,
          progress: 100
        });
      }
      break;

    case 'SET_ERROR':
      const errorData = newState.get(action.key);
      if (errorData) {
        newState.set(action.key, {
          ...errorData,
          state: LoadingState._ERROR,
          error: action.error,
          progress: 0
        });
      }
      break;

    case 'SET_PROGRESS':
      const progressData = newState.get(action.key);
      if (progressData) {
        newState.set(action.key, {
          ...progressData,
          progress: Math.max(0, Math.min(100, action.progress))
        });
      }
      break;

    case 'CLEAR_LOADING':
      newState.delete(action.key);
      break;

    case 'CLEANUP_EXPIRED':
      const now = Date.now();
      for (const [key, data] of newState.entries()) {
        if (data.config.timeout && now - data.startTime > data.config.timeout) {
          newState.delete(key);
        }
      }
      break;
  }

  return newState;
}

// =============================================================================
// LOADING CONTEXT
// =============================================================================

const LoadingContext = createContext<LoadingContextType | undefined>(undefined);

export const LoadingProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [loadingStates, dispatch] = useReducer(loadingReducer, new Map());

  // Cleanup expired loading states
  useEffect(() => {
    const interval = setInterval(() => {
      dispatch({ type: 'CLEANUP_EXPIRED' });
    }, 5000);

    return () => clearInterval(interval);
  }, []);

  const setLoading = (key: string, config: LoadingConfig) => {
    logger.debug(`Loading started: ${key}`, { config });
    dispatch({ type: 'SET_LOADING', key, config });
  };

  const setSuccess = (key: string, data?: unknown) => {
    const loadingData = loadingStates.get(key);
    if (loadingData) {
      const duration = Date.now() - loadingData.startTime;
      logger.info(`Loading completed: ${key}`, { duration, data });
    }
    dispatch({ type: 'SET_SUCCESS', key, data });
  };

  const setError = (key: string, error: Error) => {
    const loadingData = loadingStates.get(key);
    if (loadingData) {
      const duration = Date.now() - loadingData.startTime;
      logger.error(`Loading failed: ${key}`, { error, duration });
    }
    dispatch({ type: 'SET_ERROR', key, error });
  };

  const setProgress = (key: string, progress: number) => {
    dispatch({ type: 'SET_PROGRESS', key, progress });
  };

  const clearLoading = (key: string) => {
    dispatch({ type: 'CLEAR_LOADING', key });
  };

  const isLoading = (key: string) => {
    const data = loadingStates.get(key);
    return data?.state === LoadingState._LOADING;
  };

  const getLoadingState = (key: string) => {
    return loadingStates.get(key);
  };

  return (
    <LoadingContext.Provider
      value={{
        loadingStates,
        setLoading,
        setSuccess,
        setError,
        setProgress,
        clearLoading,
        isLoading,
        getLoadingState
      }}
    >
      {children}
    </LoadingContext.Provider>
  );
};

export const useLoadingState = () => {
  const context = useContext(LoadingContext);
  if (!context) {
    throw new Error('useLoadingState must be used within a LoadingProvider');
  }
  return context;
};

// =============================================================================
// LOADING COMPONENTS
// =============================================================================

interface LoadingIndicatorProps {
  loadingKey: string;
  fallback?: ReactNode;
  children?: ReactNode;
  style?: any;
}

export const LoadingIndicator: React.FC<LoadingIndicatorProps> = ({
  loadingKey,
  fallback,
  children,
  style
}) => {
  const { getLoadingState } = useLoadingState();
  const { theme: _theme } = useAppTheme();
  const loadingData = getLoadingState(loadingKey);

  if (!loadingData || loadingData.state === LoadingState._IDLE) {
    return <>{children}</>;
  }

  if (loadingData.state === LoadingState._SUCCESS) {
    return <>{children}</>;
  }

  if (loadingData.state === LoadingState._ERROR && fallback) {
    return <>{fallback}</>;
  }

  // Render loading indicator based on type
  switch (loadingData.config.type) {
    case LoadingType._SKELETON:
      return <SkeletonLoader style={style} />;
    case LoadingType._SHIMMER:
      return <ShimmerLoader style={style} />;
    case LoadingType._PROGRESS:
      return (
        <ProgressLoader
          progress={loadingData.progress}
          message={loadingData.config.message}
          style={style}
        />
      );
    case LoadingType._DOTS:
      return <DotsLoader message={loadingData.config.message} style={style} />;
    case LoadingType._PULSE:
      return <PulseLoader style={style} />;
    default:
      return <SpinnerLoader message={loadingData.config.message} style={style} />;
  }
};

// =============================================================================
// SPECIFIC LOADING COMPONENTS
// =============================================================================

const SpinnerLoader: React.FC<{ message?: string; style?: any }> = ({ message, style }) => {
  const { theme } = useAppTheme();
  const spinValue = new Animated.Value(0);

  useEffect(() => {
    const spin = () => {
      spinValue.setValue(0);
      Animated.timing(spinValue, {
        toValue: 1,
        duration: 1000,
        useNativeDriver: true
      }).start(() => spin());
    };
    spin();
  }, []);

  const rotate = spinValue.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '360deg']
  });

  return (
    <View style={[styles.centered, style]}>
      <Animated.View
        style={[
          styles.spinner,
          { borderColor: theme.primary, transform: [{ rotate }] }
        ]}
      />
      {message && (
        <Text style={[styles.message, { color: theme.textSecondary }]}>
          {message}
        </Text>
      )}
    </View>
  );
};

const SkeletonLoader: React.FC<{ style?: any }> = ({ style }) => {
  const { theme } = useAppTheme();
  const opacity = new Animated.Value(0.3);

  useEffect(() => {
    const pulse = () => {
      Animated.sequence([
        Animated.timing(opacity, {
          toValue: 0.7,
          duration: 800,
          useNativeDriver: true
        }),
        Animated.timing(opacity, {
          toValue: 0.3,
          duration: 800,
          useNativeDriver: true
        })
      ]).start(() => pulse());
    };
    pulse();
  }, []);

  return (
    <View style={[styles.skeleton, style]}>
      <Animated.View
        style={[
          styles.skeletonLine,
          { backgroundColor: theme.border, opacity }
        ]}
      />
      <Animated.View
        style={[
          styles.skeletonLine,
          styles.skeletonLineShort,
          { backgroundColor: theme.border, opacity }
        ]}
      />
      <Animated.View
        style={[
          styles.skeletonLine,
          { backgroundColor: theme.border, opacity }
        ]}
      />
    </View>
  );
};

const ShimmerLoader: React.FC<{ style?: any }> = ({ style }) => {
  const { theme } = useAppTheme();
  const translateX = new Animated.Value(-Dimensions.get('window').width);

  useEffect(() => {
    const shimmer = () => {
      translateX.setValue(-Dimensions.get('window').width);
      Animated.timing(translateX, {
        toValue: Dimensions.get('window').width,
        duration: 1500,
        useNativeDriver: true
      }).start(() => shimmer());
    };
    shimmer();
  }, []);

  return (
    <View style={[styles.shimmerContainer, style]}>
      <View style={[styles.shimmerBackground, { backgroundColor: theme.border }]} />
      <Animated.View
        style={[
          styles.shimmerOverlay,
          {
            backgroundColor: theme.background,
            transform: [{ translateX }]
          }
        ]}
      />
    </View>
  );
};

const ProgressLoader: React.FC<{
  progress: number;
  message?: string;
  style?: any;
}> = ({ progress, message, style }) => {
  const { theme } = useAppTheme();
  const progressWidth = new Animated.Value(0);

  useEffect(() => {
    Animated.timing(progressWidth, {
      toValue: progress,
      duration: 300,
      useNativeDriver: false
    }).start();
  }, [progress]);

  return (
    <View style={[styles.progressContainer, style]}>
      {message && (
        <Text style={[styles.message, { color: theme.textSecondary }]}>
          {message}
        </Text>
      )}
      <View style={[styles.progressBar, { backgroundColor: theme.border }]}>
        <Animated.View
          style={[
            styles.progressFill,
            {
              backgroundColor: theme.primary,
              width: progressWidth.interpolate({
                inputRange: [0, 100],
                outputRange: ['0%', '100%']
              })
            }
          ]}
        />
      </View>
      <Text style={[styles.progressText, { color: theme.textSecondary }]}>
        {Math.round(progress)}%
      </Text>
    </View>
  );
};

const DotsLoader: React.FC<{ message?: string; style?: any }> = ({ message, style }) => {
  const { theme } = useAppTheme();
  const dot1 = new Animated.Value(0);
  const dot2 = new Animated.Value(0);
  const dot3 = new Animated.Value(0);

  useEffect(() => {
    const animate = () => {
      Animated.sequence([
        Animated.timing(dot1, { toValue: 1, duration: 400, useNativeDriver: true }),
        Animated.timing(dot2, { toValue: 1, duration: 400, useNativeDriver: true }),
        Animated.timing(dot3, { toValue: 1, duration: 400, useNativeDriver: true }),
        Animated.timing(dot1, { toValue: 0, duration: 400, useNativeDriver: true }),
        Animated.timing(dot2, { toValue: 0, duration: 400, useNativeDriver: true }),
        Animated.timing(dot3, { toValue: 0, duration: 400, useNativeDriver: true })
      ]).start(() => animate());
    };
    animate();
  }, []);

  return (
    <View style={[styles.centered, style]}>
      <View style={styles.dotsContainer}>
        <Animated.View
          style={[
            styles.dot,
            { backgroundColor: theme.primary, opacity: dot1 }
          ]}
        />
        <Animated.View
          style={[
            styles.dot,
            { backgroundColor: theme.primary, opacity: dot2 }
          ]}
        />
        <Animated.View
          style={[
            styles.dot,
            { backgroundColor: theme.primary, opacity: dot3 }
          ]}
        />
      </View>
      {message && (
        <Text style={[styles.message, { color: theme.textSecondary }]}>
          {message}
        </Text>
      )}
    </View>
  );
};

const PulseLoader: React.FC<{ style?: any }> = ({ style }) => {
  const { theme } = useAppTheme();
  const scale = new Animated.Value(1);

  useEffect(() => {
    const pulse = () => {
      Animated.sequence([
        Animated.timing(scale, {
          toValue: 1.2,
          duration: 600,
          useNativeDriver: true
        }),
        Animated.timing(scale, {
          toValue: 1,
          duration: 600,
          useNativeDriver: true
        })
      ]).start(() => pulse());
    };
    pulse();
  }, []);

  return (
    <View style={[styles.centered, style]}>
      <Animated.View
        style={[
          styles.pulseCircle,
          {
            backgroundColor: theme.primary + '30',
            borderColor: theme.primary,
            transform: [{ scale }]
          }
        ]}
      />
    </View>
  );
};

// =============================================================================
// STYLES
// =============================================================================

const styles = StyleSheet.create({
  centered: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20
  },
  spinner: {
    width: 40,
    height: 40,
    borderWidth: 4,
    borderRadius: 20,
    borderTopColor: 'transparent',
    marginBottom: 16
  },
  message: {
    fontSize: 16,
    textAlign: 'center',
    marginTop: 8
  },
  skeleton: {
    padding: 16
  },
  skeletonLine: {
    height: 16,
    borderRadius: 8,
    marginBottom: 12
  },
  skeletonLineShort: {
    width: '60%'
  },
  shimmerContainer: {
    height: 200,
    overflow: 'hidden',
    position: 'relative'
  },
  shimmerBackground: {
    flex: 1,
    opacity: 0.3
  },
  shimmerOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    opacity: 0.5
  },
  progressContainer: {
    padding: 20,
    alignItems: 'center'
  },
  progressBar: {
    width: '100%',
    height: 8,
    borderRadius: 4,
    marginVertical: 12,
    overflow: 'hidden'
  },
  progressFill: {
    height: '100%',
    borderRadius: 4
  },
  progressText: {
    fontSize: 14,
    fontWeight: '500'
  },
  dotsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16
  },
  dot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginHorizontal: 4
  },
  pulseCircle: {
    width: 60,
    height: 60,
    borderRadius: 30,
    borderWidth: 2
  }
});

// =============================================================================
// LOADING HOOKS
// =============================================================================

/**
 * Hook for managing async operations with loading states
 */
export const useAsyncOperation = <T = unknown>(
  key: string,
  config: Partial<LoadingConfig> = {}
) => {
  const { setLoading, setSuccess, setError, setProgress, getLoadingState } = useLoadingState();

  const execute = async (
    operation: () => Promise<T>,
    customConfig?: Partial<LoadingConfig>
  ): Promise<T | undefined> => {
    const finalConfig: LoadingConfig = {
      type: LoadingType._SPINNER,
      timeout: 30000,
      minDisplayTime: 500,
      ...config,
      ...customConfig
    };

    try {
      setLoading(key, finalConfig);

      const startTime = Date.now();
      const result = await operation();

      // Ensure minimum display time for better UX
      const elapsed = Date.now() - startTime;
      if (finalConfig.minDisplayTime && elapsed < finalConfig.minDisplayTime) {
        await new Promise(resolve =>
          setTimeout(resolve, finalConfig.minDisplayTime! - elapsed)
        );
      }

      setSuccess(key, result);
      return result;
    } catch (error) {
      setError(key, error as Error);
      return undefined;
    }
  };

  const updateProgress = (progress: number) => {
    setProgress(key, progress);
  };

  const loadingState = getLoadingState(key);

  return {
    execute,
    updateProgress,
    isLoading: loadingState?.state === LoadingState._LOADING,
    isSuccess: loadingState?.state === LoadingState._SUCCESS,
    isError: loadingState?.state === LoadingState._ERROR,
    error: loadingState?.error,
    data: loadingState?.data as T,
    progress: loadingState?.progress || 0
  };
};

/**
 * Hook for managing multiple loading states
 */
export const useMultipleLoadingStates = (keys: string[]) => {
  const { getLoadingState } = useLoadingState();

  const states = keys.reduce((acc, key) => {
    acc[key] = getLoadingState(key);
    return acc;
  }, {} as Record<string, LoadingStateData | undefined>);

  const isAnyLoading = keys.some(key => {
    const state = getLoadingState(key);
    return state?.state === LoadingState._LOADING;
  });

  const areAllLoading = keys.every(key => {
    const state = getLoadingState(key);
    return state?.state === LoadingState._LOADING;
  });

  const hasAnyError = keys.some(key => {
    const state = getLoadingState(key);
    return state?.state === LoadingState._ERROR;
  });

  return {
    states,
    isAnyLoading,
    areAllLoading,
    hasAnyError
  };
};

/**
 * Hook for page-level loading management
 */
export const usePageLoading = (pageName: string) => {
  return useAsyncOperation(`page:${pageName}`, {
    type: LoadingType._SKELETON,
    timeout: 15000,
    overlay: true
  });
};

/**
 * Hook for component-level loading management
 */
export const useComponentLoading = (componentName: string) => {
  return useAsyncOperation(`component:${componentName}`, {
    type: LoadingType._SHIMMER,
    timeout: 10000
  });
};

/**
 * Hook for data fetching with loading states
 */
export const useDataFetching = <T = unknown>(
  endpoint: string,
  options: {
    cacheKey?: string;
    refreshInterval?: number;
    retryCount?: number;
  } = {}
) => {
  const key = options.cacheKey || `fetch:${endpoint}`;
  const { execute, ...rest } = useAsyncOperation<T>(key, {
    type: LoadingType._SKELETON,
    enableCaching: true,
    cacheKey: key
  });

  const fetch = (customOptions?: RequestInit) => {
    return execute(async () => {
      const response = await window.fetch(endpoint, customOptions);
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      return response.json();
    });
  };

  const refresh = () => fetch();

  return {
    fetch,
    refresh,
    ...rest
  };
};
