/**
 * Unified Error Boundary Component
 * Enterprise-grade error boundary with recovery mechanisms and user feedback
 */

import { router } from 'expo-router';
import React, { Component, ErrorInfo, ReactNode } from 'react';
import { ScrollView, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import { useAppTheme } from '../../hooks/useAppTheme';
import { AppError, ErrorCategory, errorHand<PERSON> as errorManager } from '../../utils/errorHandler';
import { logger } from '../../utils/logger';

// =============================================================================
// TYPES & INTERFACES
// =============================================================================

interface ErrorBoundaryState {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
  errorId: string | null;
  retryCount: number;
}

interface ErrorBoundaryProps {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (_error: Error, _errorInfo: ErrorInfo) => void;
  enableRetry?: boolean;
  maxRetries?: number;
  showErrorDetails?: boolean;
  level?: 'page' | 'component' | 'feature';
  context?: {
    component?: string;
    feature?: string;
    userId?: string;
  };
}

interface ErrorDisplayProps {
  error: Error;
  errorInfo: ErrorInfo | null;
  errorId: string;
  retryCount: number;
  maxRetries: number;
  onRetry: () => void;
  onGoHome: () => void;
  onReportError: () => void;
  showDetails: boolean;
  level: 'page' | 'component' | 'feature';
}

// =============================================================================
// ERROR DISPLAY COMPONENT
// =============================================================================

const ErrorDisplay: React.FC<ErrorDisplayProps> = ({
  error,
  errorInfo,
  errorId,
  retryCount,
  maxRetries,
  onRetry,
  onGoHome,
  onReportError,
  showDetails,
  level
}) => {
  const { theme } = useAppTheme();

  // Fallback theme if theme is undefined
  const safeTheme = theme || {
    text: '#393939',
    textSecondary: '#6B7280',
    textInverse: '#FFFFFF',
    primary: '#9CAF88',
    error: '#EF4444',
    border: '#E5E7EB'
  };

  const getErrorTitle = () => {
    switch (level) {
      case 'page':
        return 'Page Error';
      case 'feature':
        return 'Feature Unavailable';
      case 'component':
        return 'Component Error';
      default:
        return 'Something went wrong';
    }
  };

  const getErrorMessage = () => {
    if (error.name === 'ChunkLoadError') {
      return 'The app needs to be refreshed. Please try reloading the page.';
    }
    if (error.message.includes('Network')) {
      return 'Network connection issue. Please check your internet connection and try again.';
    }
    return 'An unexpected error occurred. Our team has been notified.';
  };

  const canRetry = retryCount < maxRetries;

  return (
    <View style={[styles.container, { backgroundColor: theme.background }]}>
      <View style={[styles.errorCard, { backgroundColor: theme.surface, borderColor: theme.border }]}>
        {/* Error Icon */}
        <View style={[styles.iconContainer, { backgroundColor: safeTheme.error + '20' }]}>
          <Text style={[styles.errorIcon, { color: safeTheme.error }]}>⚠️</Text>
        </View>

        {/* Error Title */}
        <Text style={[styles.title, { color: safeTheme.text }]}>
          {getErrorTitle()}
        </Text>

        {/* Error Message */}
        <Text style={[styles.message, { color: safeTheme.textSecondary }]}>
          {getErrorMessage()}
        </Text>

        {/* Error ID */}
        <Text style={[styles.errorId, { color: safeTheme.textSecondary }]}>
          Error ID: {errorId}
        </Text>

        {/* Action Buttons */}
        <View style={styles.buttonContainer}>
          {canRetry && (
            <TouchableOpacity
              style={[styles.button, styles.primaryButton, { backgroundColor: safeTheme.primary }]}
              onPress={onRetry}
            >
              <Text style={[styles.buttonText, { color: safeTheme.textInverse }]}>
                Try Again {retryCount > 0 && `(${retryCount}/${maxRetries})`}
              </Text>
            </TouchableOpacity>
          )}

          {level === 'page' && (
            <TouchableOpacity
              style={[styles.button, styles.secondaryButton, { borderColor: safeTheme.border }]}
              onPress={onGoHome}
            >
              <Text style={[styles.buttonText, { color: safeTheme.text }]}>
                Go Home
              </Text>
            </TouchableOpacity>
          )}

          <TouchableOpacity
            style={[styles.button, styles.secondaryButton, { borderColor: safeTheme.border }]}
            onPress={onReportError}
          >
            <Text style={[styles.buttonText, { color: safeTheme.text }]}>
              Report Issue
            </Text>
          </TouchableOpacity>
        </View>

        {/* Error Details (Development) */}
        {showDetails && __DEV__ && (
          <ScrollView style={styles.detailsContainer} showsVerticalScrollIndicator={false}>
            <Text style={[styles.detailsTitle, { color: safeTheme.text }]}>
              Error Details (Development)
            </Text>
            <Text style={[styles.detailsText, { color: safeTheme.textSecondary }]}>
              {error.message}
            </Text>
            {error.stack && (
              <Text style={[styles.detailsText, { color: safeTheme.textSecondary }]}>
                {error.stack}
              </Text>
            )}
            {errorInfo?.componentStack && (
              <Text style={[styles.detailsText, { color: safeTheme.textSecondary }]}>
                Component Stack: {errorInfo.componentStack}
              </Text>
            )}
          </ScrollView>
        )}
      </View>
    </View>
  );
};

// =============================================================================
// ERROR BOUNDARY CLASS
// =============================================================================

export class UnifiedErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState> {
  private errorId: string = '';

  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: null,
      retryCount: 0
    };
  }

  static getDerivedStateFromError(error: Error): Partial<ErrorBoundaryState> {
    return {
      hasError: true,
      error
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    this.errorId = `ERR_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    this.setState({
      error,
      errorInfo,
      errorId: this.errorId
    });

    // Create AppError for unified handling
    const appError: AppError = {
      name: error.name,
      message: error.message,
      code: this.getErrorCode(error),
      category: this.getErrorCategory(error),
      severity: this.getErrorSeverity(error),
      context: {
        component: this.props.context?.component || 'UnknownComponent',
        feature: this.props.context?.feature,
        userId: this.props.context?.userId,
        errorBoundary: true,
        level: this.props.level || 'component',
        componentStack: errorInfo.componentStack,
        timestamp: new Date().toISOString()
      },
      userMessage: this.getUserMessage(error),
      isRetryable: this.isRetryable(error),
      stack: error.stack
    } as AppError;

    // Handle through unified error manager
    errorManager.handleError(appError);

    // Call custom error handler if provided
    this.props.onError?.(error, errorInfo);

    // Log for debugging
    logger.error('Error Boundary caught error', {
      errorId: this.errorId,
      error: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      context: this.props.context
    });
  }

  private getErrorCode(error: Error): string {
    if (error.name === 'ChunkLoadError') return 'CHUNK_LOAD_ERROR';
    if (error.message.includes('Network')) return 'NETWORK_ERROR';
    if (error.message.includes('Permission')) return 'PERMISSION_ERROR';
    return 'REACT_ERROR';
  }

  private getErrorCategory(error: Error): ErrorCategory {
    if (error.name === 'ChunkLoadError') return ErrorCategory._SYSTEM;
    if (error.message.includes('Network')) return ErrorCategory._NETWORK;
    if (error.message.includes('Permission')) return ErrorCategory._PERMISSION;
    return ErrorCategory._UI;
  }

  private getErrorSeverity(_error: Error): string {
    if (this.props.level === 'page') return 'HIGH';
    if (this.props.level === 'feature') return 'MEDIUM';
    return 'LOW';
  }

  private getUserMessage(error: Error): string {
    if (error.name === 'ChunkLoadError') {
      return 'The app needs to be refreshed. Please reload the page.';
    }
    if (error.message.includes('Network')) {
      return 'Network connection issue. Please check your connection.';
    }
    return 'Something went wrong. Please try again.';
  }

  private isRetryable(error: Error): boolean {
    // Chunk load errors are retryable
    if (error.name === 'ChunkLoadError') return true;
    // Network errors are retryable
    if (error.message.includes('Network')) return true;
    // Most React errors are retryable
    return true;
  }

  handleRetry = () => {
    const maxRetries = this.props.maxRetries || 3;

    if (this.state.retryCount < maxRetries) {
      this.setState(prevState => ({
        hasError: false,
        error: null,
        errorInfo: null,
        errorId: null,
        retryCount: prevState.retryCount + 1
      }));

      logger.info('Error boundary retry attempted', {
        errorId: this.errorId,
        retryCount: this.state.retryCount + 1,
        maxRetries
      });
    }
  };

  handleGoHome = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: null,
      retryCount: 0
    });

    // Navigate to home
    router.replace('/(tabs)');
  };

  handleReportError = () => {
    // This would open a bug report modal or navigate to feedback screen
    logger.info('User requested error report', { errorId: this.errorId });

    // For now, just log the request
    // In a real app, this would open a feedback form or send to support
  };

  render() {
    if (this.state.hasError && this.state.error) {
      // Custom fallback UI
      if (this.props.fallback) {
        return this.props.fallback;
      }

      // Default error UI
      return (
        <ErrorDisplay
          error={this.state.error}
          errorInfo={this.state.errorInfo}
          errorId={this.state.errorId || 'UNKNOWN'}
          retryCount={this.state.retryCount}
          maxRetries={this.props.maxRetries || 3}
          onRetry={this.handleRetry}
          onGoHome={this.handleGoHome}
          onReportError={this.handleReportError}
          showDetails={this.props.showErrorDetails || false}
          level={this.props.level || 'component'}
        />
      );
    }

    return this.props.children;
  }
}

// =============================================================================
// STYLES
// =============================================================================

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorCard: {
    width: '100%',
    maxWidth: 400,
    padding: 24,
    borderRadius: 12,
    borderWidth: 1,
    alignItems: 'center',
  },
  iconContainer: {
    width: 60,
    height: 60,
    borderRadius: 30,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  errorIcon: {
    fontSize: 24,
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
    marginBottom: 8,
    textAlign: 'center',
  },
  message: {
    fontSize: 16,
    lineHeight: 24,
    textAlign: 'center',
    marginBottom: 16,
  },
  errorId: {
    fontSize: 12,
    fontFamily: 'monospace',
    marginBottom: 24,
    opacity: 0.7,
  },
  buttonContainer: {
    width: '100%',
    gap: 12,
  },
  button: {
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    alignItems: 'center',
  },
  primaryButton: {
    // backgroundColor set dynamically
  },
  secondaryButton: {
    borderWidth: 1,
    backgroundColor: 'transparent',
  },
  buttonText: {
    fontSize: 16,
    fontWeight: '500',
  },
  detailsContainer: {
    width: '100%',
    maxHeight: 200,
    marginTop: 20,
    padding: 12,
    backgroundColor: 'rgba(0,0,0,0.05)',
    borderRadius: 8,
  },
  detailsTitle: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 8,
  },
  detailsText: {
    fontSize: 12,
    fontFamily: 'monospace',
    lineHeight: 16,
  },
});

// =============================================================================
// CONVENIENCE WRAPPERS
// =============================================================================

export const PageErrorBoundary: React.FC<Omit<ErrorBoundaryProps, 'level'>> = (props) => (
  <UnifiedErrorBoundary {...props} level="page" maxRetries={2} />
);

export const FeatureErrorBoundary: React.FC<Omit<ErrorBoundaryProps, 'level'>> = (props) => (
  <UnifiedErrorBoundary {...props} level="feature" maxRetries={3} />
);

export const ComponentErrorBoundary: React.FC<Omit<ErrorBoundaryProps, 'level'>> = (props) => (
  <UnifiedErrorBoundary {...props} level="component" maxRetries={1} />
);
