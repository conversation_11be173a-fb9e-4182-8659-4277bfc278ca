/**
 * ErrorHandler Component
 *
 * This component provides comprehensive error handling with a delightful user experience
 * that matches the app's romantic theme.
 *
 * <AUTHOR> Us Team
 * @version 2.0.0
 */

import React, { Component, ErrorInfo, ReactNode, useState } from 'react';
import { <PERSON><PERSON>, Modal, ScrollView, StyleSheet, Text, TextInput, TouchableOpacity, View } from 'react-native';

import { router } from 'expo-router';
import { AlertTriangle, Heart, RefreshCw, Send, Share, X } from 'lucide-react-native';
import { useErrorReporting } from '../../hooks/useErrorReporting';
import { simpleErrorService } from '../../services/system/simpleErrorService';
import { colors } from '../../utils/colors';
import { logger } from '../../utils/logger';

/**
 * Props for the ErrorHandler component
 */
interface ErrorHandlerProps {
  /** Child components to wrap with error boundary */
  children: ReactNode;
  /** Custom fallback UI to display when an error occurs */
  fallback?: ReactNode;
  /** Callback function called when an error is caught */
  onError?: (_error: Error, _errorInfo: ErrorInfo) => void;
}

/**
 * State for the ErrorHandler component
 */
interface ErrorHandlerState {
  /** Whether an error has been caught */
  hasError: boolean;
  /** The error that was caught */
  error: Error | null;
  /** Additional error information from React */
  errorInfo: ErrorInfo | null;
}

/**
 * Props for the ErrorScreen component
 */
interface ErrorScreenProps {
  error?: string;
  onRetry?: () => void;
  showShareButton?: boolean;
  onShowReportModal?: () => void;
}

/**
 * Props for the ErrorReportModal component
 */
interface ErrorReportModalProps {
  visible: boolean;
  onClose: () => void;
  error?: Error;
  context?: {
    component?: string;
    action?: string;
    additionalData?: Record<string, any>;
  };
}

/**
 * ErrorScreen Component - User-friendly error display
 */
const ErrorScreen: React.FC<ErrorScreenProps> = ({
  error = 'Something went wrong',
  onRetry,
  showShareButton = true,
  onShowReportModal
}) => {
  const handleShareError = async () => {
    try {
      await simpleErrorService.shareAllErrors();
    } catch (err) {
      console.error('Failed to share errors:', err);
    }
  };

  const handleRetry = () => {
    if (onRetry) {
      onRetry();
    } else {
      // Default retry - reload the app
      if (typeof window !== 'undefined') {
        window.location.reload();
      }
    }
  };

  return (
    <View style={styles.container}>
      <ScrollView contentContainerStyle={styles.scrollContainer}>
        <View style={styles.iconContainer}>
          <AlertTriangle size={80} color={colors.error} />
        </View>

        <Text style={styles.title}>Oops! Something went wrong</Text>
        <Text style={styles.subtitle}>
          We're sorry for the inconvenience. This error has been logged and we'll work to fix it.
        </Text>

        {error && (
          <View style={styles.errorContainer}>
            <Text style={styles.errorLabel}>Error Details:</Text>
            <Text style={styles.errorText}>{error}</Text>
          </View>
        )}

        <View style={styles.buttonContainer}>
          {onRetry && (
            <TouchableOpacity style={styles.retryButton} onPress={handleRetry}>
              <RefreshCw size={20} color="white" style={styles.buttonIcon} />
              <Text style={styles.retryButtonText}>Try Again</Text>
            </TouchableOpacity>
          )}

          {showShareButton && (
            <TouchableOpacity style={styles.shareButton} onPress={handleShareError}>
              <Share size={20} color={colors.primary} style={styles.buttonIcon} />
              <Text style={styles.shareButtonText}>Share Error Report</Text>
            </TouchableOpacity>
          )}

          {onShowReportModal && (
            <TouchableOpacity style={styles.reportButton} onPress={onShowReportModal}>
              <Heart size={20} color={colors.primary} style={styles.buttonIcon} />
              <Text style={styles.reportButtonText}>Report This Issue</Text>
            </TouchableOpacity>
          )}
        </View>

        <Text style={styles.helpText}>
          If this problem persists, please share the error report so we can help you.
        </Text>
      </ScrollView>
    </View>
  );
};

/**
 * ErrorReportModal Component - Modal for error reporting
 */
const ErrorReportModal: React.FC<ErrorReportModalProps> = ({
  visible,
  onClose,
  error,
  context = {}
}) => {
  const [reportMessage, setReportMessage] = useState('');
  const { submitErrorReport, isReporting } = useErrorReporting();

  const handleSubmit = async () => {
    if (!reportMessage.trim()) {
      Alert.alert('Oops!', 'Please tell us what happened so we can help! 💕');
      return;
    }

    if (!error) {
      Alert.alert('Error', 'No error information available to report.');
      return;
    }

    const success = await submitErrorReport(error, reportMessage, context);

    if (success) {
      setReportMessage('');
      onClose();
    }
  };

  const handleClose = () => {
    setReportMessage('');
    onClose();
  };

  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      onRequestClose={handleClose}
    >
      <View style={styles.overlay}>
        <View style={styles.modalContainer}>
          <View
            style={[styles.header, { backgroundColor: colors.lightPink }]}
          >
            <View style={styles.headerContent}>
              <View style={styles.heartContainer}>
                <Heart size={24} color={colors.white} fill={colors.white} />
              </View>
              <Text style={styles.modalTitle}>Help Us Fix This! 💕</Text>
              <TouchableOpacity style={styles.closeButton} onPress={handleClose}>
                <X size={24} color={colors.white} />
              </TouchableOpacity>
            </View>
          </View>

          <View style={styles.modalContent}>
            <Text style={styles.modalSubtitle}>
              Your feedback helps us make the app better for everyone! Tell us what happened:
            </Text>

            <TextInput
              style={styles.input}
              placeholder="What were you trying to do when this happened?"
              value={reportMessage}
              onChangeText={setReportMessage}
              multiline
              numberOfLines={6}
              textAlignVertical="top"
              placeholderTextColor={colors.textTertiary}
            />

            {error && __DEV__ && (
              <View style={styles.errorInfo}>
                <Text style={styles.errorInfoTitle}>🐛 Error Details (Dev Only):</Text>
                <Text style={styles.errorInfoText}>{error.message}</Text>
              </View>
            )}

            <View style={styles.actions}>
              <TouchableOpacity
                style={styles.cancelButton}
                onPress={handleClose}
                disabled={isReporting}
              >
                <Text style={styles.cancelButtonText}>Cancel</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[
                  styles.submitButton,
                  (!reportMessage.trim() || isReporting) && styles.submitButtonDisabled
                ]}
                onPress={handleSubmit}
                disabled={!reportMessage.trim() || isReporting}
              >
                <Send size={16} color={colors.white} />
                <Text style={styles.submitButtonText}>
                  {isReporting ? 'Sending...' : 'Send Report'}
                </Text>
              </TouchableOpacity>
            </View>

            <Text style={styles.footerText}>
              Thank you for helping us improve! 💖
            </Text>
          </View>
        </View>
      </View>
    </Modal>
  );
};

/**
 * Main ErrorHandler Component - Combines ErrorBoundary, ErrorScreen, and ErrorReportModal
 */
export class ErrorHandler extends Component<ErrorHandlerProps, ErrorHandlerState> {
  constructor(props: ErrorHandlerProps) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
    };
  }

  static getDerivedStateFromError(error: Error): ErrorHandlerState {
    return {
      hasError: true,
      error,
      errorInfo: null,
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    this.setState({
      error,
      errorInfo,
    });

    // Log error securely
    logger.error('React Error Boundary caught an error:', {
      error: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
    });

    // Call custom error handler if provided
    this.props.onError?.(error, errorInfo);
  }

  handleRetry = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
    });
  };

  handleGoHome = () => {
    this.handleRetry();
    router.push('/(tabs)');
  };

  render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return this.props.fallback;
      }

      return (
        <ErrorHandlerWrapper
          error={this.state.error}
          onRetry={this.handleRetry}
        />
      );
    }

    return this.props.children;
  }
}

/**
 * ErrorHandlerWrapper - Manages the state for ErrorScreen and ErrorReportModal
 */
const ErrorHandlerWrapper: React.FC<{
  error: Error | null;
  onRetry: () => void;
}> = ({ error, onRetry }) => {
  const [showReportModal, setShowReportModal] = useState(false);

  return (
    <>
      <ErrorScreen
        error={error?.message || 'An unexpected error occurred'}
        onRetry={onRetry}
        showShareButton={true}
        onShowReportModal={() => setShowReportModal(true)}
      />
      <ErrorReportModal
        visible={showReportModal}
        onClose={() => setShowReportModal(false)}
        error={error || undefined}
        context={{
          component: 'ErrorHandler',
          action: 'errorBoundary',
        }}
      />
    </>
  );
};

// Export individual components for backward compatibility
export { ErrorReportModal, ErrorScreen };

const styles = StyleSheet.create({
  // ErrorScreen styles
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  scrollContainer: {
    flexGrow: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  iconContainer: {
    marginBottom: 24,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: colors.textPrimary,
    textAlign: 'center',
    marginBottom: 12,
  },
  subtitle: {
    fontSize: 16,
    color: colors.textSecondary,
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 24,
  },
  errorContainer: {
    backgroundColor: colors.backgroundSecondary,
    borderRadius: 12,
    padding: 16,
    marginBottom: 24,
    width: '100%',
  },
  errorLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.textPrimary,
    marginBottom: 8,
  },
  errorText: {
    fontSize: 14,
    color: colors.error,
    fontFamily: 'monospace',
  },
  buttonContainer: {
    width: '100%',
    gap: 12,
  },
  retryButton: {
    backgroundColor: colors.primary,
    borderRadius: 12,
    padding: 16,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  retryButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  shareButton: {
    backgroundColor: colors.backgroundSecondary,
    borderRadius: 12,
    padding: 16,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: colors.borderLight,
  },
  shareButtonText: {
    color: colors.primary,
    fontSize: 16,
    fontWeight: '600',
  },
  reportButton: {
    backgroundColor: colors.backgroundSecondary,
    borderRadius: 12,
    padding: 16,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: colors.borderLight,
  },
  reportButtonText: {
    color: colors.primary,
    fontSize: 16,
    fontWeight: '600',
  },
  buttonIcon: {
    marginRight: 8,
  },
  helpText: {
    fontSize: 14,
    color: colors.textSecondary,
    textAlign: 'center',
    marginTop: 24,
    lineHeight: 20,
  },

  // ErrorReportModal styles
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  modalContainer: {
    backgroundColor: colors.white,
    borderRadius: 20,
    width: '100%',
    maxWidth: 400,
    maxHeight: '80%',
    overflow: 'hidden',
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 10 },
    shadowOpacity: 0.25,
    shadowRadius: 20,
    elevation: 10,
  },
  header: {
    padding: 20,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  heartContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalTitle: {
    flex: 1,
    fontSize: 18,
    fontWeight: '700',
    color: colors.white,
    textAlign: 'center',
    marginHorizontal: 16,
  },
  closeButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    padding: 20,
  },
  modalSubtitle: {
    fontSize: 16,
    color: colors.textPrimary,
    lineHeight: 24,
    marginBottom: 16,
    textAlign: 'center',
  },
  input: {
    backgroundColor: '#F9FAFB',
    borderWidth: 1,
    borderColor: '#E5E7EB',
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    color: '#1F2937',
    marginBottom: 16,
    minHeight: 120,
  },
  errorInfo: {
    backgroundColor: '#FEF2F2',
    borderWidth: 1,
    borderColor: '#FECACA',
    borderRadius: 8,
    padding: 12,
    marginBottom: 16,
  },
  errorInfoTitle: {
    fontSize: 12,
    fontWeight: '600',
    color: '#DC2626',
    marginBottom: 4,
  },
  errorInfoText: {
    fontSize: 11,
    color: colors.redDark,
    fontFamily: 'monospace',
  },
  actions: {
    flexDirection: 'row',
    gap: 12,
    marginBottom: 16,
  },
  cancelButton: {
    flex: 1,
    backgroundColor: colors.backgroundTertiary,
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 12,
    alignItems: 'center',
  },
  cancelButtonText: {
    color: colors.textSecondary,
    fontSize: 16,
    fontWeight: '600',
  },
  submitButton: {
    flex: 1,
    backgroundColor: colors.lightPink,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 12,
    gap: 8,
  },
  submitButtonDisabled: {
    backgroundColor: colors.borderMedium,
  },
  submitButtonText: {
    color: colors.white,
    fontSize: 16,
    fontWeight: '600',
  },
  footerText: {
    fontSize: 14,
    color: colors.textTertiary,
    textAlign: 'center',
    fontStyle: 'italic',
  },
});
