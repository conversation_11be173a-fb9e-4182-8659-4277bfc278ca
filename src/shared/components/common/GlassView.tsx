/**
 * GlassView Component
 *
 * A React Native compatible glass-morphism component that works
 * properly in both light and dark themes with appropriate blur effects.
 *
 * @version 1.0.0
 * <AUTHOR> Us Team
 */

import { BlurView } from 'expo-blur';
import React from 'react';
import { Platform, View, ViewProps, ViewStyle } from 'react-native';
import { getGlassStyle } from '../../utils/theme';
import { useGlobalTheme } from './ThemeProvider';

// =============================================================================
// TYPES
// =============================================================================

export interface GlassViewProps extends ViewProps {
  /**
   * Glass effect intensity
   */
  intensity?: 'light' | 'medium' | 'strong';

  /**
   * Custom opacity override
   */
  opacity?: number;

  /**
   * Blur intensity for supported platforms
   */
  blurIntensity?: number;

  /**
   * Whether to use native blur (BlurView) or CSS backdrop-filter
   */
  useNativeBlur?: boolean;

  /**
   * Blur tint for BlurView (iOS/Android)
   */
  blurTint?: 'light' | 'dark' | 'default' | 'extraLight' | 'regular' | 'prominent';
}

// =============================================================================
// COMPONENT
// =============================================================================

export const GlassView: React.FC<GlassViewProps> = ({
  children,
  style,
  intensity = 'medium',
  opacity,
  blurIntensity,
  useNativeBlur = true,
  blurTint,
  ...props
}) => {
  const { currentTheme, isDarkMode } = useGlobalTheme();

  // Get glass style from theme system
  const glassStyle = getGlassStyle(currentTheme, intensity, opacity);

  // Determine blur settings
  const defaultBlurIntensity = blurIntensity ?? (
    intensity === 'light' ? 15 :
    intensity === 'medium' ? 25 : 35
  );

  const defaultBlurTint = blurTint ?? (isDarkMode ? 'dark' : 'light');

  // Combine styles
  const combinedStyle: ViewStyle = {
    ...glassStyle,
    ...(style && typeof style === 'object' && !Array.isArray(style) ? style : {}),
  };

  // Use BlurView for native platforms when available and requested
  if (useNativeBlur && Platform.OS !== 'web') {
    return (
      <BlurView
        intensity={defaultBlurIntensity}
        tint={defaultBlurTint}
        style={combinedStyle}
        {...props}
      >
        {children}
      </BlurView>
    );
  }

  // Fallback to regular View with glass styling
  return (
    <View style={combinedStyle} {...props}>
      {children}
    </View>
  );
};

// =============================================================================
// VARIANTS
// =============================================================================

/**
 * Light Glass View - Subtle glass effect
 */
export const LightGlassView: React.FC<Omit<GlassViewProps, 'intensity'>> = (props) => (
  <GlassView {...props} intensity="light" />
);

/**
 * Medium Glass View - Standard glass effect
 */
export const MediumGlassView: React.FC<Omit<GlassViewProps, 'intensity'>> = (props) => (
  <GlassView {...props} intensity="medium" />
);

/**
 * Strong Glass View - Prominent glass effect
 */
export const StrongGlassView: React.FC<Omit<GlassViewProps, 'intensity'>> = (props) => (
  <GlassView {...props} intensity="strong" />
);

// =============================================================================
// UTILITY HOOKS
// =============================================================================

/**
 * Hook to get glass styling for manual application
 */
export const useGlassStyle = (
  intensity: 'light' | 'medium' | 'strong' = 'medium',
  opacity?: number
) => {
  const { currentTheme } = useGlobalTheme();
  return getGlassStyle(currentTheme, intensity, opacity);
};

export default GlassView;
