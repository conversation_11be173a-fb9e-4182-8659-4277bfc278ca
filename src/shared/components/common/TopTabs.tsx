import React from 'react';
import { StyleSheet, Text, TextStyle, TouchableOpacity, View, ViewStyle } from 'react-native';
import { colors } from '../../utils/colors';

export type TopTabItem = {
  key: string;
  label: string;
  icon?: React.ReactNode; // Removed string support to discourage emoji usage
  badgeCount?: number;
};

export type TopTabsProps = {
  tabs: TopTabItem[];
  activeKey: string;
  onChange: (_key: string) => void;
  backgroundColor?: string;
  textColorActive?: string;
  textColorInactive?: string;
  containerStyle?: ViewStyle;
  tabStyle?: ViewStyle;
  labelStyle?: TextStyle;
  size?: 'sm' | 'md' | 'lg';
};

function TopTabs({
  tabs,
  activeKey,
  onChange,
  backgroundColor = colors.secondary, // Default to Secondary Lavender Purple #CBC3E3
  textColorActive = colors.textPrimary,
  textColorInactive = colors.white,
  containerStyle,
  tabStyle,
  labelStyle,
  size = 'md',
}: TopTabsProps) {
  const sizes = {
    sm: { paddingVertical: 8, fontSize: 12 },
    md: { paddingVertical: 12, fontSize: 12 },
    lg: { paddingVertical: 14, fontSize: 14 },
  } as const;

  const sizeCfg = sizes[size];

  return (
    <View style={[styles.container, { backgroundColor }, containerStyle]}>
      {tabs.map((tab) => {
        const isActive = tab.key === activeKey;
        const textColor = isActive ? textColorActive : textColorInactive;
        const pillStyle = isActive ? styles.activePill : undefined;

        const iconNode = (() => {
          if (!tab.icon) return null;
          if (React.isValidElement(tab.icon)) {
            // Set color on icon elements (lucide-react-native icons)
            return React.cloneElement(tab.icon as any, { color: textColor });
          }
          return tab.icon;
        })();

        return (
          <TouchableOpacity
            key={tab.key}
            style={[styles.pill, pillStyle, { paddingVertical: sizeCfg.paddingVertical }, tabStyle]}
            onPress={() => onChange(tab.key)}
            accessibilityRole="tab"
            accessibilityState={{ selected: isActive }}
          >
            <Text style={[styles.pillText, { color: textColor, fontSize: sizeCfg.fontSize }, labelStyle]}>
              {iconNode} {tab.label}
            </Text>
          </TouchableOpacity>
        );
      })}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    borderRadius: 16,
    padding: 4,
  },
  pill: {
    flex: 1,
    paddingHorizontal: 8,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
  },
  activePill: {
    backgroundColor: colors.white,
  },
  pillText: {
    fontWeight: '600',
  },
  iconEmoji: {
    // Keep emoji sizing balanced with label
  },
});

export default TopTabs;
