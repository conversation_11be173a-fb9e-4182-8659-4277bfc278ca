/**
 * Layout Components
 *
 * Reusable layout components for consistent page structure and organization.
 * These components provide standardized layouts for different screen types.
 *
 * <AUTHOR> Us Team
 * @version 2.0.0
 */

import React from 'react';
import {
    SafeAreaView,
    ScrollView,
    StatusBar,
    StyleSheet,
    Text,
    View
} from 'react-native';
import { colors } from '../../utils/colors';

// ============================================================================
// LAYOUT COMPONENTS
// ============================================================================

/**
 * Props for the ScreenLayout component
 */
interface ScreenLayoutProps {
  /** Screen content */
  children: React.ReactNode;
  /** Background color */
  backgroundColor?: string;
  /** Whether to use safe area */
  useSafeArea?: boolean;
  /** Status bar style */
  statusBarStyle?: 'light' | 'dark';
  /** Custom styles */
  style?: any;
}

/**
 * Base screen layout component with safe area and status bar handling
 */
export const ScreenLayout: React.FC<ScreenLayoutProps> = ({
  children,
  backgroundColor = colors.backgroundPrimary,
  useSafeArea = true,
  statusBarStyle = 'dark',
  style
}) => {
  const Wrapper = useSafeArea ? SafeAreaView : View;

  return (
    <>
      <StatusBar
        barStyle={statusBarStyle === 'light' ? 'light-content' : 'dark-content'}
        backgroundColor={backgroundColor}
      />
      <Wrapper style={[styles.screenLayout, { backgroundColor }, style]}>
        {children}
      </Wrapper>
    </>
  );
};

/**
 * Props for the HeaderLayout component
 */
interface HeaderLayoutProps {
  /** Header content */
  children: React.ReactNode;
  /** Background color */
  backgroundColor?: string;
  /** Header height */
  height?: number;
  /** Custom styles */
  style?: any;
}

/**
 * Header layout component with solid background
 */
export const HeaderLayout: React.FC<HeaderLayoutProps> = ({
  children,
  backgroundColor = colors.primary,
  height = 200,
  style
}) => {
  return (
    <View style={[styles.headerLayout, { height, backgroundColor }, style]}>
      {children}
    </View>
  );
};

/**
 * Props for the ContentLayout component
 */
interface ContentLayoutProps {
  /** Content */
  children: React.ReactNode;
  /** Whether content is scrollable */
  scrollable?: boolean;
  /** Content padding */
  padding?: number;
  /** Custom styles */
  style?: any;
}

/**
 * Content layout component with optional scrolling
 */
export const ContentLayout: React.FC<ContentLayoutProps> = ({
  children,
  scrollable = true,
  padding = 20,
  style
}) => {
  if (scrollable) {
    return (
      <ScrollView
        style={[styles.contentLayout, { padding }, style]}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
      >
        {children}
      </ScrollView>
    );
  }

  return (
    <View style={[styles.contentLayout, { padding }, style]}>
      {children}
    </View>
  );
};

/**
 * Props for the CardGrid component
 */
interface CardGridProps {
  /** Grid items */
  children: React.ReactNode;
  /** Number of columns */
  columns?: number;
  /** Item spacing */
  spacing?: number;
  /** Custom styles */
  style?: any;
}

/**
 * Grid layout component for cards
 */
export const CardGrid: React.FC<CardGridProps> = ({
  children,
  columns = 2,
  spacing = 16,
  style
}) => {
  return (
    <View style={[styles.cardGrid, { gap: spacing }, style]}>
      {React.Children.map(children, (child, _index) => (
        <View style={[styles.cardGridItem, { flex: 1 / columns }]}>
          {child}
        </View>
      ))}
    </View>
  );
};

/**
 * Props for the SectionLayout component
 */
interface SectionLayoutProps {
  /** Section title */
  title?: string;
  /** Section subtitle */
  subtitle?: string;
  /** Section content */
  children: React.ReactNode;
  /** Section margin */
  margin?: number;
  /** Custom styles */
  style?: any;
}

/**
 * Section layout component with title and content
 */
export const SectionLayout: React.FC<SectionLayoutProps> = ({
  title,
  subtitle,
  children,
  margin = 20,
  style
}) => {
  return (
    <View style={[styles.sectionLayout, { marginHorizontal: margin }, style]}>
      {title && (
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>{title}</Text>
          {subtitle && <Text style={styles.sectionSubtitle}>{subtitle}</Text>}
        </View>
      )}
      <View style={styles.sectionContent}>
        {children}
      </View>
    </View>
  );
};

/**
 * Props for the FlexRow component
 */
interface FlexRowProps {
  /** Row content */
  children: React.ReactNode;
  /** Justify content */
  justifyContent?: 'flex-start' | 'flex-end' | 'center' | 'space-between' | 'space-around' | 'space-evenly';
  /** Align items */
  alignItems?: 'flex-start' | 'flex-end' | 'center' | 'stretch' | 'baseline';
  /** Gap between items */
  gap?: number;
  /** Custom styles */
  style?: any;
}

/**
 * Flexible row layout component
 */
export const FlexRow: React.FC<FlexRowProps> = ({
  children,
  justifyContent = 'flex-start',
  alignItems = 'center',
  gap = 0,
  style
}) => {
  return (
    <View style={[
      styles.flexRow,
      {
        justifyContent,
        alignItems,
        gap
      },
      style
    ]}>
      {children}
    </View>
  );
};

/**
 * Props for the FlexColumn component
 */
interface FlexColumnProps {
  /** Column content */
  children: React.ReactNode;
  /** Justify content */
  justifyContent?: 'flex-start' | 'flex-end' | 'center' | 'space-between' | 'space-around' | 'space-evenly';
  /** Align items */
  alignItems?: 'flex-start' | 'flex-end' | 'center' | 'stretch' | 'baseline';
  /** Gap between items */
  gap?: number;
  /** Custom styles */
  style?: any;
}

/**
 * Flexible column layout component
 */
export const FlexColumn: React.FC<FlexColumnProps> = ({
  children,
  justifyContent = 'flex-start',
  alignItems = 'stretch',
  gap = 0,
  style
}) => {
  return (
    <View style={[
      styles.flexColumn,
      {
        justifyContent,
        alignItems,
        gap
      },
      style
    ]}>
      {children}
    </View>
  );
};

/**
 * Props for the Spacer component
 */
interface SpacerProps {
  /** Spacer height */
  height?: number;
  /** Spacer width */
  width?: number;
  /** Custom styles */
  style?: any;
}

/**
 * Spacer component for adding consistent spacing
 */
export const Spacer: React.FC<SpacerProps> = ({
  height = 16,
  width = 0,
  style
}) => {
  return <View style={[styles.spacer, { height, width }, style]} />;
};

/**
 * Props for the Divider component
 */
interface DividerProps {
  /** Divider color */
  color?: string;
  /** Divider thickness */
  thickness?: number;
  /** Divider margin */
  margin?: number;
  /** Custom styles */
  style?: any;
}

/**
 * Divider component for visual separation
 */
export const Divider: React.FC<DividerProps> = ({
  color = colors.borderLight,
  thickness = 1,
  margin = 20,
  style
}) => {
  return (
    <View style={[
      styles.divider,
      {
        backgroundColor: color,
        height: thickness,
        marginHorizontal: margin
      },
      style
    ]} />
  );
};

// ============================================================================
// STYLES
// ============================================================================

const styles = StyleSheet.create({
  screenLayout: {
    flex: 1,
  },
  headerLayout: {
    paddingTop: 60,
    paddingHorizontal: 32,
    paddingBottom: 32,
    alignItems: 'center',
    borderBottomLeftRadius: 24,
    borderBottomRightRadius: 24,
  },
  contentLayout: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 40,
  },
  cardGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  cardGridItem: {
    marginBottom: 16,
  },
  sectionLayout: {
    marginBottom: 24,
  },
  sectionHeader: {
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: colors.textPrimary,
    marginBottom: 4,
  },
  sectionSubtitle: {
    fontSize: 14,
    color: colors.textSecondary,
    lineHeight: 20,
  },
  sectionContent: {
    // Content styles handled by children
  },
  flexRow: {
    flexDirection: 'row',
  },
  flexColumn: {
    flexDirection: 'column',
  },
  spacer: {
    // Spacer styles handled by props
  },
  divider: {
    // Divider styles handled by props
  },
});
