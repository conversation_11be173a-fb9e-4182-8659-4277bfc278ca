/**
 * ThemedView Component
 *
 * A simple wrapper that applies theme-aware styling to any View.
 * Use this instead of regular View components to ensure consistent theming.
 *
 * <AUTHOR> Us Team
 * @version 1.0.0
 */

import React from 'react';
import { View, ViewProps, ViewStyle } from 'react-native';
import { useAppSettings } from '../../../journeys/planning/useAppSettings';

interface ThemedViewProps extends ViewProps {
  /**
   * Whether to apply the theme background color
   */
  themed?: boolean;
  /**
   * Use surface color instead of background color
   */
  surface?: boolean;
}

/**
 * ThemedView component that automatically applies theme colors
 */
export const ThemedView: React.FC<ThemedViewProps> = ({
  style,
  themed = true,
  surface = false,
  ...props
}) => {
  const { currentTheme } = useAppSettings();

  // Critical safety check for theme properties
  const safeTheme = currentTheme || {
    surface: '#FFFFFF',
    background: '#FAFAFA'
  };

  const themedStyle: ViewStyle = themed
    ? {
        backgroundColor: surface ? safeTheme.surface : safeTheme.background,
      }
    : {};

  return <View style={[themedStyle, style]} {...props} />;
};

export default ThemedView;
