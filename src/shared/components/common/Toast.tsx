/**
 * Toast Component
 *
 * Toast notification system for providing user feedback.
 * Supports different types (success, error, warning, info) and auto-dismiss.
 *
 * @version 3.0.0
 * <AUTHOR> Us Team
 */

import { AlertTriangle, Check, Info, X } from 'lucide-react-native';
import React, { useEffect, useRef } from 'react';
import {
    Animated,
    Dimensions,
    StyleSheet,
    Text,
    TouchableOpacity,
    View
} from 'react-native';
import { tokens } from '../../utils/theme';
import type { Theme } from '../../utils/theme/colors';

// =============================================================================
// TYPES
// =============================================================================

export type ToastType = 'success' | 'error' | 'warning' | 'info';

export interface ToastProps {
  id: string;
  type: ToastType;
  title: string;
  message?: string;
  duration?: number;
  onDismiss: (_id: string) => void;
  theme: Theme;
}

// =============================================================================
// COMPONENT
// =============================================================================

export const Toast: React.FC<ToastProps> = ({
  id,
  type,
  title,
  message,
  duration = 4000,
  onDismiss,
  theme,
}) => {
  const slideAnim = useRef(new Animated.Value(-100)).current;
  const opacityAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    // Slide in animation
    Animated.parallel([
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.timing(opacityAnim, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }),
    ]).start();

    // Auto dismiss
    if (duration > 0) {
      const timer = setTimeout(() => {
        handleDismiss();
      }, duration);

      return () => clearTimeout(timer);
    }
  }, []);

  const handleDismiss = () => {
    Animated.parallel([
      Animated.timing(slideAnim, {
        toValue: -100,
        duration: 250,
        useNativeDriver: true,
      }),
      Animated.timing(opacityAnim, {
        toValue: 0,
        duration: 250,
        useNativeDriver: true,
      }),
    ]).start(() => {
      onDismiss(id);
    });
  };

  const getToastConfig = () => {
    switch (type) {
      case 'success':
        return {
          icon: <Check size={20} color={theme.textInverse} />,
          backgroundColor: theme.success,
          borderColor: theme.primary,
        };
      case 'error':
        return {
          icon: <X size={20} color={theme.textInverse} />,
          backgroundColor: theme.error,
          borderColor: theme.primary,
        };
      case 'warning':
        return {
          icon: <AlertTriangle size={20} color={theme.textInverse} />,
          backgroundColor: theme.warning,
          borderColor: theme.primary,
        };
      case 'info':
      default:
        return {
          icon: <Info size={20} color={theme.textInverse} />,
          backgroundColor: theme.info,
          borderColor: theme.primary,
        };
    }
  };

  const config = getToastConfig();

  return (
    <Animated.View
      style={[
        styles.container,
        {
          backgroundColor: config.backgroundColor,
          borderColor: config.borderColor,
          transform: [{ translateY: slideAnim }],
          opacity: opacityAnim,
        },
      ]}
    >
      <View style={styles.iconContainer}>
        {config.icon}
      </View>

      <View style={styles.content}>
        <Text style={[styles.title, { color: theme.textInverse }]}>
          {title}
        </Text>
        {message && (
          <Text style={[styles.message, { color: theme.textInverse }]}>
            {message}
          </Text>
        )}
      </View>

      <TouchableOpacity
        style={styles.dismissButton}
        onPress={handleDismiss}
        hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
      >
        <X size={16} color={theme.textInverse} />
      </TouchableOpacity>
    </Animated.View>
  );
};

// =============================================================================
// TOAST MANAGER
// =============================================================================

export interface ToastData {
  id: string;
  type: ToastType;
  title: string;
  message?: string;
  duration?: number;
}

interface ToastManagerProps {
  toasts: ToastData[];
  onDismiss: (_id: string) => void;
  theme: Theme;
}

export const ToastManager: React.FC<ToastManagerProps> = ({
  toasts,
  onDismiss,
  theme,
}) => {
  if (toasts.length === 0) return null;

  return (
    <View style={styles.manager}>
      {toasts.map((toast) => (
        <Toast
          key={toast.id}
          {...toast}
          onDismiss={onDismiss}
          theme={theme}
        />
      ))}
    </View>
  );
};

// =============================================================================
// STYLES
// =============================================================================

const { width } = Dimensions.get('window');

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: tokens.spacing.md,
    marginHorizontal: tokens.spacing.md,
    marginBottom: tokens.spacing.sm,
    borderRadius: tokens.radii.md,
    borderWidth: tokens.borders.thin,
    maxWidth: width - tokens.spacing.lg * 2,
    ...tokens.shadows.md,
  },
  iconContainer: {
    marginRight: tokens.spacing.sm,
  },
  content: {
    flex: 1,
  },
  title: {
    fontSize: tokens.typography.fontSize.md,
    fontWeight: tokens.typography.fontWeight.semibold,
    lineHeight: tokens.typography.lineHeight.tight,
  },
  message: {
    fontSize: tokens.typography.fontSize.sm,
    lineHeight: tokens.typography.lineHeight.normal,
    marginTop: tokens.spacing.xxs,
    opacity: tokens.opacity.subtle,
  },
  dismissButton: {
    marginLeft: tokens.spacing.sm,
    padding: tokens.spacing.xs,
  },
  manager: {
    position: 'absolute',
    top: 60, // Below status bar and header
    left: 0,
    right: 0,
    zIndex: 9999,
    pointerEvents: 'box-none',
  },
});

export default Toast;
