/**
 * Story Data Configuration
 *
 * Centralized configuration for story sections and data structure.
 * Used across story components to maintain consistency.
 *
 * <AUTHOR> Us Team
 * @version 2.0.0
 */

import type { StoryData } from '../../types';

import { StorySection } from './StoryComponents';
import { colors } from '../../utils/colors';

/**
 * Default story sections configuration
 * This defines the structure and content for all story sections
 */
export const getDefaultStorySections = (storyData: any): StorySection[] => [
  {
    id: 'first-meeting',
    title: 'Our First Meeting',
    subtitle: 'The beginning of everything',
    content: storyData.firstMeeting || "We met at a local coffee shop on a rainy Tuesday afternoon. I was reading my favorite book, and you asked if the seat across from me was taken. Little did we know that simple question would change our lives forever.",
    photos: storyData.firstMeetingPhotos || [],
    icon: '💕',
    color: colors.accentPink,
    date: 'The day that changed everything',
    field: 'firstMeeting',
    photoField: 'firstMeetingPhotos',
    placeholder: 'Tell us about the first time you saw each other...',
  },
  {
    id: 'knew-loved',
    title: 'I Knew I Loved You When...',
    subtitle: 'That magical moment',
    content: storyData.knewILovedYou || "It was during our third date when we got caught in a sudden downpour. You took off your jacket to cover both of us, and as we ran laughing through the rain, I realized I had never felt so safe and happy with anyone before.",
    photos: storyData.knewILovedYouPhotos || [],
    icon: '✨',
    color: colors.secondary,
    date: 'When love became undeniable',
    field: 'knewILovedYou',
    photoField: 'knewILovedYouPhotos',
    placeholder: 'Describe the moment you realized you were in love...',
  },
  {
    id: 'first-kiss',
    title: 'Our First Kiss',
    subtitle: 'A moment to remember',
    content: storyData.firstKiss || "Under the stars on our rooftop, after hours of talking about our dreams and fears. It was gentle, sweet, and felt like coming home.",
    photos: storyData.firstKissPhotos || [],
    icon: '💋',
    color: colors.accentPink,
    date: 'Under the starlit sky',
    field: 'firstKiss',
    photoField: 'firstKissPhotos',
    placeholder: 'Share the story of your first kiss...',
  },
  {
    id: 'inside-jokes',
    title: 'Our Favorite Inside Jokes',
    subtitle: 'Secrets only we share',
    content: storyData.insideJokes || "The 'pancake incident' where we both tried to flip pancakes at the same time and ended up with batter everywhere. Now whenever something goes wrong, we just say 'pancake moment' and laugh.",
    photos: storyData.insideJokesPhotos || [],
    icon: '😄',
    color: colors.primary,
    date: 'The moments that make us laugh',
    field: 'insideJokes',
    photoField: 'insideJokesPhotos',
    placeholder: 'Share your favorite inside jokes and funny moments...',
  },
  {
    id: 'most-romantic',
    title: 'The Most Romantic Thing We\'ve Ever Done',
    subtitle: 'Love in action',
    content: storyData.mostRomantic || "You surprised me with a weekend getaway to the place where we had our first date. You had recreated the entire scene, down to the same coffee order and the book I was reading. It was the most thoughtful thing anyone has ever done for me.",
    photos: storyData.mostRomanticPhotos || [],
    icon: '🌹',
    color: colors.darkerPink,
    date: 'When romance reached new heights',
    field: 'mostRomantic',
    photoField: 'mostRomanticPhotos',
    placeholder: 'Describe your most romantic moment together...',
  },
  {
    id: 'biggest-challenge',
    title: 'Our Biggest Challenge',
    subtitle: 'Growing stronger together',
    content: storyData.biggestChallenge || "When I had to move across the country for work, we faced 6 months of long-distance. We overcame it by setting up daily video calls, planning visits, and focusing on our future together. It made us stronger and taught us to communicate better.",
    photos: storyData.biggestChallengePhotos || [],
    icon: '💪',
    color: colors.secondary,
    date: 'How we overcame obstacles',
    field: 'biggestChallenge',
    photoField: 'biggestChallengePhotos',
    placeholder: 'Share how you faced and overcame challenges together...',
  },
  {
    id: 'best-memories',
    title: 'Our Best Memories Together',
    subtitle: 'Treasures of our journey',
    content: storyData.bestMemories || "Our spontaneous road trip where we got lost and discovered a beautiful hidden waterfall. The time we cooked together and made a complete mess of the kitchen. Every morning we spend cuddling and planning our day. The way you look at me when you think I'm not watching.",
    photos: storyData.bestMemoriesPhotos || [],
    icon: '🌟',
    color: colors.accentPink,
    date: 'The moments that bring us joy',
    field: 'bestMemories',
    photoField: 'bestMemoriesPhotos',
    placeholder: 'Share your favorite memories and experiences...',
  },
];

/**
 * Story section field mapping
 * Maps section IDs to their corresponding data fields
 */
export const STORY_FIELD_MAPPING = {
  'first-meeting': 'firstMeeting',
  'knew-loved': 'knewILovedYou',
  'first-kiss': 'firstKiss',
  'inside-jokes': 'insideJokes',
  'most-romantic': 'mostRomantic',
  'biggest-challenge': 'biggestChallenge',
  'best-memories': 'bestMemories',
} as const;

/**
 * Story photo field mapping
 * Maps section IDs to their corresponding photo fields
 */
export const STORY_PHOTO_FIELD_MAPPING = {
  'first-meeting': 'firstMeetingPhotos',
  'knew-loved': 'knewILovedYouPhotos',
  'first-kiss': 'firstKissPhotos',
  'inside-jokes': 'insideJokesPhotos',
  'most-romantic': 'mostRomanticPhotos',
  'biggest-challenge': 'biggestChallengePhotos',
  'best-memories': 'bestMemoriesPhotos',
} as const;

/**
 * Story validation configuration
 */
export const STORY_VALIDATION = {
  MAX_TEXT_LENGTH: 2000,
  MAX_PHOTOS_PER_SECTION: 5,
  REQUIRED_FIELDS: ['firstMeeting', 'knewILovedYou', 'firstKiss'],
} as const;

/**
 * Story completion calculation
 * Returns the percentage of completed story sections
 */
export const calculateStoryCompletion = (storyData: any): number => {
  const sections = getDefaultStorySections(storyData);
  const completedSections = sections.filter(section => 
    section.content && section.content.trim() !== ''
  ).length;
  
  return Math.round((completedSections / sections.length) * 100);
};

/**
 * Get story section by ID
 */
export const getStorySectionById = (storyData: any, sectionId: string): StorySection | undefined => {
  const sections = getDefaultStorySections(storyData);
  return sections.find(section => section.id === sectionId);
};

/**
 * Get next incomplete section
 */
export const getNextIncompleteSection = (storyData: StoryData): StorySection | undefined => {
  const sections = getDefaultStorySections(storyData);
  return sections.find(section => !section.content || section.content.trim() === '');
};

/**
 * Get completed sections count
 */
export const getCompletedSectionsCount = (storyData: StoryData): number => {
  const sections = getDefaultStorySections(storyData);
  return sections.filter(section => section.content && section.content.trim() !== '').length;
};
