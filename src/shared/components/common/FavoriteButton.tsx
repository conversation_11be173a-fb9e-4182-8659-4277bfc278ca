/**
 * Reusable Favorite Button Component
 * Provides consistent favorite functionality across all features
 */

import { Heart } from 'lucide-react-native';
import React, { useCallback } from 'react';
import { Alert, StyleSheet, TouchableOpacity, ViewStyle } from 'react-native';
import { colors } from '../../../utils/colors';
import { useFavorites } from '../../hooks/useFavorites';
import { FavoriteItemType, FavoriteMetadata } from '../../services/favoritesService';

export interface FavoriteButtonProps {
  /** Unique composite ID for the item (e.g., "global:123" or "user:456") */
  compositeId: string;
  /** Content type for the favorites system */
  contentType: FavoriteItemType;
  /** Additional metadata to store with the favorite */
  metadata?: FavoriteMetadata;
  /** Size of the heart icon */
  size?: 'small' | 'medium' | 'large';
  /** Custom style for the button container */
  style?: ViewStyle;
  /** Callback when favorite state changes */
  onToggle?: (_isFavorited: boolean, _compositeId: string) => void;
  /** Whether to show loading state */
  showLoading?: boolean;
  /** Custom colors for the heart */
  customColors?: {
    favorited?: string;
    unfavorited?: string;
    loading?: string;
  };
  /** Accessibility label */
  accessibilityLabel?: string;
  /** Whether the button is disabled */
  disabled?: boolean;
}

const ICON_SIZES = {
  small: 16,
  medium: 20,
  large: 24,
} as const;

/**
 * Reusable FavoriteButton component that works with any content type
 * Provides consistent heart toggle functionality across the app
 */
export const FavoriteButton: React.FC<FavoriteButtonProps> = ({
  compositeId,
  contentType,
  metadata,
  size = 'medium',
  style,
  onToggle,
  showLoading = true,
  customColors,
  accessibilityLabel,
  disabled = false,
}) => {
  const {
    isFavorited,
    loadingStates,
    debouncedToggleFavorite,
  } = useFavorites();

  // Check if this item is favorited
  const isFavoritedState = isFavorited(compositeId, contentType);

  // Check if this item is currently loading
  const isLoading = showLoading && loadingStates[`${compositeId}:${contentType}`];

  // Handle toggle with error handling and callbacks
  const handleToggle = useCallback(async () => {
    if (disabled) return;

    try {
      const success = await debouncedToggleFavorite(
        compositeId,
        contentType,
        undefined,
        metadata
      );

      if (success && onToggle) {
        // The state will be updated by the hook, so we need to get the new state
        const newState = !isFavoritedState;
        onToggle(newState, compositeId);
      }
    } catch (err) {
      console.error('FavoriteButton: Failed to toggle favorite:', err);
      Alert.alert(
        'Error',
        'Failed to update favorite. Please try again.',
        [{ text: 'OK' }]
      );
    }
  }, [
    disabled,
    debouncedToggleFavorite,
    compositeId,
    contentType,
    metadata,
    onToggle,
    isFavoritedState
  ]);

  // Determine heart color based on state
  const getHeartColor = () => {
    if (isLoading) {
      return customColors?.loading || colors.textSecondary;
    }
    if (isFavoritedState) {
      return customColors?.favorited || colors.accent1;
    }
    return customColors?.unfavorited || colors.textSecondary;
  };

  // Generate accessibility label
  const getAccessibilityLabel = () => {
    if (accessibilityLabel) return accessibilityLabel;

    const action = isFavoritedState ? 'Remove from' : 'Add to';
    const contentName = contentType.replace('_', ' ');
    return `${action} favorites for this ${contentName}`;
  };

  return (
    <TouchableOpacity
      style={[styles.container, style]}
      onPress={handleToggle}
      disabled={disabled || isLoading}
      accessibilityRole="button"
      accessibilityLabel={getAccessibilityLabel()}
      accessibilityHint={isLoading ? 'Loading' : undefined}
      activeOpacity={0.7}
    >
      <Heart
        size={ICON_SIZES[size]}
        color={getHeartColor()}
        fill={isFavoritedState ? getHeartColor() : 'transparent'}
        strokeWidth={2}
      />
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 8,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    minWidth: 36,
    minHeight: 36,
  },
});

export default FavoriteButton;
