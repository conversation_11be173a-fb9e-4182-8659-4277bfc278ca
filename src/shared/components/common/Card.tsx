/**
 * Card Component
 *
 * Unified card component that provides consistent styling and behavior
 * across the application. Replaces scattered card implementations.
 *
 * @version 3.0.0
 * <AUTHOR> Us Team
 */

import React from 'react';
import { StyleSheet, Text, TouchableOpacity, View, ViewStyle } from 'react-native';
import { getShadowStyle, tokens } from '../../utils/theme';
import type { Theme } from '../../utils/theme/colors';

// =============================================================================
// TYPES
// =============================================================================

export interface CardProps {
  // Content
  children: React.ReactNode;

  // Behavior
  onPress?: () => void;
  disabled?: boolean;

  // Appearance
  variant?: 'default' | 'elevated' | 'outlined' | 'filled';
  padding?: 'none' | 'sm' | 'md' | 'lg';
  radius?: 'none' | 'sm' | 'md' | 'lg' | 'xl';

  // Styling
  style?: ViewStyle;

  // Accessibility
  accessibilityLabel?: string;
  accessibilityHint?: string;
  testID?: string;
}

// =============================================================================
// COMPONENT
// =============================================================================

// Internal component that requires theme prop
const CardInternal: React.FC<CardProps & { theme: Theme }> = ({
  children,
  onPress,
  disabled = false,
  variant = 'default',
  padding = 'md',
  radius = 'md',
  style,
  accessibilityLabel,
  accessibilityHint,
  testID,
  theme,
}) => {
  // Get base styles
  const getVariantStyle = (): ViewStyle => {
    switch (variant) {
      case 'elevated':
        return {
          backgroundColor: theme.surface,
          ...getShadowStyle('md'),
        };
      case 'outlined':
        return {
          backgroundColor: theme.surface,
          borderWidth: tokens.borders.thin,
          borderColor: theme.border,
        };
      case 'filled':
        return {
          backgroundColor: theme.backgroundSecondary,
        };
      case 'default':
      default:
        return {
          backgroundColor: theme.surface,
          ...getShadowStyle('sm'),
        };
    }
  };

  const getPaddingStyle = (): ViewStyle => {
    switch (padding) {
      case 'none':
        return {};
      case 'sm':
        return { padding: tokens.spacing.sm };
      case 'lg':
        return { padding: tokens.spacing.lg };
      case 'md':
      default:
        return { padding: tokens.spacing.md };
    }
  };

  const getRadiusStyle = (): ViewStyle => {
    switch (radius) {
      case 'none':
        return { borderRadius: tokens.radii.none };
      case 'sm':
        return { borderRadius: tokens.radii.sm };
      case 'lg':
        return { borderRadius: tokens.radii.lg };
      case 'xl':
        return { borderRadius: tokens.radii.xl };
      case 'md':
      default:
        return { borderRadius: tokens.radii.md };
    }
  };

  // Build final style
  const finalStyle: ViewStyle = {
    ...getVariantStyle(),
    ...getPaddingStyle(),
    ...getRadiusStyle(),
    ...(disabled && { opacity: tokens.opacity.disabled }),
    ...style,
  };

  // Render as touchable if onPress is provided
  if (onPress) {
    return (
      <TouchableOpacity
        style={finalStyle}
        onPress={onPress}
        disabled={disabled}
        accessibilityLabel={accessibilityLabel}
        accessibilityHint={accessibilityHint}
        accessibilityRole="button"
        testID={testID}
      >
        {children}
      </TouchableOpacity>
    );
  }

  // Render as regular view
  return (
    <View
      style={finalStyle}
      accessibilityLabel={accessibilityLabel}
      testID={testID}
    >
      {children}
    </View>
  );
};

// =============================================================================
// THEME-AWARE WRAPPER
// =============================================================================

import { useGlobalTheme } from './ThemeProvider';

/**
 * Card component with automatic theme injection
 */
export const Card: React.FC<CardProps> = (props) => {
  const { currentTheme } = useGlobalTheme();
  return <CardInternal {...props} theme={currentTheme} />;
};

// =============================================================================
// VARIANTS
// =============================================================================

/**
 * Elevated Card - With shadow
 */
export const ElevatedCard: React.FC<Omit<CardProps, 'variant'>> = (props) => (
  <Card {...props} variant="elevated" />
);

/**
 * Outlined Card - With border
 */
export const OutlinedCard: React.FC<Omit<CardProps, 'variant'>> = (props) => (
  <Card {...props} variant="outlined" />
);

/**
 * Filled Card - With background color
 */
export const FilledCard: React.FC<Omit<CardProps, 'variant'>> = (props) => (
  <Card {...props} variant="filled" />
);

// =============================================================================
// SPECIALIZED CARDS
// =============================================================================

/**
 * Stat Card - For displaying statistics
 */
export interface StatCardProps extends Omit<CardProps, 'children'> {
  icon: React.ReactNode;
  value: string | number;
  label: string;
  theme: Theme;
}

export const StatCard: React.FC<Omit<StatCardProps, 'theme'>> = ({
  icon,
  value,
  label,
  style,
  ...props
}) => {
  const { currentTheme } = useGlobalTheme();

  // Critical safety check for theme properties
  const safeTheme = currentTheme || {
    textPrimary: '#393939',
    textSecondary: '#6B7280'
  };

  return (
    <Card
      {...props}
      style={StyleSheet.flatten([
        {
          alignItems: 'center' as const,
          minHeight: 100,
          justifyContent: 'center' as const,
        },
        style,
      ])}
    >
      <View style={{ marginBottom: tokens.spacing.xs }}>
        {icon}
      </View>
      <Text
        style={{
          fontSize: tokens.typography.fontSize['2xl'],
          fontWeight: tokens.typography.fontWeight.bold,
          color: safeTheme.textPrimary,
          marginBottom: tokens.spacing.xxs,
        }}
      >
        {value}
      </Text>
      <Text
        style={{
          fontSize: tokens.typography.fontSize.sm,
          fontWeight: tokens.typography.fontWeight.medium,
          color: safeTheme.textSecondary,
          textAlign: 'center',
        }}
      >
        {label}
      </Text>
    </Card>
  );
};

export default Card;
