/**
 * Enhanced Week Screen Layout Component
 *
 * Modular layout component that dynamically renders activities based on configuration.
 * Eliminates code duplication across all week screens.
 *
 * <AUTHOR> Us Team
 * @version 2.0.0
 */

import React, { useCallback, useEffect, useMemo, useState } from 'react';
import {
    Dimensions,
    ScrollView,
    StyleSheet,
    Text,
    TouchableOpacity,
    View,
} from 'react-native';

import { useLocalSearchParams } from 'expo-router';
import { ArrowLeft, CheckCircle } from 'lucide-react-native';
import { usePointsSystemSupabase } from '../../../journeys/progress/usePointsSystemSupabase';
import { colors } from '../../utils/colors';
import {
    ActivityCompletionData,
    activityRegistry,
    EnhancedWeekScreenLayoutProps
} from '../activities';

const { width } = Dimensions.get('window');

/**
 * Enhanced Week Screen Layout Component
 *
 * Dynamically renders activities based on week configuration.
 * Eliminates code duplication and provides modular activity system.
 */
export const EnhancedWeekScreenLayout: React.FC<EnhancedWeekScreenLayoutProps> = ({
  weekConfig,
  currentStep,
  setCurrentStep,
  completedSections,
  onActivityComplete,
  onError,
  onNavigateBack,
  initialData = {},
}) => {
  const params = useLocalSearchParams();
  const { addPoints } = usePointsSystemSupabase();
  const [activityData, setActivityData] = useState<Record<string, any>>(initialData);

  // Handle URL parameters for direct section navigation
  useEffect(() => {
    if (params.section) {
      const sectionIndex = parseInt(params.section as string);
      if (sectionIndex >= 0 && sectionIndex < weekConfig.activityIds.length) {
        setCurrentStep(sectionIndex);
      }
    }
  }, [params.section, weekConfig.activityIds.length, setCurrentStep]);

  // Get current activity configuration
  const currentActivityId = weekConfig.activityIds[currentStep];
  const currentActivityConfig = useMemo(() => {
    return activityRegistry.getActivity(currentActivityId);
  }, [currentActivityId]);

  // Handle activity completion
  const handleActivityComplete = useCallback(async (data: ActivityCompletionData) => {
    try {
      // Award points
      const pointsToAward = Math.round(data.points * (weekConfig.pointsMultiplier || 1));
      await addPoints(pointsToAward, 'activity');

      // Update activity data
      setActivityData(prev => ({
        ...prev,
        [currentActivityId]: {
          ...prev[currentActivityId],
          ...data.data,
          completed: true,
          completedAt: data.completedAt,
        },
      }));

      // Call parent completion handler
      await onActivityComplete(currentStep, data);

      // Navigate if requested
      if (data.shouldNavigate) {
        return; // Let the activity handle navigation
      }

      // Auto-advance to next step if not the last one
      if (currentStep < weekConfig.activityIds.length - 1) {
        setCurrentStep(currentStep + 1);
      }
    } catch (error) {
      onError(error instanceof Error ? error : new Error('Failed to complete activity'));
    }
  }, [currentStep, currentActivityId, weekConfig, addPoints, onActivityComplete, onError, setCurrentStep]);

  // Handle activity errors
  const handleActivityError = useCallback((error: Error) => {
    console.error(`Activity error in ${currentActivityId}:`, error);
    onError(error);
  }, [currentActivityId, onError]);

  // Get progress percentage
  const getProgressPercentage = (): number => {
    if (weekConfig.activityIds.length === 0) return 0;
    return Math.round(((currentStep + 1) / weekConfig.activityIds.length) * 100);
  };

  // Render step navigation
  const renderStepNavigation = () => (
    <View style={styles.stepNavigation}>
      {weekConfig.activityIds.map((activityId, index) => {
        const activity = activityRegistry.getActivity(activityId);
        const stepTitle = weekConfig.stepTitles?.[index] || activity?.title || 'Activity';

        return (
          <TouchableOpacity
            key={activityId}
            style={[
              styles.stepButton,
              index === currentStep && styles.stepButtonActive,
              completedSections[index] && styles.stepButtonCompleted,
            ]}
            onPress={() => setCurrentStep(index)}
          >
            <View style={styles.stepIcon}>
              {completedSections[index] ? (
                <CheckCircle size={20} color={colors.white} />
              ) : (
                activity?.icon
              )}
            </View>
            <Text style={[
              styles.stepText,
              index === currentStep && styles.stepTextActive,
              completedSections[index] && styles.stepTextCompleted,
            ]}>
              {stepTitle}
            </Text>
          </TouchableOpacity>
        );
      })}
    </View>
  );

  // Render progress bar
  const renderProgressBar = () => (
    <View style={styles.progressContainer}>
      <View style={styles.progressBar}>
        <View
          style={[
            styles.progressFill,
            { width: `${getProgressPercentage()}%` }
          ]}
        />
      </View>
      <Text style={styles.progressText}>
        {getProgressPercentage()}% Complete
      </Text>
    </View>
  );

  // Render current activity
  const renderCurrentActivity = () => {
    if (!currentActivityConfig) {
      return (
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>
            Activity "{currentActivityId}" not found
          </Text>
        </View>
      );
    }

    const ActivityComponent = currentActivityConfig.component;

    return (
      <ActivityComponent
        onComplete={handleActivityComplete}
        onError={handleActivityError}
        initialData={activityData[currentActivityId]}
        isActive={true}
        weekNumber={weekConfig.weekNumber}
        stepIndex={currentStep}
      />
    );
  };

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={[styles.header, { backgroundColor: weekConfig.headerColor }]}>
        <View style={styles.headerContent}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={onNavigateBack}
          >
            <ArrowLeft size={24} color={colors.white} />
          </TouchableOpacity>

          <View style={styles.headerText}>
            <Text style={styles.weekTitle}>Week {weekConfig.weekNumber}</Text>
            <Text style={styles.activityTitle}>
              {weekConfig.title}
            </Text>
          </View>

          <View style={styles.placeholder} />
        </View>

        {renderStepNavigation()}
        {renderProgressBar()}
      </View>

      {/* Content */}
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {renderCurrentActivity()}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  header: {
    paddingTop: 50,
    paddingBottom: 20,
    paddingHorizontal: 20,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  backButton: {
    padding: 8,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
  },
  headerText: {
    flex: 1,
    alignItems: 'center',
  },
  weekTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: colors.white,
    marginBottom: 4,
  },
  activityTitle: {
    fontSize: 16,
    color: colors.white,
    opacity: 0.9,
  },
  placeholder: {
    width: 40, // Same width as back button for centering
  },
  stepNavigation: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 20,
  },
  stepButton: {
    alignItems: 'center',
    padding: 12,
    borderRadius: 12,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    minWidth: 80,
  },
  stepButtonActive: {
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
  },
  stepButtonCompleted: {
    backgroundColor: colors.success,
  },
  stepIcon: {
    marginBottom: 8,
  },
  stepText: {
    fontSize: 12,
    color: colors.white,
    textAlign: 'center',
    fontWeight: '500',
  },
  stepTextActive: {
    fontWeight: 'bold',
  },
  stepTextCompleted: {
    fontWeight: 'bold',
  },
  progressContainer: {
    alignItems: 'center',
  },
  progressBar: {
    width: width - 40,
    height: 6,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 3,
    overflow: 'hidden',
    marginBottom: 8,
  },
  progressFill: {
    height: '100%',
    backgroundColor: colors.white,
    borderRadius: 3,
  },
  progressText: {
    fontSize: 14,
    color: colors.white,
    fontWeight: '500',
  },
  content: {
    flex: 1,
  },
  errorContainer: {
    padding: 20,
    alignItems: 'center',
  },
  errorText: {
    fontSize: 16,
    color: colors.error,
    textAlign: 'center',
  },
});
