/**
 * Activity System Index
 * Main entry point for the modular activity system
 */

// Export the universal activity container
export { ActivityContainer } from './ActivityContainer';

// Export activity components and registry
export {
    LoveLanguageQuizActivity, WouldYouRatherActivity, activityRegistry, registerAllActivities
} from './activities';

// Export types
export type {
    ActivityCategory, ActivityComponent, ActivityContainerProps, ActivityDifficulty, ActivityError,
    ActivityEvent,
    ActivityEventType, ActivityFilterOptions, ActivityMetadata, ActivityProps, ActivityQueryOptions, ActivityRegistry, ActivityResult, ActivitySortOptions, ActivityState,
    ActivityStatus, ActivityTheme
} from '../../types/activity.types';
