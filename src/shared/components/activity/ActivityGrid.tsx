/**
 * Activity Grid Component
 * Displays a grid of available activities with filtering and search
 */

import { Filter, Play, Search } from 'lucide-react-native';
import React, { useMemo, useState } from 'react';
import {
    FlatList,
    ScrollView,
    StyleSheet,
    Text,
    TextInput,
    TouchableOpacity,
    View
} from 'react-native';

import { activityRegistry } from '../../services/activity/activityRegistry';
import {
    ActivityCategory,
    ActivityComponent,
    ActivityDifficulty
} from '../../types/activity.types';
import { colors } from '../../utils/colors';

interface ActivityGridProps {
  onActivityPress: (_activity: ActivityComponent) => void;
  selectedCategory?: ActivityCategory;
  onCategoryChange?: (_category: ActivityCategory | 'all') => void;
}

export function ActivityGrid({
  onActivityPress,
  selectedCategory = 'all',
  onCategoryChange
}: ActivityGridProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedDifficulty, setSelectedDifficulty] = useState<ActivityDifficulty | 'all'>('all');
  const [showFilters, setShowFilters] = useState(false);

  // Get all available activities
  const allActivities = activityRegistry.getAvailableActivities();

  // Filter activities based on search and filters
  const filteredActivities = useMemo(() => {
    return allActivities.filter(activity => {
      // Category filter
      if (selectedCategory !== 'all' && activity.category !== selectedCategory) {
        return false;
      }

      // Difficulty filter
      if (selectedDifficulty !== 'all' && activity.metadata.difficulty !== selectedDifficulty) {
        return false;
      }

      // Search filter
      if (searchQuery) {
        const query = searchQuery.toLowerCase();
        const matchesName = activity.name.toLowerCase().includes(query);
        const matchesDescription = activity.metadata.description.toLowerCase().includes(query);
        const matchesTags = (activity.metadata.tags || []).some(tag =>
          tag.toLowerCase().includes(query)
        );

        if (!matchesName && !matchesDescription && !matchesTags) {
          return false;
        }
      }

      return true;
    });
  }, [allActivities, selectedCategory, selectedDifficulty, searchQuery]);

  // Get category statistics
  const categoryStats = useMemo(() => {
    const stats: Record<ActivityCategory, number> = {
      'all': 0,
      'getting-to-know': 0,
      'communication': 0,
      'fun': 0,
      'reflection': 0,
      'skills': 0,
      'planning': 0
    };

    allActivities.forEach(activity => {
      stats[activity.category]++;
      stats['all']++;
    });

    return stats;
  }, [allActivities]);

  const getDifficultyColor = (difficulty: ActivityDifficulty) => {
    switch (difficulty) {
      case 'easy': return colors.success;
      case 'medium': return colors.warning;
      case 'hard': return colors.error;
      default: return colors.textSecondary;
    }
  };

  const getCategoryColor = (category: ActivityCategory) => {
    switch (category) {
      case 'getting-to-know': return colors.primary;
      case 'communication': return colors.secondary;
      case 'fun': return colors.success;
      case 'reflection': return colors.warning;
      case 'skills': return colors.error;
      case 'planning': return colors.info;
      default: return colors.primary;
    }
  };

  const renderActivityCard = ({ item: activity }: { item: ActivityComponent }) => (
    <TouchableOpacity
      style={styles.activityCard}
      onPress={() => onActivityPress(activity)}
      activeOpacity={0.8}
    >
      <View
        style={[styles.activityCardGradient, { backgroundColor: getCategoryColor(activity.category) }]}
      >
        <View style={styles.activityCardHeader}>
          <View style={styles.activityIconContainer}>
            {activity.metadata.icon}
          </View>
          <View style={styles.activityDifficulty}>
            <View style={[
              styles.difficultyDot,
              { backgroundColor: getDifficultyColor(activity.metadata.difficulty) }
            ]} />
            <Text style={styles.difficultyText}>
              {activity.metadata.difficulty.charAt(0).toUpperCase() + activity.metadata.difficulty.slice(1)}
            </Text>
          </View>
        </View>

        <Text style={styles.activityTitle}>{activity.name}</Text>
        <Text style={styles.activityDescription}>{activity.metadata.description}</Text>

        <View style={styles.activityFooter}>
          <View style={styles.activityMeta}>
            <Text style={styles.activityMetaText}>⏱️ {activity.metadata.duration}</Text>
            <Text style={styles.activityMetaText}>👥 {activity.metadata.players}</Text>
            <Text style={styles.activityMetaText}>⭐ {activity.metadata.points} pts</Text>
          </View>

          <TouchableOpacity
            style={styles.playButton}
            onPress={() => onActivityPress(activity)}
          >
            <Play size={20} color={colors.white} />
            <Text style={styles.playButtonText}>Play</Text>
          </TouchableOpacity>
        </View>
      </View>
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
      {/* Search Bar */}
      <View style={styles.searchContainer}>
        <View style={styles.searchInputContainer}>
          <Search size={20} color={colors.textSecondary} />
          <TextInput
            style={styles.searchInput}
            placeholder="Search activities..."
            placeholderTextColor={colors.textSecondary}
            value={searchQuery}
            onChangeText={setSearchQuery}
          />
        </View>
        <TouchableOpacity
          style={styles.filterButton}
          onPress={() => setShowFilters(!showFilters)}
        >
          <Filter size={20} color={colors.primary} />
        </TouchableOpacity>
      </View>

      {/* Category Filter */}
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        style={styles.categoryScroll}
        contentContainerStyle={styles.categoryContainer}
      >
        <TouchableOpacity
          style={[
            styles.categoryPill,
            selectedCategory === 'all' && styles.categoryPillActive
          ]}
          onPress={() => onCategoryChange?.('all')}
        >
          <Text style={[
            styles.categoryPillText,
            selectedCategory === 'all' && styles.categoryPillTextActive
          ]}>
            All Activities ({allActivities.length})
          </Text>
        </TouchableOpacity>

        {Object.entries(categoryStats).map(([category, count]) => (
          <TouchableOpacity
            key={category}
            style={[
              styles.categoryPill,
              selectedCategory === category && styles.categoryPillActive
            ]}
            onPress={() => onCategoryChange?.(category as ActivityCategory)}
          >
            <Text style={[
              styles.categoryPillText,
              selectedCategory === category && styles.categoryPillTextActive
            ]}>
              {category.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase())} ({count})
            </Text>
          </TouchableOpacity>
        ))}
      </ScrollView>

      {/* Difficulty Filter */}
      {showFilters && (
        <View style={styles.difficultyFilter}>
          <Text style={styles.filterLabel}>Difficulty:</Text>
          <View style={styles.difficultyButtons}>
            {(['all', 'easy', 'medium', 'hard'] as const).map((difficulty) => (
              <TouchableOpacity
                key={difficulty}
                style={[
                  styles.difficultyButton,
                  selectedDifficulty === difficulty && styles.difficultyButtonActive
                ]}
                onPress={() => setSelectedDifficulty(difficulty)}
              >
                <Text style={[
                  styles.difficultyButtonText,
                  selectedDifficulty === difficulty && styles.difficultyButtonTextActive
                ]}>
                  {difficulty === 'all' ? 'All' : difficulty.charAt(0).toUpperCase() + difficulty.slice(1)}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>
      )}

      {/* Activities Grid */}
      {filteredActivities.length === 0 ? (
        <View style={styles.emptyState}>
          <Text style={styles.emptyStateText}>No activities match your filters</Text>
          <Text style={styles.emptyStateSubtext}>Try adjusting your search or filter selection</Text>
        </View>
      ) : (
        <FlatList
          data={filteredActivities}
          renderItem={renderActivityCard}
          keyExtractor={(item) => item.id}
          numColumns={1}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.activitiesList}
        />
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    gap: 12,
  },
  searchInputContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.backgroundSecondary,
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    gap: 8,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: colors.textPrimary,
  },
  filterButton: {
    padding: 12,
    backgroundColor: colors.backgroundSecondary,
    borderRadius: 12,
  },
  categoryScroll: {
    maxHeight: 60,
  },
  categoryContainer: {
    paddingHorizontal: 20,
    gap: 12,
  },
  categoryPill: {
    backgroundColor: colors.backgroundSecondary,
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: colors.borderLight,
  },
  categoryPillActive: {
    backgroundColor: colors.primary,
    borderColor: colors.primary,
  },
  categoryPillText: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.textSecondary,
  },
  categoryPillTextActive: {
    color: colors.white,
  },
  difficultyFilter: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    marginVertical: 16,
    gap: 12,
  },
  filterLabel: {
    fontSize: 16,
    fontWeight: '500',
    color: colors.textPrimary,
  },
  difficultyButtons: {
    flexDirection: 'row',
    gap: 8,
  },
  difficultyButton: {
    backgroundColor: colors.backgroundSecondary,
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    borderWidth: 1,
    borderColor: colors.borderLight,
  },
  difficultyButtonActive: {
    backgroundColor: colors.primary,
    borderColor: colors.primary,
  },
  difficultyButtonText: {
    fontSize: 12,
    fontWeight: '500',
    color: colors.textSecondary,
  },
  difficultyButtonTextActive: {
    color: colors.white,
  },
  activitiesList: {
    padding: 20,
    gap: 16,
  },
  activityCard: {
    borderRadius: 16,
    overflow: 'hidden',
    elevation: 3,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
  },
  activityCardGradient: {
    padding: 20,
  },
  activityCardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  activityIconContainer: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 12,
    padding: 8,
  },
  activityDifficulty: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  difficultyDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
  },
  difficultyText: {
    fontSize: 12,
    fontWeight: '600',
    color: colors.white,
  },
  activityTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.white,
    marginBottom: 8,
  },
  activityDescription: {
    fontSize: 14,
    color: colors.white,
    lineHeight: 20,
    marginBottom: 16,
  },
  activityFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  activityMeta: {
    flexDirection: 'row',
    gap: 12,
  },
  activityMetaText: {
    fontSize: 12,
    color: colors.white,
    opacity: 0.9,
  },
  playButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 20,
  },
  playButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.white,
  },
  emptyState: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 40,
  },
  emptyStateText: {
    fontSize: 16,
    color: colors.textSecondary,
    textAlign: 'center',
    marginBottom: 8,
  },
  emptyStateSubtext: {
    fontSize: 14,
    color: colors.textTertiary,
    textAlign: 'center',
  },
});
