/**
 * Would You Rather Activity Component
 * Example implementation of a modular activity component
 */

import { ArrowRight, Heart } from 'lucide-react-native';
import React, { useCallback, useState } from 'react';
import {
    Dimensions,
    StyleSheet,
    Text,
    TouchableOpacity,
    View
} from 'react-native';

import {
    ActivityError,
    ActivityProps,
    ActivityResult
} from '../../../types/activity.types';
import { colors } from '../../../utils/colors';

const { width: _width } = Dimensions.get('window');

interface WouldYouRatherQuestion {
  id: string;
  question: string;
  optionA: string;
  optionB: string;
  category: string;
  difficulty: 'easy' | 'medium' | 'hard';
}

interface WouldYouRatherResponse {
  questionId: string;
  partner1Choice: 'A' | 'B';
  partner2Choice: 'A' | 'B';
  partner1Reason?: string;
  partner2Reason?: string;
  timestamp: number;
}

export function WouldYouRatherActivity({
  activityId,
  userId,
  coupleId,
  onComplete,
  onExit,
  onError,
  theme = 'default',
  customData: _customData = {}
}: ActivityProps) {
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [responses, setResponses] = useState<WouldYouRatherResponse[]>([]);
  const [currentResponse, setCurrentResponse] = useState<Partial<WouldYouRatherResponse>>({});
  const [phase, setPhase] = useState<'question' | 'reason' | 'reveal'>('question');
  const [_isLoading, _setIsLoading] = useState(false);

  // Sample questions - in a real implementation, these would come from a service
  const questions: WouldYouRatherQuestion[] = [
    {
      id: 'wyr-1',
      question: 'Would you rather...',
      optionA: 'Have the ability to fly',
      optionB: 'Have the ability to read minds',
      category: 'fun',
      difficulty: 'easy'
    },
    {
      id: 'wyr-2',
      question: 'Would you rather...',
      optionA: 'Live in a big city',
      optionB: 'Live in a small town',
      category: 'lifestyle',
      difficulty: 'easy'
    },
    {
      id: 'wyr-3',
      question: 'Would you rather...',
      optionA: 'Have unlimited money but no friends',
      optionB: 'Have unlimited friends but no money',
      category: 'values',
      difficulty: 'medium'
    }
  ];

  const currentQuestion = questions[currentQuestionIndex];

  // Get theme colors
  const getThemeColors = useCallback(() => {
    switch (theme) {
      case 'romantic':
        return colors.solidColors.modulePink;
      case 'playful':
        return colors.solidColors.moduleOrange;
      case 'reflective':
        return colors.solidColors.moduleBlue;
      default:
        return colors.primary;
    }
  }, [theme]);

  const themeColors = getThemeColors();

  // Handle option selection
  const handleOptionSelect = useCallback((choice: 'A' | 'B') => {
    setCurrentResponse(prev => ({
      ...prev,
      questionId: currentQuestion.id,
      partner1Choice: choice,
      timestamp: Date.now()
    }));

    setPhase('reason');
  }, [currentQuestion.id]);

  // Handle reason submission
  const handleReasonSubmit = useCallback((reason: string) => {
    setCurrentResponse(prev => ({
      ...prev,
      partner1Reason: reason
    }));

    // Simulate partner 2 response (in real app, this would come from partner)
    const partner2Choice: 'A' | 'B' = Math.random() > 0.5 ? 'A' : 'B';
    const partner2Reason = `Partner's reason for choosing ${partner2Choice === 'A' ? currentQuestion.optionA : currentQuestion.optionB}`;

    const completeResponse: WouldYouRatherResponse = {
      ...currentResponse,
      partner1Choice: currentResponse.partner1Choice!,
      partner2Choice,
      partner2Reason,
      timestamp: Date.now()
    } as WouldYouRatherResponse;

    setResponses(prev => [...prev, completeResponse]);
    setPhase('reveal');
  }, [currentResponse, currentQuestion]);

  // Handle next question or completion
  const handleNext = useCallback(() => {
    if (currentQuestionIndex < questions.length - 1) {
      setCurrentQuestionIndex(prev => prev + 1);
      setCurrentResponse({});
      setPhase('question');
    } else {
      // Activity completed
      const score = responses.reduce((total, response) => {
        return total + (response.partner1Choice === response.partner2Choice ? 10 : 0);
      }, 0);

      const result: ActivityResult = {
        activityId,
        userId,
        coupleId,
        score,
        points: 25, // Base points for completing the activity
        completedAt: new Date().toISOString(),
        data: {
          responses,
          totalQuestions: questions.length,
          matchingAnswers: responses.filter(r => r.partner1Choice === r.partner2Choice).length,
          activityType: 'would-you-rather'
        }
      };

      onComplete(result);
    }
  }, [currentQuestionIndex, questions.length, responses, activityId, userId, coupleId, onComplete]);

  // Handle error
  const _handleError = useCallback((error: string) => {
    const activityError: ActivityError = {
      activityId,
      userId,
      error,
      code: 'ACTIVITY_ERROR',
      timestamp: new Date().toISOString(),
      context: {
        currentQuestion: currentQuestion.id,
        phase,
        responsesCount: responses.length
      }
    };

    onError(activityError);
  }, [activityId, userId, currentQuestion, phase, responses.length, onError]);

  // Render question phase
  const renderQuestionPhase = () => (
    <View style={styles.content}>
      <View style={styles.questionContainer}>
        <Text style={styles.questionTitle}>{currentQuestion.question}</Text>
        <Text style={styles.optionText}>{currentQuestion.optionA}</Text>
        <Text style={styles.orText}>OR</Text>
        <Text style={styles.optionText}>{currentQuestion.optionB}</Text>
      </View>

      <View style={styles.optionsContainer}>
        <TouchableOpacity
          style={[styles.optionButton, { backgroundColor: themeColors[0] }]}
          onPress={() => handleOptionSelect('A')}
        >
          <Text style={styles.optionButtonText}>Choose A</Text>
          <ArrowRight size={20} color={colors.white} />
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.optionButton, { backgroundColor: themeColors[1] }]}
          onPress={() => handleOptionSelect('B')}
        >
          <Text style={styles.optionButtonText}>Choose B</Text>
          <ArrowRight size={20} color={colors.white} />
        </TouchableOpacity>
      </View>

      <View style={styles.progressContainer}>
        <Text style={styles.progressText}>
          Question {currentQuestionIndex + 1} of {questions.length}
        </Text>
      </View>
    </View>
  );

  // Render reason phase
  const renderReasonPhase = () => (
    <View style={styles.content}>
      <View style={styles.questionContainer}>
        <Text style={styles.questionTitle}>Why did you choose that?</Text>
        <Text style={styles.selectedOption}>
          {currentResponse.partner1Choice === 'A' ? currentQuestion.optionA : currentQuestion.optionB}
        </Text>
      </View>

      <View style={styles.reasonContainer}>
        <TouchableOpacity
          style={[styles.reasonButton, { backgroundColor: themeColors[0] }]}
          onPress={() => handleReasonSubmit('It sounds more interesting')}
        >
          <Text style={styles.reasonButtonText}>It sounds more interesting</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.reasonButton, { backgroundColor: themeColors[1] }]}
          onPress={() => handleReasonSubmit('It aligns with my values')}
        >
          <Text style={styles.reasonButtonText}>It aligns with my values</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.reasonButton, { backgroundColor: themeColors[2] }]}
          onPress={() => handleReasonSubmit('I would enjoy it more')}
        >
          <Text style={styles.reasonButtonText}>I would enjoy it more</Text>
        </TouchableOpacity>
      </View>
    </View>
  );

  // Render reveal phase
  const renderRevealPhase = () => {
    const lastResponse = responses[responses.length - 1];
    const isMatch = lastResponse.partner1Choice === lastResponse.partner2Choice;

    return (
      <View style={styles.content}>
        <View style={styles.revealContainer}>
          <Text style={styles.revealTitle}>Results</Text>

          <View style={styles.choiceContainer}>
            <View style={styles.choiceCard}>
              <Text style={styles.choiceLabel}>Your Choice</Text>
              <Text style={styles.choiceText}>
                {lastResponse.partner1Choice === 'A' ? currentQuestion.optionA : currentQuestion.optionB}
              </Text>
              <Text style={styles.reasonText}>{lastResponse.partner1Reason}</Text>
            </View>

            <View style={styles.choiceCard}>
              <Text style={styles.choiceLabel}>Partner's Choice</Text>
              <Text style={styles.choiceText}>
                {lastResponse.partner2Choice === 'A' ? currentQuestion.optionA : currentQuestion.optionB}
              </Text>
              <Text style={styles.reasonText}>{lastResponse.partner2Reason}</Text>
            </View>
          </View>

          <View style={[styles.matchResult, { backgroundColor: isMatch ? colors.success : colors.warning }]}>
            <Text style={styles.matchResultText}>
              {isMatch ? '🎉 You matched!' : '🤔 Different choices!'}
            </Text>
          </View>
        </View>

        <TouchableOpacity
          style={[styles.nextButton, { backgroundColor: themeColors[0] }]}
          onPress={handleNext}
        >
          <Text style={styles.nextButtonText}>
            {currentQuestionIndex < questions.length - 1 ? 'Next Question' : 'Complete Activity'}
          </Text>
          <ArrowRight size={20} color={colors.white} />
        </TouchableOpacity>
      </View>
    );
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <View style={styles.headerLeft}>
          <Heart size={24} color={colors.white} />
          <Text style={styles.headerTitle}>Would You Rather</Text>
        </View>
        <TouchableOpacity onPress={onExit} style={styles.exitButton}>
          <Text style={styles.exitButtonText}>Exit</Text>
        </TouchableOpacity>
      </View>

      {phase === 'question' && renderQuestionPhase()}
      {phase === 'reason' && renderReasonPhase()}
      {phase === 'reveal' && renderRevealPhase()}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: colors.primary,
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: colors.white,
  },
  exitButton: {
    padding: 8,
  },
  exitButtonText: {
    fontSize: 16,
    color: colors.white,
    fontWeight: '600',
  },
  content: {
    flex: 1,
    padding: 20,
    justifyContent: 'space-between',
  },
  questionContainer: {
    alignItems: 'center',
    marginBottom: 40,
  },
  questionTitle: {
    fontSize: 24,
    fontWeight: '700',
    color: colors.textPrimary,
    textAlign: 'center',
    marginBottom: 24,
  },
  optionText: {
    fontSize: 18,
    color: colors.textPrimary,
    textAlign: 'center',
    marginVertical: 8,
    lineHeight: 24,
  },
  orText: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.textSecondary,
    marginVertical: 12,
  },
  optionsContainer: {
    gap: 16,
    marginBottom: 40,
  },
  optionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderRadius: 12,
    gap: 8,
  },
  optionButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.white,
  },
  progressContainer: {
    alignItems: 'center',
  },
  progressText: {
    fontSize: 14,
    color: colors.textSecondary,
  },
  reasonContainer: {
    gap: 12,
    marginBottom: 40,
  },
  reasonButton: {
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 8,
  },
  reasonButtonText: {
    fontSize: 16,
    color: colors.white,
    textAlign: 'center',
  },
  selectedOption: {
    fontSize: 16,
    color: colors.primary,
    textAlign: 'center',
    fontWeight: '600',
    marginTop: 8,
  },
  revealContainer: {
    flex: 1,
    justifyContent: 'center',
  },
  revealTitle: {
    fontSize: 24,
    fontWeight: '700',
    color: colors.textPrimary,
    textAlign: 'center',
    marginBottom: 24,
  },
  choiceContainer: {
    gap: 16,
    marginBottom: 24,
  },
  choiceCard: {
    backgroundColor: colors.backgroundSecondary,
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: colors.borderLight,
  },
  choiceLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.textSecondary,
    marginBottom: 8,
  },
  choiceText: {
    fontSize: 16,
    color: colors.textPrimary,
    marginBottom: 8,
  },
  reasonText: {
    fontSize: 14,
    color: colors.textSecondary,
    fontStyle: 'italic',
  },
  matchResult: {
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
    marginBottom: 24,
  },
  matchResultText: {
    fontSize: 18,
    fontWeight: '700',
    color: colors.white,
  },
  nextButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderRadius: 12,
    gap: 8,
  },
  nextButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.white,
  },
});
