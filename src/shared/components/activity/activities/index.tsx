/**
 * Activity Components Index
 * Exports and registers all available activity components
 */

import { Heart, Users } from 'lucide-react-native';
import React from 'react';
import { activityRegistry } from '../../../services/activity/activityRegistry';
import { ActivityComponent } from '../../../types/activity.types';

// Import activity components
import { LoveLanguageQuizActivity } from './LoveLanguageQuizActivity';
import { WouldYouRatherActivity } from './WouldYouRatherActivity';

// Register Would You Rather Activity
const wouldYouRatherActivity: ActivityComponent = {
  id: 'would-you-rather',
  name: 'Would You Rather',
  category: 'getting-to-know',
  metadata: {
    id: 'would-you-rather',
    name: 'Would You Rather',
    description: 'Fun dilemmas that reveal preferences and spark interesting conversations.',
    category: 'getting-to-know',
    difficulty: 'easy',
    duration: '10-15 min',
    players: 2,
    points: 10,
    icon: <Users size={24} color="#FFFFFF" />,
    theme: 'playful',
    tags: ['conversation', 'preferences', 'fun'],
    isAvailable: true,
    version: '1.0.0',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  component: WouldYouRatherActivity,
  supabaseTable: 'would_you_rather_responses'
};

// Register Love Language Quiz Activity
const loveLanguageQuizActivity: ActivityComponent = {
  id: 'love-language-quiz',
  name: 'Love Language Quiz',
  category: 'getting-to-know',
  metadata: {
    id: 'love-language-quiz',
    name: 'Love Language Quiz',
    description: 'Discover your love languages and how to express love better.',
    category: 'getting-to-know',
    difficulty: 'medium',
    duration: '15-20 min',
    players: 2,
    points: 15,
    icon: <Heart size={24} color="#FFFFFF" />,
    theme: 'romantic',
    tags: ['love', 'relationships', 'communication'],
    isAvailable: true,
    version: '1.0.0',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  component: LoveLanguageQuizActivity,
  supabaseTable: 'love_language_quiz_results'
};

// Register all activities
export function registerAllActivities() {
  activityRegistry.registerActivity(wouldYouRatherActivity);
  activityRegistry.registerActivity(loveLanguageQuizActivity);

  // All activities registered successfully
}

// Export individual activities
export { LoveLanguageQuizActivity, WouldYouRatherActivity };

// Export activity registry for direct access
    export { activityRegistry };
