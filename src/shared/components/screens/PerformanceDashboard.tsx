/**
 * Performance Dashboard
 * Enterprise-grade performance monitoring dashboard for 4M+ users
 */

import React, { useState, useEffect, useCallback } from 'react';
import { View, Text, ScrollView, StyleSheet, TouchableOpacity, Alert } from 'react-native';
import { performanceMonitor } from '../../services/performanceMonitor';
import { cacheManager } from '../../services/storage/cacheManager';
import { subscriptionManager } from '../../services/system/subscriptionManager';
import { performanceOptimizationService } from '../../../journeys/progress/performanceOptimizationService';
import { logger } from '../../utils/logger';

interface PerformanceDashboardProps {
  visible?: boolean;
  onClose?: () => void;
}

export const PerformanceDashboard: React.FC<PerformanceDashboardProps> = ({
  visible = false,
  onClose
}) => {
  const [metrics, setMetrics] = useState<any>(null);
  const [insights, setInsights] = useState<any>(null);
  const [refreshing, setRefreshing] = useState(false);
  const [autoRefresh, setAutoRefresh] = useState(true);

  /**
   * Collect comprehensive performance metrics
   */
  const collectMetrics = useCallback(async () => {
    try {
      setRefreshing(true);

      const [
        performanceSummary,
        performanceInsights,
        cacheMetrics,
        subscriptionMetrics,
        optimizationMetrics
      ] = await Promise.all([
        Promise.resolve(performanceMonitor.getPerformanceSummary()),
        Promise.resolve(performanceMonitor.getPerformanceInsights()),
        Promise.resolve(cacheManager.getMetrics()),
        Promise.resolve(subscriptionManager.getHealthMetrics()),
        Promise.resolve(performanceOptimizationService.getPerformanceMetrics())
      ]);

      setMetrics({
        performance: performanceSummary,
        cache: cacheMetrics,
        subscriptions: subscriptionMetrics,
        optimization: optimizationMetrics
      });

      setInsights(performanceInsights);

    } catch (error) {
      logger.error('Error collecting performance metrics:', error);
      Alert.alert('Error', 'Failed to collect performance metrics');
    } finally {
      setRefreshing(false);
    }
  }, []);

  /**
   * Export performance data for analysis
   */
  const exportData = useCallback(() => {
    try {
      const exportData = performanceMonitor.exportPerformanceData();
      
      // In a real app, you'd save this to a file or send to analytics
      logger.info('Performance data exported:', exportData);
      Alert.alert('Export Complete', 'Performance data has been exported to logs');
    } catch (error) {
      logger.error('Error exporting performance data:', error);
      Alert.alert('Error', 'Failed to export performance data');
    }
  }, []);

  /**
   * Clear performance metrics
   */
  const clearMetrics = useCallback(() => {
    Alert.alert(
      'Clear Metrics',
      'Are you sure you want to clear all performance metrics?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Clear',
          style: 'destructive',
          onPress: () => {
            performanceOptimizationService.clearOldMetrics();
            cacheManager.clear();
            collectMetrics();
            Alert.alert('Success', 'Performance metrics cleared');
          }
        }
      ]
    );
  }, [collectMetrics]);

  /**
   * Auto-refresh metrics
   */
  useEffect(() => {
    if (!visible || !autoRefresh) return;

    const interval = setInterval(collectMetrics, 5000); // Every 5 seconds
    return () => clearInterval(interval);
  }, [visible, autoRefresh, collectMetrics]);

  /**
   * Initial metrics collection
   */
  useEffect(() => {
    if (visible) {
      collectMetrics();
    }
  }, [visible, collectMetrics]);

  if (!visible || !metrics) {
    return null;
  }

  const renderMetricCard = (title: string, value: string | number, unit?: string, color?: string) => (
    <View style={[styles.metricCard, color && { borderLeftColor: color }]}>
      <Text style={styles.metricTitle}>{title}</Text>
      <Text style={[styles.metricValue, color && { color }]}>
        {value}{unit && ` ${unit}`}
      </Text>
    </View>
  );

  const renderInsightCard = (insight: string, type: 'info' | 'warning' | 'error') => {
    const insightStyleMap = {
      info: styles.insightInfo,
      warning: styles.insightWarning,
      error: styles.insightError
    };
    
    return (
      <View style={[styles.insightCard, insightStyleMap[type]]}>
        <Text style={styles.insightText}>{insight}</Text>
      </View>
    );
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Performance Dashboard</Text>
        <View style={styles.headerButtons}>
          <TouchableOpacity
            style={[styles.button, styles.refreshButton]}
            onPress={collectMetrics}
            disabled={refreshing}
          >
            <Text style={styles.buttonText}>
              {refreshing ? 'Refreshing...' : 'Refresh'}
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.button, autoRefresh ? styles.activeButton : styles.inactiveButton]}
            onPress={() => setAutoRefresh(!autoRefresh)}
          >
            <Text style={styles.buttonText}>
              Auto: {autoRefresh ? 'ON' : 'OFF'}
            </Text>
          </TouchableOpacity>
          {onClose && (
            <TouchableOpacity style={[styles.button, styles.closeButton]} onPress={onClose}>
              <Text style={styles.buttonText}>Close</Text>
            </TouchableOpacity>
          )}
        </View>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Performance Score */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Overall Performance Score</Text>
          <View style={styles.scoreContainer}>
            <Text style={[
              styles.scoreText,
              { color: insights?.score >= 80 ? '#4CAF50' : insights?.score >= 60 ? '#FF9800' : '#F44336' }
            ]}>
              {insights?.score || 0}/100
            </Text>
          </View>
        </View>

        {/* Key Metrics */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Key Metrics</Text>
          <View style={styles.metricsGrid}>
            {renderMetricCard(
              'Avg Query Time',
              metrics.optimization?.averageQueryTime?.toFixed(1) || '0',
              'ms',
              metrics.optimization?.averageQueryTime > 1000 ? '#F44336' : '#4CAF50'
            )}
            {renderMetricCard(
              'Cache Hit Rate',
              metrics.cache?.hitRate?.toFixed(1) || '0',
              '%',
              metrics.cache?.hitRate > 70 ? '#4CAF50' : '#FF9800'
            )}
            {renderMetricCard(
              'Active Subscriptions',
              metrics.subscriptions?.activeSubscriptions || 0,
              '',
              '#2196F3'
            )}
            {renderMetricCard(
              'Memory Usage',
              metrics.performance?.system?.memoryUsage?.toFixed(1) || '0',
              'MB',
              metrics.performance?.system?.memoryUsage > 100 ? '#F44336' : '#4CAF50'
            )}
          </View>
        </View>

        {/* Cache Performance */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Cache Performance</Text>
          <View style={styles.metricsGrid}>
            {renderMetricCard('Total Entries', metrics.cache?.totalEntries || 0)}
            {renderMetricCard('Total Size', `${(metrics.cache?.totalSize / 1024 / 1024)?.toFixed(1) || 0}`, 'MB')}
            {renderMetricCard('Evictions', metrics.cache?.evictionCount || 0)}
            {renderMetricCard('Avg Access Time', `${metrics.cache?.averageAccessTime?.toFixed(1) || 0}`, 'ms')}
          </View>
        </View>

        {/* Network Performance */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Network Performance</Text>
          <View style={styles.metricsGrid}>
            {renderMetricCard('Total Requests', metrics.performance?.network?.requestCount || 0)}
            {renderMetricCard('Avg Response Time', `${metrics.performance?.network?.averageResponseTime?.toFixed(1) || 0}`, 'ms')}
            {renderMetricCard('Error Rate', `${metrics.performance?.network?.errorRate?.toFixed(1) || 0}`, '%')}
            {renderMetricCard('Slow Requests', metrics.performance?.network?.slowRequests || 0)}
          </View>
        </View>

        {/* Subscription Health */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Subscription Health</Text>
          <View style={styles.metricsGrid}>
            {renderMetricCard('Total Subscriptions', metrics.subscriptions?.totalSubscriptions || 0)}
            {renderMetricCard('Connection State', metrics.subscriptions?.connectionState || 'Unknown')}
            {renderMetricCard('Total Errors', metrics.subscriptions?.totalErrors || 0)}
            {renderMetricCard('Avg Message Rate', `${metrics.subscriptions?.averageMessageRate?.toFixed(1) || 0}`, '/min')}
          </View>
        </View>

        {/* Performance Insights */}
        {insights && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Performance Insights</Text>
            {insights.insights?.map((insight: string, index: number) => (
              <View key={index}>
                {renderInsightCard(insight, 'warning')}
              </View>
            ))}
          </View>
        )}

        {/* Recommendations */}
        {insights?.recommendations && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Recommendations</Text>
            {insights.recommendations.map((recommendation: string, index: number) => (
              <View key={index}>
                {renderInsightCard(recommendation, 'info')}
              </View>
            ))}
          </View>
        )}

        {/* Actions */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Actions</Text>
          <View style={styles.actionButtons}>
            <TouchableOpacity style={[styles.button, styles.exportButton]} onPress={exportData}>
              <Text style={styles.buttonText}>Export Data</Text>
            </TouchableOpacity>
            <TouchableOpacity style={[styles.button, styles.clearButton]} onPress={clearMetrics}>
              <Text style={styles.buttonText}>Clear Metrics</Text>
            </TouchableOpacity>
          </View>
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
  },
  headerButtons: {
    flexDirection: 'row',
    gap: 8,
  },
  button: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
    minWidth: 60,
    alignItems: 'center',
  },
  refreshButton: {
    backgroundColor: '#2196F3',
  },
  activeButton: {
    backgroundColor: '#4CAF50',
  },
  inactiveButton: {
    backgroundColor: '#757575',
  },
  closeButton: {
    backgroundColor: '#F44336',
  },
  exportButton: {
    backgroundColor: '#FF9800',
  },
  clearButton: {
    backgroundColor: '#9C27B0',
  },
  buttonText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: '600',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 12,
  },
  scoreContainer: {
    alignItems: 'center',
    padding: 20,
    backgroundColor: '#fff',
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  scoreText: {
    fontSize: 36,
    fontWeight: 'bold',
  },
  metricsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  metricCard: {
    flex: 1,
    minWidth: 150,
    padding: 16,
    backgroundColor: '#fff',
    borderRadius: 8,
    borderLeftWidth: 4,
    borderLeftColor: '#2196F3',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  metricTitle: {
    fontSize: 12,
    color: '#666',
    marginBottom: 4,
  },
  metricValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  insightCard: {
    padding: 12,
    borderRadius: 8,
    marginBottom: 8,
    borderLeftWidth: 4,
  },
  insightInfo: {
    backgroundColor: '#E3F2FD',
    borderLeftColor: '#2196F3',
  },
  insightWarning: {
    backgroundColor: '#FFF3E0',
    borderLeftColor: '#FF9800',
  },
  insightError: {
    backgroundColor: '#FFEBEE',
    borderLeftColor: '#F44336',
  },
  insightText: {
    fontSize: 14,
    color: '#333',
  },
  actionButtons: {
    flexDirection: 'row',
    gap: 12,
  },
});

export default PerformanceDashboard;
