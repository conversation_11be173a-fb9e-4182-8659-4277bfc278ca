import React, { useEffect, useState } from 'react';
import { <PERSON><PERSON>, ScrollView, Text, TouchableOpacity, View } from 'react-native';
import { CoupleMilestone, MilestoneTemplate } from '../../../journeys/memories/milestoneService';
import { useRelationshipMilestones, ValidationResult } from '../../../journeys/memories/useRelationshipMilestones';
import { colors } from '../../../utils/colors';
import { logger } from '../../utils/logger';
import { MilestoneFormField } from './MilestoneFormField';

interface MilestoneFormProps {
  template: MilestoneTemplate;
  milestone?: CoupleMilestone;
  onSave?: (_milestoneId: string, _data: Record<string, any>) => void;
  onCancel?: () => void;
  disabled?: boolean;
}

export const MilestoneForm: React.FC<MilestoneFormProps> = ({
  template,
  milestone,
  onSave,
  onCancel,
  disabled = false,
}) => {
  const { validateMilestoneData, updateMilestone } = useRelationshipMilestones();

  // Form state
  const [formData, setFormData] = useState<Record<string, any>>({});
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [validation, setValidation] = useState<ValidationResult | null>(null);

  // Initialize form data from existing milestone
  useEffect(() => {
    if (milestone?.milestone_data) {
      setFormData(milestone.milestone_data);
    }
  }, [milestone]);

  // Validate form data whenever it changes
  useEffect(() => {
    const validationResult = validateMilestoneData(template, formData);
    setValidation(validationResult);

    // Clear field errors that are now valid
    const newErrors = { ...errors };
    Object.keys(newErrors).forEach(key => {
      if (!validationResult.errors.some(error => error.includes(key))) {
        delete newErrors[key];
      }
    });
    setErrors(newErrors);
  }, [formData, template, validateMilestoneData]);

  const handleFieldChange = (fieldKey: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [fieldKey]: value,
    }));

    // Clear field-specific error when user starts typing
    if (errors[fieldKey]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[fieldKey];
        return newErrors;
      });
    }
  };

  const handleSubmit = async () => {
    if (!milestone?.id) {
      Alert.alert('Error', 'No milestone found to update');
      return;
    }

    // Final validation
    const validationResult = validateMilestoneData(template, formData);

    if (!validationResult.isValid) {
      // Map validation errors to field errors
      const fieldErrors: Record<string, string> = {};
      validationResult.errors.forEach(error => {
        // Try to match error to field (simple approach)
        const field = template.field_schema.fields.find(f =>
          error.toLowerCase().includes(f.label.toLowerCase())
        );
        if (field) {
          fieldErrors[field.key] = error;
        }
      });
      setErrors(fieldErrors);

      Alert.alert(
        'Validation Error',
        'Please fix the errors below before saving.',
        [{ text: 'OK' }]
      );
      return;
    }

    setIsSubmitting(true);
    try {
      await updateMilestone(milestone.id, formData);

      // Call onSave callback if provided
      onSave?.(milestone.id, formData);

      Alert.alert(
        'Success!',
        'Your milestone has been saved successfully.',
        [{ text: 'OK', onPress: onCancel }]
      );

      logger.info(`Milestone ${template.milestone_key} saved successfully`);
    } catch (error) {
      logger.error('Error saving milestone:', error);
      Alert.alert(
        'Error',
        'Failed to save milestone. Please try again.',
        [{ text: 'OK' }]
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  const renderFormFields = () => {
    if (!template.field_schema?.fields) {
      return (
        <Text style={styles.noFieldsText}>
          No fields configured for this milestone.
        </Text>
      );
    }

    return template.field_schema.fields.map((field) => (
      <MilestoneFormField
        key={field.key}
        field={field}
        value={formData[field.key]}
        onChange={(value) => handleFieldChange(field.key, value)}
        error={errors[field.key]}
        disabled={disabled}
      />
    ));
  };

  const renderValidationSummary = () => {
    if (!validation) return null;

    return (
      <View style={styles.validationContainer}>
        {validation.errors.length > 0 && (
          <View style={styles.errorsContainer}>
            <Text style={styles.validationTitle}>Please fix these issues:</Text>
            {validation.errors.map((error, index) => (
              <Text key={index} style={styles.errorText}>• {error}</Text>
            ))}
          </View>
        )}

        {validation.warnings.length > 0 && (
          <View style={styles.warningsContainer}>
            <Text style={styles.validationTitle}>Suggestions:</Text>
            {validation.warnings.map((warning, index) => (
              <Text key={index} style={styles.warningText}>• {warning}</Text>
            ))}
          </View>
        )}
      </View>
    );
  };

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.title}>{template.title}</Text>
        {template.description && (
          <Text style={styles.description}>{template.description}</Text>
        )}
        <View style={styles.categoryBadge}>
          <Text style={styles.categoryText}>{template.category}</Text>
        </View>
      </View>

      {/* Form Fields */}
      <View style={styles.formContainer}>
        {renderFormFields()}
      </View>

      {/* Validation Summary */}
      {renderValidationSummary()}

      {/* Action Buttons */}
      <View style={styles.buttonContainer}>
        {onCancel && (
          <TouchableOpacity
            style={[styles.button, styles.cancelButton]}
            onPress={onCancel}
            disabled={isSubmitting}
          >
            <Text style={styles.cancelButtonText}>Cancel</Text>
          </TouchableOpacity>
        )}

        <TouchableOpacity
          style={[
            styles.button,
            styles.saveButton,
            (!validation?.isValid || isSubmitting || disabled) && styles.buttonDisabled,
          ]}
          onPress={handleSubmit}
          disabled={!validation?.isValid || isSubmitting || disabled}
        >
          <Text style={styles.saveButtonText}>
            {isSubmitting ? 'Saving...' : 'Save Milestone'}
          </Text>
        </TouchableOpacity>
      </View>
    </ScrollView>
  );
};

const styles = {
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  header: {
    padding: 20,
    backgroundColor: colors.white,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold' as const,
    color: colors.text,
    marginBottom: 8,
  },
  description: {
    fontSize: 16,
    color: colors.textSecondary,
    marginBottom: 12,
    lineHeight: 22,
  },
  categoryBadge: {
    alignSelf: 'flex-start' as const,
    backgroundColor: colors.primary,
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 12,
  },
  categoryText: {
    color: colors.white,
    fontSize: 12,
    fontWeight: '600' as const,
    textTransform: 'capitalize' as const,
  },
  formContainer: {
    padding: 20,
  },
  noFieldsText: {
    textAlign: 'center' as const,
    color: colors.textSecondary,
    fontStyle: 'italic' as const,
    marginVertical: 40,
  },
  validationContainer: {
    margin: 20,
    marginTop: 0,
  },
  errorsContainer: {
    backgroundColor: '#FFF5F5',
    borderLeftWidth: 4,
    borderLeftColor: colors.error,
    padding: 12,
    marginBottom: 12,
  },
  warningsContainer: {
    backgroundColor: '#FFFBF0',
    borderLeftWidth: 4,
    borderLeftColor: '#F59E0B',
    padding: 12,
  },
  validationTitle: {
    fontWeight: '600' as const,
    marginBottom: 8,
    color: colors.text,
  },
  errorText: {
    color: colors.error,
    fontSize: 14,
    marginBottom: 4,
  },
  warningText: {
    color: '#F59E0B',
    fontSize: 14,
    marginBottom: 4,
  },
  buttonContainer: {
    flexDirection: 'row' as const,
    padding: 20,
    paddingTop: 0,
    gap: 12,
  },
  button: {
    flex: 1,
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: 'center' as const,
    justifyContent: 'center' as const,
  },
  saveButton: {
    backgroundColor: colors.primary,
  },
  cancelButton: {
    backgroundColor: colors.background,
    borderWidth: 1,
    borderColor: colors.border,
  },
  saveButtonText: {
    color: colors.white,
    fontSize: 16,
    fontWeight: '600' as const,
  },
  cancelButtonText: {
    color: colors.text,
    fontSize: 16,
    fontWeight: '600' as const,
  },
  buttonDisabled: {
    opacity: 0.6,
  },
};
