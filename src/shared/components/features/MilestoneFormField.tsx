import DateTimePicker from '@react-native-community/datetimepicker';
import React, { useState } from 'react';
import { ScrollView, Text, TextInput, TouchableOpacity, View } from 'react-native';
import { MilestoneField } from '../../../journeys/memories/milestoneService';
import { colors } from '../../../utils/colors';
// import { PhotoUpload } from '../shared/PhotoUpload';
// import { CheckBox } from '../shared/CheckBox';

// Temporary placeholder components until we create the shared components
const PhotoUpload: React.FC<any> = ({ photos, onPhotosChange, maxPhotos, disabled }) => (
  <Text style={{ color: colors.textSecondary, fontStyle: 'italic' }}>Photo upload component (to be implemented)</Text>
);

const CheckBox: React.FC<any> = ({ checked, onPress, label, disabled }) => (
  <TouchableOpacity onPress={onPress} disabled={disabled} style={{ flexDirection: 'row', alignItems: 'center', marginVertical: 4 }}>
    <View style={{
      width: 20,
      height: 20,
      borderWidth: 1,
      borderColor: colors.border,
      backgroundColor: checked ? colors.primary : colors.white,
      marginRight: 8,
      borderRadius: 4,
    }} />
    <Text style={{ color: disabled ? colors.textSecondary : colors.text }}>{label}</Text>
  </TouchableOpacity>
);

interface MilestoneFormFieldProps {
  field: MilestoneField;
  value: any;
  onChange: (value: any) => void;
  error?: string;
  disabled?: boolean;
}

export const MilestoneFormField: React.FC<MilestoneFormFieldProps> = ({
  field,
  value,
  onChange,
  error,
  disabled = false,
}) => {
  const [showDatePicker, setShowDatePicker] = useState(false);

  const renderField = () => {
    switch (field.type) {
      case 'text':
        return (
          <TextInput
            style={[
              styles.textInput,
              error && styles.inputError,
              disabled && styles.inputDisabled,
            ]}
            value={value || ''}
            onChangeText={onChange}
            placeholder={field.placeholder || `Enter ${field.label.toLowerCase()}`}
            editable={!disabled}
            multiline={false}
          />
        );

      case 'textarea':
        return (
          <TextInput
            style={[
              styles.textArea,
              error && styles.inputError,
              disabled && styles.inputDisabled,
            ]}
            value={value || ''}
            onChangeText={onChange}
            placeholder={field.placeholder || `Enter ${field.label.toLowerCase()}`}
            editable={!disabled}
            multiline={true}
            numberOfLines={4}
            textAlignVertical="top"
          />
        );

      case 'date':
        return (
          <View>
            <TouchableOpacity
              style={[
                styles.dateButton,
                error && styles.inputError,
                disabled && styles.inputDisabled,
              ]}
              onPress={() => !disabled && setShowDatePicker(true)}
              disabled={disabled}
            >
              <Text style={[
                styles.dateButtonText,
                !value && styles.placeholderText,
                disabled && styles.disabledText,
              ]}>
                {value ? new Date(value).toLocaleDateString() : 'Select date'}
              </Text>
            </TouchableOpacity>

            {showDatePicker && (
              <DateTimePicker
                value={value ? new Date(value) : new Date()}
                mode="date"
                display="default"
                onChange={(event, selectedDate) => {
                  setShowDatePicker(false);
                  if (selectedDate) {
                    onChange(selectedDate.toISOString().split('T')[0]);
                  }
                }}
              />
            )}
          </View>
        );

      case 'select':
        return (
          <View style={[
            styles.pickerContainer,
            error && styles.inputError,
            disabled && styles.inputDisabled,
          ]}>
            <Text style={styles.placeholderText}>
              Select field (install @react-native-picker/picker for full functionality)
            </Text>
            <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.optionsScroll}>
              {field.options?.map((option) => (
                <TouchableOpacity
                  key={option}
                  style={[
                    styles.optionButton,
                    value === option && styles.optionButtonSelected,
                  ]}
                  onPress={() => !disabled && onChange(option)}
                  disabled={disabled}
                >
                  <Text style={[
                    styles.optionButtonText,
                    value === option && styles.optionButtonTextSelected,
                  ]}>
                    {option}
                  </Text>
                </TouchableOpacity>
              ))}
            </ScrollView>
          </View>
        );

      case 'boolean':
        return (
          <CheckBox
            checked={value || false}
            onPress={() => !disabled && onChange(!value)}
            label={field.label}
            disabled={disabled}
          />
        );

      case 'number':
        return (
          <TextInput
            style={[
              styles.textInput,
              error && styles.inputError,
              disabled && styles.inputDisabled,
            ]}
            value={value?.toString() || ''}
            onChangeText={(text) => {
              const numValue = parseFloat(text);
              onChange(isNaN(numValue) ? '' : numValue);
            }}
            placeholder={field.placeholder || `Enter ${field.label.toLowerCase()}`}
            keyboardType="numeric"
            editable={!disabled}
          />
        );

      case 'text_array':
        return (
          <TextArrayInput
            value={value || []}
            onChange={onChange}
            placeholder={`Add ${field.label.toLowerCase()}`}
            disabled={disabled}
            error={error}
          />
        );

      case 'photo_array':
        return (
          <PhotoArrayInput
            value={value || []}
            onChange={onChange}
            disabled={disabled}
            error={error}
          />
        );

      case 'checkbox_array':
        return (
          <CheckboxArrayInput
            options={field.options || []}
            value={value || []}
            onChange={onChange}
            disabled={disabled}
            error={error}
          />
        );

      default:
        return (
          <Text style={styles.unsupportedField}>
            Unsupported field type: {field.type}
          </Text>
        );
    }
  };

  return (
    <View style={styles.fieldContainer}>
      <Text style={[styles.label, field.required && styles.requiredLabel]}>
        {field.label}
        {field.required && <Text style={styles.requiredAsterisk}> *</Text>}
      </Text>

      {renderField()}

      {error && (
        <Text style={styles.errorText}>{error}</Text>
      )}
    </View>
  );
};

// Helper component for text array input
const TextArrayInput: React.FC<{
  value: string[];
  onChange: (value: string[]) => void;
  placeholder: string;
  disabled: boolean;
  error?: string;
}> = ({ value, onChange, placeholder, disabled, error }) => {
  const [inputText, setInputText] = useState('');

  const addItem = () => {
    if (inputText.trim()) {
      onChange([...value, inputText.trim()]);
      setInputText('');
    }
  };

  const removeItem = (index: number) => {
    onChange(value.filter((_, i) => i !== index));
  };

  return (
    <View>
      <View style={styles.arrayInputContainer}>
        <TextInput
          style={[
            styles.arrayTextInput,
            error && styles.inputError,
            disabled && styles.inputDisabled,
          ]}
          value={inputText}
          onChangeText={setInputText}
          placeholder={placeholder}
          editable={!disabled}
          onSubmitEditing={addItem}
        />
        <TouchableOpacity
          style={[styles.addButton, disabled && styles.buttonDisabled]}
          onPress={addItem}
          disabled={disabled}
        >
          <Text style={styles.addButtonText}>Add</Text>
        </TouchableOpacity>
      </View>

      {value.map((item, index) => (
        <View key={index} style={styles.arrayItem}>
          <Text style={styles.arrayItemText}>{item}</Text>
          <TouchableOpacity
            style={styles.removeButton}
            onPress={() => removeItem(index)}
            disabled={disabled}
          >
            <Text style={styles.removeButtonText}>×</Text>
          </TouchableOpacity>
        </View>
      ))}
    </View>
  );
};

// Helper component for photo array input
const PhotoArrayInput: React.FC<{
  value: string[];
  onChange: (value: string[]) => void;
  disabled: boolean;
  error?: string;
}> = ({ value, onChange, disabled, error: _error }) => {
  const _handlePhotoAdd = (photoUri: string) => {
    onChange([...value, photoUri]);
  };

  const _handlePhotoRemove = (index: number) => {
    onChange(value.filter((_, i) => i !== index));
  };

  return (
    <PhotoUpload
      photos={value}
      onPhotosChange={onChange}
      maxPhotos={10}
      disabled={disabled}
    />
  );
};

// Helper component for checkbox array input
const CheckboxArrayInput: React.FC<{
  options: string[];
  value: string[];
  onChange: (value: string[]) => void;
  disabled: boolean;
  error?: string;
}> = ({ options, value, onChange, disabled, error }) => {
  const toggleOption = (option: string) => {
    if (value.includes(option)) {
      onChange(value.filter(v => v !== option));
    } else {
      onChange([...value, option]);
    }
  };

  return (
    <View>
      {options.map((option) => (
        <CheckBox
          key={option}
          checked={value.includes(option)}
          onPress={() => !disabled && toggleOption(option)}
          label={option}
          disabled={disabled}
        />
      ))}
    </View>
  );
};

const styles = {
  fieldContainer: {
    marginBottom: 20,
  },
  label: {
    fontSize: 16,
    fontWeight: '600' as const,
    color: colors.charcoalGray,
    marginBottom: 8,
  },
  requiredLabel: {
    color: colors.charcoalGray,
  },
  requiredAsterisk: {
    color: colors.error,
  },
  textInput: {
    borderWidth: 1,
    borderColor: colors.borderLight,
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    backgroundColor: colors.white,
    color: colors.charcoalGray,
  },
  textArea: {
    borderWidth: 1,
    borderColor: colors.borderLight,
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    backgroundColor: colors.white,
    color: colors.charcoalGray,
    minHeight: 100,
  },
  dateButton: {
    borderWidth: 1,
    borderColor: colors.border,
    borderRadius: 8,
    padding: 12,
    backgroundColor: colors.white,
  },
  dateButtonText: {
    fontSize: 16,
    color: colors.text,
  },
  placeholderText: {
    color: colors.textSecondary,
  },
  pickerContainer: {
    borderWidth: 1,
    borderColor: colors.border,
    borderRadius: 8,
    backgroundColor: colors.white,
  },
  picker: {
    height: 50,
  },
  inputError: {
    borderColor: colors.error,
  },
  inputDisabled: {
    backgroundColor: colors.background,
    opacity: 0.6,
  },
  disabledText: {
    color: colors.textSecondary,
  },
  buttonDisabled: {
    opacity: 0.6,
  },
  errorText: {
    color: colors.error,
    fontSize: 14,
    marginTop: 4,
  },
  unsupportedField: {
    color: colors.textSecondary,
    fontStyle: 'italic' as const,
    padding: 12,
    backgroundColor: colors.background,
    borderRadius: 8,
  },
  arrayInputContainer: {
    flexDirection: 'row' as const,
    marginBottom: 8,
  },
  arrayTextInput: {
    flex: 1,
    borderWidth: 1,
    borderColor: colors.border,
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    backgroundColor: colors.white,
    marginRight: 8,
  },
  addButton: {
    backgroundColor: colors.primary,
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 8,
    justifyContent: 'center' as const,
  },
  addButtonText: {
    color: colors.white,
    fontWeight: '600' as const,
  },
  arrayItem: {
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    backgroundColor: colors.background,
    padding: 8,
    borderRadius: 6,
    marginBottom: 4,
  },
  arrayItemText: {
    flex: 1,
    color: colors.text,
  },
  removeButton: {
    backgroundColor: colors.error,
    width: 24,
    height: 24,
    borderRadius: 12,
    justifyContent: 'center' as const,
    alignItems: 'center' as const,
  },
  removeButtonText: {
    color: colors.white,
    fontWeight: 'bold' as const,
    fontSize: 16,
  },
  optionsScroll: {
    marginTop: 8,
  },
  optionButton: {
    backgroundColor: colors.background,
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 6,
    marginRight: 8,
    borderWidth: 1,
    borderColor: colors.border,
  },
  optionButtonSelected: {
    backgroundColor: colors.primary,
    borderColor: colors.primary,
  },
  optionButtonText: {
    color: colors.text,
    fontSize: 14,
  },
  optionButtonTextSelected: {
    color: colors.white,
  },
};
