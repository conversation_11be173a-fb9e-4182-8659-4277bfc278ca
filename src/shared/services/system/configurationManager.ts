/**
 * Enterprise Configuration Management System
 * Centralized configuration with environment variables, feature flags, and runtime settings
 */

import { logger } from '../../utils/logger';
// Removed non-existent validation imports

// =============================================================================
// CONFIGURATION TYPES & INTERFACES
// =============================================================================

export enum Environment {
  DEVELOPMENT = 'development',
  STAGING = 'staging',
  PRODUCTION = 'production',
  TEST = 'test'
}

export interface DatabaseConfig {
  url: string;
  maxConnections: number;
  connectionTimeout: number;
  queryTimeout: number;
  ssl: boolean;
}

export interface AuthConfig {
  sessionTimeout: number;
  maxLoginAttempts: number;
  lockoutDuration: number;
  passwordMinLength: number;
  requireMFA: boolean;
  jwtSecret: string;
  jwtExpiration: string;
}

export interface CacheConfig {
  enabled: boolean;
  defaultTTL: number;
  maxSize: number;
  maxEntries: number;
  compressionEnabled: boolean;
  persistToDisk: boolean;
}

export interface PerformanceConfig {
  enableMonitoring: boolean;
  metricsRetention: number;
  slowQueryThreshold: number;
  memoryWarningThreshold: number;
  batchSize: number;
  maxRetries: number;
  retryDelay: number;
}

export interface SecurityConfig {
  enableCSP: boolean;
  enableCORS: boolean;
  allowedOrigins: string[];
  rateLimitEnabled: boolean;
  rateLimitRequests: number;
  rateLimitWindow: number;
  encryptionKey: string;
}

export interface FeatureFlags {
  enableOfflineMode: boolean;
  enablePushNotifications: boolean;
  enableAnalytics: boolean;
  enableBetaFeatures: boolean;
  enableAdvancedSearch: boolean;
  enableSocialSharing: boolean;
  enableVideoUpload: boolean;
  enableAIRecommendations: boolean;
  maxFileUploadSize: number;
  maxPhotosPerAlbum: number;
}

export interface UIConfig {
  theme: 'light' | 'dark' | 'auto';
  primaryColor: string;
  secondaryColor: string;
  enableAnimations: boolean;
  enableHapticFeedback: boolean;
  defaultLanguage: string;
  supportedLanguages: string[];
  accessibilityEnabled: boolean;
}

export interface AppConfiguration {
  environment: Environment;
  version: string;
  buildNumber: string;
  apiBaseUrl: string;
  webUrl: string;
  database: DatabaseConfig;
  auth: AuthConfig;
  cache: CacheConfig;
  performance: PerformanceConfig;
  security: SecurityConfig;
  features: FeatureFlags;
  ui: UIConfig;
  debug: {
    enableLogging: boolean;
    logLevel: 'debug' | 'info' | 'warn' | 'error';
    enableErrorReporting: boolean;
    enablePerformanceTracking: boolean;
  };
}

// =============================================================================
// CONFIGURATION VALIDATION SCHEMA
// =============================================================================

const ConfigurationSchema: Record<string, any> = {
  environment: {
    type: 'enum',
    required: true
  },
  version: {
    type: 'string',
    required: true,
    pattern: /^\d+\.\d+\.\d+$/
  },
  apiBaseUrl: {
    type: 'url',
    required: true
  }
};

// =============================================================================
// CONFIGURATION MANAGER
// =============================================================================

class ConfigurationManager {
  private static instance: ConfigurationManager;
  private config: AppConfiguration;
  private listeners: Set<(config: AppConfiguration) => void> = new Set();
  private featureFlagOverrides: Map<string, boolean> = new Map();

  private constructor() {
    this.config = this.loadConfiguration();
    this.validateConfiguration();
  }

  public static getInstance(): ConfigurationManager {
    if (!ConfigurationManager.instance) {
      ConfigurationManager.instance = new ConfigurationManager();
    }
    return ConfigurationManager.instance;
  }

  /**
   * Get the complete configuration
   */
  public getConfig(): AppConfiguration {
    return { ...this.config };
  }

  /**
   * Get a specific configuration section
   */
  public get<K extends keyof AppConfiguration>(section: K): AppConfiguration[K] {
    return this.config[section];
  }

  /**
   * Get a nested configuration value
   */
  public getValue<T>(path: string): T | undefined {
    return this.getNestedValue(this.config, path) as T;
  }

  /**
   * Check if a feature flag is enabled
   */
  public isFeatureEnabled(feature: keyof FeatureFlags): boolean {
    // Check for runtime override first
    const override = this.featureFlagOverrides.get(feature);
    if (override !== undefined) {
      return override;
    }

    return this.config.features[feature] as boolean;
  }

  /**
   * Override a feature flag at runtime
   */
  public setFeatureFlag(feature: keyof FeatureFlags, enabled: boolean): void {
    this.featureFlagOverrides.set(feature, enabled);
    logger.info(`Feature flag overridden: ${feature} = ${enabled}`);
    this.notifyListeners();
  }

  /**
   * Get environment-specific configuration
   */
  public isDevelopment(): boolean {
    return this.config.environment === Environment.DEVELOPMENT;
  }

  public isProduction(): boolean {
    return this.config.environment === Environment.PRODUCTION;
  }

  public isStaging(): boolean {
    return this.config.environment === Environment.STAGING;
  }

  public isTest(): boolean {
    return this.config.environment === Environment.TEST;
  }

  /**
   * Update configuration at runtime (for non-sensitive settings)
   */
  public updateConfig(updates: Partial<AppConfiguration>): void {
    const newConfig = { ...this.config, ...updates };

    // Simple validation - check required fields
    if (!newConfig.environment || !newConfig.version || !newConfig.apiBaseUrl) {
      logger.error('Configuration update failed validation - missing required fields', {
        config: newConfig
      });
      throw new Error('Configuration validation failed: missing required fields');
    }

    this.config = newConfig;
    logger.info('Configuration updated', { updates });
    this.notifyListeners();
  }

  /**
   * Add configuration change listener
   */
  public addListener(listener: (config: AppConfiguration) => void): () => void {
    this.listeners.add(listener);
    return () => this.listeners.delete(listener);
  }

  /**
   * Get configuration for external services
   */
  public getSupabaseConfig() {
    return {
      url: this.getValue<string>('database.url'),
      anonKey: process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY,
      serviceRoleKey: process.env.SUPABASE_SERVICE_ROLE_KEY
    };
  }

  public getAnalyticsConfig() {
    return {
      enabled: this.isFeatureEnabled('enableAnalytics'),
      trackingId: process.env.ANALYTICS_TRACKING_ID,
      debug: this.isDevelopment()
    };
  }

  // =============================================================================
  // PRIVATE METHODS
  // =============================================================================

  private loadConfiguration(): AppConfiguration {
    const env = (process.env.NODE_ENV as Environment) || Environment.DEVELOPMENT;

    return {
      environment: env,
      version: process.env.EXPO_PUBLIC_APP_VERSION || '1.0.0',
      buildNumber: process.env.EXPO_PUBLIC_BUILD_NUMBER || '1',
      apiBaseUrl: process.env.EXPO_PUBLIC_API_BASE_URL || 'http://localhost:3000',
      webUrl: process.env.EXPO_PUBLIC_WEB_URL || 'http://localhost:3000',

      database: {
        url: process.env.EXPO_PUBLIC_SUPABASE_URL || '',
        maxConnections: parseInt(process.env.DB_MAX_CONNECTIONS || '10'),
        connectionTimeout: parseInt(process.env.DB_CONNECTION_TIMEOUT || '10000'),
        queryTimeout: parseInt(process.env.DB_QUERY_TIMEOUT || '30000'),
        ssl: env === Environment.PRODUCTION
      },

      auth: {
        sessionTimeout: parseInt(process.env.AUTH_SESSION_TIMEOUT || '3600000'), // 1 hour
        maxLoginAttempts: parseInt(process.env.AUTH_MAX_LOGIN_ATTEMPTS || '5'),
        lockoutDuration: parseInt(process.env.AUTH_LOCKOUT_DURATION || '900000'), // 15 minutes
        passwordMinLength: parseInt(process.env.AUTH_PASSWORD_MIN_LENGTH || '8'),
        requireMFA: process.env.AUTH_REQUIRE_MFA === 'true',
        jwtSecret: process.env.JWT_SECRET || 'default-secret',
        jwtExpiration: process.env.JWT_EXPIRATION || '24h'
      },

      cache: {
        enabled: process.env.CACHE_ENABLED !== 'false',
        defaultTTL: parseInt(process.env.CACHE_DEFAULT_TTL || '300000'), // 5 minutes
        maxSize: parseInt(process.env.CACHE_MAX_SIZE || '100'), // 100MB
        maxEntries: parseInt(process.env.CACHE_MAX_ENTRIES || '10000'),
        compressionEnabled: process.env.CACHE_COMPRESSION === 'true',
        persistToDisk: process.env.CACHE_PERSIST === 'true'
      },

      performance: {
        enableMonitoring: process.env.PERFORMANCE_MONITORING !== 'false',
        metricsRetention: parseInt(process.env.METRICS_RETENTION || '604800000'), // 7 days
        slowQueryThreshold: parseInt(process.env.SLOW_QUERY_THRESHOLD || '1000'),
        memoryWarningThreshold: parseInt(process.env.MEMORY_WARNING_THRESHOLD || '100'),
        batchSize: parseInt(process.env.BATCH_SIZE || '100'),
        maxRetries: parseInt(process.env.MAX_RETRIES || '3'),
        retryDelay: parseInt(process.env.RETRY_DELAY || '1000')
      },

      security: {
        enableCSP: env === Environment.PRODUCTION,
        enableCORS: true,
        allowedOrigins: (process.env.ALLOWED_ORIGINS || '').split(',').filter(Boolean),
        rateLimitEnabled: env === Environment.PRODUCTION,
        rateLimitRequests: parseInt(process.env.RATE_LIMIT_REQUESTS || '100'),
        rateLimitWindow: parseInt(process.env.RATE_LIMIT_WINDOW || '900000'), // 15 minutes
        encryptionKey: process.env.ENCRYPTION_KEY || 'default-key'
      },

      features: {
        enableOfflineMode: process.env.FEATURE_OFFLINE_MODE === 'true',
        enablePushNotifications: process.env.FEATURE_PUSH_NOTIFICATIONS === 'true',
        enableAnalytics: process.env.FEATURE_ANALYTICS === 'true',
        enableBetaFeatures: process.env.FEATURE_BETA === 'true',
        enableAdvancedSearch: process.env.FEATURE_ADVANCED_SEARCH === 'true',
        enableSocialSharing: process.env.FEATURE_SOCIAL_SHARING === 'true',
        enableVideoUpload: process.env.FEATURE_VIDEO_UPLOAD === 'true',
        enableAIRecommendations: process.env.FEATURE_AI_RECOMMENDATIONS === 'true',
        maxFileUploadSize: parseInt(process.env.MAX_FILE_UPLOAD_SIZE || '10485760'), // 10MB
        maxPhotosPerAlbum: parseInt(process.env.MAX_PHOTOS_PER_ALBUM || '100')
      },

      ui: {
        theme: (process.env.DEFAULT_THEME as 'light' | 'dark' | 'auto') || 'auto',
        primaryColor: process.env.PRIMARY_COLOR || '#9CAF88',
        secondaryColor: process.env.SECONDARY_COLOR || '#CBC3E3',
        enableAnimations: process.env.ENABLE_ANIMATIONS !== 'false',
        enableHapticFeedback: process.env.ENABLE_HAPTIC_FEEDBACK !== 'false',
        defaultLanguage: process.env.DEFAULT_LANGUAGE || 'en',
        supportedLanguages: (process.env.SUPPORTED_LANGUAGES || 'en').split(','),
        accessibilityEnabled: process.env.ACCESSIBILITY_ENABLED === 'true'
      },

      debug: {
        enableLogging: env !== Environment.PRODUCTION,
        logLevel: (process.env.LOG_LEVEL as any) || (env === Environment.PRODUCTION ? 'error' : 'debug'),
        enableErrorReporting: env === Environment.PRODUCTION,
        enablePerformanceTracking: process.env.PERFORMANCE_TRACKING === 'true'
      }
    };
  }

  private validateConfiguration(): void {
    // Simple validation - check required fields
    if (!this.config.environment || !this.config.version || !this.config.apiBaseUrl) {
      logger.error('Configuration validation failed - missing required fields', {
        config: this.config
      });

      // In production, this should be a critical error
      if (this.config.environment === Environment.PRODUCTION) {
        throw new Error('Invalid production configuration');
      }
    }

    logger.info('Configuration loaded and validated', {
      environment: this.config.environment,
      version: this.config.version,
      features: Object.keys(this.config.features).filter(key =>
        this.config.features[key as keyof FeatureFlags]
      )
    });
  }

  private getNestedValue(obj: any, path: string): unknown {
    return path.split('.').reduce((current, key) => current?.[key], obj);
  }

  private notifyListeners(): void {
    this.listeners.forEach(listener => {
      try {
        listener(this.config);
      } catch (error) {
        logger.error('Configuration listener error', error);
      }
    });
  }
}

// =============================================================================
// EXPORTS
// =============================================================================

export { ConfigurationManager };
export const configManager = ConfigurationManager.getInstance();

// Convenience functions
export const getConfig = () => configManager.getConfig();
export const getValue = <T>(path: string) => configManager.getValue<T>(path);
export const isFeatureEnabled = (feature: keyof FeatureFlags) => configManager.isFeatureEnabled(feature);
export const isDevelopment = () => configManager.isDevelopment();
export const isProduction = () => configManager.isProduction();
