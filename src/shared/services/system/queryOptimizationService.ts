/**
 * Query Optimization Service
 * Provides optimized database queries to prevent N+1 problems and improve performance
 */

import { logger } from '../../utils/logger';
import { sanitizeSQLSearch, validateUUID } from '../../utils/validation';
import { supabase } from '../supabase/client';

export interface OptimizedDateNightData {
  allIdeas: any[];
  weeklyIdeas: any[];
  userIdeas: any[];
  categories: string[];
  userFavorites: string[];
}

export interface OptimizedMealData {
  globalIdeas: any[];
  userIdeas: any[];
  categories: string[];
  userFavorites: string[];
}

export interface OptimizedUserData {
  profile: any;
  pointsSystem: any;
  onboardingProgress: any;
  preferences: any;
}

class QueryOptimizationService {
  /**
   * Get all date night data in a single optimized query
   * Replaces multiple separate API calls with one efficient query
   */
  async getOptimizedDateNightData(userId?: string): Promise<OptimizedDateNightData> {
    try {
      // Global ideas with categories
      const globalIdeasQuery = supabase
        .from('date_night_ideas_global')
        .select('*, category')
        .order('category, title');

      // Weekly ideas
      const weeklyIdeasQuery = supabase
        .from('date_night_ideas_global')
        .select('*')
        .not('week_number', 'is', null)
        .order('week_number, title');

      const queries: any[] = [globalIdeasQuery, weeklyIdeasQuery];

      // Add user-specific queries if authenticated
      if (userId) {
        const userValidation = validateUUID(userId);
        if (userValidation.isValid) {
          // User ideas query
          const userIdeasQuery = supabase
            .from('date_night_ideas_user')
            .select('*')
            .eq('user_id', userId)
            .order('created_at', { ascending: false });

          // User favorites query
          const userFavoritesQuery = supabase
            .from('favorites')
            .select('item_id')
            .eq('user_id', userId)
            .eq('type', 'date_night');

          queries.push(userIdeasQuery, userFavoritesQuery);
        }
      }

      const results = await Promise.all(queries);

      // Extract categories from global ideas
      const globalIdeas = results[0].data || [];
      const categorySet = new Set(globalIdeas.map((idea: any) => idea.category).filter(Boolean));
      const categories = Array.from(categorySet) as string[];

      return {
        allIdeas: globalIdeas,
        weeklyIdeas: results[1].data || [],
        userIdeas: userId && results[2] ? results[2].data || [] : [],
        categories,
        userFavorites: userId && results[3] ? (results[3].data || []).map((f: any) => f.item_id) : []
      };
    } catch (error) {
      logger.error('Error in getOptimizedDateNightData:', error);
      throw new Error('Failed to fetch date night data');
    }
  }

  /**
   * Get all meal data in a single optimized query
   */
  async getOptimizedMealData(userId: string, category?: string): Promise<OptimizedMealData> {
    try {
      const userValidation = validateUUID(userId);
      if (!userValidation.isValid) {
        throw new Error('Invalid user ID');
      }

      let globalQuery = supabase
        .from('meal_ideas_global')
        .select('*')
        .order('created_at', { ascending: false });

      let userQuery = supabase
        .from('meal_ideas_users')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false });

      // Apply category filter if provided
      if (category && category !== 'all') {
        globalQuery = globalQuery.eq('category', category);
        userQuery = userQuery.eq('category', category);
      }

      const [globalResult, userResult, categoriesResult, favoritesResult] = await Promise.all([
        globalQuery,
        userQuery,
        supabase
          .from('meal_ideas_global')
          .select('category')
          .not('category', 'is', null),
        supabase
          .from('favorites')
          .select('item_id')
          .eq('user_id', userId)
          .eq('type', 'meal')
      ]);

      // Extract unique categories
      const categorySet = new Set((categoriesResult.data || []).map((item: any) => item.category));
      const categories = Array.from(categorySet) as string[];

      return {
        globalIdeas: globalResult.data || [],
        userIdeas: userResult.data || [],
        categories,
        userFavorites: (favoritesResult.data || []).map((f: any) => f.item_id)
      };
    } catch (error) {
      logger.error('Error in getOptimizedMealData:', error);
      throw new Error('Failed to fetch meal data');
    }
  }

  /**
   * Get all user data in a single optimized query
   */
  async getOptimizedUserData(userId: string): Promise<OptimizedUserData> {
    try {
      const userValidation = validateUUID(userId);
      if (!userValidation.isValid) {
        throw new Error('Invalid user ID');
      }

      const [profileResult, pointsResult, progressResult] = await Promise.all([
        supabase
          .from('profiles')
          .select('*')
          .eq('id', userId)
          .single(),

        supabase
          .from('points_system')
          .select('*')
          .eq('user_id', userId)
          .single(),

        supabase
          .from('onboarding_progress')
          .select('*')
          .eq('user_id', userId)
          .single()
      ]);

      return {
        profile: profileResult.data,
        pointsSystem: pointsResult.data,
        onboardingProgress: progressResult.data,
        preferences: null // TODO: Implement user preferences table
      };
    } catch (error) {
      logger.error('Error in getOptimizedUserData:', error);
      throw new Error('Failed to fetch user data');
    }
  }

  /**
   * Optimized search across multiple content types
   */
  async optimizedSearch(query: string, userId?: string, contentTypes: string[] = ['date_night', 'meal']): Promise<any> {
    try {
      const validation = sanitizeSQLSearch(query);
      if (!validation.isValid || !validation.sanitizedValue) {
        return { dateNightIdeas: [], mealIdeas: [] };
      }

      const sanitizedQuery = validation.sanitizedValue;
      const queries = [];

      if (contentTypes.includes('date_night')) {
        queries.push(
          supabase
            .from('date_night_ideas_global')
            .select('*')
            .textSearch('title,description,category', sanitizedQuery)
            .limit(20)
        );
      }

      if (contentTypes.includes('meal')) {
        queries.push(
          supabase
            .from('meal_ideas_global')
            .select('*')
            .textSearch('title,description', sanitizedQuery)
            .limit(20)
        );
      }

      const results = await Promise.all(queries);

      return {
        dateNightIdeas: contentTypes.includes('date_night') ? results[0]?.data || [] : [],
        mealIdeas: contentTypes.includes('meal') ? results[contentTypes.includes('date_night') ? 1 : 0]?.data || [] : []
      };
    } catch (error) {
      logger.error('Error in optimizedSearch:', error);
      return { dateNightIdeas: [], mealIdeas: [] };
    }
  }

  /**
   * Get couple data with all related information in one query
   */
  async getOptimizedCoupleData(coupleId: string): Promise<any> {
    try {
      const coupleValidation = validateUUID(coupleId);
      if (!coupleValidation.isValid) {
        throw new Error('Invalid couple ID');
      }

      const [coupleResult, storyResult, timelineResult, milestonesResult] = await Promise.all([
        supabase
          .from('couples')
          .select('*')
          .eq('id', coupleId)
          .single(),

        supabase
          .from('couple_stories')
          .select('*')
          .eq('couple_id', coupleId)
          .single(),

        supabase
          .from('timeline_events')
          .select(`
            *,
            timeline_photos (*)
          `)
          .eq('couple_id', coupleId)
          .eq('is_visible', true)
          .order('event_date', { ascending: false })
          .limit(50),

        supabase
          .from('couple_milestones')
          .select(`
            *,
            milestone_templates (*)
          `)
          .eq('couple_id', coupleId)
          .order('created_at', { ascending: false })
      ]);

      return {
        couple: coupleResult.data,
        story: storyResult.data,
        timeline: timelineResult.data || [],
        milestones: milestonesResult.data || []
      };
    } catch (error) {
      logger.error('Error in getOptimizedCoupleData:', error);
      throw new Error('Failed to fetch couple data');
    }
  }

  /**
   * Batch update multiple records efficiently
   */
  async batchUpdate(table: string, updates: Array<{ id: string; data: any }>): Promise<boolean> {
    try {
      if (!updates.length) return true;

      // Validate all IDs
      for (const update of updates) {
        const validation = validateUUID(update.id);
        if (!validation.isValid) {
          throw new Error(`Invalid ID: ${update.id}`);
        }
      }

      // Use upsert for efficient batch updates
      const { error } = await supabase
        .from(table as any)
        .upsert(updates.map(u => ({ id: u.id, ...u.data })));

      if (error) {
        logger.error('Error in batchUpdate:', error);
        return false;
      }

      return true;
    } catch (error) {
      logger.error('Error in batchUpdate:', error);
      return false;
    }
  }
}

export const queryOptimizationService = new QueryOptimizationService();
export default queryOptimizationService;
