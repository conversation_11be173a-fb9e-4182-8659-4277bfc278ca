/**
 * Subscription Manager
 * Enterprise-grade subscription management with memory leak prevention
 */

import { RealtimeChannel, RealtimePostgresChangesPayload } from '@supabase/supabase-js';
import { supabase } from '../supabase/client';
import { logger } from '../../utils/logger';

export interface SubscriptionConfig {
  table: string;
  event?: 'INSERT' | 'UPDATE' | 'DELETE' | '*';
  filter?: string;
  callback: (payload: RealtimePostgresChangesPayload<any>) => void;
  onError?: (error: Error) => void;
  autoReconnect?: boolean;
  maxReconnectAttempts?: number;
  reconnectDelay?: number;
}

export interface SubscriptionMetrics {
  id: string;
  table: string;
  event: string;
  createdAt: number;
  lastActivity: number;
  messageCount: number;
  errorCount: number;
  isActive: boolean;
  reconnectAttempts: number;
}

class SubscriptionManager {
  private static instance: SubscriptionManager;
  private subscriptions = new Map<string, {
    channel: RealtimeChannel;
    config: SubscriptionConfig;
    metrics: SubscriptionMetrics;
    cleanup: () => void;
    reconnectTimer?: NodeJS.Timeout;
  }>();
  
  private connectionState: 'connected' | 'disconnected' | 'reconnecting' = 'disconnected';
  private heartbeatInterval?: NodeJS.Timeout;
  private cleanupInterval?: NodeJS.Timeout;
  private maxSubscriptions = 50; // Prevent memory issues
  private heartbeatFrequency = 30000; // 30 seconds

  private constructor() {
    this.initializeManager();
  }

  public static getInstance(): SubscriptionManager {
    if (!SubscriptionManager.instance) {
      SubscriptionManager.instance = new SubscriptionManager();
    }
    return SubscriptionManager.instance;
  }

  /**
   * Initialize subscription manager with cleanup and monitoring
   */
  private initializeManager(): void {
    // Initialize connection state
    this.connectionState = 'connected';
    logger.info('Subscription manager initialized');

    // Start heartbeat monitoring
    this.startHeartbeat();

    // Start periodic cleanup
    this.startPeriodicCleanup();

    // Handle app lifecycle events
    if (typeof window !== 'undefined') {
      window.addEventListener('beforeunload', () => this.cleanup());
      window.addEventListener('visibilitychange', () => {
        if (document.hidden) {
          this.pauseSubscriptions();
        } else {
          this.resumeSubscriptions();
        }
      });
    }
  }

  /**
   * Create a managed subscription with automatic cleanup
   */
  subscribe(config: SubscriptionConfig): string {
    // Check subscription limits
    if (this.subscriptions.size >= this.maxSubscriptions) {
      logger.warn(`Maximum subscriptions (${this.maxSubscriptions}) reached. Cleaning up oldest inactive subscriptions.`);
      this.cleanupInactiveSubscriptions();
    }

    const subscriptionId = this.generateSubscriptionId(config);
    
    // Check if subscription already exists
    if (this.subscriptions.has(subscriptionId)) {
      logger.warn(`Subscription ${subscriptionId} already exists`);
      return subscriptionId;
    }

    try {
      const channel = supabase.channel(`subscription_${subscriptionId}`);
      
      // Configure the subscription using current Supabase API pattern
      (channel as any).on(
        'postgres_changes',
        {
          event: config.event || '*',
          schema: 'public',
          table: config.table,
          filter: config.filter
        },
        (payload: RealtimePostgresChangesPayload<any>) => {
          try {
            // Update metrics
            const subscription = this.subscriptions.get(subscriptionId);
            if (subscription) {
              subscription.metrics.lastActivity = Date.now();
              subscription.metrics.messageCount++;
            }

            // Execute callback
            config.callback(payload);
          } catch (error) {
            logger.error(`Error in subscription callback for ${subscriptionId}:`, error);

            // Update error metrics
            const subscription = this.subscriptions.get(subscriptionId);
            if (subscription) {
              subscription.metrics.errorCount++;
              if (config.onError) {
                config.onError(error as Error);
              }
            }
          }
        }
      );

      // Subscribe to the channel
      channel.subscribe((status: string) => {
        const subscription = this.subscriptions.get(subscriptionId);
        if (subscription) {
          subscription.metrics.isActive = status === 'SUBSCRIBED';
        }

        if (status === 'SUBSCRIBED') {
          logger.info(`Subscription ${subscriptionId} active`);
        } else if (status === 'CHANNEL_ERROR') {
          logger.error(`Subscription ${subscriptionId} error`);
          if (config.autoReconnect !== false) {
            this.scheduleReconnect(subscriptionId);
          }
        }
      });

      // Create cleanup function using current Supabase API
      const cleanup = () => {
        try {
          supabase.removeChannel(channel);
          logger.info(`Subscription ${subscriptionId} cleaned up`);
        } catch (error) {
          logger.error(`Error cleaning up subscription ${subscriptionId}:`, error);
        }
      };

      // Store subscription with metrics
      this.subscriptions.set(subscriptionId, {
        channel,
        config,
        cleanup,
        metrics: {
          id: subscriptionId,
          table: config.table,
          event: config.event || '*',
          createdAt: Date.now(),
          lastActivity: Date.now(),
          messageCount: 0,
          errorCount: 0,
          isActive: false,
          reconnectAttempts: 0
        }
      });

      logger.info(`Created subscription ${subscriptionId} for table ${config.table}`);
      return subscriptionId;

    } catch (error) {
      logger.error(`Failed to create subscription for ${config.table}:`, error);
      throw error;
    }
  }

  /**
   * Unsubscribe and cleanup a specific subscription
   */
  unsubscribe(subscriptionId: string): boolean {
    const subscription = this.subscriptions.get(subscriptionId);
    if (!subscription) {
      logger.warn(`Subscription ${subscriptionId} not found`);
      return false;
    }

    try {
      // Clear reconnect timer if exists
      if (subscription.reconnectTimer) {
        clearTimeout(subscription.reconnectTimer);
      }

      // Execute cleanup
      subscription.cleanup();
      
      // Remove from map
      this.subscriptions.delete(subscriptionId);
      
      logger.info(`Unsubscribed from ${subscriptionId}`);
      return true;
    } catch (error) {
      logger.error(`Error unsubscribing from ${subscriptionId}:`, error);
      return false;
    }
  }

  /**
   * Get subscription metrics for monitoring
   */
  getSubscriptionMetrics(): SubscriptionMetrics[] {
    return Array.from(this.subscriptions.values()).map(sub => ({ ...sub.metrics }));
  }

  /**
   * Get overall subscription health
   */
  getHealthMetrics(): {
    totalSubscriptions: number;
    activeSubscriptions: number;
    connectionState: string;
    averageMessageRate: number;
    totalErrors: number;
    memoryUsage: number;
  } {
    const metrics = this.getSubscriptionMetrics();
    const activeCount = metrics.filter(m => m.isActive).length;
    const totalMessages = metrics.reduce((sum, m) => sum + m.messageCount, 0);
    const totalErrors = metrics.reduce((sum, m) => sum + m.errorCount, 0);
    const avgMessageRate = metrics.length > 0 ? totalMessages / metrics.length : 0;

    return {
      totalSubscriptions: this.subscriptions.size,
      activeSubscriptions: activeCount,
      connectionState: this.connectionState,
      averageMessageRate: avgMessageRate,
      totalErrors,
      memoryUsage: this.getMemoryUsage()
    };
  }

  /**
   * Pause all subscriptions (useful for background mode)
   */
  private pauseSubscriptions(): void {
    logger.info('Pausing all subscriptions');
    this.subscriptions.forEach((subscription, id) => {
      try {
        supabase.removeChannel(subscription.channel);
      } catch (error) {
        logger.error(`Error pausing subscription ${id}:`, error);
      }
    });
  }

  /**
   * Resume all subscriptions
   */
  private resumeSubscriptions(): void {
    logger.info('Resuming all subscriptions');
    this.subscriptions.forEach((subscription, id) => {
      try {
        subscription.channel.subscribe();
      } catch (error) {
        logger.error(`Error resuming subscription ${id}:`, error);
      }
    });
  }

  /**
   * Handle connection loss with automatic reconnection
   */
  private handleConnectionLoss(): void {
    if (this.connectionState === 'reconnecting') return;
    
    this.connectionState = 'reconnecting';
    logger.info('Attempting to reconnect subscriptions...');

    // Mark all subscriptions as inactive
    this.subscriptions.forEach(subscription => {
      subscription.metrics.isActive = false;
    });

    // Attempt to reconnect after delay
    setTimeout(() => {
      if (this.connectionState === 'reconnecting') {
        this.reconnectAllSubscriptions();
      }
    }, 5000);
  }

  /**
   * Reconnect all subscriptions
   */
  private reconnectAllSubscriptions(): void {
    this.subscriptions.forEach((_subscription, id) => {
      this.scheduleReconnect(id);
    });
  }

  /**
   * Schedule reconnection for a specific subscription
   */
  private scheduleReconnect(subscriptionId: string): void {
    const subscription = this.subscriptions.get(subscriptionId);
    if (!subscription) return;

    const maxAttempts = subscription.config.maxReconnectAttempts || 5;
    const delay = subscription.config.reconnectDelay || 1000;

    if (subscription.metrics.reconnectAttempts >= maxAttempts) {
      logger.error(`Max reconnect attempts reached for ${subscriptionId}`);
      return;
    }

    subscription.metrics.reconnectAttempts++;
    
    subscription.reconnectTimer = setTimeout(() => {
      try {
        subscription.channel.subscribe();
        logger.info(`Reconnection attempt ${subscription.metrics.reconnectAttempts} for ${subscriptionId}`);
      } catch (error) {
        logger.error(`Reconnection failed for ${subscriptionId}:`, error);
        this.scheduleReconnect(subscriptionId);
      }
    }, delay * subscription.metrics.reconnectAttempts);
  }

  /**
   * Start heartbeat monitoring
   */
  private startHeartbeat(): void {
    this.heartbeatInterval = setInterval(() => {
      // Check for inactive subscriptions
      const now = Date.now();
      const staleThreshold = 300000; // 5 minutes

      this.subscriptions.forEach((subscription, id) => {
        if (now - subscription.metrics.lastActivity > staleThreshold) {
          logger.warn(`Subscription ${id} appears stale, last activity: ${new Date(subscription.metrics.lastActivity)}`);
        }
      });
    }, this.heartbeatFrequency);
  }

  /**
   * Start periodic cleanup of inactive subscriptions
   */
  private startPeriodicCleanup(): void {
    this.cleanupInterval = setInterval(() => {
      this.cleanupInactiveSubscriptions();
    }, 600000); // 10 minutes
  }

  /**
   * Clean up inactive subscriptions to prevent memory leaks
   */
  private cleanupInactiveSubscriptions(): void {
    const now = Date.now();
    const inactiveThreshold = 1800000; // 30 minutes
    let cleanedCount = 0;

    this.subscriptions.forEach((subscription, id) => {
      if (!subscription.metrics.isActive && 
          now - subscription.metrics.lastActivity > inactiveThreshold) {
        this.unsubscribe(id);
        cleanedCount++;
      }
    });

    if (cleanedCount > 0) {
      logger.info(`Cleaned up ${cleanedCount} inactive subscriptions`);
    }
  }

  /**
   * Generate unique subscription ID
   */
  private generateSubscriptionId(config: SubscriptionConfig): string {
    const parts = [
      config.table,
      config.event || 'all',
      config.filter || 'no-filter'
    ];
    return parts.join('_').replace(/[^a-zA-Z0-9_]/g, '_');
  }

  /**
   * Get memory usage estimate
   */
  private getMemoryUsage(): number {
    return this.subscriptions.size * 1024; // Rough estimate in bytes
  }

  /**
   * Complete cleanup of all subscriptions
   */
  cleanup(): void {
    logger.info('Cleaning up all subscriptions...');

    // Clear intervals
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
    }
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }

    // Unsubscribe from all
    this.subscriptions.forEach((subscription, id) => {
      try {
        if (subscription.reconnectTimer) {
          clearTimeout(subscription.reconnectTimer);
        }
        subscription.cleanup();
      } catch (error) {
        logger.error(`Error cleaning up subscription ${id}:`, error);
      }
    });

    this.subscriptions.clear();
    logger.info('All subscriptions cleaned up');
  }
}

export const subscriptionManager = SubscriptionManager.getInstance();
export default subscriptionManager;
