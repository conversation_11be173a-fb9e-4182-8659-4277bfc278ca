/**
 * Cache Manager
 * Enterprise-grade caching system for 4M+ users with intelligent invalidation
 */

import { logger } from '../../utils/logger';

export interface CacheEntry<T> {
  data: T;
  timestamp: number;
  ttl: number;
  accessCount: number;
  lastAccessed: number;
  tags: string[];
  size: number;
}

export interface CacheConfig {
  maxSize: number; // Maximum cache size in MB
  defaultTTL: number; // Default TTL in milliseconds
  maxEntries: number; // Maximum number of entries
  compressionThreshold: number; // Compress entries larger than this (bytes)
  persistToDisk: boolean; // Persist cache to localStorage/AsyncStorage
}

export interface CacheMetrics {
  hitRate: number;
  missRate: number;
  totalHits: number;
  totalMisses: number;
  totalEntries: number;
  totalSize: number;
  averageAccessTime: number;
  evictionCount: number;
  compressionRatio: number;
}

export interface CacheStrategy {
  name: 'LRU' | 'LFU' | 'TTL' | 'FIFO';
  maxAge?: number;
  maxSize?: number;
}

class CacheManager {
  private static instance: CacheManager;
  private cache = new Map<string, CacheEntry<any>>();
  private config: CacheConfig;
  private metrics: CacheMetrics;
  private strategy: CacheStrategy;
  private cleanupInterval?: NodeJS.Timeout;
  private compressionEnabled: boolean;

  private constructor(config?: Partial<CacheConfig>) {
    this.config = {
      maxSize: 100, // 100MB default
      defaultTTL: 300000, // 5 minutes
      maxEntries: 10000,
      compressionThreshold: 10240, // 10KB
      persistToDisk: true,
      ...config
    };

    this.metrics = {
      hitRate: 0,
      missRate: 0,
      totalHits: 0,
      totalMisses: 0,
      totalEntries: 0,
      totalSize: 0,
      averageAccessTime: 0,
      evictionCount: 0,
      compressionRatio: 0
    };

    this.strategy = { name: 'LRU' };
    this.compressionEnabled = typeof window !== 'undefined' && 'CompressionStream' in window;

    this.initializeCache();
  }

  public static getInstance(config?: Partial<CacheConfig>): CacheManager {
    if (!CacheManager.instance) {
      CacheManager.instance = new CacheManager(config);
    }
    return CacheManager.instance;
  }

  /**
   * Initialize cache with cleanup and persistence
   */
  private initializeCache(): void {
    // Load persisted cache if available
    if (this.config.persistToDisk) {
      this.loadPersistedCache();
    }

    // Start periodic cleanup
    this.cleanupInterval = setInterval(() => {
      this.cleanup();
    }, 60000); // Every minute

    // Handle app lifecycle for persistence
    if (typeof window !== 'undefined') {
      window.addEventListener('beforeunload', () => {
        if (this.config.persistToDisk) {
          this.persistCache();
        }
      });
    }
  }

  /**
   * Get cached data with intelligent access tracking
   */
  async get<T>(key: string): Promise<T | null> {
    const startTime = Date.now();

    try {
      const entry = this.cache.get(key);

      if (!entry) {
        this.metrics.totalMisses++;
        this.updateHitRate();
        return null;
      }

      // Check TTL
      if (Date.now() - entry.timestamp > entry.ttl) {
        this.cache.delete(key);
        this.metrics.totalMisses++;
        this.updateHitRate();
        return null;
      }

      // Update access metrics
      entry.accessCount++;
      entry.lastAccessed = Date.now();
      this.metrics.totalHits++;
      this.updateHitRate();

      // Decompress if needed
      let data = entry.data;
      if (this.isCompressed(data)) {
        data = await this.decompress(data);
      }

      const accessTime = Date.now() - startTime;
      this.updateAverageAccessTime(accessTime);

      return data;

    } catch (error) {
      logger.error(`Cache get error for key ${key}:`, error);
      this.metrics.totalMisses++;
      this.updateHitRate();
      return null;
    }
  }

  /**
   * Set cached data with intelligent compression and eviction
   */
  async set<T>(
    key: string, 
    data: T, 
    options: {
      ttl?: number;
      tags?: string[];
      compress?: boolean;
    } = {}
  ): Promise<boolean> {
    try {
      const ttl = options.ttl || this.config.defaultTTL;
      const tags = options.tags || [];
      
      // Calculate data size
      const serializedData = JSON.stringify(data);
      let finalData: T = data;
      let size = new Blob([serializedData]).size;

      // Compress large entries
      if (size > this.config.compressionThreshold && 
          (options.compress !== false) && 
          this.compressionEnabled) {
        try {
          finalData = await this.compress(serializedData);
          const compressedSize = new Blob([JSON.stringify(finalData)]).size;
          this.metrics.compressionRatio = (size - compressedSize) / size;
          size = compressedSize;
        } catch (compressionError) {
          logger.warn(`Compression failed for key ${key}, storing uncompressed`);
        }
      }

      // Check if we need to evict entries
      if (this.cache.size >= this.config.maxEntries || 
          this.getTotalSize() + size > this.config.maxSize * 1024 * 1024) {
        await this.evictEntries(size);
      }

      // Create cache entry
      const entry: CacheEntry<T> = {
        data: finalData,
        timestamp: Date.now(),
        ttl,
        accessCount: 0,
        lastAccessed: Date.now(),
        tags,
        size
      };

      this.cache.set(key, entry);
      this.updateMetrics();

      return true;

    } catch (error) {
      logger.error(`Cache set error for key ${key}:`, error);
      return false;
    }
  }

  /**
   * Invalidate cache entries by key or tags
   */
  invalidate(keyOrTag: string, isTag: boolean = false): number {
    let deletedCount = 0;

    if (isTag) {
      // Invalidate by tag
      for (const [key, entry] of this.cache.entries()) {
        if (entry.tags.includes(keyOrTag)) {
          this.cache.delete(key);
          deletedCount++;
        }
      }
    } else {
      // Invalidate by key
      if (this.cache.delete(keyOrTag)) {
        deletedCount = 1;
      }
    }

    if (deletedCount > 0) {
      this.updateMetrics();
      logger.info(`Invalidated ${deletedCount} cache entries for ${isTag ? 'tag' : 'key'}: ${keyOrTag}`);
    }

    return deletedCount;
  }

  /**
   * Batch operations for better performance
   */
  async mget<T>(keys: string[]): Promise<Map<string, T | null>> {
    const results = new Map<string, T | null>();
    
    await Promise.all(
      keys.map(async (key) => {
        const value = await this.get<T>(key);
        results.set(key, value);
      })
    );

    return results;
  }

  async mset<T>(entries: Array<{ key: string; data: T; options?: any }>): Promise<boolean[]> {
    return Promise.all(
      entries.map(entry => 
        this.set(entry.key, entry.data, entry.options)
      )
    );
  }

  /**
   * Smart cache warming for frequently accessed data
   */
  async warmCache(
    warmingStrategy: {
      keys: string[];
      dataLoader: (key: string) => Promise<any>;
      priority?: 'high' | 'medium' | 'low';
    }
  ): Promise<void> {
    const { keys, dataLoader, priority = 'medium' } = warmingStrategy;
    
    logger.info(`Warming cache for ${keys.length} keys with ${priority} priority`);

    const batchSize = priority === 'high' ? 10 : priority === 'medium' ? 5 : 2;
    
    for (let i = 0; i < keys.length; i += batchSize) {
      const batch = keys.slice(i, i + batchSize);
      
      await Promise.all(
        batch.map(async (key) => {
          try {
            if (!this.cache.has(key)) {
              const data = await dataLoader(key);
              await this.set(key, data, { 
                tags: ['warmed'],
                ttl: this.config.defaultTTL * 2 // Longer TTL for warmed data
              });
            }
          } catch (error) {
            logger.error(`Failed to warm cache for key ${key}:`, error);
          }
        })
      );

      // Small delay between batches to prevent overwhelming
      if (i + batchSize < keys.length) {
        await new Promise(resolve => setTimeout(resolve, 10));
      }
    }
  }

  /**
   * Intelligent cache eviction based on strategy
   */
  private async evictEntries(requiredSpace: number): Promise<void> {
    const entries = Array.from(this.cache.entries());
    let evicted = 0;
    let freedSpace = 0;

    switch (this.strategy.name) {
      case 'LRU':
        // Evict least recently used
        entries.sort((a, b) => a[1].lastAccessed - b[1].lastAccessed);
        break;
      case 'LFU':
        // Evict least frequently used
        entries.sort((a, b) => a[1].accessCount - b[1].accessCount);
        break;
      case 'TTL':
        // Evict entries closest to expiration
        entries.sort((a, b) => 
          (a[1].timestamp + a[1].ttl) - (b[1].timestamp + b[1].ttl)
        );
        break;
      case 'FIFO':
        // Evict oldest entries
        entries.sort((a, b) => a[1].timestamp - b[1].timestamp);
        break;
    }

    // Evict entries until we have enough space
    for (const [key, entry] of entries) {
      if (freedSpace >= requiredSpace && evicted >= 10) break;
      
      this.cache.delete(key);
      freedSpace += entry.size;
      evicted++;
    }

    this.metrics.evictionCount += evicted;
    logger.info(`Evicted ${evicted} entries, freed ${freedSpace} bytes`);
  }

  /**
   * Compress data using available compression
   */
  private async compress(data: string): Promise<any> {
    if (!this.compressionEnabled) return data;

    try {
      // Simple compression using built-in methods
      const compressed = btoa(
        String.fromCharCode(...new Uint8Array(
          await new Response(
            new ReadableStream({
              start(controller) {
                controller.enqueue(new TextEncoder().encode(data));
                controller.close();
              }
            }).pipeThrough(new CompressionStream('gzip'))
          ).arrayBuffer()
        ))
      );

      return { __compressed: true, data: compressed };
    } catch (error) {
      logger.warn('Compression failed, storing uncompressed:', error);
      return data;
    }
  }

  /**
   * Decompress data
   */
  private async decompress(compressedData: any): Promise<any> {
    if (!this.isCompressed(compressedData)) return compressedData;

    try {
      const binaryString = atob(compressedData.data);
      const bytes = new Uint8Array(binaryString.length);
      for (let i = 0; i < binaryString.length; i++) {
        bytes[i] = binaryString.charCodeAt(i);
      }

      const decompressed = await new Response(
        new ReadableStream({
          start(controller) {
            controller.enqueue(bytes);
            controller.close();
          }
        }).pipeThrough(new DecompressionStream('gzip'))
      ).text();

      return JSON.parse(decompressed);
    } catch (error) {
      logger.error('Decompression failed:', error);
      return null;
    }
  }

  /**
   * Check if data is compressed
   */
  private isCompressed(data: any): boolean {
    return data && typeof data === 'object' && data.__compressed === true;
  }

  /**
   * Get total cache size in bytes
   */
  private getTotalSize(): number {
    let totalSize = 0;
    for (const entry of this.cache.values()) {
      totalSize += entry.size;
    }
    return totalSize;
  }

  /**
   * Update cache metrics
   */
  private updateMetrics(): void {
    this.metrics.totalEntries = this.cache.size;
    this.metrics.totalSize = this.getTotalSize();
  }

  /**
   * Update hit rate metrics
   */
  private updateHitRate(): void {
    const total = this.metrics.totalHits + this.metrics.totalMisses;
    this.metrics.hitRate = total > 0 ? (this.metrics.totalHits / total) * 100 : 0;
    this.metrics.missRate = 100 - this.metrics.hitRate;
  }

  /**
   * Update average access time
   */
  private updateAverageAccessTime(accessTime: number): void {
    const totalAccesses = this.metrics.totalHits;
    this.metrics.averageAccessTime = 
      (this.metrics.averageAccessTime * (totalAccesses - 1) + accessTime) / totalAccesses;
  }

  /**
   * Periodic cleanup of expired entries
   */
  private cleanup(): void {
    const now = Date.now();
    let cleanedCount = 0;

    for (const [key, entry] of this.cache.entries()) {
      if (now - entry.timestamp > entry.ttl) {
        this.cache.delete(key);
        cleanedCount++;
      }
    }

    if (cleanedCount > 0) {
      this.updateMetrics();
      logger.info(`Cleaned up ${cleanedCount} expired cache entries`);
    }
  }

  /**
   * Persist cache to storage
   */
  private persistCache(): void {
    try {
      if (typeof localStorage === 'undefined') return;

      const cacheData = Array.from(this.cache.entries()).slice(0, 1000); // Limit persisted entries
      localStorage.setItem('app_cache', JSON.stringify(cacheData));
      logger.info(`Persisted ${cacheData.length} cache entries`);
    } catch (error) {
      logger.error('Failed to persist cache:', error);
    }
  }

  /**
   * Load persisted cache
   */
  private loadPersistedCache(): void {
    try {
      if (typeof localStorage === 'undefined') return;

      const cached = localStorage.getItem('app_cache');
      if (cached) {
        const cacheData = JSON.parse(cached);
        const now = Date.now();
        let loadedCount = 0;

        for (const [key, entry] of cacheData) {
          // Only load non-expired entries
          if (now - entry.timestamp < entry.ttl) {
            this.cache.set(key, entry);
            loadedCount++;
          }
        }

        logger.info(`Loaded ${loadedCount} cache entries from persistence`);
        this.updateMetrics();
      }
    } catch (error) {
      logger.error('Failed to load persisted cache:', error);
    }
  }

  /**
   * Get cache metrics for monitoring
   */
  getMetrics(): CacheMetrics {
    this.updateMetrics();
    return { ...this.metrics };
  }

  /**
   * Clear all cache entries
   */
  clear(): void {
    this.cache.clear();
    this.updateMetrics();
    logger.info('Cache cleared');
  }

  /**
   * Cleanup and destroy cache manager
   */
  destroy(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }

    if (this.config.persistToDisk) {
      this.persistCache();
    }

    this.cache.clear();
    logger.info('Cache manager destroyed');
  }
}

export const cacheManager = CacheManager.getInstance();
export default cacheManager;
