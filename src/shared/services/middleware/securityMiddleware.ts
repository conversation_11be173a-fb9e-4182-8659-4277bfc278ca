/**
 * Security Middleware
 * Centralized security validation and protection layer
 */

import { logger } from '../../utils/logger';
import { globalRateLimiter, sanitizeSQLSearch, sanitizeText, validateUUID, ValidationResult } from '../../utils/validation';

export interface SecurityContext {
  userId?: string;
  ipAddress?: string;
  userAgent?: string;
  timestamp: number;
}

export interface SecureQueryParams {
  search?: string;
  userId?: string;
  category?: string;
  limit?: number;
  offset?: number;
}

export interface SecurityValidationResult {
  isValid: boolean;
  sanitizedParams: SecureQueryParams;
  errors: string[];
  warnings: string[];
}

class SecurityMiddleware {
  private static instance: SecurityMiddleware;
  private securityEvents: Array<{ type: string; context: SecurityContext; timestamp: number }> = [];

  private constructor() {}

  public static getInstance(): SecurityMiddleware {
    if (!SecurityMiddleware.instance) {
      SecurityMiddleware.instance = new SecurityMiddleware();
    }
    return SecurityMiddleware.instance;
  }

  /**
   * Validate and sanitize query parameters for database operations
   */
  validateQueryParams(params: SecureQueryParams, context: SecurityContext): SecurityValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];
    const sanitizedParams: SecureQueryParams = {};

    try {
      // Validate search parameter
      if (params.search !== undefined) {
        if (params.search === null || params.search === '') {
          // Allow empty search
          sanitizedParams.search = '';
        } else {
          const searchValidation = sanitizeSQLSearch(params.search);
          if (searchValidation.isValid && searchValidation.sanitizedValue) {
            sanitizedParams.search = searchValidation.sanitizedValue;
          } else {
            errors.push(`Invalid search parameter: ${searchValidation.error}`);
            this.logSecurityEvent('INVALID_SEARCH_PARAM', context, { input: params.search });
          }
        }
      }

      // Validate userId parameter
      if (params.userId !== undefined) {
        const userIdValidation = validateUUID(params.userId);
        if (userIdValidation.isValid && userIdValidation.sanitizedValue) {
          sanitizedParams.userId = userIdValidation.sanitizedValue;
        } else {
          errors.push(`Invalid userId parameter: ${userIdValidation.error}`);
          this.logSecurityEvent('INVALID_USER_ID', context, { input: params.userId });
        }
      }

      // Validate category parameter
      if (params.category !== undefined) {
        const categoryValidation = sanitizeText(params.category, 50);
        if (categoryValidation.isValid && categoryValidation.sanitizedValue) {
          sanitizedParams.category = categoryValidation.sanitizedValue;
        } else {
          errors.push(`Invalid category parameter: ${categoryValidation.error}`);
        }
      }

      // Validate limit parameter
      if (params.limit !== undefined) {
        const limit = parseInt(String(params.limit), 10);
        if (isNaN(limit) || limit < 1 || limit > 100) {
          errors.push('Limit must be a number between 1 and 100');
        } else {
          sanitizedParams.limit = limit;
        }
      }

      // Validate offset parameter
      if (params.offset !== undefined) {
        const offset = parseInt(String(params.offset), 10);
        if (isNaN(offset) || offset < 0) {
          errors.push('Offset must be a non-negative number');
        } else {
          sanitizedParams.offset = offset;
        }
      }

      // Rate limiting check
      const identifier = context.userId || context.ipAddress || 'anonymous';
      if (!globalRateLimiter.isAllowed(identifier)) {
        errors.push('Rate limit exceeded. Please try again later.');
        this.logSecurityEvent('RATE_LIMIT_EXCEEDED', context);
      }

      return {
        isValid: errors.length === 0,
        sanitizedParams,
        errors,
        warnings
      };

    } catch (error) {
      logger.error('Security middleware error:', error);
      this.logSecurityEvent('MIDDLEWARE_ERROR', context, { error: error instanceof Error ? error.message : String(error) });

      return {
        isValid: false,
        sanitizedParams: {},
        errors: ['Security validation failed'],
        warnings: []
      };
    }
  }

  /**
   * Validate database query before execution
   */
  validateDatabaseQuery(query: string, params: any[], context: SecurityContext): boolean {
    try {
      // Check for suspicious query patterns
      const suspiciousPatterns = [
        /;\s*(drop|delete|truncate|alter|create)\s+/i,
        /union\s+select/i,
        /exec\s*\(/i,
        /xp_cmdshell/i,
        /sp_executesql/i,
        /'.*'.*or.*'.*'.*=/i
      ];

      for (const pattern of suspiciousPatterns) {
        if (pattern.test(query)) {
          this.logSecurityEvent('SUSPICIOUS_QUERY', context, { query, params });
          logger.error('Suspicious database query detected:', { query, params });
          return false;
        }
      }

      // Validate parameters
      for (const param of params) {
        if (typeof param === 'string') {
          const validation = sanitizeText(param);
          if (!validation.isValid) {
            this.logSecurityEvent('INVALID_QUERY_PARAM', context, { param });
            return false;
          }
        }
      }

      return true;
    } catch (error) {
      logger.error('Query validation error:', error);
      return false;
    }
  }

  /**
   * Log security events for monitoring
   */
  private logSecurityEvent(type: string, context: SecurityContext, details?: any): void {
    const event = {
      type,
      context,
      timestamp: Date.now(),
      details
    };

    this.securityEvents.push(event);

    // Keep only last 1000 events to prevent memory issues
    if (this.securityEvents.length > 1000) {
      this.securityEvents = this.securityEvents.slice(-1000);
    }

    // Log based on severity
    switch (type) {
      case 'SUSPICIOUS_QUERY':
      case 'RATE_LIMIT_EXCEEDED':
        logger.error(`Security Event [${type}]:`, event);
        break;
      case 'INVALID_SEARCH_PARAM':
      case 'INVALID_USER_ID':
        logger.warn(`Security Event [${type}]:`, event);
        break;
      default:
        logger.info(`Security Event [${type}]:`, event);
    }
  }

  /**
   * Get recent security events for monitoring
   */
  getSecurityEvents(limit: number = 100): Array<any> {
    return this.securityEvents.slice(-limit);
  }

  /**
   * Clear security events (for testing or maintenance)
   */
  clearSecurityEvents(): void {
    this.securityEvents = [];
  }

  /**
   * Create security context from request
   */
  createSecurityContext(userId?: string, ipAddress?: string, userAgent?: string): SecurityContext {
    return {
      userId,
      ipAddress,
      userAgent,
      timestamp: Date.now()
    };
  }

  /**
   * Validate file upload security
   */
  validateFileUpload(file: { name: string; size: number; type: string }, context: SecurityContext): ValidationResult {
    const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
    const maxSize = 5 * 1024 * 1024; // 5MB

    if (!allowedTypes.includes(file.type)) {
      this.logSecurityEvent('INVALID_FILE_TYPE', context, { fileType: file.type });
      return { isValid: false, error: 'Invalid file type. Only images are allowed.' };
    }

    if (file.size > maxSize) {
      this.logSecurityEvent('FILE_TOO_LARGE', context, { fileSize: file.size });
      return { isValid: false, error: 'File too large. Maximum size is 5MB.' };
    }

    // Check for suspicious file names
    const suspiciousPatterns = [
      /\.(exe|bat|cmd|com|pif|scr|vbs|js)$/i,
      /[<>:"|?*]/,
      /^\./,
      /\.\./
    ];

    for (const pattern of suspiciousPatterns) {
      if (pattern.test(file.name)) {
        this.logSecurityEvent('SUSPICIOUS_FILENAME', context, { fileName: file.name });
        return { isValid: false, error: 'Invalid file name.' };
      }
    }

    return { isValid: true };
  }

  /**
   * Generate security report
   */
  generateSecurityReport(): any {
    const now = Date.now();
    const last24Hours = now - (24 * 60 * 60 * 1000);

    const recentEvents = this.securityEvents.filter(event => event.timestamp > last24Hours);

    const eventCounts = recentEvents.reduce((counts, event) => {
      counts[event.type] = (counts[event.type] || 0) + 1;
      return counts;
    }, {} as Record<string, number>);

    return {
      timestamp: now,
      totalEvents: recentEvents.length,
      eventTypes: eventCounts,
      criticalEvents: recentEvents.filter(event =>
        ['SUSPICIOUS_QUERY', 'RATE_LIMIT_EXCEEDED', 'INVALID_USER_ID'].includes(event.type)
      ),
      recommendations: this.generateSecurityRecommendations(eventCounts)
    };
  }

  /**
   * Generate security recommendations based on events
   */
  private generateSecurityRecommendations(eventCounts: Record<string, number>): string[] {
    const recommendations: string[] = [];

    if (eventCounts.RATE_LIMIT_EXCEEDED > 10) {
      recommendations.push('Consider implementing stricter rate limiting or IP blocking');
    }

    if (eventCounts.SUSPICIOUS_QUERY > 0) {
      recommendations.push('Review database query patterns and implement additional SQL injection protection');
    }

    if (eventCounts.INVALID_SEARCH_PARAM > 50) {
      recommendations.push('Review search input validation and user education');
    }

    if (Object.keys(eventCounts).length === 0) {
      recommendations.push('Security monitoring is working correctly');
    }

    return recommendations;
  }
}

export const securityMiddleware = SecurityMiddleware.getInstance();
export default securityMiddleware;
