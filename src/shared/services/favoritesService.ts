/**
 * Unified Favorites Service
 * Handles favorite functionality across all features (daily questions, meal ideas, date night ideas)
 */

import { logger } from '../utils/logger';
import { supabase } from './supabase/client';

export type FavoriteItemType = 'daily_question' | 'meal_idea' | 'date_night_idea' | 'activity' | 'memory';

export interface FavoriteItem {
  id: string;
  user_id: string;
  item_id: string;
  item_type: FavoriteItemType;
  source_table?: string;
  metadata?: Record<string, any>;
  created_at: string;
  updated_at: string;
}

export interface FavoriteMetadata {
  title?: string;
  category?: string;
  emoji?: string;
  description?: string;
  [key: string]: any;
}

class FavoritesService {
  /**
   * Add an item to favorites
   */
  async addFavorite(
    userId: string,
    itemId: string,
    itemType: FavoriteItemType,
    sourceTable?: string,
    metadata?: FavoriteMetadata
  ): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('user_favorites')
        .insert({
          user_id: userId,
          item_id: itemId,
          item_type: itemType,
          source_table: sourceTable,
          metadata: metadata || {}
        });

      if (error) {
        // If it's a unique constraint violation, it's already favorited
        if (error.code === '23505') {
          logger.info('Item already favorited:', { userId, itemId, itemType });
          console.log(`ℹ️ [FAVORITES_SERVICE] Item already favorited: ${itemId}`);
          return true;
        }
        logger.error('Error adding favorite:', error);
        console.log(`❌ [FAVORITES_SERVICE] Add favorite error:`, {
          code: error.code,
          message: error.message,
          details: error.details,
          hint: error.hint
        });
        return false;
      }

      logger.info('Added to favorites:', { userId, itemId, itemType });
      return true;
    } catch (error) {
      logger.error('Error in addFavorite:', error);
      return false;
    }
  }

  /**
   * Remove an item from favorites
   */
  async removeFavorite(
    userId: string,
    itemId: string,
    itemType: FavoriteItemType
  ): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('user_favorites')
        .delete()
        .eq('user_id', userId)
        .eq('item_id', itemId)
        .eq('item_type', itemType);

      if (error) {
        logger.error('Error removing favorite:', error);
        console.log(`❌ [FAVORITES_SERVICE] Remove favorite error:`, {
          code: error.code,
          message: error.message,
          details: error.details,
          hint: error.hint
        });
        return false;
      }

      logger.info('Removed from favorites:', { userId, itemId, itemType });
      return true;
    } catch (error) {
      logger.error('Error in removeFavorite:', error);
      return false;
    }
  }

  /**
   * Toggle favorite status
   */
  async toggleFavorite(
    userId: string,
    itemId: string,
    itemType: FavoriteItemType,
    sourceTable?: string,
    metadata?: FavoriteMetadata
  ): Promise<boolean> {
    try {
      console.log(`🔄 [FAVORITES_SERVICE] Toggling favorite:`, {
        userId,
        itemId,
        itemType,
        sourceTable,
        metadata
      });

      const isFavorited = await this.isFavorited(userId, itemId, itemType);
      console.log(`🔍 [FAVORITES_SERVICE] Current favorite status:`, isFavorited);

      if (isFavorited) {
        const result = await this.removeFavorite(userId, itemId, itemType);
        console.log(`➖ [FAVORITES_SERVICE] Remove result:`, result);
        return result;
      } else {
        const result = await this.addFavorite(userId, itemId, itemType, sourceTable, metadata);
        console.log(`➕ [FAVORITES_SERVICE] Add result:`, result);
        return result;
      }
    } catch (error) {
      logger.error('Error in toggleFavorite:', error);
      console.log(`❌ [FAVORITES_SERVICE] Toggle error:`, error);
      return false;
    }
  }

  /**
   * Check if an item is favorited
   */
  async isFavorited(
    userId: string,
    itemId: string,
    itemType: FavoriteItemType
  ): Promise<boolean> {
    try {
      const { data, error } = await supabase
        .from('user_favorites')
        .select('id')
        .eq('user_id', userId)
        .eq('item_id', itemId)
        .eq('item_type', itemType)
        .single();

      if (error && error.code !== 'PGRST116') { // PGRST116 = no rows returned
        logger.error('Error checking favorite status:', error);
        return false;
      }

      return !!data;
    } catch (error) {
      logger.error('Error in isFavorited:', error);
      return false;
    }
  }

  /**
   * Get all favorites for a user by type
   */
  async getFavoritesByType(
    userId: string,
    itemType: FavoriteItemType
  ): Promise<FavoriteItem[]> {
    try {
      const { data, error } = await supabase
        .from('user_favorites')
        .select('*')
        .eq('user_id', userId)
        .eq('item_type', itemType)
        .order('created_at', { ascending: false });

      if (error) {
        logger.error('Error getting favorites by type:', error);
        return [];
      }

      return (data || []).map(item => ({
        ...item,
        item_type: item.item_type as FavoriteItemType
      }));
    } catch (error) {
      logger.error('Error in getFavoritesByType:', error);
      return [];
    }
  }

  /**
   * Get all favorites for a user
   */
  async getAllFavorites(userId: string): Promise<FavoriteItem[]> {
    try {
      const { data, error } = await supabase
        .from('user_favorites')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false });

      if (error) {
        logger.error('Error getting all favorites:', error);
        return [];
      }

      return (data || []).map(item => ({
        ...item,
        item_type: item.item_type as FavoriteItemType
      }));
    } catch (error) {
      logger.error('Error in getAllFavorites:', error);
      return [];
    }
  }

  /**
   * Get favorite status for multiple items
   */
  async getFavoriteStatuses(
    userId: string,
    items: Array<{ id: string; type: FavoriteItemType }>
  ): Promise<Record<string, boolean>> {
    try {
      const { data, error } = await supabase
        .from('user_favorites')
        .select('item_id, item_type')
        .eq('user_id', userId)
        .in('item_id', items.map(item => item.id))
        .in('item_type', items.map(item => item.type));

      if (error) {
        logger.error('Error getting favorite statuses:', error);
        return {};
      }

      const statuses: Record<string, boolean> = {};
      items.forEach(item => {
        const key = `${item.id}:${item.type}`;
        statuses[key] = data?.some(fav =>
          fav.item_id === item.id && fav.item_type === item.type
        ) || false;
      });

      return statuses;
    } catch (error) {
      logger.error('Error in getFavoriteStatuses:', error);
      return {};
    }
  }

  /**
   * Get favorite count by type
   */
  async getFavoriteCount(
    userId: string,
    itemType: FavoriteItemType
  ): Promise<number> {
    try {
      const { count, error } = await supabase
        .from('user_favorites')
        .select('*', { count: 'exact', head: true })
        .eq('user_id', userId)
        .eq('item_type', itemType);

      if (error) {
        logger.error('Error getting favorite count:', error);
        return 0;
      }

      return count || 0;
    } catch (error) {
      logger.error('Error in getFavoriteCount:', error);
      return 0;
    }
  }
}

// Export singleton instance
export const favoritesService = new FavoritesService();

// Export convenience functions
export const addFavorite = (userId: string, itemId: string, itemType: FavoriteItemType, sourceTable?: string, metadata?: FavoriteMetadata) =>
  favoritesService.addFavorite(userId, itemId, itemType, sourceTable, metadata);

export const removeFavorite = (userId: string, itemId: string, itemType: FavoriteItemType) =>
  favoritesService.removeFavorite(userId, itemId, itemType);

export const toggleFavorite = (userId: string, itemId: string, itemType: FavoriteItemType, sourceTable?: string, metadata?: FavoriteMetadata) =>
  favoritesService.toggleFavorite(userId, itemId, itemType, sourceTable, metadata);

export const isFavorited = (userId: string, itemId: string, itemType: FavoriteItemType) =>
  favoritesService.isFavorited(userId, itemId, itemType);

export const getFavoritesByType = (userId: string, itemType: FavoriteItemType) =>
  favoritesService.getFavoritesByType(userId, itemType);

export const getAllFavorites = (userId: string) =>
  favoritesService.getAllFavorites(userId);

export const getFavoriteStatuses = (userId: string, items: Array<{ id: string; type: FavoriteItemType }>) =>
  favoritesService.getFavoriteStatuses(userId, items);

export const getFavoriteCount = (userId: string, itemType: FavoriteItemType) =>
  favoritesService.getFavoriteCount(userId, itemType);
