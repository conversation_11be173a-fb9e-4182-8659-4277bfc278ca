/**
 * Error Recovery Service
 * 
 * Handles error recovery and fallback mechanisms
 */

export interface RecoveryStrategy {
  name: string;
  execute: () => Promise<boolean>;
  priority: number;
}

export interface RecoveryContext {
  error: Error;
  component?: string;
  action?: string;
  userId?: string;
  timestamp: number;
}

class ErrorRecoveryService {
  private strategies: Map<string, RecoveryStrategy[]> = new Map();
  private recoveryHistory: RecoveryContext[] = [];

  registerStrategy(errorType: string, strategy: RecoveryStrategy): void {
    if (!this.strategies.has(errorType)) {
      this.strategies.set(errorType, []);
    }
    
    const strategies = this.strategies.get(errorType)!;
    strategies.push(strategy);
    
    // Sort by priority (higher priority first)
    strategies.sort((a, b) => b.priority - a.priority);
  }

  async recoverFromError(error: Error, context?: Partial<RecoveryContext>): Promise<boolean> {
    const recoveryContext: RecoveryContext = {
      error,
      timestamp: Date.now(),
      ...context
    };

    this.recoveryHistory.push(recoveryContext);

    // Try to find matching strategies
    const errorType = error.constructor.name;
    const strategies = this.strategies.get(errorType) || this.strategies.get('default') || [];

    for (const strategy of strategies) {
      try {
        const success = await strategy.execute();
        if (success) {
          console.log(`Recovery successful using strategy: ${strategy.name}`);
          return true;
        }
      } catch (strategyError) {
        console.warn(`Recovery strategy ${strategy.name} failed:`, strategyError);
      }
    }

    console.error('All recovery strategies failed for error:', error);
    return false;
  }

  getRecoveryHistory(): RecoveryContext[] {
    return [...this.recoveryHistory];
  }

  clearHistory(): void {
    this.recoveryHistory = [];
  }

  // Common recovery strategies
  private createRetryStrategy(retryFn: () => Promise<any>, maxRetries = 3): RecoveryStrategy {
    return {
      name: 'retry',
      priority: 100,
      execute: async () => {
        for (let i = 0; i < maxRetries; i++) {
          try {
            await retryFn();
            return true;
          } catch (error) {
            if (i === maxRetries - 1) {
              throw error;
            }
            // Wait before retry
            await new Promise(resolve => setTimeout(resolve, 1000 * (i + 1)));
          }
        }
        return false;
      }
    };
  }

  private createFallbackStrategy(fallbackFn: () => Promise<any>): RecoveryStrategy {
    return {
      name: 'fallback',
      priority: 50,
      execute: async () => {
        try {
          await fallbackFn();
          return true;
        } catch (error) {
          return false;
        }
      }
    };
  }

  private createReloadStrategy(): RecoveryStrategy {
    return {
      name: 'reload',
      priority: 10,
      execute: async () => {
        try {
          // In React Native, we might want to restart the app or navigate to a safe screen
          // For now, just return false to indicate this strategy didn't work
          return false;
        } catch (error) {
          return false;
        }
      }
    };
  }

  // Initialize default strategies
  initializeDefaultStrategies(): void {
    // Network error recovery
    this.registerStrategy('NetworkError', this.createRetryStrategy(async () => {
      // Retry network request
      throw new Error('Retry not implemented');
    }));

    // Generic fallback
    this.registerStrategy('default', this.createFallbackStrategy(async () => {
      // Generic fallback behavior
      console.log('Using generic fallback strategy');
    }));

    // Last resort reload
    this.registerStrategy('default', this.createReloadStrategy());
  }
}

export const errorRecoveryService = new ErrorRecoveryService();

// Initialize default strategies
errorRecoveryService.initializeDefaultStrategies();

// Legacy export
export const recoverFromError = (error: Error, context?: Partial<RecoveryContext>) => 
  errorRecoveryService.recoverFromError(error, context);
