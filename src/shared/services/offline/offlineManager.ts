/**
 * Offline Manager
 *
 * Handles offline functionality and operation queuing
 */

export enum OperationType {
  _CREATE = 'create',
  _UPDATE = 'update',
  _DELETE = 'delete',
  _SYNC = 'sync'
}

export interface OfflineOperation {
  id: string;
  type: OperationType;
  table: string;
  data: any;
  timestamp: number;
  retryCount: number;
  maxRetries: number;
}

export interface NetworkStatus {
  isOnline: boolean;
  connectionType?: string;
  lastChecked: number;
}

class OfflineManager {
  private operations: OfflineOperation[] = [];
  private networkStatus: NetworkStatus = {
    isOnline: true,
    lastChecked: Date.now()
  };
  private listeners: ((_status: NetworkStatus) => void)[] = [];
  private syncInProgress = false;

  constructor() {
    this.initializeNetworkMonitoring();
    this.loadQueuedOperations();
  }

  private initializeNetworkMonitoring(): void {
    // In a real React Native app, you'd use @react-native-community/netinfo
    // For now, we'll simulate network monitoring
    setInterval(() => {
      this.checkNetworkStatus();
    }, 5000);
  }

  private async checkNetworkStatus(): Promise<void> {
    try {
      // Simple network check - in production, use proper network detection
      const isOnline = navigator.onLine !== false;

      const previousStatus = this.networkStatus.isOnline;
      this.networkStatus = {
        isOnline,
        lastChecked: Date.now()
      };

      // If we just came back online, trigger sync
      if (!previousStatus && isOnline) {
        this.syncQueuedOperations();
      }

      // Notify listeners
      this.listeners.forEach(listener => listener(this.networkStatus));
    } catch (error) {
      console.warn('Network status check failed:', error);
    }
  }

  isOnline(): boolean {
    return this.networkStatus.isOnline;
  }

  onNetworkStatusChange(listener: (_status: NetworkStatus) => void): () => void {
    this.listeners.push(listener);

    // Return unsubscribe function
    return () => {
      const index = this.listeners.indexOf(listener);
      if (index > -1) {
        this.listeners.splice(index, 1);
      }
    };
  }

  queueOfflineOperation(operation: Omit<OfflineOperation, 'id' | 'timestamp' | 'retryCount'>): void {
    const queuedOperation: OfflineOperation = {
      ...operation,
      id: this.generateOperationId(),
      timestamp: Date.now(),
      retryCount: 0
    };

    this.operations.push(queuedOperation);
    this.saveQueuedOperations();

    // If online, try to sync immediately
    if (this.isOnline()) {
      this.syncQueuedOperations();
    }
  }

  private generateOperationId(): string {
    return `offline_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  async syncQueuedOperations(): Promise<void> {
    if (this.syncInProgress || !this.isOnline() || this.operations.length === 0) {
      return;
    }

    this.syncInProgress = true;

    try {
      const operationsToSync = [...this.operations];

      for (const operation of operationsToSync) {
        try {
          await this.executeOperation(operation);

          // Remove successful operation from queue
          this.operations = this.operations.filter(op => op.id !== operation.id);
        } catch (error) {
          console.warn(`Failed to sync operation ${operation.id}:`, error);

          // Increment retry count
          const operationIndex = this.operations.findIndex(op => op.id === operation.id);
          if (operationIndex > -1) {
            this.operations[operationIndex].retryCount++;

            // Remove if max retries exceeded
            if (this.operations[operationIndex].retryCount >= operation.maxRetries) {
              console.error(`Operation ${operation.id} exceeded max retries, removing from queue`);
              this.operations.splice(operationIndex, 1);
            }
          }
        }
      }

      this.saveQueuedOperations();
    } finally {
      this.syncInProgress = false;
    }
  }

  private async executeOperation(operation: OfflineOperation): Promise<void> {
    // In a real implementation, this would execute the actual database operation
    console.log(`Executing offline operation:`, operation);

    // Simulate operation execution
    await new Promise(resolve => setTimeout(resolve, 100));

    // For now, just log success
    console.log(`Operation ${operation.id} executed successfully`);
  }

  private saveQueuedOperations(): void {
    try {
      localStorage.setItem('offline_operations', JSON.stringify(this.operations));
    } catch (error) {
      console.warn('Failed to save queued operations:', error);
    }
  }

  private loadQueuedOperations(): void {
    try {
      const saved = localStorage.getItem('offline_operations');
      if (saved) {
        this.operations = JSON.parse(saved);
      }
    } catch (error) {
      console.warn('Failed to load queued operations:', error);
      this.operations = [];
    }
  }

  getQueuedOperations(): OfflineOperation[] {
    return [...this.operations];
  }

  clearQueue(): void {
    this.operations = [];
    this.saveQueuedOperations();
  }
}

export const offlineManager = new OfflineManager();

// Legacy exports
export const isOnline = () => offlineManager.isOnline();
export const queueOfflineOperation = (operation: Omit<OfflineOperation, 'id' | 'timestamp' | 'retryCount'>) =>
  offlineManager.queueOfflineOperation(operation);
