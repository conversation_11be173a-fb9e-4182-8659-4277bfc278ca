/**
 * Activity Event Service
 * Handles standardized event system for activity interactions
 */

import {
    ActivityEvent,
    ActivityEventType,
    ActivitySession
} from '../../types/activity.types';
import { logger } from '../../utils/logger';
import { supabase } from '../supabase/client';

class ActivityEventService {
  private eventListeners: Map<ActivityEventType, ((_event: ActivityEvent) => void)[]> = new Map();

  constructor() {
    // Initialize event listener maps
    const eventTypes: ActivityEventType[] = [
      'activity_started',
      'activity_paused',
      'activity_resumed',
      'activity_completed',
      'activity_exited',
      'activity_error',
      'question_answered',
      'milestone_reached',
      'score_updated'
    ];

    eventTypes.forEach(eventType => {
      this.eventListeners.set(eventType, []);
    });

    logger.info('Activity Event Service initialized');
  }

  /**
   * Emit an activity event
   */
  async emitEvent(event: ActivityEvent): Promise<void> {
    try {
      // Log the event
      logger.info(`Activity Event: ${event.eventType}`, event);

      // Save to database
      await this.saveEventToDatabase(event);

      // Notify listeners
      this.notifyListeners(event);

      // Send to analytics (if configured)
      await this.sendToAnalytics(event);
    } catch (error) {
      logger.error('Failed to emit activity event:', error);
      throw error;
    }
  }

  /**
   * Subscribe to activity events
   */
  subscribe(eventType: ActivityEventType, callback: (_event: ActivityEvent) => void): () => void {
    const listeners = this.eventListeners.get(eventType) || [];
    listeners.push(callback);
    this.eventListeners.set(eventType, listeners);

    // Return unsubscribe function
    return () => {
      const currentListeners = this.eventListeners.get(eventType) || [];
      const index = currentListeners.indexOf(callback);
      if (index > -1) {
        currentListeners.splice(index, 1);
        this.eventListeners.set(eventType, currentListeners);
      }
    };
  }

  /**
   * Create a new activity session
   */
  async createSession(
    activityId: string,
    userId: string,
    coupleId: string
  ): Promise<ActivitySession> {
    try {
      const session: Omit<ActivitySession, 'id' | 'createdAt' | 'updatedAt'> = {
        activityId,
        userId,
        coupleId,
        status: 'not_started',
        startedAt: new Date().toISOString(),
        score: 0,
        points: 0,
        data: {},
        events: []
      };

      const { data, error } = await supabase
        .from('activity_sessions')
        .insert({
          activity_id: session.activityId,
          user_id: session.userId,
          couple_id: session.coupleId,
          status: session.status,
          started_at: session.startedAt,
          completed_at: session.completedAt,
          score: session.score,
          points: session.points,
          data: session.data as any,
          events: session.events as any
        })
        .select()
        .single();

      if (error) {
        throw error;
      }

      logger.info(`Activity session created: ${data.id}`);
      return {
        id: data.id,
        activityId: data.activity_id,
        userId: data.user_id,
        coupleId: data.couple_id,
        status: data.status as any,
        startedAt: data.started_at,
        completedAt: data.completed_at || undefined,
        score: data.score,
        points: data.points,
        data: data.data as Record<string, any>,
        events: data.events as any,
        createdAt: data.created_at,
        updatedAt: data.updated_at
      };
    } catch (error) {
      logger.error('Failed to create activity session:', error);
      throw error;
    }
  }

  /**
   * Update activity session
   */
  async updateSession(
    sessionId: string,
    updates: Partial<ActivitySession>
  ): Promise<ActivitySession> {
    try {
      const { data, error } = await supabase
        .from('activity_sessions')
        .update({
          activity_id: updates.activityId,
          user_id: updates.userId,
          couple_id: updates.coupleId,
          status: updates.status,
          started_at: updates.startedAt,
          completed_at: updates.completedAt,
          score: updates.score,
          points: updates.points,
          data: updates.data as any,
          events: updates.events as any,
          updated_at: new Date().toISOString()
        })
        .eq('id', sessionId)
        .select()
        .single();

      if (error) {
        throw error;
      }

      return {
        id: data.id,
        activityId: data.activity_id,
        userId: data.user_id,
        coupleId: data.couple_id,
        status: data.status as any,
        startedAt: data.started_at,
        completedAt: data.completed_at || undefined,
        score: data.score,
        points: data.points,
        data: data.data as Record<string, any>,
        events: data.events as any,
        createdAt: data.created_at,
        updatedAt: data.updated_at
      };
    } catch (error) {
      logger.error('Failed to update activity session:', error);
      throw error;
    }
  }

  /**
   * Get activity session by ID
   */
  async getSession(sessionId: string): Promise<ActivitySession | null> {
    try {
      const { data, error } = await supabase
        .from('activity_sessions')
        .select('*')
        .eq('id', sessionId)
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          return null; // Session not found
        }
        throw error;
      }

      return {
        id: data.id,
        activityId: data.activity_id,
        userId: data.user_id,
        coupleId: data.couple_id,
        status: data.status as any,
        startedAt: data.started_at,
        completedAt: data.completed_at || undefined,
        score: data.score,
        points: data.points,
        data: data.data as Record<string, any>,
        events: data.events as any,
        createdAt: data.created_at,
        updatedAt: data.updated_at
      };
    } catch (error) {
      logger.error('Failed to get activity session:', error);
      throw error;
    }
  }

  /**
   * Get user's activity sessions
   */
  async getUserSessions(
    userId: string,
    limit: number = 50,
    offset: number = 0
  ): Promise<ActivitySession[]> {
    try {
      const { data, error } = await supabase
        .from('activity_sessions')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false })
        .range(offset, offset + limit - 1);

      if (error) {
        throw error;
      }

      return (data || []).map(session => ({
        id: session.id,
        activityId: session.activity_id,
        userId: session.user_id,
        coupleId: session.couple_id,
        status: session.status as any,
        startedAt: session.started_at,
        completedAt: session.completed_at || undefined,
        score: session.score,
        points: session.points,
        data: session.data as Record<string, any>,
        events: session.events as any,
        createdAt: session.created_at,
        updatedAt: session.updated_at
      }));
    } catch (error) {
      logger.error('Failed to get user activity sessions:', error);
      throw error;
    }
  }

  /**
   * Save event to database
   */
  private async saveEventToDatabase(event: ActivityEvent): Promise<void> {
    try {
      const { error } = await supabase
        .from('activity_events')
        .insert({
          id: event.id,
          activity_id: event.activityId,
          user_id: event.userId,
          couple_id: event.coupleId,
          event_type: event.eventType,
          data: event.data,
          timestamp: event.timestamp,
          metadata: event.metadata
        });

      if (error) {
        throw error;
      }
    } catch (error) {
      logger.error('Failed to save activity event to database:', error);
      // Don't throw here - we don't want to break the activity flow
    }
  }

  /**
   * Notify event listeners
   */
  private notifyListeners(event: ActivityEvent): void {
    const listeners = this.eventListeners.get(event.eventType) || [];
    listeners.forEach(callback => {
      try {
        callback(event);
      } catch (error) {
        logger.error('Error in activity event listener:', error);
      }
    });
  }

  /**
   * Send event to analytics service
   */
  private async sendToAnalytics(event: ActivityEvent): Promise<void> {
    try {
      // TODO: Implement analytics integration
      // This could send events to services like Mixpanel, Amplitude, etc.
      logger.debug('Sending activity event to analytics:', event.eventType);
    } catch (error) {
      logger.error('Failed to send activity event to analytics:', error);
      // Don't throw here - analytics failures shouldn't break the activity
    }
  }

  /**
   * Get activity statistics for a user
   */
  async getUserActivityStats(userId: string): Promise<{
    totalSessions: number;
    totalScore: number;
    totalPoints: number;
    averageScore: number;
    favoriteActivity: string;
    completionRate: number;
  }> {
    try {
      const { data, error } = await supabase
        .from('activity_sessions')
        .select('*')
        .eq('userId', userId);

      if (error) {
        throw error;
      }

      const sessions = data || [];
      const completedSessions = sessions.filter(s => s.status === 'completed');

      const totalScore = sessions.reduce((sum, s) => sum + s.score, 0);
      const totalPoints = sessions.reduce((sum, s) => sum + s.points, 0);
      const averageScore = sessions.length > 0 ? totalScore / sessions.length : 0;

      // Find favorite activity
      const activityCounts = sessions.reduce((counts, session) => {
        counts[session.activity_id] = (counts[session.activity_id] || 0) + 1;
        return counts;
      }, {} as Record<string, number>);

      const favoriteActivity = Object.entries(activityCounts)
        .sort(([, a], [, b]) => b - a)[0]?.[0] || '';

      const completionRate = sessions.length > 0
        ? (completedSessions.length / sessions.length) * 100
        : 0;

      return {
        totalSessions: sessions.length,
        totalScore,
        totalPoints,
        averageScore,
        favoriteActivity,
        completionRate
      };
    } catch (error) {
      logger.error('Failed to get user activity stats:', error);
      throw error;
    }
  }
}

// Create singleton instance
export const activityEventService = new ActivityEventService();

// Export the class for testing
export { ActivityEventService };
