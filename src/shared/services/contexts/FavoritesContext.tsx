import React, { createContext, useCallback, useContext, useRef } from 'react';
import { FavoriteItemType } from '../favoritesService';

export interface FavoritesContextValue {
  /** Notify all subscribers of a favorite change */
  notifyFavoriteChange: (_contentType: FavoriteItemType, _itemId: string, _isFavorited: boolean) => void;
  /** Subscribe to favorite changes for a specific content type */
  subscribeTo: (_contentType: FavoriteItemType, _callback: (_itemId: string, _isFavorited: boolean) => void) => () => void;
  /** Subscribe to all favorite changes */
  subscribeToAll: (_callback: (_contentType: FavoriteItemType, _itemId: string, _isFavorited: boolean) => void) => () => void;
}

const FavoritesContext = createContext<FavoritesContextValue | null>(null);

export interface FavoritesProviderProps {
  children: React.ReactNode;
}

/**
 * Global favorites context provider for managing state synchronization across components
 */
export const FavoritesProvider: React.FC<FavoritesProviderProps> = ({ children }) => {
  // Store subscribers by content type
  const subscribersRef = useRef<Record<FavoriteItemType, Set<(_itemId: string, _isFavorited: boolean) => void>>>({
    date_night_idea: new Set(),
    meal_idea: new Set(),
    memory: new Set(),
    daily_question: new Set(),
    activity: new Set(),
  });

  // Store global subscribers (listen to all changes)
  const globalSubscribersRef = useRef<Set<(_contentType: FavoriteItemType, _itemId: string, _isFavorited: boolean) => void>>(new Set());

  const notifyFavoriteChange = useCallback((contentType: FavoriteItemType, itemId: string, isFavorited: boolean) => {
    // Notify content-specific subscribers
    const contentSubscribers = subscribersRef.current[contentType];
    if (contentSubscribers) {
      contentSubscribers.forEach(callback => {
        try {
          callback(itemId, isFavorited);
        } catch (error) {
          console.error('Error in favorite change callback:', error);
        }
      });
    }

    // Notify global subscribers
    globalSubscribersRef.current.forEach(callback => {
      try {
        callback(contentType, itemId, isFavorited);
      } catch (error) {
        console.error('Error in global favorite change callback:', error);
      }
    });
  }, []);

  const subscribeTo = useCallback((contentType: FavoriteItemType, callback: (_itemId: string, _isFavorited: boolean) => void) => {
    const subscribers = subscribersRef.current[contentType];
    if (subscribers) {
      subscribers.add(callback);
    }

    // Return unsubscribe function
    return () => {
      const subscribers = subscribersRef.current[contentType];
      if (subscribers) {
        subscribers.delete(callback);
      }
    };
  }, []);

  const subscribeToAll = useCallback((callback: (_contentType: FavoriteItemType, _itemId: string, _isFavorited: boolean) => void) => {
    globalSubscribersRef.current.add(callback);

    // Return unsubscribe function
    return () => {
      globalSubscribersRef.current.delete(callback);
    };
  }, []);

  const value: FavoritesContextValue = {
    notifyFavoriteChange,
    subscribeTo,
    subscribeToAll,
  };

  return (
    <FavoritesContext.Provider value={value}>
      {children}
    </FavoritesContext.Provider>
  );
};

/**
 * Hook to access the favorites context
 */
export const useFavoritesContext = (): FavoritesContextValue => {
  const context = useContext(FavoritesContext);
  if (!context) {
    throw new Error('useFavoritesContext must be used within a FavoritesProvider');
  }
  return context;
};

/**
 * Hook to subscribe to favorite changes for a specific content type
 */
export const useFavoriteChangeListener = (
  contentType: FavoriteItemType,
  callback: (_itemId: string, _isFavorited: boolean) => void
) => {
  const { subscribeTo } = useFavoritesContext();

  React.useEffect(() => {
    const unsubscribe = subscribeTo(contentType, callback);
    return unsubscribe;
  }, [subscribeTo, contentType, callback]);
};

/**
 * Hook to subscribe to all favorite changes across all content types
 */
export const useGlobalFavoriteChangeListener = (
  callback: (_contentType: FavoriteItemType, _itemId: string, _isFavorited: boolean) => void
) => {
  const { subscribeToAll } = useFavoritesContext();

  React.useEffect(() => {
    const unsubscribe = subscribeToAll(callback);
    return unsubscribe;
  }, [subscribeToAll, callback]);
};
