/**
 * Central type definitions for the EVERLASTING-US app
 * Following React Native best practices structure
 */

// Re-export types from other type files
export * from './matchGame.types';

// Core API types
export interface ApiResponse<T = any> {
  data?: T;
  error?: string;
  success: boolean;
  message?: string;
}

export interface Metadata {
  total?: number;
  page?: number;
  limit?: number;
  hasMore?: boolean;
}

export interface SanitizedObject {
  [key: string]: any;
}

// Date Night types
export type CostLevel = 'free' | 'low' | 'medium' | 'high';
export type DifficultyLevel = 'easy' | 'medium' | 'hard';
export type LocationType = 'indoor' | 'outdoor' | 'both';

export interface DateNightIdea {
  id: string;
  title: string;
  description: string;
  category: string;
  difficulty?: string;
  emoji?: string;
  tags?: string[];
  isFavorited?: boolean;
  // User-specific properties (optional for global ideas)
  user_id?: string;
  estimatedDuration?: number;
  costLevel?: string;
  indoorOutdoor?: string;
  weekNumber?: number;
  source?: 'global' | 'user';
  composite_id?: string;
  // Additional properties from database
  created_at?: string;
  updated_at?: string;
}

export interface DateNightIdeasByWeek {
  [weekNumber: string]: DateNightIdea[];
}

// Surprise Me types
export interface SurpriseItem {
  id: string;
  title: string;
  description?: string;
  emoji?: string;
  category: string;
  type: 'activity' | 'question' | 'challenge';
  difficulty?: string;
  tags?: string[];
  prepTime?: number;
  cookTime?: number;
  servings?: number;
  ingredients?: string[];
  instructions?: string[];
  weekNumber?: number;
}

// Story types
export interface StorySection {
  id: string;
  title: string;
  subtitle?: string;
  content: string;
  date?: string;
  photos?: string[];
  icon?: string;
  color?: string;
  tags?: string[];
  isEditable?: boolean;
}

export interface StoryData {
  sections: StorySection[];
  metadata?: {
    createdAt: string;
    updatedAt: string;
    version: number;
  };
}

// Game types
export interface MatchGameResponse {
  success: boolean;
  data?: any;
  error?: string;
}

export interface GameActivityResponse {
  points: number;
  bonusPoints: number;
  success: boolean;
  message?: string;
}

// Error handling types
export interface ErrorContext {
  component?: string;
  action?: string;
  userId?: string;
  timestamp?: string;
  metadata?: Record<string, any>;
}

export interface ErrorReport {
  id: string;
  message: string;
  stack?: string;
  context: ErrorContext;
  severity: 'low' | 'medium' | 'high' | 'critical';
  resolved: boolean;
  createdAt: string;
}

// User types
export interface UserProfile {
  id: string;
  email?: string;
  name?: string;
  avatar?: string;
  preferences?: UserSettings;
  createdAt: string;
  updatedAt: string;
}

export interface UserSettings {
  theme: 'light' | 'dark' | 'auto';
  notifications: boolean;
  language: string;
  timezone: string;
}

// UI Component types
export interface ThemeAwareProps {
  theme?: 'light' | 'dark';
}

export interface LoadingStateProps {
  loading?: boolean;
  error?: string | null;
}

export interface ModalProps {
  visible: boolean;
  onClose: () => void;
  title?: string;
}

// Date Night Filters
export interface DateNightFilters {
  category?: string;
  cost?: 'free' | 'low' | 'medium' | 'high';
  indoor_outdoor?: 'indoor' | 'outdoor' | 'both';
  difficulty?: 'easy' | 'medium' | 'hard';
  week_number?: number;
  search?: string;
}

// Utility types
export type FunctionReturn<T extends (..._args: unknown[]) => unknown> = T extends (..._args: unknown[]) => infer R ? R : never;
