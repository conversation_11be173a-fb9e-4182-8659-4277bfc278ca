/**
 * Activity Component System Types
 * Standardized interfaces for modular activity components
 */

import { ReactNode } from 'react';

// Core activity metadata
export interface ActivityMetadata {
  id: string;
  name: string;
  description: string;
  category: ActivityCategory;
  difficulty: ActivityDifficulty;
  duration: string;
  players: number;
  points: number;
  icon: ReactNode;
  theme?: ActivityTheme;
  tags?: string[];
  isAvailable: boolean;
  version: string;
  createdAt: string;
  updatedAt: string;
}

// Activity categories following existing patterns
export type ActivityCategory =
  | 'all'
  | 'getting-to-know'
  | 'communication'
  | 'fun'
  | 'reflection'
  | 'skills'
  | 'planning';

// Activity difficulty levels
export type ActivityDifficulty = 'easy' | 'medium' | 'hard';

// Activity themes following existing patterns
export type ActivityTheme = 'default' | 'romantic' | 'playful' | 'reflective';

// Activity component interface
export interface ActivityComponent {
  id: string;
  name: string;
  category: ActivityCategory;
  metadata: ActivityMetadata;
  component: React.ComponentType<ActivityProps>;
  supabaseTable: string;
  eventHandlers?: ActivityEventHandlers;
}

// Standardized activity props interface
export interface ActivityProps {
  activityId: string;
  userId: string;
  coupleId: string;
  onComplete: (_result: ActivityResult) => void;
  onExit: () => void;
  onError: (_error: ActivityError) => void;
  theme?: ActivityTheme;
  customData?: Record<string, any>;
}

// Activity result interface
export interface ActivityResult {
  activityId: string;
  userId: string;
  coupleId: string;
  score: number;
  points: number;
  completedAt: string;
  data: Record<string, any>;
  metadata?: Record<string, any>;
}

// Activity error interface
export interface ActivityError {
  activityId: string;
  userId: string;
  error: string;
  code?: string;
  timestamp: string;
  context?: Record<string, any>;
}

// Activity event types
export type ActivityEventType =
  | 'activity_started'
  | 'activity_paused'
  | 'activity_resumed'
  | 'activity_completed'
  | 'activity_exited'
  | 'activity_error'
  | 'question_answered'
  | 'milestone_reached'
  | 'score_updated';

// Activity event interface
export interface ActivityEvent {
  id: string;
  activityId: string;
  userId: string;
  coupleId: string;
  eventType: ActivityEventType;
  data: Record<string, any>;
  timestamp: string;
  metadata?: Record<string, any>;
}

// Activity event handlers
export interface ActivityEventHandlers {
  onActivityStarted?: (_event: ActivityEvent) => void;
  onActivityPaused?: (_event: ActivityEvent) => void;
  onActivityResumed?: (_event: ActivityEvent) => void;
  onActivityCompleted?: (_event: ActivityEvent) => void;
  onActivityExited?: (_event: ActivityEvent) => void;
  onActivityError?: (_event: ActivityEvent) => void;
  onQuestionAnswered?: (_event: ActivityEvent) => void;
  onMilestoneReached?: (_event: ActivityEvent) => void;
  onScoreUpdated?: (_event: ActivityEvent) => void;
}

// Activity registry interface
export interface ActivityRegistry {
  activities: Map<string, ActivityComponent>;
  categories: Map<ActivityCategory, ActivityComponent[]>;
  registerActivity: (_activity: ActivityComponent) => void;
  unregisterActivity: (_activityId: string) => void;
  getActivity: (_activityId: string) => ActivityComponent | undefined;
  getActivitiesByCategory: (_category: ActivityCategory) => ActivityComponent[];
  getAllActivities: () => ActivityComponent[];
  getAvailableActivities: () => ActivityComponent[];
}

// Activity container props
export interface ActivityContainerProps {
  activityId: string;
  userId: string;
  coupleId: string;
  onComplete?: (_result: ActivityResult) => void;
  onExit?: () => void;
  onError?: (_error: ActivityError) => void;
  theme?: ActivityTheme;
  customData?: Record<string, any>;
  showHeader?: boolean;
  showProgress?: boolean;
  showExitButton?: boolean;
}

// Activity state interface
export interface ActivityState {
  activityId: string;
  userId: string;
  coupleId: string;
  status: ActivityStatus;
  currentPhase: string;
  progress: number;
  score: number;
  points: number;
  startedAt?: string;
  completedAt?: string;
  data: Record<string, any>;
  error?: string;
}

// Activity status
export type ActivityStatus =
  | 'not_started'
  | 'loading'
  | 'in_progress'
  | 'paused'
  | 'completed'
  | 'error'
  | 'exited';

// Activity session interface
export interface ActivitySession {
  id: string;
  activityId: string;
  userId: string;
  coupleId: string;
  status: ActivityStatus;
  startedAt: string;
  completedAt?: string;
  score: number;
  points: number;
  data: Record<string, any>;
  events: ActivityEvent[];
  createdAt: string;
  updatedAt: string;
}

// Activity statistics interface
export interface ActivityStats {
  activityId: string;
  userId: string;
  totalPlays: number;
  totalScore: number;
  totalPoints: number;
  averageScore: number;
  bestScore: number;
  lastPlayed: string;
  completionRate: number;
  favoriteCategory: ActivityCategory;
}

// Activity filter options
export interface ActivityFilterOptions {
  category?: ActivityCategory;
  difficulty?: ActivityDifficulty;
  theme?: ActivityTheme;
  isAvailable?: boolean;
  tags?: string[];
  search?: string;
}

// Activity sort options
export interface ActivitySortOptions {
  field: 'name' | 'category' | 'difficulty' | 'points' | 'createdAt';
  direction: 'asc' | 'desc';
}

// Activity query options
export interface ActivityQueryOptions {
  filters?: ActivityFilterOptions;
  sort?: ActivitySortOptions;
  limit?: number;
  offset?: number;
}
