export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  // Allows to automatically instantiate createClient with right options
  // instead of createClient<Database, { PostgrestVersion: 'XX' }>(URL, KEY)
  __InternalSupabase: {
    PostgrestVersion: "13.0.4"
  }
  graphql_public: {
    Tables: {
      [_ in never]: never
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      graphql: {
        Args: {
          extensions?: Json
          operationName?: string
          query?: string
          variables?: Json
        }
        Returns: Json
      }
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
  public: {
    Tables: {
      activity_events: {
        Row: {
          activity_id: string
          couple_id: string
          created_at: string
          data: Json | null
          event_type: string
          id: string
          metadata: Json | null
          timestamp: string
          user_id: string
        }
        Insert: {
          activity_id: string
          couple_id: string
          created_at?: string
          data?: Json | null
          event_type: string
          id: string
          metadata?: Json | null
          timestamp: string
          user_id: string
        }
        Update: {
          activity_id?: string
          couple_id?: string
          created_at?: string
          data?: Json | null
          event_type?: string
          id?: string
          metadata?: Json | null
          timestamp?: string
          user_id?: string
        }
        Relationships: []
      }
      activity_sessions: {
        Row: {
          activity_id: string
          completed_at: string | null
          couple_id: string
          created_at: string
          data: Json | null
          events: Json | null
          id: string
          points: number
          score: number
          started_at: string
          status: string
          updated_at: string
          user_id: string
        }
        Insert: {
          activity_id: string
          completed_at?: string | null
          couple_id: string
          created_at?: string
          data?: Json | null
          events?: Json | null
          id?: string
          points?: number
          score?: number
          started_at?: string
          status?: string
          updated_at?: string
          user_id: string
        }
        Update: {
          activity_id?: string
          completed_at?: string | null
          couple_id?: string
          created_at?: string
          data?: Json | null
          events?: Json | null
          id?: string
          points?: number
          score?: number
          started_at?: string
          status?: string
          updated_at?: string
          user_id?: string
        }
        Relationships: []
      }
      couple_milestones: {
        Row: {
          completed_by: string | null
          completion_date: string | null
          couple_id: string
          created_at: string | null
          id: string
          is_completed: boolean | null
          milestone_data: Json
          milestone_template_id: string
          timeline_event_id: string | null
          updated_at: string | null
        }
        Insert: {
          completed_by?: string | null
          completion_date?: string | null
          couple_id: string
          created_at?: string | null
          id?: string
          is_completed?: boolean | null
          milestone_data?: Json
          milestone_template_id: string
          timeline_event_id?: string | null
          updated_at?: string | null
        }
        Update: {
          completed_by?: string | null
          completion_date?: string | null
          couple_id?: string
          created_at?: string | null
          id?: string
          is_completed?: boolean | null
          milestone_data?: Json
          milestone_template_id?: string
          timeline_event_id?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "couple_milestones_milestone_template_id_fkey"
            columns: ["milestone_template_id"]
            isOneToOne: false
            referencedRelation: "milestone_templates"
            referencedColumns: ["id"]
          },
        ]
      }
      couple_stories: {
        Row: {
          completed_sections: string[]
          couple_id: string
          created_at: string
          id: string
          last_updated: string
          last_updated_by: string | null
          story_data: Json
        }
        Insert: {
          completed_sections?: string[]
          couple_id: string
          created_at?: string
          id?: string
          last_updated?: string
          last_updated_by?: string | null
          story_data?: Json
        }
        Update: {
          completed_sections?: string[]
          couple_id?: string
          created_at?: string
          id?: string
          last_updated?: string
          last_updated_by?: string | null
          story_data?: Json
        }
        Relationships: [
          {
            foreignKeyName: "couple_stories_couple_fkey"
            columns: ["couple_id"]
            isOneToOne: true
            referencedRelation: "couples"
            referencedColumns: ["id"]
          },
        ]
      }
      couples: {
        Row: {
          couple_code: string
          created_at: string
          expires_at: string
          id: string
          partner1_user_id: string
          partner2_user_id: string | null
          qr_code_data: string | null
          status: string
        }
        Insert: {
          couple_code: string
          created_at?: string
          expires_at?: string
          id?: string
          partner1_user_id: string
          partner2_user_id?: string | null
          qr_code_data?: string | null
          status?: string
        }
        Update: {
          couple_code?: string
          created_at?: string
          expires_at?: string
          id?: string
          partner1_user_id?: string
          partner2_user_id?: string | null
          qr_code_data?: string | null
          status?: string
        }
        Relationships: []
      }
      daily_question_responses: {
        Row: {
          answered_at: string | null
          couple_id: string | null
          id: string
          is_visible_to_partner: boolean | null
          question_date: string
          question_id: string | null
          response_text: string
          user_id: string | null
        }
        Insert: {
          answered_at?: string | null
          couple_id?: string | null
          id?: string
          is_visible_to_partner?: boolean | null
          question_date: string
          question_id?: string | null
          response_text: string
          user_id?: string | null
        }
        Update: {
          answered_at?: string | null
          couple_id?: string | null
          id?: string
          is_visible_to_partner?: boolean | null
          question_date?: string
          question_id?: string | null
          response_text?: string
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "daily_question_responses_couple_id_fkey"
            columns: ["couple_id"]
            isOneToOne: false
            referencedRelation: "couples"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "daily_question_responses_question_id_fkey"
            columns: ["question_id"]
            isOneToOne: false
            referencedRelation: "daily_questions"
            referencedColumns: ["question_id"]
          },
        ]
      }
      daily_question_schedule: {
        Row: {
          created_at: string | null
          id: string
          is_active: boolean | null
          question_date: string
          question_id: string | null
        }
        Insert: {
          created_at?: string | null
          id?: string
          is_active?: boolean | null
          question_date: string
          question_id?: string | null
        }
        Update: {
          created_at?: string | null
          id?: string
          is_active?: boolean | null
          question_date?: string
          question_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "daily_question_schedule_question_id_fkey"
            columns: ["question_id"]
            isOneToOne: false
            referencedRelation: "daily_questions"
            referencedColumns: ["question_id"]
          },
        ]
      }
      daily_questions: {
        Row: {
          category: string
          created_at: string | null
          difficulty: string | null
          id: string
          is_active: boolean | null
          question_id: string
          question_text: string
          tone: string | null
        }
        Insert: {
          category: string
          created_at?: string | null
          difficulty?: string | null
          id?: string
          is_active?: boolean | null
          question_id: string
          question_text: string
          tone?: string | null
        }
        Update: {
          category?: string
          created_at?: string | null
          difficulty?: string | null
          id?: string
          is_active?: boolean | null
          question_id?: string
          question_text?: string
          tone?: string | null
        }
        Relationships: []
      }
      date_night_ideas_global: {
        Row: {
          category: string | null
          cost: Database["public"]["Enums"]["cost_level"]
          created_at: string | null
          description: string
          difficulty: Database["public"]["Enums"]["difficulty_level"]
          emoji: string | null
          estimated_duration: number | null
          id: string
          indoor_outdoor: Database["public"]["Enums"]["location_type"]
          slug: string | null
          source: Database["public"]["Enums"]["idea_source"]
          title: string
          updated_at: string | null
          week_number: number | null
        }
        Insert: {
          category?: string | null
          cost: Database["public"]["Enums"]["cost_level"]
          created_at?: string | null
          description: string
          difficulty: Database["public"]["Enums"]["difficulty_level"]
          emoji?: string | null
          estimated_duration?: number | null
          id?: string
          indoor_outdoor: Database["public"]["Enums"]["location_type"]
          slug?: string | null
          source?: Database["public"]["Enums"]["idea_source"]
          title: string
          updated_at?: string | null
          week_number?: number | null
        }
        Update: {
          category?: string | null
          cost?: Database["public"]["Enums"]["cost_level"]
          created_at?: string | null
          description?: string
          difficulty?: Database["public"]["Enums"]["difficulty_level"]
          emoji?: string | null
          estimated_duration?: number | null
          id?: string
          indoor_outdoor?: Database["public"]["Enums"]["location_type"]
          slug?: string | null
          source?: Database["public"]["Enums"]["idea_source"]
          title?: string
          updated_at?: string | null
          week_number?: number | null
        }
        Relationships: []
      }
      date_night_ideas_user: {
        Row: {
          category: string | null
          cost: Database["public"]["Enums"]["cost_level"] | null
          created_at: string | null
          description: string | null
          difficulty: Database["public"]["Enums"]["difficulty_level"] | null
          emoji: string | null
          estimated_duration: number | null
          id: string
          indoor_outdoor: Database["public"]["Enums"]["location_type"] | null
          title: string
          updated_at: string | null
          user_id: string | null
        }
        Insert: {
          category?: string | null
          cost?: Database["public"]["Enums"]["cost_level"] | null
          created_at?: string | null
          description?: string | null
          difficulty?: Database["public"]["Enums"]["difficulty_level"] | null
          emoji?: string | null
          estimated_duration?: number | null
          id?: string
          indoor_outdoor?: Database["public"]["Enums"]["location_type"] | null
          title: string
          updated_at?: string | null
          user_id?: string | null
        }
        Update: {
          category?: string | null
          cost?: Database["public"]["Enums"]["cost_level"] | null
          created_at?: string | null
          description?: string | null
          difficulty?: Database["public"]["Enums"]["difficulty_level"] | null
          emoji?: string | null
          estimated_duration?: number | null
          id?: string
          indoor_outdoor?: Database["public"]["Enums"]["location_type"] | null
          title?: string
          updated_at?: string | null
          user_id?: string | null
        }
        Relationships: []
      }
      error_logs: {
        Row: {
          action: string | null
          component: string | null
          created_at: string | null
          error_message: string
          id: string
          metadata: Json | null
          session_id: string
          severity: string | null
          stack_trace: string | null
          url: string | null
          user_agent: string | null
          user_id: string | null
        }
        Insert: {
          action?: string | null
          component?: string | null
          created_at?: string | null
          error_message: string
          id: string
          metadata?: Json | null
          session_id: string
          severity?: string | null
          stack_trace?: string | null
          url?: string | null
          user_agent?: string | null
          user_id?: string | null
        }
        Update: {
          action?: string | null
          component?: string | null
          created_at?: string | null
          error_message?: string
          id?: string
          metadata?: Json | null
          session_id?: string
          severity?: string | null
          stack_trace?: string | null
          url?: string | null
          user_agent?: string | null
          user_id?: string | null
        }
        Relationships: []
      }
      favorites: {
        Row: {
          created_at: string | null
          id: string
          item_id: string
          metadata: Json | null
          type: string
          updated_at: string | null
          user_id: string
        }
        Insert: {
          created_at?: string | null
          id?: string
          item_id: string
          metadata?: Json | null
          type: string
          updated_at?: string | null
          user_id: string
        }
        Update: {
          created_at?: string | null
          id?: string
          item_id?: string
          metadata?: Json | null
          type?: string
          updated_at?: string | null
          user_id?: string
        }
        Relationships: []
      }
      match_game_questions: {
        Row: {
          category: string
          created_at: string | null
          difficulty: string | null
          id: string
          is_active: boolean | null
          question_id: string
          question_text: string
          question_type: string | null
          updated_at: string | null
        }
        Insert: {
          category: string
          created_at?: string | null
          difficulty?: string | null
          id?: string
          is_active?: boolean | null
          question_id: string
          question_text: string
          question_type?: string | null
          updated_at?: string | null
        }
        Update: {
          category?: string
          created_at?: string | null
          difficulty?: string | null
          id?: string
          is_active?: boolean | null
          question_id?: string
          question_text?: string
          question_type?: string | null
          updated_at?: string | null
        }
        Relationships: []
      }
      match_game_results: {
        Row: {
          answered_at: string | null
          id: string
          partner1_answer: string | null
          partner1_correct: boolean | null
          partner1_guess: string | null
          partner1_user_id: string | null
          partner2_answer: string | null
          partner2_correct: boolean | null
          partner2_guess: string | null
          partner2_user_id: string | null
          question_id: string | null
          session_id: string | null
        }
        Insert: {
          answered_at?: string | null
          id?: string
          partner1_answer?: string | null
          partner1_correct?: boolean | null
          partner1_guess?: string | null
          partner1_user_id?: string | null
          partner2_answer?: string | null
          partner2_correct?: boolean | null
          partner2_guess?: string | null
          partner2_user_id?: string | null
          question_id?: string | null
          session_id?: string | null
        }
        Update: {
          answered_at?: string | null
          id?: string
          partner1_answer?: string | null
          partner1_correct?: boolean | null
          partner1_guess?: string | null
          partner1_user_id?: string | null
          partner2_answer?: string | null
          partner2_correct?: boolean | null
          partner2_guess?: string | null
          partner2_user_id?: string | null
          question_id?: string | null
          session_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "match_game_results_question_id_fkey"
            columns: ["question_id"]
            isOneToOne: false
            referencedRelation: "match_game_questions"
            referencedColumns: ["question_id"]
          },
          {
            foreignKeyName: "match_game_results_session_id_fkey"
            columns: ["session_id"]
            isOneToOne: false
            referencedRelation: "match_game_sessions"
            referencedColumns: ["id"]
          },
        ]
      }
      match_game_sessions: {
        Row: {
          completed: boolean | null
          correct_matches: number | null
          couple_id: string | null
          created_at: string | null
          id: string
          session_date: string | null
          total_questions: number | null
        }
        Insert: {
          completed?: boolean | null
          correct_matches?: number | null
          couple_id?: string | null
          created_at?: string | null
          id?: string
          session_date?: string | null
          total_questions?: number | null
        }
        Update: {
          completed?: boolean | null
          correct_matches?: number | null
          couple_id?: string | null
          created_at?: string | null
          id?: string
          session_date?: string | null
          total_questions?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "match_game_sessions_couple_id_fkey"
            columns: ["couple_id"]
            isOneToOne: false
            referencedRelation: "couples"
            referencedColumns: ["id"]
          },
        ]
      }
      match_game_user_answers: {
        Row: {
          answer_text: string
          created_at: string | null
          id: string
          question_id: string | null
          updated_at: string | null
          user_id: string | null
        }
        Insert: {
          answer_text: string
          created_at?: string | null
          id?: string
          question_id?: string | null
          updated_at?: string | null
          user_id?: string | null
        }
        Update: {
          answer_text?: string
          created_at?: string | null
          id?: string
          question_id?: string | null
          updated_at?: string | null
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "match_game_user_answers_question_id_fkey"
            columns: ["question_id"]
            isOneToOne: false
            referencedRelation: "match_game_questions"
            referencedColumns: ["question_id"]
          },
        ]
      }
      meal_ideas_global: {
        Row: {
          category: string
          cook_time: number | null
          created_at: string | null
          description: string | null
          difficulty: string | null
          emoji: string | null
          id: string
          ingredients: string[] | null
          instructions: string[] | null
          link: string | null
          prep_time: number | null
          servings: number | null
          source: string | null
          tags: string[] | null
          title: string
          updated_at: string | null
          week_number: number | null
        }
        Insert: {
          category: string
          cook_time?: number | null
          created_at?: string | null
          description?: string | null
          difficulty?: string | null
          emoji?: string | null
          id?: string
          ingredients?: string[] | null
          instructions?: string[] | null
          link?: string | null
          prep_time?: number | null
          servings?: number | null
          source?: string | null
          tags?: string[] | null
          title: string
          updated_at?: string | null
          week_number?: number | null
        }
        Update: {
          category?: string
          cook_time?: number | null
          created_at?: string | null
          description?: string | null
          difficulty?: string | null
          emoji?: string | null
          id?: string
          ingredients?: string[] | null
          instructions?: string[] | null
          link?: string | null
          prep_time?: number | null
          servings?: number | null
          source?: string | null
          tags?: string[] | null
          title?: string
          updated_at?: string | null
          week_number?: number | null
        }
        Relationships: []
      }
      meal_ideas_users: {
        Row: {
          category: string
          completed_at: string | null
          cook_time: number | null
          created_at: string | null
          description: string | null
          difficulty: string | null
          emoji: string | null
          id: string
          idea_id: string | null
          ingredients: string[] | null
          instructions: string[] | null
          is_completed: boolean | null
          is_favorite: boolean | null
          link: string | null
          prep_time: number | null
          servings: number | null
          source: string | null
          tags: string[] | null
          title: string
          updated_at: string | null
          user_id: string
          week_number: number | null
        }
        Insert: {
          category: string
          completed_at?: string | null
          cook_time?: number | null
          created_at?: string | null
          description?: string | null
          difficulty?: string | null
          emoji?: string | null
          id?: string
          idea_id?: string | null
          ingredients?: string[] | null
          instructions?: string[] | null
          is_completed?: boolean | null
          is_favorite?: boolean | null
          link?: string | null
          prep_time?: number | null
          servings?: number | null
          source?: string | null
          tags?: string[] | null
          title: string
          updated_at?: string | null
          user_id: string
          week_number?: number | null
        }
        Update: {
          category?: string
          completed_at?: string | null
          cook_time?: number | null
          created_at?: string | null
          description?: string | null
          difficulty?: string | null
          emoji?: string | null
          id?: string
          idea_id?: string | null
          ingredients?: string[] | null
          instructions?: string[] | null
          is_completed?: boolean | null
          is_favorite?: boolean | null
          link?: string | null
          prep_time?: number | null
          servings?: number | null
          source?: string | null
          tags?: string[] | null
          title?: string
          updated_at?: string | null
          user_id?: string
          week_number?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "meal_ideas_users_idea_id_fkey"
            columns: ["idea_id"]
            isOneToOne: false
            referencedRelation: "meal_ideas_global"
            referencedColumns: ["id"]
          },
        ]
      }
      meal_voting: {
        Row: {
          couple_id: string
          created_at: string
          id: string
          meal_id: string
          updated_at: string
          user_id: string
          vote_type: string
        }
        Insert: {
          couple_id: string
          created_at?: string
          id?: string
          meal_id: string
          updated_at?: string
          user_id: string
          vote_type: string
        }
        Update: {
          couple_id?: string
          created_at?: string
          id?: string
          meal_id?: string
          updated_at?: string
          user_id?: string
          vote_type?: string
        }
        Relationships: []
      }
      milestone_templates: {
        Row: {
          category: string
          created_at: string | null
          description: string | null
          display_order: number | null
          field_schema: Json
          id: string
          is_active: boolean | null
          is_core_milestone: boolean | null
          milestone_key: string
          title: string
          ui_config: Json | null
          updated_at: string | null
        }
        Insert: {
          category: string
          created_at?: string | null
          description?: string | null
          display_order?: number | null
          field_schema: Json
          id?: string
          is_active?: boolean | null
          is_core_milestone?: boolean | null
          milestone_key: string
          title: string
          ui_config?: Json | null
          updated_at?: string | null
        }
        Update: {
          category?: string
          created_at?: string | null
          description?: string | null
          display_order?: number | null
          field_schema?: Json
          id?: string
          is_active?: boolean | null
          is_core_milestone?: boolean | null
          milestone_key?: string
          title?: string
          ui_config?: Json | null
          updated_at?: string | null
        }
        Relationships: []
      }
      origin_story: {
        Row: {
          best_memories_photos: Json | null
          biggest_challenge_photos: Json | null
          couple_id: string | null
          created_at: string | null
          data: Json
          first_kiss_photos: Json | null
          first_meeting_photos: Json | null
          id: string
          inside_jokes_photos: Json | null
          knew_loved_photos: Json | null
          last_updated: string | null
          most_romantic_photos: Json | null
          updated_at: string | null
          user_id: string
        }
        Insert: {
          best_memories_photos?: Json | null
          biggest_challenge_photos?: Json | null
          couple_id?: string | null
          created_at?: string | null
          data?: Json
          first_kiss_photos?: Json | null
          first_meeting_photos?: Json | null
          id?: string
          inside_jokes_photos?: Json | null
          knew_loved_photos?: Json | null
          last_updated?: string | null
          most_romantic_photos?: Json | null
          updated_at?: string | null
          user_id: string
        }
        Update: {
          best_memories_photos?: Json | null
          biggest_challenge_photos?: Json | null
          couple_id?: string | null
          created_at?: string | null
          data?: Json
          first_kiss_photos?: Json | null
          first_meeting_photos?: Json | null
          id?: string
          inside_jokes_photos?: Json | null
          knew_loved_photos?: Json | null
          last_updated?: string | null
          most_romantic_photos?: Json | null
          updated_at?: string | null
          user_id?: string
        }
        Relationships: []
      }
      pairing_attempts: {
        Row: {
          attempted_code: string
          created_at: string
          id: string
          success: boolean
          user_id: string
        }
        Insert: {
          attempted_code: string
          created_at?: string
          id?: string
          success?: boolean
          user_id: string
        }
        Update: {
          attempted_code?: string
          created_at?: string
          id?: string
          success?: boolean
          user_id?: string
        }
        Relationships: []
      }
      points_system: {
        Row: {
          achievements: Json
          created_at: string | null
          id: string
          last_activity: string | null
          level: number
          total_points: number
          updated_at: string | null
          user_id: string
        }
        Insert: {
          achievements?: Json
          created_at?: string | null
          id?: string
          last_activity?: string | null
          level?: number
          total_points?: number
          updated_at?: string | null
          user_id: string
        }
        Update: {
          achievements?: Json
          created_at?: string | null
          id?: string
          last_activity?: string | null
          level?: number
          total_points?: number
          updated_at?: string | null
          user_id?: string
        }
        Relationships: []
      }
      profiles: {
        Row: {
          created_at: string | null
          id: string
          is_complete: boolean
          partner1_icon: string
          partner1_name: string
          partner1_profile_picture: string | null
          partner2_icon: string
          partner2_name: string
          partner2_profile_picture: string | null
          relationship_start_date: string | null
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          id: string
          is_complete?: boolean
          partner1_icon?: string
          partner1_name?: string
          partner1_profile_picture?: string | null
          partner2_icon?: string
          partner2_name?: string
          partner2_profile_picture?: string | null
          relationship_start_date?: string | null
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          id?: string
          is_complete?: boolean
          partner1_icon?: string
          partner1_name?: string
          partner1_profile_picture?: string | null
          partner2_icon?: string
          partner2_name?: string
          partner2_profile_picture?: string | null
          relationship_start_date?: string | null
          updated_at?: string | null
        }
        Relationships: []
      }
      scrapbook: {
        Row: {
          created_at: string | null
          entries: Json
          id: string
          photos: Json | null
          updated_at: string | null
          user_id: string
        }
        Insert: {
          created_at?: string | null
          entries?: Json
          id?: string
          photos?: Json | null
          updated_at?: string | null
          user_id: string
        }
        Update: {
          created_at?: string | null
          entries?: Json
          id?: string
          photos?: Json | null
          updated_at?: string | null
          user_id?: string
        }
        Relationships: []
      }
      timeline_events: {
        Row: {
          couple_id: string
          created_at: string | null
          created_by: string | null
          description: string | null
          event_date: string
          event_type: string
          id: string
          is_featured: boolean | null
          is_visible: boolean | null
          metadata: Json | null
          source_id: string | null
          source_type: string
          title: string
          updated_at: string | null
        }
        Insert: {
          couple_id: string
          created_at?: string | null
          created_by?: string | null
          description?: string | null
          event_date: string
          event_type: string
          id?: string
          is_featured?: boolean | null
          is_visible?: boolean | null
          metadata?: Json | null
          source_id?: string | null
          source_type: string
          title: string
          updated_at?: string | null
        }
        Update: {
          couple_id?: string
          created_at?: string | null
          created_by?: string | null
          description?: string | null
          event_date?: string
          event_type?: string
          id?: string
          is_featured?: boolean | null
          is_visible?: boolean | null
          metadata?: Json | null
          source_id?: string | null
          source_type?: string
          title?: string
          updated_at?: string | null
        }
        Relationships: []
      }
      timeline_photos: {
        Row: {
          caption: string | null
          created_at: string | null
          display_order: number | null
          file_size: number | null
          height: number | null
          id: string
          mime_type: string | null
          photo_url: string
          thumbnail_url: string | null
          timeline_event_id: string
          uploaded_by: string | null
          width: number | null
        }
        Insert: {
          caption?: string | null
          created_at?: string | null
          display_order?: number | null
          file_size?: number | null
          height?: number | null
          id?: string
          mime_type?: string | null
          photo_url: string
          thumbnail_url?: string | null
          timeline_event_id: string
          uploaded_by?: string | null
          width?: number | null
        }
        Update: {
          caption?: string | null
          created_at?: string | null
          display_order?: number | null
          file_size?: number | null
          height?: number | null
          id?: string
          mime_type?: string | null
          photo_url?: string
          thumbnail_url?: string | null
          timeline_event_id?: string
          uploaded_by?: string | null
          width?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "timeline_photos_timeline_event_id_fkey"
            columns: ["timeline_event_id"]
            isOneToOne: false
            referencedRelation: "timeline_events"
            referencedColumns: ["id"]
          },
        ]
      }
      user_events: {
        Row: {
          couple_id: string | null
          created_at: string | null
          event_category: string | null
          event_name: string | null
          event_type: string | null
          id: string
          metadata: Json
          points_awarded: number | null
          streak_eligible: boolean | null
          user_id: string | null
        }
        Insert: {
          couple_id?: string | null
          created_at?: string | null
          event_category?: string | null
          event_name?: string | null
          event_type?: string | null
          id?: string
          metadata?: Json
          points_awarded?: number | null
          streak_eligible?: boolean | null
          user_id?: string | null
        }
        Update: {
          couple_id?: string | null
          created_at?: string | null
          event_category?: string | null
          event_name?: string | null
          event_type?: string | null
          id?: string
          metadata?: Json
          points_awarded?: number | null
          streak_eligible?: boolean | null
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "user_events_couple_id_fkey"
            columns: ["couple_id"]
            isOneToOne: false
            referencedRelation: "couples"
            referencedColumns: ["id"]
          },
        ]
      }
      user_favorites: {
        Row: {
          created_at: string
          id: string
          item_id: string
          item_type: string
          updated_at: string
          user_id: string
        }
        Insert: {
          created_at?: string
          id?: string
          item_id: string
          item_type: string
          updated_at?: string
          user_id: string
        }
        Update: {
          created_at?: string
          id?: string
          item_id?: string
          item_type?: string
          updated_at?: string
          user_id?: string
        }
        Relationships: []
      }
      user_preferences: {
        Row: {
          created_at: string
          favorite_categories: string[]
          hidden_date_night_ideas: string[]
          id: string
          notification_preferences: Json
          preferred_cost: string | null
          preferred_difficulty: string | null
          preferred_duration_max: number | null
          preferred_duration_min: number | null
          privacy_settings: Json
          ui_preferences: Json
          updated_at: string
          user_id: string
        }
        Insert: {
          created_at?: string
          favorite_categories?: string[]
          hidden_date_night_ideas?: string[]
          id?: string
          notification_preferences?: Json
          preferred_cost?: string | null
          preferred_difficulty?: string | null
          preferred_duration_max?: number | null
          preferred_duration_min?: number | null
          privacy_settings?: Json
          ui_preferences?: Json
          updated_at?: string
          user_id: string
        }
        Update: {
          created_at?: string
          favorite_categories?: string[]
          hidden_date_night_ideas?: string[]
          id?: string
          notification_preferences?: Json
          preferred_cost?: string | null
          preferred_difficulty?: string | null
          preferred_duration_max?: number | null
          preferred_duration_min?: number | null
          privacy_settings?: Json
          ui_preferences?: Json
          updated_at?: string
          user_id?: string
        }
        Relationships: []
      }
      weekly_data: {
        Row: {
          completed_at: string | null
          completed_sections: boolean[]
          created_at: string | null
          data: Json
          id: string
          updated_at: string | null
          user_id: string
          week_number: number
        }
        Insert: {
          completed_at?: string | null
          completed_sections?: boolean[]
          created_at?: string | null
          data?: Json
          id?: string
          updated_at?: string | null
          user_id: string
          week_number: number
        }
        Update: {
          completed_at?: string | null
          completed_sections?: boolean[]
          created_at?: string | null
          data?: Json
          id?: string
          updated_at?: string | null
          user_id?: string
          week_number?: number
        }
        Relationships: []
      }
    }
    Views: {
      error_stats: {
        Row: {
          affected_sessions: number | null
          affected_users: number | null
          component: string | null
          date: string | null
          error_count: number | null
          severity: string | null
        }
        Relationships: []
      }
      onboarding_progress: {
        Row: {
          completed: boolean | null
          intro_viewed: boolean | null
          journal_done: boolean | null
          last_event_at: string | null
          partner_done: boolean | null
          partner_invited: boolean | null
          ritual_done: boolean | null
          started: boolean | null
          user_id: string | null
        }
        Relationships: []
      }
    }
    Functions: {
      calculate_user_streak: {
        Args: { p_category?: string; p_user_id: string }
        Returns: number
      }
      check_pairing_attempts: {
        Args: { user_uuid: string }
        Returns: boolean
      }
      expire_old_couples: {
        Args: Record<PropertyKey, never>
        Returns: undefined
      }
      generate_couple_code: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      get_activity_completion_rate: {
        Args: { p_activity_id: string }
        Returns: {
          completion_rate: number
          total_completions: number
          total_starts: number
        }[]
      }
      get_couple_activity_stats: {
        Args: { p_couple_id: string }
        Returns: {
          average_score: number
          completed_activities: number
          shared_activities: string[]
          total_activities: number
          total_points: number
        }[]
      }
      get_date_night_ideas_by_category: {
        Args: { p_category: string; p_limit?: number }
        Returns: {
          category: string
          cost: string
          created_at: string
          description: string
          difficulty: string
          emoji: string
          estimated_duration: number
          id: string
          indoor_outdoor: string
          title: string
          week_number: number
        }[]
      }
      get_meal_voting_results: {
        Args: { p_couple_id: string; p_meal_id?: string }
        Returns: {
          consensus: string
          dislikes: number
          likes: number
          meal_id: string
          neutrals: number
          total_votes: number
        }[]
      }
      get_milestone_progress: {
        Args: { p_couple_id: string }
        Returns: {
          category: string
          completed_milestones: number
          completion_percentage: number
          total_milestones: number
        }[]
      }
      get_random_date_night_ideas: {
        Args: {
          p_category?: string
          p_cost?: string
          p_difficulty?: string
          p_limit?: number
        }
        Returns: {
          category: string
          cost: string
          created_at: string
          description: string
          difficulty: string
          emoji: string
          estimated_duration: number
          id: string
          indoor_outdoor: string
          title: string
          week_number: number
        }[]
      }
      get_user_activity_stats: {
        Args: { p_user_id: string }
        Returns: {
          average_score: number
          completed_activities: number
          last_activity_date: string
          total_activities: number
          total_points: number
        }[]
      }
      get_user_favorites_count: {
        Args: { p_user_id: string }
        Returns: {
          favorites_by_type: Json
          total_favorites: number
        }[]
      }
      initialize_couple_milestones: {
        Args: { p_couple_id: string }
        Returns: number
      }
      is_item_favorited: {
        Args: { p_item_id: string; p_item_type: string; p_user_id: string }
        Returns: boolean
      }
      get_user_preferences: {
        Args: { user_uuid: string }
        Returns: {
          id: string
          user_id: string
          hidden_date_night_ideas: string[]
          favorite_categories: string[]
          preferred_difficulty: string | null
          preferred_cost: string | null
          preferred_duration_min: number | null
          preferred_duration_max: number | null
          notification_preferences: Json
          privacy_settings: Json
          ui_preferences: Json
          created_at: string
          updated_at: string
        }
      }
      hide_date_night_idea: {
        Args: { user_uuid: string; idea_composite_id: string }
        Returns: boolean
      }
      unhide_date_night_idea: {
        Args: { user_uuid: string; idea_composite_id: string }
        Returns: boolean
      }
      is_idea_hidden: {
        Args: { user_uuid: string; idea_composite_id: string }
        Returns: boolean
      }
    }
    Enums: {
      cost_level: "free" | "low" | "medium" | "high"
      difficulty_level: "easy" | "medium" | "hard"
      idea_source: "weekly" | "system" | "user"
      location_type: "indoor" | "outdoor" | "both"
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DatabaseWithoutInternals = Omit<Database, "__InternalSupabase">

type DefaultSchema = DatabaseWithoutInternals[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof DatabaseWithoutInternals },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof DatabaseWithoutInternals },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  graphql_public: {
    Enums: {},
  },
  public: {
    Enums: {
      cost_level: ["free", "low", "medium", "high"],
      difficulty_level: ["easy", "medium", "hard"],
      idea_source: ["weekly", "system", "user"],
      location_type: ["indoor", "outdoor", "both"],
    },
  },
} as const

// Export commonly used types
export type DateNightIdeaGlobal = Tables<'date_night_ideas_global'>
export type DateNightIdeaGlobalInsert = TablesInsert<'date_night_ideas_global'>
export type DateNightIdeaGlobalUpdate = TablesUpdate<'date_night_ideas_global'>
export type DateNightIdeaUser = Tables<'date_night_ideas_user'>
export type DateNightIdeaUserInsert = TablesInsert<'date_night_ideas_user'>
export type DateNightIdeaUserUpdate = TablesUpdate<'date_night_ideas_user'>

export type IdeaSource = 'weekly' | 'system' | 'user'

export interface PaginatedResponse<T> {
  data: T[];
  count: number;
  page: number;
  limit: number;
  hasMore: boolean;
}
