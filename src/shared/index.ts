/**
 * Shared Functionality - Index
 *
 * Centralized exports for all shared/reusable functionality:
 * - UI components and design system
 * - Common hooks and utilities
 * - Business logic services
 * - Type definitions
 * - Helper functions
 *
 * <AUTHOR> Us Team
 */

// Components - UI components and design system
export * from './components';

// Hooks - Common React hooks
export * from './hooks';

// Services - Business logic and API integrations
export * from './services';

// Types - TypeScript definitions (excluding UserPreferences to avoid conflict with hooks)
export * from './types';

// Utils - Utility functions and helpers
export * from './utils';
