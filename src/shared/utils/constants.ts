/**
 * Unified Constants System
 *
 * Consolidates all hardcoded values from across the application into a single,
 * maintainable constants system. This replaces scattered magic numbers and
 * strings throughout the codebase.
 *
 * @version 3.0.0
 * <AUTHOR> Us Team
 */

// =============================================================================
// CORE APP CONSTANTS
// =============================================================================

export const APP_CONSTANTS = {
  // Input limits
  MAX_INPUT_LENGTH: parseInt(process.env.EXPO_PUBLIC_MAX_INPUT_LENGTH || '2000'),
  MAX_IMAGE_SIZE: parseInt(process.env.EXPO_PUBLIC_MAX_IMAGE_SIZE || '5242880'), // 5MB

  // Rate limiting
  RATE_LIMIT_ATTEMPTS: parseInt(process.env.EXPO_PUBLIC_RATE_LIMIT_ATTEMPTS || '10'),
  RATE_LIMIT_WINDOW_MS: parseInt(process.env.EXPO_PUBLIC_RATE_LIMIT_WINDOW || '60000'),

  // API timeouts
  API_TIMEOUT: 30000,
  RETRY_ATTEMPTS: 3,

  // Storage keys
  STORAGE_KEYS: {
    USER_PROFILE: 'user_profile',
    CONSENT: 'error_reporting_consent',
    SETTINGS: 'app_settings',
    PROFILE_PICTURES: 'profile_pictures',
    WEEKLY_RITUAL: 'weekly_ritual',
    JOURNAL_ICON_PREFERENCE: 'journal_icon_preference',
  } as const,
} as const;

// =============================================================================
// UI CONSTANTS
// =============================================================================

export const UI_CONSTANTS = {
  // Layout Dimensions
  DIMENSIONS: {
    TAB_BAR_HEIGHT: 70,
    HEADER_HEIGHT: 56,
    BORDER_RADIUS_SMALL: 8,
    BORDER_RADIUS_MEDIUM: 12,
    BORDER_RADIUS_LARGE: 16,
    BORDER_RADIUS_EXTRA_LARGE: 20,
    BORDER_RADIUS_CARD: 30,
    BORDER_RADIUS_PILL: 9999,
    PADDING_SMALL: 8,
    PADDING_MEDIUM: 16,
    PADDING_LARGE: 20,
    PADDING_EXTRA_LARGE: 24,
    PADDING_SCREEN: 30,
    PADDING_BOTTOM_SCROLL: 50,
    MARGIN_SMALL: 4,
    MARGIN_MEDIUM: 8,
    MARGIN_LARGE: 12,
    MARGIN_EXTRA_LARGE: 16,
    MARGIN_CARD: 24,
    ICON_SIZE_SMALL: 16,
    ICON_SIZE_MEDIUM: 24,
    ICON_SIZE_LARGE: 32,
    ICON_SIZE_EXTRA_LARGE: 48,
    ICON_SIZE_HERO: 80,
    AVATAR_SIZE_SMALL: 32,
    AVATAR_SIZE_MEDIUM: 48,
    AVATAR_SIZE_LARGE: 64,
    BUTTON_HEIGHT_SMALL: 32,
    BUTTON_HEIGHT_MEDIUM: 44,
    BUTTON_HEIGHT_LARGE: 56,
    INPUT_HEIGHT_SMALL: 40,
    INPUT_HEIGHT_MEDIUM: 48,
    INPUT_HEIGHT_LARGE: 80,
    INPUT_HEIGHT_LIST: 100,
  },

  // Typography
  TYPOGRAPHY: {
    FONT_SIZE_CAPTION: 10,
    FONT_SIZE_SMALL: 12,
    FONT_SIZE_BODY: 14,
    FONT_SIZE_MEDIUM: 16,
    FONT_SIZE_LARGE: 18,
    FONT_SIZE_TITLE: 20,
    FONT_SIZE_HEADING: 24,
    FONT_SIZE_HERO: 28,
    FONT_SIZE_DISPLAY: 48,
    FONT_WEIGHT_NORMAL: '400' as const,
    FONT_WEIGHT_MEDIUM: '500' as const,
    FONT_WEIGHT_SEMIBOLD: '600' as const,
    FONT_WEIGHT_BOLD: '700' as const,
    FONT_WEIGHT_EXTRA_BOLD: '800' as const,
    LINE_HEIGHT_TIGHT: 20,
    LINE_HEIGHT_NORMAL: 22,
    LINE_HEIGHT_RELAXED: 24,
    LINE_HEIGHT_LOOSE: 26,
    LETTER_SPACING_TIGHT: -1,
    LETTER_SPACING_NORMAL: 0,
    LETTER_SPACING_WIDE: 0.5,
    LETTER_SPACING_WIDER: 2,
  },

  // Visual Effects
  EFFECTS: {
    OPACITY_DISABLED: 0.6,
    OPACITY_OVERLAY_LIGHT: 0.3,
    OPACITY_OVERLAY_MEDIUM: 0.5,
    OPACITY_OVERLAY_DARK: 0.7,
    OPACITY_SUBTLE: 0.8,
    OPACITY_STRONG: 0.9,
    SHADOW_ELEVATION_LOW: 2,
    SHADOW_ELEVATION_MEDIUM: 4,
    SHADOW_ELEVATION_HIGH: 6,
    SHADOW_ELEVATION_EXTRA_HIGH: 8,
    SHADOW_OPACITY_LIGHT: 0.08,
    SHADOW_OPACITY_MEDIUM: 0.1,
    SHADOW_OPACITY_STRONG: 0.15,
    BORDER_WIDTH_HAIRLINE: 0.5,
    BORDER_WIDTH_THIN: 1,
    BORDER_WIDTH_THICK: 2,
  },

  // Animations
  ANIMATIONS: {
    DURATION_INSTANT: 0,
    DURATION_FAST: 200,
    DURATION_MEDIUM: 300,
    DURATION_SLOW: 600,
    DURATION_EXTRA_SLOW: 1000,

    DURATION_TOAST: 3000,
    EASING_EASE_IN: 'ease-in' as const,
    EASING_EASE_OUT: 'ease-out' as const,
    EASING_EASE_IN_OUT: 'ease-in-out' as const,
  },
} as const;

// =============================================================================
// VALIDATION RULES
// =============================================================================

export const VALIDATION_RULES = {
  NAME_MIN_LENGTH: 2,
  NAME_MAX_LENGTH: 50,
  EMAIL_REGEX: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  PARTNER_NAME_MIN_LENGTH: 1,
  PARTNER_NAME_MAX_LENGTH: 30,
  RITUAL_TIME_REGEX: /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/,
} as const;

// =============================================================================
// CONTENT CONSTANTS
// =============================================================================

export const CONTENT_CONSTANTS = {
  // App Branding
  APP_NAME: 'Nestled',
  APP_TAGLINE: 'Couples Crave Closeness, Not Complexity.',

  // Onboarding Content
  ONBOARDING: {
    SETUP_STEPS: {
      PARTNER_PROFILE_TITLE: 'Set up your couple profile',
      PARTNER_PROFILE_SUBTITLE: 'Add names and choose icons for each partner',
      INVITE_PARTNER_TITLE: 'Invite your partner',
      INVITE_PARTNER_SUBTITLE: 'Create a connection code to link your journals together',
      WEEKLY_RITUAL_TITLE: 'Pick your weekly ritual',
      WEEKLY_RITUAL_SUBTITLE: 'Choose a day and time to check in and journal together',
      JOURNAL_ICON_TITLE: 'Choose your journal icon',
      JOURNAL_ICON_SUBTITLE: 'This icon will appear on your journal entries',
      COMPLETION_TITLE: 'All set!',
      COMPLETION_SUBTITLE: 'Welcome to Everlasting Us — your space for connection, laughter, and growth.',
    },
  },

  // Default Activities
  DEFAULT_ACTIVITIES: [
    { emoji: '🍕', text: 'Pizza & Movie Nights' },
    { emoji: '🥾', text: 'Weekend Hiking Adventures' },
    { emoji: '🍳', text: 'Cooking Together' },
  ],

  // Error Messages
  ERROR_MESSAGES: {
    GENERIC: 'Something went wrong. Please try again.',
    GENERIC_TITLE: 'Something went wrong',
    NETWORK: 'Network error. Please check your connection.',
    AUTH_REQUIRED: 'Please sign in to continue.',
    INVALID_INPUT: 'Please check your input and try again.',
    PARTNER_NAME_REQUIRED: 'Partner names are required.',
    RITUAL_TIME_INVALID: 'Please enter a valid time in 24-hour format (e.g., 19:00).',
    SHARE_PROMPT: 'An error occurred. Would you like to share the error details to help us fix it?',
  },

  // Success Messages
  SUCCESS_MESSAGES: {
    PROFILE_UPDATED: 'Profile updated successfully!',
    SETTINGS_SAVED: 'Settings saved successfully!',
    CODE_COPIED: 'Code copied to clipboard!',
  },
} as const;

// =============================================================================
// ICON MAPPINGS
// =============================================================================

export const ICON_MAPPINGS = {
  // Partner Icons
  PARTNER_ICONS: {
    heart: '❤️',
    star: '⭐',
    flower: '🌸',
    moon: '🌙',
    sun: '☀️',
    sparkles: '✨',
    crown: '👑',
    rainbow: '🌈',
    diamond: '💎',
  },

  // Journal Icons
  JOURNAL_ICONS: {
    heart: '❤️',
    book: '📖',
    sparkle: '✨',
    star: '⭐',
  },

  // Achievement Icons
  ACHIEVEMENT_ICONS: {
    streak: '🔥',
    module: '⭐',
    trophy: '🏆',
    level: '⭐',
  },

  // Status Icons
  STATUS_ICONS: {
    success: '✅',
    error: '❌',
    warning: '⚠️',
    info: 'ℹ️',
    loading: '⏳',
    waiting: '⏳',
    active: '🎉',
    pending: '⏳',
  },
} as const;

// =============================================================================
// FEATURE FLAGS
// =============================================================================

export const FEATURE_FLAGS = {
  // Authentication
  ENABLE_AUTHENTICATION: process.env.EXPO_PUBLIC_ENABLE_AUTH !== 'false',
  ENABLE_GUEST_MODE: process.env.EXPO_PUBLIC_ENABLE_GUEST_MODE === 'true',

  // Features
  ENABLE_PARTNER_INVITES: true,
  ENABLE_WEEKLY_RITUALS: true,
  ENABLE_JOURNAL_ICONS: true,
  ENABLE_ACHIEVEMENTS: true,
  ENABLE_POINTS_SYSTEM: true,

  // Development
  ENABLE_DEBUG_LOGGING: process.env.NODE_ENV === 'development',
  ENABLE_PERFORMANCE_MONITORING: process.env.NODE_ENV === 'production',
  ENABLE_TOAST_NOTIFICATIONS: true,
} as const;

// =============================================================================
// ROUTE CONSTANTS
// =============================================================================

export const ROUTES = {
  // Main Routes
  INDEX: '/',
  AUTH: '/auth',
  LOGIN: '/login',
  TABS: '/(tabs)',

  // Tab Routes
  HOME: '/(tabs)',
  MODULES: '/(tabs)/modules',
  DATE_NIGHT: '/(tabs)/date-night',
  ACTIVITIES: '/(tabs)/activities',

  // Feature Routes
  OUR_STORY: '/our-story',
  COUPLE_PROFILE: '/couple-profile',
  SETTINGS: '/settings',
  APP_SETTINGS: '/app-settings',
  NOTIFICATIONS: '/notifications',
  SCRAPBOOK: '/scrapbook',

  // Week Routes
  WEEK_ONE: '/week-one',
  WEEK_TWO: '/week-two',
  WEEK_THREE: '/week-three',
  WEEK_FOUR: '/week-four',
  WEEK_FIVE: '/week-five',
  WEEK_SIX: '/week-six',
  WEEK_SEVEN: '/week-seven',
  WEEK_EIGHT: '/week-eight',
  WEEK_NINE: '/week-nine',
  WEEK_TEN: '/week-ten',
  WEEK_ELEVEN: '/week-eleven',
  WEEK_TWELVE: '/week-twelve',
} as const;

// =============================================================================
// HELPER FUNCTIONS
// =============================================================================

/**
 * Get icon emoji from icon ID
 */
export const getPartnerIconEmoji = (iconId?: string): string | undefined => {
  if (!iconId) return undefined;
  return ICON_MAPPINGS.PARTNER_ICONS[iconId as keyof typeof ICON_MAPPINGS.PARTNER_ICONS];
};

/**
 * Get journal icon emoji from icon ID
 */
export const getJournalIconEmoji = (iconId?: string): string | undefined => {
  if (!iconId) return undefined;
  return ICON_MAPPINGS.JOURNAL_ICONS[iconId as keyof typeof ICON_MAPPINGS.JOURNAL_ICONS];
};

/**
 * Get achievement icon emoji from type
 */
export const getAchievementIconEmoji = (type?: string): string => {
  if (!type) return ICON_MAPPINGS.ACHIEVEMENT_ICONS.trophy;
  return ICON_MAPPINGS.ACHIEVEMENT_ICONS[type as keyof typeof ICON_MAPPINGS.ACHIEVEMENT_ICONS] || ICON_MAPPINGS.ACHIEVEMENT_ICONS.trophy;
};

/**
 * Get status icon emoji from status
 */
export const getStatusIconEmoji = (status?: string): string => {
  if (!status) return ICON_MAPPINGS.STATUS_ICONS.info;
  return ICON_MAPPINGS.STATUS_ICONS[status as keyof typeof ICON_MAPPINGS.STATUS_ICONS] || ICON_MAPPINGS.STATUS_ICONS.info;
};
