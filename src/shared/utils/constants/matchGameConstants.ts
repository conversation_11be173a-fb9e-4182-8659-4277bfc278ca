/**
 * Match Game Constants
 * Static values and configuration for match game functionality
 */

import { MatchGameCategory, MatchGameDifficulty, MatchGameQuestionType } from '../../types/matchGame.types';

// Default game configuration
export const DEFAULT_QUESTION_COUNT = 10;
export const MAX_QUESTION_COUNT = 50;
export const MIN_QUESTION_COUNT = 3;

// Game timing constants (in seconds)
export const GAME_TIMING = {
  ANSWER_TIME: 300, // 5 minutes per answer
  GUESS_TIME: 180, // 3 minutes per guess
  REVEAL_TIME: 30, // 30 seconds to view results
  TRANSITION_TIME: 5 // 5 seconds between questions
} as const;

// Scoring constants
export const SCORING = {
  CORRECT_GUESS_POINTS: 10,
  PERFECT_MATCH_BONUS: 5,
  DIFFICULTY_MULTIPLIERS: {
    easy: 1.0,
    medium: 1.5,
    hard: 2.0
  },
  STREAK_BONUS_THRESHOLD: 3,
  STREAK_BONUS_MULTIPLIER: 1.2
} as const;

// Answer similarity threshold for matching
export const ANSWER_SIMILARITY_THRESHOLD = 80; // Percentage

// Game phases
export const GAME_PHASES = {
  SETUP: 'setup',
  ANSWERING: 'answering',
  GUESSING: 'guessing',
  REVEALING: 'revealing',
  COMPLETED: 'completed'
} as const;

// Session types
export const SESSION_TYPES = {
  WEEKLY: 'weekly',
  CUSTOM: 'custom',
  PRACTICE: 'practice'
} as const;

// Question categories with display information
export const CATEGORY_INFO: Record<MatchGameCategory, {
  displayName: string;
  icon: string;
  color: string;
  description: string;
  difficulty: MatchGameDifficulty;
}> = {
  Food: {
    displayName: 'Food & Dining',
    icon: '🍽️',
    color: '#F59E0B',
    description: 'Questions about food preferences, cooking, and dining experiences',
    difficulty: 'easy'
  },
  Entertainment: {
    displayName: 'Entertainment',
    icon: '🎬',
    color: '#8B5CF6',
    description: 'Questions about movies, TV shows, books, and media preferences',
    difficulty: 'easy'
  },
  Personal: {
    displayName: 'Personal',
    icon: '👤',
    color: '#EC4899',
    description: 'Questions about personal habits, preferences, and lifestyle',
    difficulty: 'medium'
  },
  Music: {
    displayName: 'Music & Dance',
    icon: '🎵',
    color: '#06B6D4',
    description: 'Questions about musical preferences, dancing, and concerts',
    difficulty: 'easy'
  },
  Travel: {
    displayName: 'Travel',
    icon: '✈️',
    color: '#10B981',
    description: 'Questions about travel preferences, destinations, and adventures',
    difficulty: 'medium'
  },
  Relationship: {
    displayName: 'Relationship',
    icon: '💕',
    color: '#EF4444',
    description: 'Questions about love, communication, and relationship preferences',
    difficulty: 'hard'
  },
  Work: {
    displayName: 'Work & Career',
    icon: '💼',
    color: '#6366F1',
    description: 'Questions about career goals, work style, and professional life',
    difficulty: 'medium'
  },
  Hobbies: {
    displayName: 'Hobbies',
    icon: '🎨',
    color: '#84CC16',
    description: 'Questions about hobbies, interests, and creative pursuits',
    difficulty: 'easy'
  },
  Family: {
    displayName: 'Family & Friends',
    icon: '👨‍👩‍👧‍👦',
    color: '#F97316',
    description: 'Questions about family relationships and friendships',
    difficulty: 'medium'
  },
  Values: {
    displayName: 'Values & Beliefs',
    icon: '⭐',
    color: '#6B7280',
    description: 'Questions about core values, beliefs, and life philosophy',
    difficulty: 'hard'
  }
};

// Difficulty levels with display information
export const DIFFICULTY_INFO: Record<MatchGameDifficulty, {
  displayName: string;
  color: string;
  description: string;
  pointsMultiplier: number;
}> = {
  easy: {
    displayName: 'Easy',
    color: '#10B981',
    description: 'Simple questions with straightforward answers',
    pointsMultiplier: 1.0
  },
  medium: {
    displayName: 'Medium',
    color: '#F59E0B',
    description: 'Moderate questions requiring some thought',
    pointsMultiplier: 1.5
  },
  hard: {
    displayName: 'Hard',
    color: '#EF4444',
    description: 'Complex questions requiring deep reflection',
    pointsMultiplier: 2.0
  }
};

// Question types with display information
export const QUESTION_TYPE_INFO: Record<MatchGameQuestionType, {
  displayName: string;
  description: string;
  icon: string;
}> = {
  guess: {
    displayName: 'Guess',
    description: 'Guess what your partner would answer',
    icon: '🤔'
  },
  text: {
    displayName: 'Text',
    description: 'Open-ended text response',
    icon: '📝'
  },
  list: {
    displayName: 'List',
    description: 'Provide a list of items',
    icon: '📋'
  }
};

// Performance levels
export const PERFORMANCE_LEVELS = {
  BEGINNER: {
    threshold: 0,
    name: 'Beginner',
    color: '#EF4444',
    icon: '🌱',
    description: 'Just getting started!'
  },
  INTERMEDIATE: {
    threshold: 50,
    name: 'Intermediate',
    color: '#F59E0B',
    icon: '🌿',
    description: 'Getting the hang of it!'
  },
  ADVANCED: {
    threshold: 75,
    name: 'Advanced',
    color: '#3B82F6',
    icon: '🌳',
    description: 'You know each other well!'
  },
  EXPERT: {
    threshold: 90,
    name: 'Expert',
    color: '#10B981',
    icon: '🏆',
    description: 'Perfect match!'
  }
} as const;

// Game limits and constraints
export const GAME_LIMITS = {
  MAX_QUESTIONS_PER_SESSION: 50,
  MIN_QUESTIONS_PER_SESSION: 3,
  MAX_ANSWER_LENGTH: 500,
  MIN_ANSWER_LENGTH: 1,
  MAX_SESSIONS_PER_DAY: 5,
  MAX_SESSIONS_PER_WEEK: 20
} as const;

// Error messages
export const ERROR_MESSAGES = {
  NETWORK_ERROR: 'Network connection error. Please check your internet connection.',
  AUTH_ERROR: 'Authentication error. Please log in again.',
  PERMISSION_ERROR: 'You don\'t have permission to perform this action.',
  VALIDATION_ERROR: 'Please check your input and try again.',
  NOT_FOUND_ERROR: 'The requested resource was not found.',
  SERVER_ERROR: 'Server error. Please try again later.',
  RATE_LIMIT_ERROR: 'Too many requests. Please wait before trying again.',
  SESSION_EXPIRED: 'Your session has expired. Please log in again.',
  INVALID_GAME_STATE: 'Invalid game state. Please restart the game.',
  QUESTIONS_NOT_FOUND: 'No questions available. Please try again later.'
} as const;

// Success messages
export const SUCCESS_MESSAGES = {
  ANSWER_SAVED: 'Answer saved successfully!',
  SESSION_CREATED: 'Game session created successfully!',
  SESSION_COMPLETED: 'Game session completed!',
  RESULT_SAVED: 'Game result saved successfully!',
  QUESTIONS_LOADED: 'Questions loaded successfully!',
  STATS_UPDATED: 'Statistics updated successfully!'
} as const;

// UI constants
export const UI_CONSTANTS = {
  ANIMATION_DURATION: 300, // milliseconds
  DEBOUNCE_DELAY: 500, // milliseconds
  LOADING_TIMEOUT: 10000, // milliseconds
  RETRY_ATTEMPTS: 3,
  POLLING_INTERVAL: 5000 // milliseconds
} as const;

// Cache keys
export const CACHE_KEYS = {
  QUESTIONS: 'match_game_questions',
  USER_ANSWERS: 'match_game_user_answers',
  SESSIONS: 'match_game_sessions',
  STATS: 'match_game_stats',
  CATEGORIES: 'match_game_categories'
} as const;

// Cache expiration times (in seconds)
export const CACHE_EXPIRATION = {
  QUESTIONS: 3600, // 1 hour
  USER_ANSWERS: 1800, // 30 minutes
  SESSIONS: 900, // 15 minutes
  STATS: 600, // 10 minutes
  CATEGORIES: 7200 // 2 hours
} as const;

// Feature flags
export const FEATURE_FLAGS = {
  ENABLE_STREAK_TRACKING: true,
  ENABLE_DIFFICULTY_BASED_SCORING: true,
  ENABLE_CATEGORY_FILTERING: true,
  ENABLE_OFFLINE_MODE: false,
  ENABLE_REAL_TIME_UPDATES: true,
  ENABLE_ANALYTICS: true,
  ENABLE_SOUND_EFFECTS: false
} as const;

// Analytics events
export const ANALYTICS_EVENTS = {
  GAME_STARTED: 'match_game_started',
  GAME_COMPLETED: 'match_game_completed',
  QUESTION_ANSWERED: 'match_game_question_answered',
  GUESS_MADE: 'match_game_guess_made',
  ANSWER_REVEALED: 'match_game_answer_revealed',
  SESSION_CREATED: 'match_game_session_created',
  STATS_VIEWED: 'match_game_stats_viewed',
  CATEGORY_SELECTED: 'match_game_category_selected',
  DIFFICULTY_SELECTED: 'match_game_difficulty_selected'
} as const;

// Local storage keys
export const STORAGE_KEYS = {
  CURRENT_SESSION: 'match_game_current_session',
  GAME_STATE: 'match_game_game_state',
  USER_PREFERENCES: 'match_game_user_preferences',
  OFFLINE_ANSWERS: 'match_game_offline_answers',
  CACHED_QUESTIONS: 'match_game_cached_questions'
} as const;

// Default user preferences
export const DEFAULT_USER_PREFERENCES = {
  questionCount: DEFAULT_QUESTION_COUNT,
  preferredCategories: [] as MatchGameCategory[],
  preferredDifficulty: 'medium' as MatchGameDifficulty,
  enableNotifications: true,
  enableSounds: false,
  enableHapticFeedback: true,
  autoSaveAnswers: true
} as const;
