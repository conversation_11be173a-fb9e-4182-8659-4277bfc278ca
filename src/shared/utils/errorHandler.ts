/**
 * Enhanced error handling utilities for better reliability
 */

import { Alert } from 'react-native';
import { logger } from './logger';

import { ErrorContext } from '../types';

export enum ErrorCategory {
  _NETWORK = 'NETWORK',
  _AUTHENTICATION = 'AUTHENTICATION',
  _VALIDATION = 'VALIDATION',
  _SYSTEM = 'SYSTEM',
  _UI = 'UI',
  _PERMISSION = 'PERMISSION',
  _STORAGE = 'STORAGE'
}

export class AppError extends Error {
  public readonly code: string;
  public readonly category: ErrorCategory;
  public readonly context: ErrorContext;
  public readonly isRetryable: boolean;
  public readonly userMessage: string;

  constructor(
    message: string,
    code: string = 'UNKNOWN_ERROR',
    category: ErrorCategory = ErrorCategory._SYSTEM,
    context: ErrorContext = {},
    isRetryable: boolean = false,
    userMessage: string = 'Something went wrong. Please try again.'
  ) {
    super(message);
    this.name = 'AppError';
    this.code = code;
    this.category = category;
    this.context = context;
    this.isRetryable = isRetryable;
    this.userMessage = userMessage;
  }
}

export class ErrorHandler {
  private static instance: ErrorHandler;
  private errorCounts: Map<string, number> = new Map();
  private readonly maxErrorsPerMinute = 5;

  private constructor() {}

  public static getInstance(): ErrorHandler {
    if (!ErrorHandler.instance) {
      ErrorHandler.instance = new ErrorHandler();
    }
    return ErrorHandler.instance;
  }

  /**
   * Handle errors with context and user-friendly messages
   */
  public handleError(error: Error, context: ErrorContext = {}): void {
    const errorKey = `${error.name}:${error.message}`;
    const currentCount = this.errorCounts.get(errorKey) || 0;

    // Rate limiting to prevent error spam
    if (currentCount >= this.maxErrorsPerMinute) {
      logger.warn('Error rate limit exceeded, suppressing error:', { errorKey, count: currentCount });
      return;
    }

    this.errorCounts.set(errorKey, currentCount + 1);

    // Reset counter after 1 minute
    setTimeout(() => {
      this.errorCounts.delete(errorKey);
    }, 60000);

    // Log error with context
    logger.error('Error handled:', {
      message: error.message,
      stack: error.stack,
      context: {
        ...context,
        timestamp: Date.now(),
      },
    });

    // Handle specific error types
    if (error instanceof AppError) {
      this.handleAppError(error);
    } else {
      this.handleGenericError(error, context);
    }
  }

  /**
   * Handle application-specific errors
   */
  private handleAppError(error: AppError): void {
    // Log with additional context
    logger.error('App Error:', {
      code: error.code,
      message: error.message,
      context: error.context,
      isRetryable: error.isRetryable,
    });

    // Show user-friendly message
    this.showUserError(error.userMessage, error.isRetryable);
  }

  /**
   * Handle generic errors
   */
  private handleGenericError(error: Error, context: ErrorContext): void {
    // Determine if error is retryable based on type
    const isRetryable = this.isRetryableError(error);

    // Create user-friendly message
    const userMessage = this.getUserFriendlyMessage(error, context);

    // Show error to user
    this.showUserError(userMessage, isRetryable);
  }

  /**
   * Determine if an error is retryable
   */
  private isRetryableError(error: Error): boolean {
    const retryableErrors = [
      'NetworkError',
      'TimeoutError',
      'ConnectionError',
      'ECONNRESET',
      'ENOTFOUND',
      'ETIMEDOUT',
    ];

    return retryableErrors.some(errorType =>
      error.name.includes(errorType) || error.message.includes(errorType)
    );
  }

  /**
   * Get user-friendly error message
   */
  private getUserFriendlyMessage(error: Error, context: ErrorContext): string {
    // Network errors
    if (error.message.includes('network') || error.message.includes('fetch')) {
      return 'Please check your internet connection and try again.';
    }

    // Storage errors
    if (error.message.includes('storage') || error.message.includes('SecureStore')) {
      return 'There was a problem saving your data. Please try again.';
    }

    // Validation errors
    if (error.message.includes('validation') || error.message.includes('invalid')) {
      return 'Please check your input and try again.';
    }

    // Permission errors
    if (error.message.includes('permission') || error.message.includes('denied')) {
      return 'Please grant the required permissions to continue.';
    }

    // Component-specific errors
    if (context.component) {
      switch (context.component) {
        case 'PhotoPicker':
          return 'There was a problem with the photo. Please try selecting a different image.';
        case 'PointsSystem':
          return 'There was a problem updating your points. Your progress is still saved.';
        case 'UserProfile':
          return 'There was a problem saving your profile. Please try again.';
        default:
          return 'Something went wrong. Please try again.';
      }
    }

    return 'Something went wrong. Please try again.';
  }

  /**
   * Show error to user with appropriate actions
   */
  private showUserError(message: string, isRetryable: boolean = false): void {
    const buttons = [
      { text: 'OK', style: 'default' as const }
    ];

    if (isRetryable) {
      buttons.unshift({ text: 'Retry', style: 'default' as const });
    }

    Alert.alert(
      'Error',
      message,
      buttons,
      { cancelable: true }
    );
  }

  /**
   * Wrap async functions with error handling
   */
  public async withErrorHandling<T>(
    fn: () => Promise<T>,
    context: ErrorContext = {}
  ): Promise<T | null> {
    try {
      return await fn();
    } catch (error) {
      this.handleError(error as Error, context);
      return null;
    }
  }

  /**
   * Wrap sync functions with error handling
   */
  public withSyncErrorHandling<T>(
    fn: () => T,
    context: ErrorContext = {}
  ): T | null {
    try {
      return fn();
    } catch (error) {
      this.handleError(error as Error, context);
      return null;
    }
  }
}

// Export singleton instance
export const errorHandler = ErrorHandler.getInstance();

// Convenience functions
export const handleError = (error: Error, context?: ErrorContext) =>
  errorHandler.handleError(error, context);

export const withErrorHandling = <T>(
  fn: () => Promise<T>,
  context?: ErrorContext
) => errorHandler.withErrorHandling(fn, context);

export const withSyncErrorHandling = <T>(
  fn: () => T,
  context?: ErrorContext
) => errorHandler.withSyncErrorHandling(fn, context);

// Common error types
export const createNetworkError = (message: string, context?: ErrorContext) =>
  new AppError(message, 'NETWORK_ERROR', ErrorCategory._NETWORK, context, true, 'Please check your internet connection.');

export const createStorageError = (message: string, context?: ErrorContext) =>
  new AppError(message, 'STORAGE_ERROR', ErrorCategory._STORAGE, context, true, 'There was a problem saving your data.');

export const createValidationError = (message: string, context?: ErrorContext) =>
  new AppError(message, 'VALIDATION_ERROR', ErrorCategory._VALIDATION, context, false, 'Please check your input.');

export const createPermissionError = (message: string, context?: ErrorContext) =>
  new AppError(message, 'PERMISSION_ERROR', ErrorCategory._PERMISSION, context, false, 'Please grant the required permissions.');
