/**
 * Journey Analytics
 *
 * Centralized analytics tracking for user journeys.
 * Provides insights into journey completion rates, performance, and user behavior.
 *
 * <AUTHOR> Us Team
 */

export type JourneyType = 'onboarding' | 'daily' | 'activities' | 'memories' | 'planning' | 'progress';

export interface JourneyEvent {
  journey: JourneyType;
  action: string;
  timestamp: Date;
  userId?: string;
  coupleId?: string;
  metadata?: Record<string, any>;
  duration?: number;
  success?: boolean;
  error?: string;
}

export interface JourneyMetrics {
  journey: JourneyType;
  startTime: Date;
  endTime?: Date;
  duration?: number;
  stepsCompleted: number;
  totalSteps: number;
  completionRate: number;
  errors: string[];
  performance: {
    loadTime: number;
    renderTime: number;
    apiCalls: number;
  };
}

class JourneyAnalytics {
  private events: JourneyEvent[] = [];
  private activeJourneys: Map<string, JourneyMetrics> = new Map();

  /**
   * Track the start of a user journey
   */
  trackJourneyStart(
    journey: JourneyType,
    userId?: string,
    coupleId?: string,
    metadata?: Record<string, any>
  ): string {
    const journeyId = `${journey}_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

    const event: JourneyEvent = {
      journey,
      action: 'journey_start',
      timestamp: new Date(),
      userId,
      coupleId,
      metadata
    };

    this.events.push(event);

    const metrics: JourneyMetrics = {
      journey,
      startTime: new Date(),
      stepsCompleted: 0,
      totalSteps: this.getJourneyStepCount(journey),
      completionRate: 0,
      errors: [],
      performance: {
        loadTime: 0,
        renderTime: 0,
        apiCalls: 0
      }
    };

    this.activeJourneys.set(journeyId, metrics);

    // Journey started - logged to analytics

    return journeyId;
  }

  /**
   * Track journey step completion
   */
  trackJourneyStep(
    journeyId: string,
    stepName: string,
    success: boolean = true,
    metadata?: Record<string, any>
  ): void {
    const metrics = this.activeJourneys.get(journeyId);
    if (!metrics) return;

    if (success) {
      metrics.stepsCompleted++;
      metrics.completionRate = (metrics.stepsCompleted / metrics.totalSteps) * 100;
    }

    const event: JourneyEvent = {
      journey: metrics.journey,
      action: `step_${success ? 'completed' : 'failed'}`,
      timestamp: new Date(),
      metadata: { stepName, ...metadata },
      success
    };

    this.events.push(event);

    // Journey step tracked - logged to analytics
  }

  /**
   * Track journey completion
   */
  trackJourneyCompletion(
    journeyId: string,
    success: boolean = true,
    metadata?: Record<string, any>
  ): void {
    const metrics = this.activeJourneys.get(journeyId);
    if (!metrics) return;

    metrics.endTime = new Date();
    metrics.duration = metrics.endTime.getTime() - metrics.startTime.getTime();

    const event: JourneyEvent = {
      journey: metrics.journey,
      action: success ? 'journey_completed' : 'journey_abandoned',
      timestamp: new Date(),
      duration: metrics.duration,
      success,
      metadata: {
        completionRate: metrics.completionRate,
        stepsCompleted: metrics.stepsCompleted,
        totalSteps: metrics.totalSteps,
        ...metadata
      }
    };

    this.events.push(event);

    // Journey completion tracked - logged to analytics

    // Clean up active journey
    this.activeJourneys.delete(journeyId);
  }

  /**
   * Track journey errors
   */
  trackJourneyError(
    journeyId: string,
    error: Error,
    context?: Record<string, any>
  ): void {
    const metrics = this.activeJourneys.get(journeyId);
    if (metrics) {
      metrics.errors.push(error.message);
    }

    const event: JourneyEvent = {
      journey: metrics?.journey || 'onboarding',
      action: 'journey_error',
      timestamp: new Date(),
      success: false,
      error: error.message,
      metadata: context
    };

    this.events.push(event);

    console.error(`📊 Journey error: ${error.message}`, {
      journey: metrics?.journey,
      context
    });
  }

  /**
   * Track performance metrics
   */
  trackPerformance(
    journeyId: string,
    metric: 'loadTime' | 'renderTime' | 'apiCalls',
    value: number
  ): void {
    const metrics = this.activeJourneys.get(journeyId);
    if (!metrics) return;

    metrics.performance[metric] = value;

    // Performance metric tracked - logged to analytics
  }

  /**
   * Get journey analytics summary
   */
  getJourneyAnalytics(journey?: JourneyType): {
    totalJourneys: number;
    completedJourneys: number;
    averageDuration: number;
    completionRate: number;
    commonErrors: string[];
    averageSteps: number;
  } {
    const relevantEvents = journey
      ? this.events.filter(e => e.journey === journey)
      : this.events;

    const startEvents = relevantEvents.filter(e => e.action === 'journey_start');
    const completedEvents = relevantEvents.filter(e => e.action === 'journey_completed');
    const errorEvents = relevantEvents.filter(e => e.action === 'journey_error');

    const totalJourneys = startEvents.length;
    const completedJourneys = completedEvents.length;
    const completionRate = totalJourneys > 0 ? (completedJourneys / totalJourneys) * 100 : 0;

    const durations = completedEvents
      .filter(e => e.duration)
      .map(e => e.duration!);
    const averageDuration = durations.length > 0
      ? durations.reduce((sum, d) => sum + d, 0) / durations.length
      : 0;

    const errorMessages = errorEvents.map(e => e.error!);
    const commonErrors = [...new Set(errorMessages)];

    const stepCounts = completedEvents
      .map(e => e.metadata?.stepsCompleted)
      .filter(Boolean);
    const averageSteps = stepCounts.length > 0
      ? stepCounts.reduce((sum, s) => sum + s, 0) / stepCounts.length
      : 0;

    return {
      totalJourneys,
      completedJourneys,
      averageDuration,
      completionRate,
      commonErrors,
      averageSteps
    };
  }

  /**
   * Get expected step count for each journey
   */
  private getJourneyStepCount(journey: JourneyType): number {
    const stepCounts: Record<JourneyType, number> = {
      onboarding: 5, // Auth, pairing, profile, intro, first activity
      daily: 3,      // Load question, respond, view partner response
      activities: 4, // Browse, select, favorite, complete
      memories: 4,   // Create, upload, organize, share
      planning: 3,   // Set preferences, create goals, schedule
      progress: 2    // View stats, celebrate achievements
    };

    return stepCounts[journey] || 1;
  }

  /**
   * Export analytics data for external analysis
   */
  exportAnalytics(): {
    events: JourneyEvent[];
    summary: Record<JourneyType, {
      totalJourneys: number;
      completedJourneys: number;
      averageDuration: number;
      completionRate: number;
      commonErrors: string[];
      averageSteps: number;
    }>;
  } {
    const journeys: JourneyType[] = ['onboarding', 'daily', 'activities', 'memories', 'planning', 'progress'];

    const summary = journeys.reduce((acc, journey) => {
      acc[journey] = this.getJourneyAnalytics(journey);
      return acc;
    }, {} as Record<JourneyType, {
      totalJourneys: number;
      completedJourneys: number;
      averageDuration: number;
      completionRate: number;
      commonErrors: string[];
      averageSteps: number;
    }>);

    return {
      events: [...this.events],
      summary
    };
  }

  /**
   * Clear analytics data (for testing or privacy)
   */
  clearAnalytics(): void {
    this.events = [];
    this.activeJourneys.clear();
    // Analytics data cleared
  }
}

// Export singleton instance
export const journeyAnalytics = new JourneyAnalytics();

// Export for testing
export { JourneyAnalytics };
