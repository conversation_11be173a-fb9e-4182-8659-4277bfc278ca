/**
 * Design Tokens
 *
 * Centralized design tokens that define the visual language of the app.
 * These tokens are used by the design system components and should be
 * the single source of truth for all design decisions.
 *
 * @version 3.0.0
 * <AUTHOR> Us Team
 */

import { Platform } from 'react-native';
import { UI_CONSTANTS } from '../constants';

// =============================================================================
// SPACING TOKENS
// =============================================================================

export const spacing = {
  xxs: UI_CONSTANTS.DIMENSIONS.MARGIN_SMALL,      // 4
  xs: UI_CONSTANTS.DIMENSIONS.PADDING_SMALL,      // 8
  sm: UI_CONSTANTS.DIMENSIONS.MARGIN_LARGE,       // 12
  md: UI_CONSTANTS.DIMENSIONS.PADDING_MEDIUM,     // 16
  lg: UI_CONSTANTS.DIMENSIONS.PADDING_LARGE,      // 20
  xl: UI_CONSTANTS.DIMENSIONS.PADDING_EXTRA_LARGE, // 24
  xxl: UI_CONSTANTS.DIMENSIONS.PADDING_SCREEN,    // 30
  xxxl: UI_CONSTANTS.DIMENSIONS.PADDING_BOTTOM_SCROLL, // 50
} as const;

// =============================================================================
// RADIUS TOKENS
// =============================================================================

export const radii = {
  none: 0,
  xs: UI_CONSTANTS.DIMENSIONS.BORDER_RADIUS_SMALL,       // 8
  sm: UI_CONSTANTS.DIMENSIONS.BORDER_RADIUS_MEDIUM,      // 12 - HTML mockup: inputs
  md: UI_CONSTANTS.DIMENSIONS.BORDER_RADIUS_LARGE,       // 16
  lg: UI_CONSTANTS.DIMENSIONS.BORDER_RADIUS_EXTRA_LARGE, // 20 - HTML mockup: cards
  xl: UI_CONSTANTS.DIMENSIONS.BORDER_RADIUS_CARD,        // 30
  pill: UI_CONSTANTS.DIMENSIONS.BORDER_RADIUS_PILL,      // 9999

  // HTML Mockup specific radii
  button: 25,  // HTML mockup: 25px for buttons
  card: 20,    // HTML mockup: 20px for cards
  input: 12,   // HTML mockup: 12px for inputs
} as const;

// =============================================================================
// TYPOGRAPHY TOKENS
// =============================================================================

export const typography = {
  // Font Sizes
  fontSize: {
    xs: UI_CONSTANTS.TYPOGRAPHY.FONT_SIZE_CAPTION,   // 10
    sm: UI_CONSTANTS.TYPOGRAPHY.FONT_SIZE_SMALL,     // 12
    base: UI_CONSTANTS.TYPOGRAPHY.FONT_SIZE_BODY,    // 14
    md: UI_CONSTANTS.TYPOGRAPHY.FONT_SIZE_MEDIUM,    // 16
    lg: UI_CONSTANTS.TYPOGRAPHY.FONT_SIZE_LARGE,     // 18
    xl: UI_CONSTANTS.TYPOGRAPHY.FONT_SIZE_TITLE,     // 20
    '2xl': UI_CONSTANTS.TYPOGRAPHY.FONT_SIZE_HEADING, // 24
    '3xl': UI_CONSTANTS.TYPOGRAPHY.FONT_SIZE_HERO,   // 28
    '4xl': UI_CONSTANTS.TYPOGRAPHY.FONT_SIZE_DISPLAY, // 48
  },

  // Font Weights - HTML Mockup Aligned
  fontWeight: {
    normal: UI_CONSTANTS.TYPOGRAPHY.FONT_WEIGHT_NORMAL,     // '400'
    medium: UI_CONSTANTS.TYPOGRAPHY.FONT_WEIGHT_MEDIUM,     // '500' - HTML mockup: labels
    semibold: UI_CONSTANTS.TYPOGRAPHY.FONT_WEIGHT_SEMIBOLD, // '600' - HTML mockup: headings
    bold: UI_CONSTANTS.TYPOGRAPHY.FONT_WEIGHT_BOLD,         // '700'
    extrabold: UI_CONSTANTS.TYPOGRAPHY.FONT_WEIGHT_EXTRA_BOLD, // '800'

    // HTML Mockup specific weights
    label: UI_CONSTANTS.TYPOGRAPHY.FONT_WEIGHT_MEDIUM,      // '500' - for labels
    heading: UI_CONSTANTS.TYPOGRAPHY.FONT_WEIGHT_SEMIBOLD,  // '600' - for headings
  },

  // Line Heights
  lineHeight: {
    tight: UI_CONSTANTS.TYPOGRAPHY.LINE_HEIGHT_TIGHT,    // 20
    normal: UI_CONSTANTS.TYPOGRAPHY.LINE_HEIGHT_NORMAL,  // 22
    relaxed: UI_CONSTANTS.TYPOGRAPHY.LINE_HEIGHT_RELAXED, // 24
    loose: UI_CONSTANTS.TYPOGRAPHY.LINE_HEIGHT_LOOSE,    // 26
  },

  // Letter Spacing
  letterSpacing: {
    tight: UI_CONSTANTS.TYPOGRAPHY.LETTER_SPACING_TIGHT,   // -1
    normal: UI_CONSTANTS.TYPOGRAPHY.LETTER_SPACING_NORMAL, // 0
    wide: UI_CONSTANTS.TYPOGRAPHY.LETTER_SPACING_WIDE,     // 0.5
    wider: UI_CONSTANTS.TYPOGRAPHY.LETTER_SPACING_WIDER,   // 2
  },

  // Font Family - System fonts as per HTML mockup
  fontFamily: {
    system: 'System',  // Use system font
    default: 'System', // Default to system font
  },
} as const;

// =============================================================================
// SHADOW TOKENS
// =============================================================================

export const shadows = {
  none: Platform.select({
    ios: { shadowOpacity: 0 },
    android: { elevation: 0 },
    default: {},
  }),

  sm: Platform.select({
    ios: {
      shadowColor: '#000000',
      shadowOpacity: UI_CONSTANTS.EFFECTS.SHADOW_OPACITY_LIGHT, // 0.08
      shadowOffset: { width: 0, height: 2 },
      shadowRadius: 6,
    },
    android: { elevation: UI_CONSTANTS.EFFECTS.SHADOW_ELEVATION_LOW }, // 2
    default: {},
  }),

  md: Platform.select({
    ios: {
      shadowColor: '#000000',
      shadowOpacity: UI_CONSTANTS.EFFECTS.SHADOW_OPACITY_MEDIUM, // 0.1
      shadowOffset: { width: 0, height: 6 },
      shadowRadius: 12,
    },
    android: { elevation: UI_CONSTANTS.EFFECTS.SHADOW_ELEVATION_MEDIUM }, // 4
    default: {},
  }),

  lg: Platform.select({
    ios: {
      shadowColor: '#000000',
      shadowOpacity: UI_CONSTANTS.EFFECTS.SHADOW_OPACITY_STRONG, // 0.15
      shadowOffset: { width: 0, height: 12 },
      shadowRadius: 24,
    },
    android: { elevation: UI_CONSTANTS.EFFECTS.SHADOW_ELEVATION_HIGH }, // 6
    default: {},
  }),

  xl: Platform.select({
    ios: {
      shadowColor: '#000000',
      shadowOpacity: UI_CONSTANTS.EFFECTS.SHADOW_OPACITY_STRONG, // 0.15
      shadowOffset: { width: 0, height: 20 },
      shadowRadius: 40,
    },
    android: { elevation: UI_CONSTANTS.EFFECTS.SHADOW_ELEVATION_EXTRA_HIGH }, // 8
    default: {},
  }),

  // HTML Mockup Shadow - box-shadow: 0 8px 32px rgba(0,0,0,0.1)
  glass: Platform.select({
    ios: {
      shadowColor: '#000000',
      shadowOpacity: 0.1,  // HTML mockup shadow opacity
      shadowOffset: { width: 0, height: 8 },  // HTML mockup: 0 8px
      shadowRadius: 32,    // HTML mockup: 32px blur
    },
    android: { elevation: 5 },
    default: {},
  }),
} as const;

// =============================================================================
// BORDER TOKENS
// =============================================================================

export const borders = {
  none: 0,
  hairline: UI_CONSTANTS.EFFECTS.BORDER_WIDTH_HAIRLINE, // 0.5
  thin: UI_CONSTANTS.EFFECTS.BORDER_WIDTH_THIN,         // 1
  thick: UI_CONSTANTS.EFFECTS.BORDER_WIDTH_THICK,       // 2
} as const;

// =============================================================================
// OPACITY TOKENS
// =============================================================================

export const opacity = {
  disabled: UI_CONSTANTS.EFFECTS.OPACITY_DISABLED,        // 0.6
  overlay: UI_CONSTANTS.EFFECTS.OPACITY_OVERLAY_MEDIUM,   // 0.5
  overlayLight: UI_CONSTANTS.EFFECTS.OPACITY_OVERLAY_LIGHT, // 0.3
  overlayDark: UI_CONSTANTS.EFFECTS.OPACITY_OVERLAY_DARK,  // 0.7
  subtle: UI_CONSTANTS.EFFECTS.OPACITY_SUBTLE,            // 0.8
  strong: UI_CONSTANTS.EFFECTS.OPACITY_STRONG,            // 0.9
} as const;

// =============================================================================
// SIZE TOKENS
// =============================================================================

export const sizes = {
  // Icon Sizes
  icon: {
    xs: UI_CONSTANTS.DIMENSIONS.ICON_SIZE_SMALL,       // 16
    sm: UI_CONSTANTS.DIMENSIONS.ICON_SIZE_MEDIUM,      // 24
    md: UI_CONSTANTS.DIMENSIONS.ICON_SIZE_LARGE,       // 32
    lg: UI_CONSTANTS.DIMENSIONS.ICON_SIZE_EXTRA_LARGE, // 48
    xl: UI_CONSTANTS.DIMENSIONS.ICON_SIZE_HERO,        // 80
  },

  // Avatar Sizes
  avatar: {
    sm: UI_CONSTANTS.DIMENSIONS.AVATAR_SIZE_SMALL,  // 32
    md: UI_CONSTANTS.DIMENSIONS.AVATAR_SIZE_MEDIUM, // 48
    lg: UI_CONSTANTS.DIMENSIONS.AVATAR_SIZE_LARGE,  // 64
  },

  // Button Heights
  button: {
    sm: UI_CONSTANTS.DIMENSIONS.BUTTON_HEIGHT_SMALL,  // 32
    md: UI_CONSTANTS.DIMENSIONS.BUTTON_HEIGHT_MEDIUM, // 44
    lg: UI_CONSTANTS.DIMENSIONS.BUTTON_HEIGHT_LARGE,  // 56
  },

  // Input Heights
  input: {
    sm: UI_CONSTANTS.DIMENSIONS.INPUT_HEIGHT_SMALL,  // 40
    md: UI_CONSTANTS.DIMENSIONS.INPUT_HEIGHT_MEDIUM, // 48
    lg: UI_CONSTANTS.DIMENSIONS.INPUT_HEIGHT_LARGE,  // 80
    xl: UI_CONSTANTS.DIMENSIONS.INPUT_HEIGHT_LIST,   // 100
  },

  // Layout Heights
  layout: {
    tabBar: UI_CONSTANTS.DIMENSIONS.TAB_BAR_HEIGHT, // 70
    header: UI_CONSTANTS.DIMENSIONS.HEADER_HEIGHT,  // 56
  },
} as const;

// =============================================================================
// ANIMATION TOKENS
// =============================================================================

export const animations = {
  duration: {
    instant: UI_CONSTANTS.ANIMATIONS.DURATION_INSTANT,    // 0
    fast: UI_CONSTANTS.ANIMATIONS.DURATION_FAST,          // 200
    medium: UI_CONSTANTS.ANIMATIONS.DURATION_MEDIUM,      // 300
    slow: UI_CONSTANTS.ANIMATIONS.DURATION_SLOW,          // 600
    extraSlow: UI_CONSTANTS.ANIMATIONS.DURATION_EXTRA_SLOW, // 1000
  },

  easing: {
    easeIn: UI_CONSTANTS.ANIMATIONS.EASING_EASE_IN,       // 'ease-in'
    easeOut: UI_CONSTANTS.ANIMATIONS.EASING_EASE_OUT,     // 'ease-out'
    easeInOut: UI_CONSTANTS.ANIMATIONS.EASING_EASE_IN_OUT, // 'ease-in-out'
  },
} as const;

// =============================================================================
// GLASS-MORPHISM TOKENS (HTML Mockup)
// =============================================================================

export const glassMorphism = {
  // HTML Mockup Glass Effect
  background: 'rgba(255, 255, 255, 0.8)',  // background: rgba(255, 255, 255, 0.8)
  backdropBlur: 10,                         // backdrop-filter: blur(10px)
  border: 'rgba(0, 0, 0, 0.05)',          // Subtle border
  borderWidth: 1,

  // Glass intensity variants
  light: {
    background: 'rgba(255, 255, 255, 0.6)',
    backdropBlur: 8,
  },
  medium: {
    background: 'rgba(255, 255, 255, 0.8)',
    backdropBlur: 10,
  },
  strong: {
    background: 'rgba(255, 255, 255, 0.9)',
    backdropBlur: 12,
  },
} as const;

// =============================================================================
// COMBINED DESIGN TOKENS
// =============================================================================

export const tokens = {
  spacing,
  radii,
  typography,
  shadows,
  borders,
  opacity,
  sizes,
  animations,
  glassMorphism,
  // Flat exports for backward compatibility
  fontSizes: typography.fontSize,
  fontWeights: typography.fontWeight,
  lineHeights: typography.lineHeight,
} as const;

export default tokens;
