/**
 * Theme System Index
 *
 * Main entry point for the theme system. Exports all theme-related
 * utilities, colors, tokens, and helper functions.
 *
 * @version 3.0.0
 * <AUTHOR> Us Team
 */

// =============================================================================
// EXPORTS
// =============================================================================

// Colors
export {
    BRAND_COLORS, colors, darkTheme, getColorWithOpacity, getThemeColors, isLightColor, lightTheme
} from './colors';

export type {
    ColorKey, Theme, ThemeMode
} from './colors';

// Design Tokens
export {
    animations, borders,
    opacity, radii, shadows, sizes, spacing, tokens, typography
} from './tokens';

// =============================================================================
// THEME UTILITIES
// =============================================================================

import type { Theme, ThemeMode } from './colors';
import { getThemeColors, lightTheme } from './colors';
import { tokens } from './tokens';

/**
 * Complete theme object with colors and tokens
 */
export interface CompleteTheme extends Theme {
  tokens: typeof tokens;
}

/**
 * Get complete theme (colors + tokens) based on mode
 */
export const getCompleteTheme = (
  mode: ThemeMode,
  systemColorScheme?: 'light' | 'dark' | null
): CompleteTheme => {
  const colors = getThemeColors(mode, systemColorScheme);
  return {
    ...colors,
    tokens,
  };
};

/**
 * Theme context value type
 */
export interface ThemeContextValue {
  mode: ThemeMode;
  theme: CompleteTheme;
  isDarkMode: boolean;
  setMode: (mode: ThemeMode) => void;
  toggleMode: () => void;
}

/**
 * Default theme values
 */
export const DEFAULT_THEME_MODE: ThemeMode = 'auto';
export const DEFAULT_THEME = getCompleteTheme(DEFAULT_THEME_MODE);

// =============================================================================
// LEGACY SUPPORT
// =============================================================================

// Re-export for backward compatibility (remove duplicate exports)
// export { default as colors } from './colors';
// export { default as tokens } from './tokens';

// Legacy design system exports (to be deprecated)
export const ds = {
  spacing: tokens.spacing,
  radii: tokens.radii,
  shadows: tokens.shadows,
  borders: tokens.borders,
  brand: {
    primary: lightTheme.primary,
    secondary: lightTheme.secondary,
    pink: lightTheme.accent,
    darkerPink: lightTheme.accentDark,
    text: lightTheme.textPrimary,
    white: '#FFFFFF',
    black: '#000000',
  },
};

// Legacy translucency (to be deprecated)
export const translucency = {
  whiteSoft: 'rgba(255,255,255,0.7)',
  whiteMedium: 'rgba(255,255,255,0.85)',
  whiteStrong: 'rgba(255,255,255,0.92)',
  blackSoft: 'rgba(0,0,0,0.04)',
  blackMedium: 'rgba(0,0,0,0.08)',
};

// =============================================================================
// HELPER FUNCTIONS
// =============================================================================

/**
 * Get glass-morphism style based on HTML mockup specifications
 */
export const getGlassStyle = (
  theme: Theme,
  intensity: 'light' | 'medium' | 'strong' = 'medium',
  customOpacity?: number
) => {
  // HTML Mockup Glass-morphism settings
  const glassSettings = tokens.glassMorphism[intensity];

  return {
    backgroundColor: glassSettings.background,
    borderWidth: tokens.glassMorphism.borderWidth,
    borderColor: tokens.glassMorphism.border,
    borderRadius: tokens.radii.card, // HTML mockup: 20px for cards
    // Note: backdrop-filter is not supported in React Native
    // The blur effect is achieved through BlurView component
    ...tokens.shadows.glass, // HTML mockup shadow
  };
};

/**
 * Get shadow style for elevation
 */
export const getShadowStyle = (elevation: 'sm' | 'md' | 'lg' | 'xl' = 'md') => {
  return tokens.shadows[elevation];
};

/**
 * Get responsive spacing based on screen size
 */
export const getResponsiveSpacing = (
  base: keyof typeof tokens.spacing,
  screenWidth: number
) => {
  const baseValue = tokens.spacing[base];

  // Adjust spacing for larger screens
  if (screenWidth > 768) {
    return baseValue * 1.5;
  }

  return baseValue;
};

/**
 * Get text style based on variant
 */
export const getTextStyle = (
  variant: 'caption' | 'body' | 'subtitle' | 'title' | 'heading' | 'display',
  theme: Theme
) => {
  const styles = {
    caption: {
      fontSize: tokens.typography.fontSize.xs,
      fontWeight: tokens.typography.fontWeight.normal,
      color: theme.textTertiary,
      lineHeight: tokens.typography.lineHeight.tight,
    },
    body: {
      fontSize: tokens.typography.fontSize.base,
      fontWeight: tokens.typography.fontWeight.normal,
      color: theme.textPrimary,
      lineHeight: tokens.typography.lineHeight.normal,
    },
    subtitle: {
      fontSize: tokens.typography.fontSize.md,
      fontWeight: tokens.typography.fontWeight.medium,
      color: theme.textSecondary,
      lineHeight: tokens.typography.lineHeight.relaxed,
    },
    title: {
      fontSize: tokens.typography.fontSize.xl,
      fontWeight: tokens.typography.fontWeight.semibold,
      color: theme.textPrimary,
      lineHeight: tokens.typography.lineHeight.tight,
    },
    heading: {
      fontSize: tokens.typography.fontSize['2xl'],
      fontWeight: tokens.typography.fontWeight.bold,
      color: theme.textPrimary,
      lineHeight: tokens.typography.lineHeight.tight,
    },
    display: {
      fontSize: tokens.typography.fontSize['4xl'],
      fontWeight: tokens.typography.fontWeight.extrabold,
      color: theme.textPrimary,
      lineHeight: tokens.typography.lineHeight.tight,
      letterSpacing: tokens.typography.letterSpacing.tight,
    },
  };

  return styles[variant];
};

/**
 * Get button style based on variant and size
 */
export const getButtonStyle = (
  variant: 'primary' | 'secondary' | 'outline' | 'ghost',
  size: 'sm' | 'md' | 'lg',
  theme: Theme
) => {
  const baseStyle = {
    height: tokens.sizes.button[size],
    paddingHorizontal: tokens.spacing.md,
    borderRadius: tokens.radii.sm,
    alignItems: 'center' as const,
    justifyContent: 'center' as const,
    flexDirection: 'row' as const,
  };

  const variants = {
    primary: {
      backgroundColor: theme.primary,
      borderWidth: 0,
    },
    secondary: {
      backgroundColor: theme.secondary,
      borderWidth: 0,
    },
    outline: {
      backgroundColor: 'transparent',
      borderWidth: tokens.borders.thin,
      borderColor: theme.border,
    },
    ghost: {
      backgroundColor: 'transparent',
      borderWidth: 0,
    },
  };

  return {
    ...baseStyle,
    ...variants[variant],
  };
};

export default {
  getCompleteTheme,
  getGlassStyle,
  getShadowStyle,
  getResponsiveSpacing,
  getTextStyle,
  getButtonStyle,
  DEFAULT_THEME_MODE,
  DEFAULT_THEME,
};
