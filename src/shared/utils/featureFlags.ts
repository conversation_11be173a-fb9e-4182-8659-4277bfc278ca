/**
 * Feature Flags
 * 
 * Centralized feature flag management for gradual rollout of the journey architecture.
 * Allows safe deployment and easy rollback if issues are discovered.
 * 
 * <AUTHOR> Us Team
 */

export interface FeatureFlags {
  // Journey Architecture Flags
  USE_JOURNEY_ARCHITECTURE: boolean;
  ENABLE_JOURNEY_ANALYTICS: boolean;
  ENABLE_JOURNEY_PERFORMANCE_MONITORING: boolean;
  
  // Individual Journey Flags
  ENABLE_ONBOARDING_JOURNEY: boolean;
  ENABLE_DAILY_JOURNEY: boolean;
  ENABLE_ACTIVITIES_JOURNEY: boolean;
  ENABLE_MEMORIES_JOURNEY: boolean;
  ENABLE_PLANNING_JOURNEY: boolean;
  ENABLE_PROGRESS_JOURNEY: boolean;
  
  // Migration Flags
  ENABLE_LEGACY_FALLBACK: boolean;
  ENABLE_MIGRATION_LOGGING: boolean;
  ENABLE_IMPORT_PATH_VALIDATION: boolean;
  
  // Development Flags
  ENABLE_JOURNEY_DEBUG_MODE: boolean;
  ENABLE_PERFORMANCE_PROFILING: boolean;
  SHOW_JOURNEY_BOUNDARIES: boolean;
}

/**
 * Default feature flag values
 */
const DEFAULT_FLAGS: FeatureFlags = {
  // Journey Architecture - Start disabled for safe rollout
  USE_JOURNEY_ARCHITECTURE: false,
  ENABLE_JOURNEY_ANALYTICS: false,
  ENABLE_JOURNEY_PERFORMANCE_MONITORING: false,
  
  // Individual Journeys - Can be enabled one by one
  ENABLE_ONBOARDING_JOURNEY: false,
  ENABLE_DAILY_JOURNEY: false,
  ENABLE_ACTIVITIES_JOURNEY: false,
  ENABLE_MEMORIES_JOURNEY: false,
  ENABLE_PLANNING_JOURNEY: false,
  ENABLE_PROGRESS_JOURNEY: false,
  
  // Migration Support
  ENABLE_LEGACY_FALLBACK: true,  // Keep legacy code working
  ENABLE_MIGRATION_LOGGING: true, // Log migration issues
  ENABLE_IMPORT_PATH_VALIDATION: false,
  
  // Development
  ENABLE_JOURNEY_DEBUG_MODE: __DEV__,
  ENABLE_PERFORMANCE_PROFILING: false,
  SHOW_JOURNEY_BOUNDARIES: __DEV__
};

/**
 * Get feature flag value from environment or default
 */
function getEnvFlag(key: string, defaultValue: boolean): boolean {
  const envKey = `EXPO_PUBLIC_${key}`;
  const envValue = process.env[envKey];
  
  if (envValue === undefined) {
    return defaultValue;
  }
  
  return envValue.toLowerCase() === 'true';
}

/**
 * Initialize feature flags from environment variables
 */
function initializeFlags(): FeatureFlags {
  return {
    // Journey Architecture
    USE_JOURNEY_ARCHITECTURE: getEnvFlag('USE_JOURNEY_ARCHITECTURE', DEFAULT_FLAGS.USE_JOURNEY_ARCHITECTURE),
    ENABLE_JOURNEY_ANALYTICS: getEnvFlag('ENABLE_JOURNEY_ANALYTICS', DEFAULT_FLAGS.ENABLE_JOURNEY_ANALYTICS),
    ENABLE_JOURNEY_PERFORMANCE_MONITORING: getEnvFlag('ENABLE_JOURNEY_PERFORMANCE_MONITORING', DEFAULT_FLAGS.ENABLE_JOURNEY_PERFORMANCE_MONITORING),
    
    // Individual Journeys
    ENABLE_ONBOARDING_JOURNEY: getEnvFlag('ENABLE_ONBOARDING_JOURNEY', DEFAULT_FLAGS.ENABLE_ONBOARDING_JOURNEY),
    ENABLE_DAILY_JOURNEY: getEnvFlag('ENABLE_DAILY_JOURNEY', DEFAULT_FLAGS.ENABLE_DAILY_JOURNEY),
    ENABLE_ACTIVITIES_JOURNEY: getEnvFlag('ENABLE_ACTIVITIES_JOURNEY', DEFAULT_FLAGS.ENABLE_ACTIVITIES_JOURNEY),
    ENABLE_MEMORIES_JOURNEY: getEnvFlag('ENABLE_MEMORIES_JOURNEY', DEFAULT_FLAGS.ENABLE_MEMORIES_JOURNEY),
    ENABLE_PLANNING_JOURNEY: getEnvFlag('ENABLE_PLANNING_JOURNEY', DEFAULT_FLAGS.ENABLE_PLANNING_JOURNEY),
    ENABLE_PROGRESS_JOURNEY: getEnvFlag('ENABLE_PROGRESS_JOURNEY', DEFAULT_FLAGS.ENABLE_PROGRESS_JOURNEY),
    
    // Migration
    ENABLE_LEGACY_FALLBACK: getEnvFlag('ENABLE_LEGACY_FALLBACK', DEFAULT_FLAGS.ENABLE_LEGACY_FALLBACK),
    ENABLE_MIGRATION_LOGGING: getEnvFlag('ENABLE_MIGRATION_LOGGING', DEFAULT_FLAGS.ENABLE_MIGRATION_LOGGING),
    ENABLE_IMPORT_PATH_VALIDATION: getEnvFlag('ENABLE_IMPORT_PATH_VALIDATION', DEFAULT_FLAGS.ENABLE_IMPORT_PATH_VALIDATION),
    
    // Development
    ENABLE_JOURNEY_DEBUG_MODE: getEnvFlag('ENABLE_JOURNEY_DEBUG_MODE', DEFAULT_FLAGS.ENABLE_JOURNEY_DEBUG_MODE),
    ENABLE_PERFORMANCE_PROFILING: getEnvFlag('ENABLE_PERFORMANCE_PROFILING', DEFAULT_FLAGS.ENABLE_PERFORMANCE_PROFILING),
    SHOW_JOURNEY_BOUNDARIES: getEnvFlag('SHOW_JOURNEY_BOUNDARIES', DEFAULT_FLAGS.SHOW_JOURNEY_BOUNDARIES)
  };
}

/**
 * Global feature flags instance
 */
export const FEATURE_FLAGS = initializeFlags();

/**
 * Feature flag utilities
 */
export class FeatureFlagManager {
  private flags: FeatureFlags;
  
  constructor(flags: FeatureFlags = FEATURE_FLAGS) {
    this.flags = flags;
  }
  
  /**
   * Check if journey architecture is enabled
   */
  isJourneyArchitectureEnabled(): boolean {
    return this.flags.USE_JOURNEY_ARCHITECTURE;
  }
  
  /**
   * Check if specific journey is enabled
   */
  isJourneyEnabled(journey: 'onboarding' | 'daily' | 'activities' | 'memories' | 'planning' | 'progress'): boolean {
    const flagMap = {
      onboarding: this.flags.ENABLE_ONBOARDING_JOURNEY,
      daily: this.flags.ENABLE_DAILY_JOURNEY,
      activities: this.flags.ENABLE_ACTIVITIES_JOURNEY,
      memories: this.flags.ENABLE_MEMORIES_JOURNEY,
      planning: this.flags.ENABLE_PLANNING_JOURNEY,
      progress: this.flags.ENABLE_PROGRESS_JOURNEY
    };
    
    return this.flags.USE_JOURNEY_ARCHITECTURE && flagMap[journey];
  }
  
  /**
   * Check if legacy fallback is enabled
   */
  shouldUseLegacyFallback(): boolean {
    return this.flags.ENABLE_LEGACY_FALLBACK;
  }
  
  /**
   * Check if migration logging is enabled
   */
  shouldLogMigration(): boolean {
    return this.flags.ENABLE_MIGRATION_LOGGING;
  }
  
  /**
   * Check if journey analytics is enabled
   */
  isAnalyticsEnabled(): boolean {
    return this.flags.ENABLE_JOURNEY_ANALYTICS;
  }
  
  /**
   * Check if performance monitoring is enabled
   */
  isPerformanceMonitoringEnabled(): boolean {
    return this.flags.ENABLE_JOURNEY_PERFORMANCE_MONITORING;
  }
  
  /**
   * Check if debug mode is enabled
   */
  isDebugModeEnabled(): boolean {
    return this.flags.ENABLE_JOURNEY_DEBUG_MODE;
  }
  
  /**
   * Get all flag values for debugging
   */
  getAllFlags(): FeatureFlags {
    return { ...this.flags };
  }
  
  /**
   * Log current flag status
   */
  logFlagStatus(): void {
    if (this.flags.ENABLE_JOURNEY_DEBUG_MODE) {
      console.log('🚩 Feature Flags Status:', {
        journeyArchitecture: this.flags.USE_JOURNEY_ARCHITECTURE,
        enabledJourneys: {
          onboarding: this.flags.ENABLE_ONBOARDING_JOURNEY,
          daily: this.flags.ENABLE_DAILY_JOURNEY,
          activities: this.flags.ENABLE_ACTIVITIES_JOURNEY,
          memories: this.flags.ENABLE_MEMORIES_JOURNEY,
          planning: this.flags.ENABLE_PLANNING_JOURNEY,
          progress: this.flags.ENABLE_PROGRESS_JOURNEY
        },
        migration: {
          legacyFallback: this.flags.ENABLE_LEGACY_FALLBACK,
          logging: this.flags.ENABLE_MIGRATION_LOGGING
        },
        monitoring: {
          analytics: this.flags.ENABLE_JOURNEY_ANALYTICS,
          performance: this.flags.ENABLE_JOURNEY_PERFORMANCE_MONITORING
        }
      });
    }
  }
}

/**
 * Global feature flag manager instance
 */
export const featureFlagManager = new FeatureFlagManager();

/**
 * Hook for using feature flags in React components
 */
export function useFeatureFlags() {
  return {
    flags: FEATURE_FLAGS,
    manager: featureFlagManager,
    isJourneyEnabled: (journey: Parameters<FeatureFlagManager['isJourneyEnabled']>[0]) => 
      featureFlagManager.isJourneyEnabled(journey),
    isJourneyArchitectureEnabled: () => featureFlagManager.isJourneyArchitectureEnabled(),
    shouldUseLegacyFallback: () => featureFlagManager.shouldUseLegacyFallback(),
    isAnalyticsEnabled: () => featureFlagManager.isAnalyticsEnabled(),
    isDebugModeEnabled: () => featureFlagManager.isDebugModeEnabled()
  };
}

/**
 * Conditional import helper for gradual migration
 */
export async function conditionalImport<T>(
  journeyImport: () => Promise<T>,
  legacyImport: () => Promise<T>,
  journey: Parameters<FeatureFlagManager['isJourneyEnabled']>[0]
): Promise<T> {
  if (featureFlagManager.isJourneyEnabled(journey)) {
    try {
      return await journeyImport();
    } catch (error) {
      if (featureFlagManager.shouldLogMigration()) {
        console.warn(`Failed to load journey ${journey}, falling back to legacy:`, error);
      }
      
      if (featureFlagManager.shouldUseLegacyFallback()) {
        return await legacyImport();
      }
      
      throw error;
    }
  }
  
  return await legacyImport();
}

// Log flag status on initialization
if (__DEV__) {
  featureFlagManager.logFlagStatus();
}
