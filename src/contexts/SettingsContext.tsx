/**
 * Settings Context
 * Manages app settings and user preferences
 */

import React, { createContext, ReactNode, useCallback, useContext, useEffect, useState } from 'react';

export interface NotificationSettings {
  dailyReminders: boolean;
  weeklyReports: boolean;
  specialOccasions: boolean;
  dateNightSuggestions: boolean;
  pushNotifications: boolean;
  emailNotifications: boolean;
}

export interface PrivacySettings {
  shareAnalytics: boolean;
  shareUsageData: boolean;
  allowPersonalization: boolean;
  showOnlineStatus: boolean;
}

export interface DisplaySettings {
  theme: 'light' | 'dark' | 'auto';
  fontSize: 'small' | 'medium' | 'large';
  colorScheme: 'default' | 'high-contrast' | 'colorblind-friendly';
  reduceMotion: boolean;
  showAnimations: boolean;
}

export interface AppSettings {
  notifications: NotificationSettings;
  privacy: PrivacySettings;
  display: DisplaySettings;
  language: string;
  timezone: string;
  dateFormat: 'MM/DD/YYYY' | 'DD/MM/YYYY' | 'YYYY-MM-DD';
  timeFormat: '12h' | '24h';
  firstDayOfWeek: 'sunday' | 'monday';
  theme: 'light' | 'dark' | 'auto'; // Alias for display.theme for backward compatibility
  // Additional flat properties for backward compatibility
  autoSave: boolean;
  dataSync: boolean;
  soundEnabled: boolean;
  vibrationEnabled: boolean;
  accessibility: boolean;
  fontSize: 'small' | 'medium' | 'large';
}

export interface SettingsContextType {
  settings: AppSettings;
  updateSettings: (_newSettings: Partial<AppSettings>) => void;
  updateSetting: (_key: string, _value: unknown) => void;
  updateNotificationSettings: (_notifications: Partial<NotificationSettings>) => void;
  updatePrivacySettings: (_privacy: Partial<PrivacySettings>) => void;
  updateDisplaySettings: (_display: Partial<DisplaySettings>) => void;
  resetSettings: () => void;
  isLoading: boolean;
  isDarkMode: boolean;
  systemColorScheme: 'light' | 'dark';
}

const defaultSettings: AppSettings = {
  notifications: {
    dailyReminders: true,
    weeklyReports: true,
    specialOccasions: true,
    dateNightSuggestions: true,
    pushNotifications: true,
    emailNotifications: false
  },
  privacy: {
    shareAnalytics: false,
    shareUsageData: false,
    allowPersonalization: true,
    showOnlineStatus: true
  },
  display: {
    theme: 'auto',
    fontSize: 'medium',
    colorScheme: 'default',
    reduceMotion: false,
    showAnimations: true
  },
  language: 'en',
  timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
  dateFormat: 'MM/DD/YYYY',
  timeFormat: '12h',
  firstDayOfWeek: 'sunday',
  theme: 'light', // Default theme
  // Additional flat properties for backward compatibility
  autoSave: true,
  dataSync: true,
  soundEnabled: true,
  vibrationEnabled: true,
  accessibility: false,
  fontSize: 'medium'
};

const SettingsContext = createContext<SettingsContextType | undefined>(undefined);

export interface SettingsProviderProps {
  children: ReactNode;
}

export function SettingsProvider({ children }: SettingsProviderProps) {
  const [settings, setSettings] = useState<AppSettings>(defaultSettings);
  const [isLoading, setIsLoading] = useState(true);

  // Load settings on mount
  useEffect(() => {
    loadSettings();
  }, []);

  const saveSettings = useCallback(async () => {
    try {
      // In a real app, this would save to AsyncStorage or API
      localStorage.setItem('app_settings', JSON.stringify(settings));
    } catch (error) {
      console.error('Failed to save settings:', error);
    }
  }, [settings]);

  // Save settings whenever they change
  useEffect(() => {
    if (!isLoading) {
      saveSettings();
    }
  }, [settings, isLoading, saveSettings]);

  const loadSettings = async () => {
    try {
      setIsLoading(true);
      // In a real app, this would load from AsyncStorage or API
      const stored = localStorage.getItem('app_settings');
      if (stored) {
        const parsedSettings = JSON.parse(stored);
        // Merge with defaults to handle new settings
        setSettings({ ...defaultSettings, ...parsedSettings });
      }
    } catch (error) {
      console.error('Failed to load settings:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const updateSettings = (newSettings: Partial<AppSettings>) => {
    setSettings(prev => ({ ...prev, ...newSettings }));
  };

  const updateNotificationSettings = (notifications: Partial<NotificationSettings>) => {
    setSettings(prev => ({
      ...prev,
      notifications: { ...prev.notifications, ...notifications }
    }));
  };

  const updatePrivacySettings = (privacy: Partial<PrivacySettings>) => {
    setSettings(prev => ({
      ...prev,
      privacy: { ...prev.privacy, ...privacy }
    }));
  };

  const updateDisplaySettings = (display: Partial<DisplaySettings>) => {
    setSettings(prev => ({
      ...prev,
      display: { ...prev.display, ...display }
    }));
  };

  const resetSettings = () => {
    setSettings(defaultSettings);
  };

  const updateSetting = (key: string, value: unknown) => {
    setSettings(prev => ({
      ...prev,
      [key]: value
    }));
  };

  // Detect system color scheme
  const getSystemColorScheme = (): 'light' | 'dark' => {
    if (typeof window !== 'undefined' && window.matchMedia) {
      return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
    }
    return 'light';
  };

  const systemColorScheme = getSystemColorScheme();
  const isDarkMode = settings.display.theme === 'dark' ||
    (settings.display.theme === 'auto' && systemColorScheme === 'dark');

  const value: SettingsContextType = {
    settings,
    updateSettings,
    updateSetting,
    updateNotificationSettings,
    updatePrivacySettings,
    updateDisplaySettings,
    resetSettings,
    isLoading,
    isDarkMode,
    systemColorScheme
  };

  return (
    <SettingsContext.Provider value={value}>
      {children}
    </SettingsContext.Provider>
  );
}

export function useSettings(): SettingsContextType {
  const context = useContext(SettingsContext);
  if (context === undefined) {
    throw new Error('useSettings must be used within a SettingsProvider');
  }
  return context;
}

// Convenience hooks for specific settings
export function useNotificationSettings() {
  const { settings, updateNotificationSettings } = useSettings();
  return {
    notifications: settings.notifications,
    updateNotifications: updateNotificationSettings
  };
}

export function usePrivacySettings() {
  const { settings, updatePrivacySettings } = useSettings();
  return {
    privacy: settings.privacy,
    updatePrivacy: updatePrivacySettings
  };
}

export function useDisplaySettings() {
  const { settings, updateDisplaySettings } = useSettings();
  return {
    display: settings.display,
    updateDisplay: updateDisplaySettings
  };
}

export function useTheme() {
  const { settings } = useSettings();
  return settings.display.theme;
}

export function useDateTimeFormat() {
  const { settings } = useSettings();
  return {
    dateFormat: settings.dateFormat,
    timeFormat: settings.timeFormat,
    timezone: settings.timezone,
    firstDayOfWeek: settings.firstDayOfWeek
  };
}
