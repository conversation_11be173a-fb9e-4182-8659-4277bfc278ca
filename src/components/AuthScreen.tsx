/**
 * Authentication Screen Component
 * Handles user login and registration
 */

import React, { useState } from 'react';
import { Alert, StyleSheet, Text, TextInput, TouchableOpacity, View } from 'react-native';

export interface AuthScreenProps {
  onLogin?: (_email: string, _password: string) => Promise<void>;
  onRegister?: (_email: string, _password: string, _name: string) => Promise<void>;
  onForgotPassword?: (_email: string) => Promise<void>;
  isLoading?: boolean;
}

export interface AuthFormData {
  email: string;
  password: string;
  confirmPassword?: string;
  name?: string;
}

export default function AuthScreen({
  onLogin,
  onRegister,
  onForgotPassword,
  isLoading = false
}: AuthScreenProps) {
  const [mode, setMode] = useState<'login' | 'register' | 'forgot'>('login');
  const [formData, setFormData] = useState<AuthFormData>({
    email: '',
    password: '',
    confirmPassword: '',
    name: ''
  });
  const [errors, setErrors] = useState<Partial<AuthFormData>>({});

  const validateForm = (): boolean => {
    const newErrors: Partial<AuthFormData> = {};

    // Email validation
    if (!formData.email) {
      newErrors.email = 'Email is required';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'Email is invalid';
    }

    // Password validation
    if (mode !== 'forgot') {
      if (!formData.password) {
        newErrors.password = 'Password is required';
      } else if (formData.password.length < 8) {
        newErrors.password = 'Password must be at least 8 characters';
      }
    }

    // Registration-specific validation
    if (mode === 'register') {
      if (!formData.name) {
        newErrors.name = 'Name is required';
      }

      if (formData.password !== formData.confirmPassword) {
        newErrors.confirmPassword = 'Passwords do not match';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) return;

    try {
      switch (mode) {
        case 'login':
          await onLogin?.(formData.email, formData.password);
          break;
        case 'register':
          await onRegister?.(formData.email, formData.password, formData.name || '');
          break;
        case 'forgot':
          await onForgotPassword?.(formData.email);
          Alert.alert('Success', 'Password reset email sent!');
          setMode('login');
          break;
      }
    } catch (error) {
      Alert.alert('Error', error instanceof Error ? error.message : 'An error occurred');
    }
  };

  const updateFormData = (field: keyof AuthFormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  const renderInput = (
    field: keyof AuthFormData,
    placeholder: string,
    secureTextEntry: boolean = false
  ) => (
    <View style={styles.inputContainer}>
      <TextInput
        style={[styles.input, errors[field] && styles.inputError]}
        placeholder={placeholder}
        value={formData[field] || ''}
        onChangeText={(value) => updateFormData(field, value)}
        secureTextEntry={secureTextEntry}
        autoCapitalize="none"
        keyboardType={field === 'email' ? 'email-address' : 'default'}
        editable={!isLoading}
      />
      {errors[field] && <Text style={styles.errorText}>{errors[field]}</Text>}
    </View>
  );

  const getTitle = () => {
    switch (mode) {
      case 'login': return 'Welcome Back';
      case 'register': return 'Create Account';
      case 'forgot': return 'Reset Password';
    }
  };

  const getButtonText = () => {
    switch (mode) {
      case 'login': return 'Sign In';
      case 'register': return 'Create Account';
      case 'forgot': return 'Send Reset Email';
    }
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>{getTitle()}</Text>
        <Text style={styles.subtitle}>
          {mode === 'login' && 'Sign in to continue your journey together'}
          {mode === 'register' && 'Start your journey as a couple'}
          {mode === 'forgot' && 'Enter your email to reset your password'}
        </Text>
      </View>

      <View style={styles.form}>
        {mode === 'register' && renderInput('name', 'Full Name')}

        {renderInput('email', 'Email Address')}

        {mode !== 'forgot' && renderInput('password', 'Password', true)}

        {mode === 'register' && renderInput('confirmPassword', 'Confirm Password', true)}

        <TouchableOpacity
          style={[styles.submitButton, isLoading && styles.submitButtonDisabled]}
          onPress={handleSubmit}
          disabled={isLoading}
        >
          <Text style={styles.submitButtonText}>
            {isLoading ? 'Please wait...' : getButtonText()}
          </Text>
        </TouchableOpacity>
      </View>

      <View style={styles.footer}>
        {mode === 'login' && (
          <>
            <TouchableOpacity onPress={() => setMode('forgot')}>
              <Text style={styles.linkText}>Forgot Password?</Text>
            </TouchableOpacity>

            <View style={styles.switchMode}>
              <Text style={styles.switchText}>Don't have an account? </Text>
              <TouchableOpacity onPress={() => setMode('register')}>
                <Text style={styles.linkText}>Sign Up</Text>
              </TouchableOpacity>
            </View>
          </>
        )}

        {mode === 'register' && (
          <View style={styles.switchMode}>
            <Text style={styles.switchText}>Already have an account? </Text>
            <TouchableOpacity onPress={() => setMode('login')}>
              <Text style={styles.linkText}>Sign In</Text>
            </TouchableOpacity>
          </View>
        )}

        {mode === 'forgot' && (
          <TouchableOpacity onPress={() => setMode('login')}>
            <Text style={styles.linkText}>Back to Sign In</Text>
          </TouchableOpacity>
        )}
      </View>
    </View>
  );
}

// eslint-disable-next-line no-restricted-syntax
const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    justifyContent: 'center',
    backgroundColor: '#fff',
  },
  header: {
    alignItems: 'center',
    marginBottom: 40,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    lineHeight: 22,
  },
  form: {
    marginBottom: 30,
  },
  inputContainer: {
    marginBottom: 20,
  },
  input: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 15,
    fontSize: 16,
    backgroundColor: '#f9f9f9',
  },
  inputError: {
    borderColor: '#ff6b6b',
  },
  errorText: {
    color: '#ff6b6b',
    fontSize: 14,
    marginTop: 5,
    marginLeft: 5,
  },
  submitButton: {
    backgroundColor: '#9CAF88',
    borderRadius: 8,
    padding: 15,
    alignItems: 'center',
    marginTop: 10,
  },
  submitButtonDisabled: {
    backgroundColor: '#ccc',
  },
  submitButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  footer: {
    alignItems: 'center',
  },
  switchMode: {
    flexDirection: 'row',
    marginTop: 15,
  },
  switchText: {
    color: '#666',
    fontSize: 14,
  },
  linkText: {
    color: '#9CAF88',
    fontSize: 14,
    fontWeight: '600',
  },
});
