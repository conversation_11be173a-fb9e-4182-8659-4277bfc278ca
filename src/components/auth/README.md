# Authentication System

A production-ready authentication system for the React Native couples app with comprehensive security features, brand-compliant UI, and biometric authentication support.

## Features

### 🔐 Security Features
- **Input Validation & Sanitization**: Comprehensive client-side validation with server-side verification
- **Rate Limiting**: Protection against brute force attacks
- **Secure Token Storage**: Uses Expo SecureStore for sensitive data
- **Password Strength Validation**: Real-time password strength indicators
- **Email Verification**: Secure email verification flow
- **Biometric Authentication**: Face ID/Touch ID support where available
- **Session Management**: Automatic token refresh and session handling

### 🎨 UI/UX Features
- **Brand-Compliant Design**: Uses established brand colors (no gradients)
- **Glass-morphism Effects**: Modern UI with subtle glass effects
- **Responsive Design**: Works across different screen sizes
- **Loading States**: Comprehensive loading and error states
- **Accessibility**: Full accessibility support with proper labels
- **Dark Mode Support**: Automatic theme adaptation

### 🔗 Integration Features
- **Supabase Integration**: Full integration with Supabase Auth
- **Social Login**: Google and Apple Sign-In support (configurable)
- **Protected Routes**: Route guards for authenticated content
- **Error Boundaries**: Comprehensive error handling and recovery
- **Analytics Integration**: Built-in event tracking

## Components

### Core Components

#### `AuthScreenLayout`
Base layout component for authentication screens with keyboard avoidance and scrolling support.

```tsx
<AuthScreenLayout backgroundColor={brandColors.sageGreen}>
  {/* Your auth content */}
</AuthScreenLayout>
```

#### `AuthHeader`
Branded header component with emoji, title, and subtitle support.

```tsx
<AuthHeader
  title="Welcome Back"
  subtitle="Sign in to continue your journey together"
  emoji="💕"
/>
```

#### `AuthInput`
Enhanced input component with validation, icons, and password visibility toggle.

```tsx
<AuthInput
  value={email}
  onChangeText={setEmail}
  placeholder="Enter your email"
  label="Email"
  type="email"
  leftIcon={<Mail size={20} color="rgba(255, 255, 255, 0.7)" />}
  error={errors.email}
/>
```

#### `AuthButton`
Versatile button component with multiple variants and loading states.

```tsx
<AuthButton
  title="Sign In"
  onPress={handleSignIn}
  variant="primary"
  loading={isLoading}
  disabled={isLoading}
/>
```

#### `SocialButton`
Specialized button for social login providers.

```tsx
<SocialButton
  provider="google"
  onPress={handleGoogleSignIn}
  loading={isLoading}
  disabled={isLoading}
/>
```

#### `PasswordStrength`
Real-time password strength indicator with visual feedback.

```tsx
<PasswordStrength password={password} />
```

### Utility Components

#### `AuthDivider`
Styled divider for separating content sections.

```tsx
<AuthDivider text="or continue with" />
```

#### `AuthLink`
Styled link component for navigation between auth screens.

```tsx
<AuthLink
  text="Don't have an account?"
  linkText="Sign Up"
  onPress={handleSignUp}
/>
```

### Protection Components

#### `ProtectedRoute`
Route guard component that redirects unauthenticated users.

```tsx
<ProtectedRoute redirectTo="/auth/welcome">
  <ProtectedContent />
</ProtectedRoute>
```

#### `AuthGuard`
Higher-order component for authentication guarding.

```tsx
<AuthGuard requireAuth={true}>
  <ProtectedContent />
</AuthGuard>
```

#### `AuthErrorBoundary`
Comprehensive error boundary for authentication flows.

```tsx
<AuthErrorBoundary onError={handleError}>
  <AuthFlow />
</AuthErrorBoundary>
```

## Authentication Provider

The `AuthProvider` component manages authentication state and provides methods for all authentication operations.

### Available Methods

```tsx
const {
  // State
  user,
  profile,
  isLoading,
  isAuthenticated,
  isInitialized,
  isBiometricEnabled,
  biometricType,
  
  // Authentication methods
  signIn,
  signUp,
  signOut,
  refreshUser,
  
  // Password management
  resetPassword,
  verifyEmail,
  
  // Biometric authentication
  enableBiometric,
  disableBiometric,
  signInWithBiometric,
  
  // Social login
  signInWithGoogle,
  signInWithApple,
} = useAuth();
```

## Screens

### Authentication Screens

1. **Welcome Screen** (`/auth/welcome`)
   - App introduction and feature highlights
   - Primary and social login options
   - Brand-compliant design with glass-morphism

2. **Login Screen** (`/auth/login`)
   - Email/password authentication
   - Biometric authentication (if enabled)
   - Social login options
   - Forgot password link

3. **Signup Screen** (`/auth/signup`)
   - User registration with validation
   - Password strength indicator
   - Social signup options
   - Terms and privacy links

4. **Forgot Password Screen** (`/auth/forgot-password`)
   - Email-based password reset
   - Success confirmation
   - Resend functionality

5. **Email Verification Screen** (`/auth/verify-email`)
   - Email verification handling
   - Success/error states
   - Resend verification option

## Usage Examples

### Basic Authentication Flow

```tsx
import { AuthProvider, useAuth } from './src/components/auth';

function App() {
  return (
    <AuthProvider>
      <AppContent />
    </AuthProvider>
  );
}

function LoginForm() {
  const { signIn, isLoading } = useAuth();
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');

  const handleLogin = async () => {
    try {
      await signIn(email, password);
      // Redirect handled automatically
    } catch (error) {
      // Handle error
    }
  };

  return (
    <AuthScreenLayout>
      <AuthInput
        value={email}
        onChangeText={setEmail}
        placeholder="Email"
        type="email"
      />
      <AuthInput
        value={password}
        onChangeText={setPassword}
        placeholder="Password"
        type="password"
      />
      <AuthButton
        title="Sign In"
        onPress={handleLogin}
        loading={isLoading}
      />
    </AuthScreenLayout>
  );
}
```

### Protected Route Usage

```tsx
import { ProtectedRoute } from './src/components/auth';

function ProtectedScreen() {
  return (
    <ProtectedRoute>
      <YourProtectedContent />
    </ProtectedRoute>
  );
}
```

## Configuration

### Environment Variables

```env
EXPO_PUBLIC_SUPABASE_URL=your_supabase_url
EXPO_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
EXPO_PUBLIC_APP_URL=your_app_url
EXPO_PUBLIC_BYPASS_AUTH=false
```

### Feature Flags

```tsx
// src/utils/constants.ts
export const FEATURE_FLAGS = {
  ENABLE_AUTHENTICATION: true,
  ENABLE_BIOMETRIC_AUTH: true,
  ENABLE_SOCIAL_LOGIN: true,
  // ... other flags
};
```

## Testing

The authentication system includes comprehensive unit tests:

```bash
# Run authentication tests
npm test -- --testPathPattern=auth

# Run specific test files
npm test AuthComponents.test.tsx
npm test AuthProvider.test.tsx
```

## Security Considerations

1. **Input Validation**: All inputs are validated both client and server-side
2. **Rate Limiting**: Built-in protection against brute force attacks
3. **Secure Storage**: Sensitive data stored using platform secure storage
4. **Token Management**: Automatic token refresh and secure handling
5. **Error Handling**: No sensitive information exposed in error messages
6. **Biometric Security**: Proper biometric authentication implementation

## Accessibility

- Full screen reader support
- Proper focus management
- High contrast support
- Keyboard navigation
- Semantic HTML elements
- ARIA labels and descriptions

## Performance

- Lazy loading of authentication screens
- Optimized re-renders with proper memoization
- Efficient state management
- Minimal bundle size impact
- Fast authentication checks

## Browser/Platform Support

- iOS (React Native)
- Android (React Native)
- Web (React Native Web)
- Biometric authentication (iOS/Android only)
- Secure storage (all platforms)
