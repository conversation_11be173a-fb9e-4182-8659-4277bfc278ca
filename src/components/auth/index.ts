/**
 * Authentication Components Index
 * 
 * Central export point for all authentication-related components,
 * hooks, and utilities.
 * 
 * @version 3.0.0
 * <AUTHOR> Us Team
 */

// Core authentication components
export {
  AuthScreenLayout,
  AuthHeader,
  AuthInput,
  AuthButton,
  SocialButton,
  PasswordStrength,
  AuthDivider,
  AuthLink,
} from './AuthComponents';

// Authentication guards and protection
export {
  ProtectedRoute,
  AuthGuard,
  useAuthGuard,
} from './ProtectedRoute';

// Error handling
export {
  AuthErrorBoundary,
  withAuthErrorBoundary,
} from './AuthErrorBoundary';

// Types
export type {
  AuthScreenLayoutProps,
  AuthHeaderProps,
  AuthInputProps,
  AuthButtonProps,
  SocialButtonProps,
  PasswordStrengthProps,
} from './AuthComponents';
