/**
 * Authentication Error Boundary
 *
 * Comprehensive error boundary for authentication flows with
 * user-friendly error messages and recovery options.
 *
 * @version 3.0.0
 * <AUTHOR> Us Team
 */

import { router } from 'expo-router';
import { AlertTriangle, Home, RefreshCw } from 'lucide-react-native';
import React, { Component, ReactNode } from 'react';
import { StyleSheet, Text, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { GlassView } from '../../shared/components/common/GlassView';
import { brandColors } from '../../shared/utils/colors';
import { logger } from '../../shared/utils/logger';
import {
    AuthButton,
    AuthHeader,
    AuthScreenLayout,
} from './AuthComponents';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (_error: Error, _errorInfo: React.ErrorInfo) => void;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: React.ErrorInfo | null;
}

export class AuthErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
    };
  }

  static getDerivedStateFromError(error: Error): State {
    return {
      hasError: true,
      error,
      errorInfo: null,
    };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    logger.error('Auth Error Boundary caught an error:', { error, errorInfo });

    this.setState({
      error,
      errorInfo,
    });

    // Call custom error handler if provided
    this.props.onError?.(error, errorInfo);

    // Report to error tracking service
    // TODO: Integrate with error tracking service (e.g., Sentry, Bugsnag)
  }

  handleRetry = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
    });
  };

  handleGoHome = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
    });
    router.replace('/(tabs)');
  };

  handleGoToAuth = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
    });
    router.replace('/auth/welcome');
  };

  getErrorMessage = (error: Error): string => {
    const message = error.message.toLowerCase();

    if (message.includes('network')) {
      return 'Network connection error. Please check your internet connection and try again.';
    }

    if (message.includes('timeout')) {
      return 'Request timed out. Please check your connection and try again.';
    }

    if (message.includes('auth')) {
      return 'Authentication error. Please try signing in again.';
    }

    if (message.includes('permission')) {
      return 'Permission denied. Please check your account permissions.';
    }

    return 'An unexpected error occurred. Please try again.';
  };

  render() {
    if (this.state.hasError) {
      // Custom fallback UI
      if (this.props.fallback) {
        return this.props.fallback;
      }

      const errorMessage = this.state.error
        ? this.getErrorMessage(this.state.error)
        : 'An unexpected error occurred';

      return (
        <SafeAreaView style={styles.safeArea}>
          <AuthScreenLayout backgroundColor={brandColors.sageGreen}>
            <View style={styles.container}>
              <AlertTriangle size={80} color="#F59E0B" style={styles.icon} />

              <AuthHeader
                title="Oops! Something went wrong"
                subtitle="We encountered an unexpected error"
                emoji=""
              />

              <GlassView style={styles.errorContainer}>
                <Text style={styles.errorTitle}>Error Details</Text>
                <Text style={styles.errorMessage}>{errorMessage}</Text>

                {__DEV__ && this.state.error && (
                  <View style={styles.debugContainer}>
                    <Text style={styles.debugTitle}>Debug Information:</Text>
                    <Text style={styles.debugText}>
                      {this.state.error.name}: {this.state.error.message}
                    </Text>
                    {this.state.error.stack && (
                      <Text style={styles.debugStack}>
                        {this.state.error.stack.substring(0, 500)}...
                      </Text>
                    )}
                  </View>
                )}
              </GlassView>

              <View style={styles.actionsContainer}>
                <AuthButton
                  title="Try Again"
                  onPress={this.handleRetry}
                  variant="primary"
                  leftIcon={<RefreshCw size={20} color={brandColors.charcoal} />}
                />

                <AuthButton
                  title="Go to Home"
                  onPress={this.handleGoHome}
                  variant="outline"
                  leftIcon={<Home size={20} color="#FFFFFF" />}
                />

                <AuthButton
                  title="Back to Sign In"
                  onPress={this.handleGoToAuth}
                  variant="ghost"
                />
              </View>
            </View>
          </AuthScreenLayout>
        </SafeAreaView>
      );
    }

    return this.props.children;
  }
}

/**
 * Higher-order component for wrapping components with auth error boundary
 */
export const withAuthErrorBoundary = <P extends object>(
  Component: React.ComponentType<P>,
  errorBoundaryProps?: Omit<Props, 'children'>
) => {
  const WrappedComponent = (props: P) => (
    <AuthErrorBoundary {...errorBoundaryProps}>
      <Component {...props} />
    </AuthErrorBoundary>
  );

  WrappedComponent.displayName = `withAuthErrorBoundary(${Component.displayName || Component.name})`;

  return WrappedComponent;
};

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: brandColors.sageGreen,
  },
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
  },
  icon: {
    marginBottom: 24,
  },
  errorContainer: {
    padding: 24,
    borderRadius: 16,
    marginVertical: 32,
    width: '100%',
  },
  errorTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#F59E0B',
    marginBottom: 12,
    textAlign: 'center',
  },
  errorMessage: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.8)',
    textAlign: 'center',
    lineHeight: 20,
    marginBottom: 16,
  },
  debugContainer: {
    marginTop: 16,
    padding: 12,
    backgroundColor: 'rgba(0, 0, 0, 0.2)',
    borderRadius: 8,
  },
  debugTitle: {
    fontSize: 12,
    fontWeight: '600',
    color: '#F59E0B',
    marginBottom: 8,
  },
  debugText: {
    fontSize: 11,
    color: 'rgba(255, 255, 255, 0.7)',
    fontFamily: 'monospace',
    marginBottom: 4,
  },
  debugStack: {
    fontSize: 10,
    color: 'rgba(255, 255, 255, 0.5)',
    fontFamily: 'monospace',
  },
  actionsContainer: {
    width: '100%',
    gap: 12,
  },
});
