/**
 * Protected Route Component
 * 
 * Authentication guard component that redirects unauthenticated
 * users to the authentication flow.
 * 
 * @version 3.0.0
 * <AUTHOR> Us Team
 */

import { Redirect } from 'expo-router';
import React from 'react';
import { ActivityIndicator, StyleSheet, Text, View } from 'react-native';
import { useAuth } from '../../hooks/useAuth';
import { useGlobalTheme } from '../../shared/components/common/ThemeProvider';
import { FEATURE_FLAGS } from '../../utils/constants';

interface ProtectedRouteProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
  redirectTo?: string;
}

export const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  fallback,
  redirectTo = '/auth/welcome',
}) => {
  const { isAuthenticated, isLoading, isInitialized } = useAuth();
  const { currentTheme } = useGlobalTheme();

  // If authentication is disabled, render children directly
  if (!FEATURE_FLAGS.ENABLE_AUTHENTICATION) {
    return <>{children}</>;
  }

  // Show loading state while initializing
  if (isLoading || !isInitialized) {
    return (
      fallback || (
        <View style={[styles.loadingContainer, { backgroundColor: currentTheme.background }]}>
          <ActivityIndicator size="large" color={currentTheme.primary} />
          <Text style={[styles.loadingText, { color: currentTheme.textPrimary }]}>
            Loading...
          </Text>
        </View>
      )
    );
  }

  // Redirect to auth if not authenticated
  if (!isAuthenticated) {
    return <Redirect href={redirectTo} />;
  }

  // Render protected content
  return <>{children}</>;
};

interface AuthGuardProps {
  children: React.ReactNode;
  requireAuth?: boolean;
  fallback?: React.ReactNode;
}

/**
 * Higher-order component for authentication guarding
 */
export const AuthGuard: React.FC<AuthGuardProps> = ({
  children,
  requireAuth = true,
  fallback,
}) => {
  const { isAuthenticated, isLoading, isInitialized } = useAuth();
  const { currentTheme } = useGlobalTheme();

  // If authentication is disabled, render children directly
  if (!FEATURE_FLAGS.ENABLE_AUTHENTICATION) {
    return <>{children}</>;
  }

  // If auth is not required, render children directly
  if (!requireAuth) {
    return <>{children}</>;
  }

  // Show loading state while initializing
  if (isLoading || !isInitialized) {
    return (
      fallback || (
        <View style={[styles.loadingContainer, { backgroundColor: currentTheme.background }]}>
          <ActivityIndicator size="large" color={currentTheme.primary} />
          <Text style={[styles.loadingText, { color: currentTheme.textPrimary }]}>
            Authenticating...
          </Text>
        </View>
      )
    );
  }

  // Return null if not authenticated (let parent handle redirect)
  if (!isAuthenticated) {
    return null;
  }

  // Render protected content
  return <>{children}</>;
};

/**
 * Hook for checking authentication status
 */
export const useAuthGuard = () => {
  const { isAuthenticated, isLoading, isInitialized } = useAuth();

  const canAccess = (requireAuth: boolean = true) => {
    if (!FEATURE_FLAGS.ENABLE_AUTHENTICATION) {
      return true;
    }

    if (!requireAuth) {
      return true;
    }

    if (isLoading || !isInitialized) {
      return false;
    }

    return isAuthenticated;
  };

  const shouldRedirect = (requireAuth: boolean = true) => {
    if (!FEATURE_FLAGS.ENABLE_AUTHENTICATION) {
      return false;
    }

    if (!requireAuth) {
      return false;
    }

    if (isLoading || !isInitialized) {
      return false;
    }

    return !isAuthenticated;
  };

  return {
    canAccess,
    shouldRedirect,
    isLoading: isLoading || !isInitialized,
    isAuthenticated,
  };
};

const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
  },
  loadingText: {
    fontSize: 16,
    marginTop: 16,
    textAlign: 'center',
  },
});
