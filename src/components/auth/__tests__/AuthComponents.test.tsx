/**
 * Authentication Components Tests
 * 
 * Unit tests for authentication UI components with comprehensive
 * coverage of user interactions and edge cases.
 * 
 * @version 3.0.0
 * <AUTHOR> Us Team
 */

import React from 'react';
import { render, fireEvent, waitFor } from '@testing-library/react-native';
import {
  AuthInput,
  AuthButton,
  SocialButton,
  PasswordStrength,
  AuthDivider,
  AuthLink,
} from '../AuthComponents';

// Mock dependencies
jest.mock('../../../shared/components/common/GlassView', () => ({
  GlassView: ({ children, ...props }: any) => (
    <div testID="glass-view" {...props}>
      {children}
    </div>
  ),
}));

jest.mock('../../../shared/components/common/ThemeProvider', () => ({
  useGlobalTheme: () => ({
    currentTheme: {
      primary: '#9CAF88',
      background: '#FFFFFF',
      textPrimary: '#393939',
    },
  }),
}));

describe('AuthInput', () => {
  it('renders correctly with basic props', () => {
    const { getByPlaceholderText, getByText } = render(
      <AuthInput
        value=""
        onChangeText={jest.fn()}
        placeholder="Enter email"
        label="Email"
      />
    );

    expect(getByText('Email')).toBeTruthy();
    expect(getByPlaceholderText('Enter email')).toBeTruthy();
  });

  it('handles text input changes', () => {
    const mockOnChangeText = jest.fn();
    const { getByPlaceholderText } = render(
      <AuthInput
        value=""
        onChangeText={mockOnChangeText}
        placeholder="Enter email"
      />
    );

    const input = getByPlaceholderText('Enter email');
    fireEvent.changeText(input, '<EMAIL>');

    expect(mockOnChangeText).toHaveBeenCalledWith('<EMAIL>');
  });

  it('displays error message when provided', () => {
    const { getByText } = render(
      <AuthInput
        value=""
        onChangeText={jest.fn()}
        placeholder="Enter email"
        error="Invalid email"
      />
    );

    expect(getByText('Invalid email')).toBeTruthy();
  });

  it('toggles password visibility for password type', () => {
    const { getByPlaceholderText, getByRole } = render(
      <AuthInput
        value="password123"
        onChangeText={jest.fn()}
        placeholder="Enter password"
        type="password"
      />
    );

    const input = getByPlaceholderText('Enter password');
    expect(input.props.secureTextEntry).toBe(true);

    // Find and press the toggle button
    const toggleButton = getByRole('button');
    fireEvent.press(toggleButton);

    expect(input.props.secureTextEntry).toBe(false);
  });

  it('disables input when disabled prop is true', () => {
    const { getByPlaceholderText } = render(
      <AuthInput
        value=""
        onChangeText={jest.fn()}
        placeholder="Enter email"
        disabled={true}
      />
    );

    const input = getByPlaceholderText('Enter email');
    expect(input.props.editable).toBe(false);
  });
});

describe('AuthButton', () => {
  it('renders correctly with title', () => {
    const { getByText } = render(
      <AuthButton title="Sign In" onPress={jest.fn()} />
    );

    expect(getByText('Sign In')).toBeTruthy();
  });

  it('calls onPress when pressed', () => {
    const mockOnPress = jest.fn();
    const { getByText } = render(
      <AuthButton title="Sign In" onPress={mockOnPress} />
    );

    fireEvent.press(getByText('Sign In'));
    expect(mockOnPress).toHaveBeenCalledTimes(1);
  });

  it('shows loading indicator when loading', () => {
    const { getByTestId, queryByText } = render(
      <AuthButton title="Sign In" onPress={jest.fn()} loading={true} />
    );

    expect(getByTestId('activity-indicator')).toBeTruthy();
    expect(queryByText('Sign In')).toBeNull();
  });

  it('is disabled when disabled prop is true', () => {
    const mockOnPress = jest.fn();
    const { getByText } = render(
      <AuthButton title="Sign In" onPress={mockOnPress} disabled={true} />
    );

    const button = getByText('Sign In').parent;
    fireEvent.press(button);
    expect(mockOnPress).not.toHaveBeenCalled();
  });

  it('applies correct styles for different variants', () => {
    const { getByText: getPrimaryText } = render(
      <AuthButton title="Primary" onPress={jest.fn()} variant="primary" />
    );
    const { getByText: getSecondaryText } = render(
      <AuthButton title="Secondary" onPress={jest.fn()} variant="secondary" />
    );

    const primaryButton = getPrimaryText('Primary').parent;
    const secondaryButton = getSecondaryText('Secondary').parent;

    expect(primaryButton?.props.style).toContainEqual(
      expect.objectContaining({ backgroundColor: '#FFFFFF' })
    );
    expect(secondaryButton?.props.style).toContainEqual(
      expect.objectContaining({ backgroundColor: '#CBC3E3' })
    );
  });
});

describe('SocialButton', () => {
  it('renders Google button correctly', () => {
    const { getByText } = render(
      <SocialButton provider="google" onPress={jest.fn()} />
    );

    expect(getByText('Continue with Google')).toBeTruthy();
  });

  it('renders Apple button correctly', () => {
    const { getByText } = render(
      <SocialButton provider="apple" onPress={jest.fn()} />
    );

    expect(getByText('Continue with Apple')).toBeTruthy();
  });

  it('calls onPress when pressed', () => {
    const mockOnPress = jest.fn();
    const { getByText } = render(
      <SocialButton provider="google" onPress={mockOnPress} />
    );

    fireEvent.press(getByText('Continue with Google'));
    expect(mockOnPress).toHaveBeenCalledTimes(1);
  });
});

describe('PasswordStrength', () => {
  it('returns null for empty password', () => {
    const { container } = render(<PasswordStrength password="" />);
    expect(container.children).toHaveLength(0);
  });

  it('shows weak strength for simple password', () => {
    const { getByText } = render(<PasswordStrength password="123" />);
    expect(getByText('Password strength: weak')).toBeTruthy();
  });

  it('shows strong strength for complex password', () => {
    const { getByText } = render(
      <PasswordStrength password="MyStr0ng!Password" />
    );
    expect(getByText('Password strength: strong')).toBeTruthy();
  });

  it('updates strength indicator width based on password complexity', () => {
    const { getByTestId, rerender } = render(
      <PasswordStrength password="weak" />
    );

    const strengthFill = getByTestId('password-strength-fill');
    expect(strengthFill.props.style.width).toBe('25%');

    rerender(<PasswordStrength password="MyStr0ng!Password" />);
    expect(strengthFill.props.style.width).toBe('100%');
  });
});

describe('AuthDivider', () => {
  it('renders with default text', () => {
    const { getByText } = render(<AuthDivider />);
    expect(getByText('or')).toBeTruthy();
  });

  it('renders with custom text', () => {
    const { getByText } = render(<AuthDivider text="continue with" />);
    expect(getByText('continue with')).toBeTruthy();
  });
});

describe('AuthLink', () => {
  it('renders text and link correctly', () => {
    const { getByText } = render(
      <AuthLink
        text="Don't have an account?"
        linkText="Sign Up"
        onPress={jest.fn()}
      />
    );

    expect(getByText("Don't have an account?")).toBeTruthy();
    expect(getByText('Sign Up')).toBeTruthy();
  });

  it('calls onPress when link is pressed', () => {
    const mockOnPress = jest.fn();
    const { getByText } = render(
      <AuthLink
        text="Don't have an account?"
        linkText="Sign Up"
        onPress={mockOnPress}
      />
    );

    fireEvent.press(getByText('Sign Up'));
    expect(mockOnPress).toHaveBeenCalledTimes(1);
  });
});

// Integration tests
describe('AuthComponents Integration', () => {
  it('works together in a form-like structure', async () => {
    const mockSubmit = jest.fn();
    const mockEmailChange = jest.fn();
    const mockPasswordChange = jest.fn();

    const { getByPlaceholderText, getByText } = render(
      <>
        <AuthInput
          value=""
          onChangeText={mockEmailChange}
          placeholder="Enter email"
          type="email"
        />
        <AuthInput
          value=""
          onChangeText={mockPasswordChange}
          placeholder="Enter password"
          type="password"
        />
        <AuthButton title="Sign In" onPress={mockSubmit} />
      </>
    );

    // Simulate user input
    fireEvent.changeText(getByPlaceholderText('Enter email'), '<EMAIL>');
    fireEvent.changeText(getByPlaceholderText('Enter password'), 'password123');
    fireEvent.press(getByText('Sign In'));

    expect(mockEmailChange).toHaveBeenCalledWith('<EMAIL>');
    expect(mockPasswordChange).toHaveBeenCalledWith('password123');
    expect(mockSubmit).toHaveBeenCalledTimes(1);
  });
});
