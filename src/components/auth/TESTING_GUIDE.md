# 🧪 Authentication System Testing Guide

## 🚀 **Quick Start Testing**

### 1. **Launch Development Server**
```bash
# Start with fresh cache
npx expo start --clear

# Open in browser
# Press 'w' or visit http://localhost:8081
```

### 2. **Test Authentication Flow**

#### **Step 1: Welcome Screen**
- Navigate to: `http://localhost:8081/auth/welcome`
- ✅ Verify: Sage <PERSON> background (#9CAF88)
- ✅ Verify: Glass-morphism effects on cards
- ✅ Verify: Social login buttons render
- ✅ Verify: Navigation to login/signup works

#### **Step 2: Signup Screen**
- Navigate to: `http://localhost:8081/auth/signup`
- ✅ Test: Form validation (empty fields)
- ✅ Test: Email format validation
- ✅ Test: Password strength indicator
- ✅ Test: Password confirmation matching
- ✅ Test: Loading states on form submission

#### **Step 3: Login Screen**
- Navigate to: `http://localhost:8081/auth/login`
- ✅ Test: Email/password validation
- ✅ Test: "Forgot Password" link navigation
- ✅ Test: Error handling for invalid credentials
- ✅ Test: Loading states

#### **Step 4: Forgot Password Screen** (Enhanced)
- Navigate to: `http://localhost:8081/auth/forgot-password`
- ✅ Test: Email validation
- ✅ Test: Success state with instructions
- ✅ Test: Resend functionality with rate limiting
- ✅ Test: Security notice display
- ✅ Test: Timeout notice display

## 🔧 **Local Testing Configuration**

### **Environment Setup**
```env
# .env file
EXPO_PUBLIC_SUPABASE_URL=your_supabase_url
EXPO_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
EXPO_PUBLIC_BYPASS_AUTH=false  # Set to true for UI-only testing
```

### **Testing Modes**

#### **Mode 1: Full Integration Testing**
```typescript
// src/utils/constants.ts
export const FEATURE_FLAGS = {
  ENABLE_AUTHENTICATION: true,  // Full auth flow
  ENABLE_BIOMETRIC_AUTH: false, // Disable for web testing
  ENABLE_SOCIAL_LOGIN: false,   // Disable if not configured
};
```

#### **Mode 2: UI-Only Testing**
```typescript
// For testing UI without backend calls
export const FEATURE_FLAGS = {
  ENABLE_AUTHENTICATION: true,
  MOCK_AUTH_RESPONSES: true,    // Add this flag
};
```

## 📱 **Testing Checklist**

### **Visual Design & Brand Compliance**
- [ ] Sage Green (#9CAF88) background on all auth screens
- [ ] No gradients visible (solid colors only)
- [ ] Glass-morphism effects on cards and containers
- [ ] Proper typography hierarchy and spacing
- [ ] Brand-compliant button colors and styles
- [ ] Consistent rounded corners (12px border radius)
- [ ] Subtle shadows on interactive elements

### **Form Validation**
- [ ] Email format validation with real-time feedback
- [ ] Password strength indicator (weak/medium/strong)
- [ ] Password confirmation matching
- [ ] Required field validation
- [ ] Input sanitization working
- [ ] Error messages display correctly

### **Navigation & Flow**
- [ ] Welcome → Login navigation
- [ ] Welcome → Signup navigation
- [ ] Login → Forgot Password navigation
- [ ] Forgot Password → Login navigation
- [ ] Back button functionality
- [ ] Proper route protection

### **Loading States & Feedback**
- [ ] Button loading states during form submission
- [ ] Disabled states for invalid forms
- [ ] Loading spinners display correctly
- [ ] Success/error message display
- [ ] Rate limiting feedback (forgot password)

### **Error Handling**
- [ ] Network error handling
- [ ] Invalid credentials error
- [ ] User not found error
- [ ] Email already exists error
- [ ] Rate limiting error messages

## 🔍 **Advanced Testing Scenarios**

### **Test Case 1: Complete Signup Flow**
```typescript
// Test data
const testUser = {
  fullName: "Test Couple",
  email: "<EMAIL>",
  password: "SecurePass123!",
  confirmPassword: "SecurePass123!"
};

// Expected behavior:
// 1. Form validates correctly
// 2. Password strength shows "strong"
// 3. Submission shows loading state
// 4. Success redirects to email verification
```

### **Test Case 2: Password Reset Flow**
```typescript
// Test data
const resetEmail = "<EMAIL>";

// Expected behavior:
// 1. Email validation passes
// 2. Success state shows with instructions
// 3. Resend button has 30-second cooldown
// 4. Security notice displays
```

### **Test Case 3: Error Scenarios**
```typescript
// Test various error conditions:
// 1. Invalid email format
// 2. Weak password
// 3. Password mismatch
// 4. Network timeout
// 5. Rate limiting
```

## 🌐 **Browser Testing**

### **Supported Browsers**
- ✅ Chrome/Chromium (recommended)
- ✅ Firefox
- ✅ Safari
- ✅ Edge

### **Mobile Responsive Testing**
```bash
# Test different screen sizes in browser dev tools:
# - iPhone SE (375x667)
# - iPhone 12 Pro (390x844)
# - iPad (768x1024)
# - Desktop (1200x800)
```

## 📊 **Performance Testing**

### **Metrics to Monitor**
- [ ] Initial page load time < 3 seconds
- [ ] Form submission response time < 1 second
- [ ] Smooth animations (60fps)
- [ ] Memory usage stable
- [ ] No console errors

### **Testing Tools**
```bash
# Performance testing
npm run test:performance

# Bundle size analysis
npx expo export --platform web --analyze
```

## 🐛 **Common Issues & Solutions**

### **Issue 1: Glass-morphism Not Rendering**
```typescript
// Check if BlurView is properly imported
import { BlurView } from 'expo-blur';

// Ensure proper styling
const styles = {
  glassContainer: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    backdropFilter: 'blur(10px)', // Web fallback
  }
};
```

### **Issue 2: Form Validation Not Working**
```typescript
// Check validation functions
const validateEmail = (email: string) => {
  return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
};
```

### **Issue 3: Navigation Issues**
```typescript
// Ensure proper router imports
import { router } from 'expo-router';

// Check route definitions in app directory
```

## 🚀 **Production Readiness Checklist**

### **Before Production Deployment**
- [ ] All environment variables configured
- [ ] Supabase RLS policies enabled
- [ ] Email templates configured
- [ ] Social login credentials added
- [ ] Error tracking service integrated
- [ ] Analytics events implemented
- [ ] Performance optimizations applied
- [ ] Security headers configured

### **Post-Deployment Testing**
- [ ] Email delivery working
- [ ] Social login functional
- [ ] Biometric auth working on mobile
- [ ] Password reset emails received
- [ ] Session persistence working
- [ ] Error reporting functional

## 📞 **Support & Debugging**

### **Debug Mode**
```bash
# Enable debug logging
EXPO_PUBLIC_DEBUG_AUTH=true npx expo start
```

### **Common Debug Commands**
```bash
# Clear all caches
rm -rf .expo && rm -rf node_modules/.cache

# Reset Metro bundler
npx expo start --clear --reset-cache

# Check bundle analysis
npx expo export --platform web --analyze
```

### **Useful Browser Dev Tools**
- **Network Tab**: Monitor API calls
- **Console Tab**: Check for JavaScript errors
- **Application Tab**: Inspect local storage/session storage
- **Performance Tab**: Monitor rendering performance

---

## 🎯 **Testing Priority Order**

1. **High Priority**: UI rendering and navigation
2. **Medium Priority**: Form validation and error handling
3. **Low Priority**: Backend integration (can be mocked)
4. **Production Only**: Email delivery and social login
