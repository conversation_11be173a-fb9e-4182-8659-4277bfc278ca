/**
 * Enhanced Authentication Components
 *
 * Production-ready authentication components with brand colors,
 * glass-morphism effects, and comprehensive functionality.
 *
 * @version 3.0.0
 * <AUTHOR> Us Team
 */

import { Apple, Chrome, Eye, EyeOff } from 'lucide-react-native';
import React, { useState } from 'react';
import {
    ActivityIndicator,
    Dimensions,
    KeyboardAvoidingView,
    Platform,
    ScrollView,
    Text,
    TextInput,
    TextStyle,
    TouchableOpacity,
    View,
    ViewStyle
} from 'react-native';
import { GlassView } from '../../shared/components/common/GlassView';
import { useGlobalTheme } from '../../shared/components/common/ThemeProvider';
import { brandColors } from '../../shared/utils/colors';

const { width: screenWidth } = Dimensions.get('window');

// ============================================================================
// TYPES
// ============================================================================

export interface AuthScreenLayoutProps {
  children: React.ReactNode;
  backgroundColor?: string;
  useKeyboardAvoiding?: boolean;
  scrollable?: boolean;
  style?: ViewStyle;
}

export interface AuthHeaderProps {
  title: string;
  subtitle?: string;
  emoji?: string;
  style?: ViewStyle;
}

export interface AuthInputProps {
  value: string;
  onChangeText: (text: string) => void;
  placeholder: string;
  label?: string;
  type?: 'email' | 'password' | 'text';
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  error?: string;
  disabled?: boolean;
  style?: ViewStyle;
  inputStyle?: TextStyle;
}

export interface AuthButtonProps {
  title: string;
  onPress: () => void;
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'social';
  disabled?: boolean;
  loading?: boolean;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  style?: ViewStyle;
  textStyle?: TextStyle;
}

export interface SocialButtonProps {
  provider: 'google' | 'apple';
  onPress: () => void;
  disabled?: boolean;
  loading?: boolean;
  style?: ViewStyle;
}

export interface PasswordStrengthProps {
  password: string;
  style?: ViewStyle;
}

// ============================================================================
// AUTH LAYOUT COMPONENTS
// ============================================================================

export const AuthScreenLayout: React.FC<AuthScreenLayoutProps> = ({
  children,
  backgroundColor = brandColors.sageGreen,
  useKeyboardAvoiding = true,
  scrollable = true,
  style
}) => {
  const content = scrollable ? (
    <ScrollView
      contentContainerStyle={styles.scrollContent}
      showsVerticalScrollIndicator={false}
      keyboardShouldPersistTaps="handled"
    >
      {children}
    </ScrollView>
  ) : (
    <View style={styles.content}>
      {children}
    </View>
  );

  const wrappedContent = useKeyboardAvoiding ? (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      style={styles.keyboardAvoidingView}
    >
      {content}
    </KeyboardAvoidingView>
  ) : content;

  return (
    <View style={[styles.container, { backgroundColor }, style]}>
      {wrappedContent}
    </View>
  );
};

// ============================================================================
// AUTH HEADER COMPONENT
// ============================================================================

export const AuthHeader: React.FC<AuthHeaderProps> = ({
  title,
  subtitle,
  emoji = '💕',
  style
}) => {
  return (
    <View style={[styles.header, style]}>
      <Text style={styles.emoji}>{emoji}</Text>
      <Text style={styles.title}>{title}</Text>
      {subtitle && <Text style={styles.subtitle}>{subtitle}</Text>}
    </View>
  );
};

// ============================================================================
// AUTH INPUT COMPONENT
// ============================================================================

export const AuthInput: React.FC<AuthInputProps> = ({
  value,
  onChangeText,
  placeholder,
  label,
  type = 'text',
  leftIcon,
  rightIcon,
  error,
  disabled = false,
  style,
  inputStyle
}) => {
  const [isPasswordVisible, setIsPasswordVisible] = useState(false);
  const { currentTheme } = useGlobalTheme();

  const isPassword = type === 'password';
  const secureTextEntry = isPassword && !isPasswordVisible;

  const togglePasswordVisibility = () => {
    setIsPasswordVisible(!isPasswordVisible);
  };

  const passwordToggleIcon = isPasswordVisible ? (
    <EyeOff size={20} color="rgba(255, 255, 255, 0.7)" />
  ) : (
    <Eye size={20} color="rgba(255, 255, 255, 0.7)" />
  );

  return (
    <View style={[styles.inputContainer, style]}>
      {label && <Text style={styles.inputLabel}>{label}</Text>}
      <GlassView style={[styles.inputWrapper, error && styles.inputError]}>
        {leftIcon && <View style={styles.inputIcon}>{leftIcon}</View>}
        <TextInput
          style={[styles.input, inputStyle]}
          value={value}
          onChangeText={onChangeText}
          placeholder={placeholder}
          placeholderTextColor="rgba(255, 255, 255, 0.6)"
          secureTextEntry={secureTextEntry}
          keyboardType={type === 'email' ? 'email-address' : 'default'}
          autoCapitalize={type === 'email' ? 'none' : 'sentences'}
          autoCorrect={false}
          editable={!disabled}
        />
        {isPassword && (
          <TouchableOpacity
            style={styles.inputIcon}
            onPress={togglePasswordVisibility}
          >
            {passwordToggleIcon}
          </TouchableOpacity>
        )}
        {rightIcon && !isPassword && (
          <View style={styles.inputIcon}>{rightIcon}</View>
        )}
      </GlassView>
      {error && <Text style={styles.inputErrorText}>{error}</Text>}
    </View>
  );
};

// ============================================================================
// AUTH BUTTON COMPONENT
// ============================================================================

export const AuthButton: React.FC<AuthButtonProps> = ({
  title,
  onPress,
  variant = 'primary',
  disabled = false,
  loading = false,
  leftIcon,
  rightIcon,
  style,
  textStyle
}) => {
  const getButtonStyle = () => {
    switch (variant) {
      case 'secondary':
        return styles.secondaryButton;
      case 'outline':
        return styles.outlineButton;
      case 'ghost':
        return styles.ghostButton;
      case 'social':
        return styles.socialButton;
      default:
        return styles.primaryButton;
    }
  };

  const getTextStyle = () => {
    switch (variant) {
      case 'secondary':
        return styles.secondaryButtonText;
      case 'outline':
        return styles.outlineButtonText;
      case 'ghost':
        return styles.ghostButtonText;
      case 'social':
        return styles.socialButtonText;
      default:
        return styles.primaryButtonText;
    }
  };

  return (
    <TouchableOpacity
      style={[
        styles.button,
        getButtonStyle(),
        disabled && styles.disabledButton,
        style
      ]}
      onPress={onPress}
      disabled={disabled || loading}
      activeOpacity={0.8}
    >
      {loading ? (
        <ActivityIndicator color={variant === 'primary' ? '#FFFFFF' : brandColors.sageGreen} />
      ) : (
        <>
          {leftIcon && <View style={styles.buttonIcon}>{leftIcon}</View>}
          <Text style={[getTextStyle(), textStyle]}>{title}</Text>
          {rightIcon && <View style={styles.buttonIcon}>{rightIcon}</View>}
        </>
      )}
    </TouchableOpacity>
  );
};

// ============================================================================
// SOCIAL BUTTON COMPONENT
// ============================================================================

export const SocialButton: React.FC<SocialButtonProps> = ({
  provider,
  onPress,
  disabled = false,
  loading = false,
  style
}) => {
  const getProviderConfig = () => {
    switch (provider) {
      case 'apple':
        return {
          title: 'Continue with Apple',
          icon: <Apple size={20} color={brandColors.charcoal} />,
          backgroundColor: '#FFFFFF',
        };
      case 'google':
        return {
          title: 'Continue with Google',
          icon: <Chrome size={20} color={brandColors.charcoal} />,
          backgroundColor: '#FFFFFF',
        };
      default:
        return {
          title: 'Continue',
          icon: null,
          backgroundColor: '#FFFFFF',
        };
    }
  };

  const config = getProviderConfig();

  return (
    <TouchableOpacity
      style={[
        styles.socialButton,
        { backgroundColor: config.backgroundColor },
        disabled && styles.disabledButton,
        style
      ]}
      onPress={onPress}
      disabled={disabled || loading}
      activeOpacity={0.8}
    >
      {loading ? (
        <ActivityIndicator color={brandColors.charcoal} />
      ) : (
        <>
          {config.icon && <View style={styles.buttonIcon}>{config.icon}</View>}
          <Text style={styles.socialButtonText}>{config.title}</Text>
        </>
      )}
    </TouchableOpacity>
  );
};

// ============================================================================
// PASSWORD STRENGTH COMPONENT
// ============================================================================

export const PasswordStrength: React.FC<PasswordStrengthProps> = ({
  password,
  style
}) => {
  const getPasswordStrength = (pwd: string) => {
    let score = 0;
    const checks = {
      length: pwd.length >= 8,
      lowercase: /[a-z]/.test(pwd),
      uppercase: /[A-Z]/.test(pwd),
      number: /\d/.test(pwd),
      special: /[!@#$%^&*(),.?":{}|<>]/.test(pwd),
    };

    Object.values(checks).forEach(check => {
      if (check) score++;
    });

    if (score < 2) return { strength: 'weak', color: '#EF4444', width: '25%' };
    if (score < 4) return { strength: 'medium', color: '#F59E0B', width: '50%' };
    if (score < 5) return { strength: 'good', color: '#10B981', width: '75%' };
    return { strength: 'strong', color: '#059669', width: '100%' };
  };

  if (!password) return null;

  const { strength, color, width } = getPasswordStrength(password);

  return (
    <View style={[styles.passwordStrengthContainer, style]}>
      <View style={styles.passwordStrengthBar}>
        <View
          style={[
            styles.passwordStrengthFill,
            { backgroundColor: color, width }
          ]}
        />
      </View>
      <Text style={[styles.passwordStrengthText, { color }]}>
        Password strength: {strength}
      </Text>
    </View>
  );
};

// ============================================================================
// DIVIDER COMPONENT
// ============================================================================

export const AuthDivider: React.FC<{ text?: string; style?: ViewStyle }> = ({
  text = 'or',
  style
}) => {
  return (
    <View style={[styles.divider, style]}>
      <View style={styles.dividerLine} />
      <Text style={styles.dividerText}>{text}</Text>
      <View style={styles.dividerLine} />
    </View>
  );
};

// ============================================================================
// LINK COMPONENT
// ============================================================================

export const AuthLink: React.FC<{
  text: string;
  linkText: string;
  onPress: () => void;
  style?: ViewStyle;
}> = ({ text, linkText, onPress, style }) => {
  return (
    <View style={[styles.linkContainer, style]}>
      <Text style={styles.linkText}>{text} </Text>
      <TouchableOpacity onPress={onPress}>
        <Text style={styles.link}>{linkText}</Text>
      </TouchableOpacity>
    </View>
  );
};

// ============================================================================
// STYLES
// ============================================================================

const styles = StyleSheet.create({
  // Layout styles
  container: {
    flex: 1,
  },
  keyboardAvoidingView: {
    flex: 1,
  },
  content: {
    flex: 1,
    padding: 24,
  },
  scrollContent: {
    flexGrow: 1,
    padding: 24,
    justifyContent: 'center',
  },

  // Header styles
  header: {
    alignItems: 'center',
    marginBottom: 32,
  },
  emoji: {
    fontSize: 60,
    marginBottom: 16,
  },
  title: {
    fontSize: 28,
    fontWeight: '700',
    color: '#FFFFFF',
    textAlign: 'center',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.8)',
    textAlign: 'center',
    lineHeight: 22,
  },

  // Input styles
  inputContainer: {
    marginBottom: 20,
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: '#FFFFFF',
    marginBottom: 8,
  },
  inputWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: 12,
    paddingHorizontal: 16,
    minHeight: 52,
  },
  inputError: {
    borderWidth: 1,
    borderColor: '#EF4444',
  },
  input: {
    flex: 1,
    fontSize: 16,
    color: '#FFFFFF',
    paddingVertical: 16,
  },
  inputIcon: {
    marginHorizontal: 4,
  },
  inputErrorText: {
    fontSize: 12,
    color: '#EF4444',
    marginTop: 4,
    marginLeft: 4,
  },

  // Button styles
  button: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 12,
    paddingVertical: 16,
    paddingHorizontal: 24,
    marginBottom: 12,
    minHeight: 52,
  },
  primaryButton: {
    backgroundColor: '#FFFFFF',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  secondaryButton: {
    backgroundColor: brandColors.lavender,
  },
  outlineButton: {
    backgroundColor: 'transparent',
    borderWidth: 2,
    borderColor: '#FFFFFF',
  },
  ghostButton: {
    backgroundColor: 'transparent',
  },
  socialButton: {
    backgroundColor: '#FFFFFF',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  disabledButton: {
    opacity: 0.6,
  },
  buttonIcon: {
    marginHorizontal: 8,
  },

  // Button text styles
  primaryButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: brandColors.charcoal,
  },
  secondaryButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  outlineButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  ghostButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: 'rgba(255, 255, 255, 0.8)',
  },
  socialButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: brandColors.charcoal,
  },

  // Password strength styles
  passwordStrengthContainer: {
    marginTop: 8,
  },
  passwordStrengthBar: {
    height: 4,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 2,
    marginBottom: 4,
  },
  passwordStrengthFill: {
    height: '100%',
    borderRadius: 2,
  },
  passwordStrengthText: {
    fontSize: 12,
    fontWeight: '500',
  },

  // Divider styles
  divider: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 24,
  },
  dividerLine: {
    flex: 1,
    height: 1,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
  },
  dividerText: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.7)',
    marginHorizontal: 16,
  },

  // Link styles
  linkContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 16,
  },
  linkText: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.8)',
  },
  link: {
    fontSize: 14,
    fontWeight: '600',
    color: '#FFFFFF',
    textDecorationLine: 'underline',
  },
});
