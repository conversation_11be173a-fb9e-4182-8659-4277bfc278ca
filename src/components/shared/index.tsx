/**
 * Shared Components Index
 *
 * Re-exports from the new shared component system for backward compatibility
 */

// Essential component exports
export { Button, PrimaryButton, SecondaryButton } from '../../shared/components/common/Button';
export { Card } from '../../shared/components/common/Card';
export { GlassView } from '../../shared/components/common/GlassView';
export { Input } from '../../shared/components/common/Input';
export { HeaderLayout, ScreenLayout } from '../../shared/components/common/LayoutComponents';

// Couple components
export { AchievementsSection, FavoritesSection, ProfileHeader, StatsOverview } from '../../shared/components/common/CoupleComponents';
export { DailyQuestionCard } from '../../shared/components/common/DailyQuestionCard';
export { DailyQuestionsStreakCard } from '../../shared/components/common/DailyQuestionsStreakCard';
export { StreakDisplay } from '../../shared/components/common/StreakDisplay';

// Legacy aliases
export { But<PERSON> as DSButton } from '../../shared/components/common/Button';
export { Card as DSCard } from '../../shared/components/common/Card';
export { Input as DSInput } from '../../shared/components/common/Input';

// DSHeaderBar - simple header bar component
import React from 'react';
import { Image, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import { colors } from '../../shared/utils/colors';
import { tokens } from '../../shared/utils/theme/tokens';

export interface DSHeaderBarProps {
  title?: string;
  left?: React.ReactNode;
  right?: React.ReactNode;
  tone?: string;
  backgroundColor?: string;
}

export const DSHeaderBar: React.FC<DSHeaderBarProps> = ({
  title,
  left,
  right,
  backgroundColor = colors.primary
}) => {
  return (
    <View style={[headerStyles.headerBar, { backgroundColor }]}>
      <View style={headerStyles.headerSide}>{left}</View>
      <Text style={headerStyles.headerTitle}>{title}</Text>
      <View style={headerStyles.headerSide}>{right}</View>
    </View>
  );
};

// eslint-disable-next-line no-restricted-syntax
const headerStyles = StyleSheet.create({
  headerBar: {
    height: 56,
    alignItems: 'center',
    flexDirection: 'row',
    paddingHorizontal: 16,
  },
  headerSide: {
    width: 56,
    alignItems: 'center',
    justifyContent: 'center'
  },
  headerTitle: {
    flex: 1,
    textAlign: 'center',
    color: colors.white,
    fontSize: 18,
    fontWeight: '700'
  },
});

// Header and navigation components
  export { default as HamburgerMenu } from '../../shared/components/layout/HamburgerMenu';

// Design System Components - Implemented
interface DSAvatarProps {
  source?: { uri: string } | number;
  size?: number;
  style?: object;
  [key: string]: unknown;
}

export const DSAvatar = ({ source, size = tokens.sizes.avatar.md, style, ...props }: DSAvatarProps) => {
  return (
    <View style={[{
      width: size,
      height: size,
      borderRadius: size / 2,
      backgroundColor: colors.backgroundSecondary,
      alignItems: 'center',
      justifyContent: 'center',
      overflow: 'hidden'
    }, style]} {...props}>
      {source ? (
        <Image
          source={source}
          style={{ width: size, height: size, borderRadius: size / 2 }}
          resizeMode="cover"
        />
      ) : (
        <Text style={{
          color: colors.textSecondary,
          fontSize: size * 0.4,
          fontWeight: tokens.typography.fontWeight.heading
        }}>
          👤
        </Text>
      )}
    </View>
  );
};

interface DSBadgeProps {
  children: React.ReactNode;
  variant?: 'default' | 'primary' | 'success' | 'warning' | 'error';
  style?: object;
  [key: string]: unknown;
}

export const DSBadge = ({ children, variant = 'default', style, ...props }: DSBadgeProps) => {
  const badgeColors: Record<string, { bg: string; text: string }> = {
    default: { bg: colors.backgroundSecondary, text: colors.textSecondary },
    primary: { bg: colors.primary, text: colors.white },
    success: { bg: colors.success, text: colors.white },
    warning: { bg: colors.warning, text: colors.white },
    error: { bg: colors.error, text: colors.white }
  };

  const colorScheme = badgeColors[variant] || badgeColors.default;

  return (
    <View style={[{
      backgroundColor: colorScheme.bg,
      paddingHorizontal: tokens.spacing.xs,
      paddingVertical: tokens.spacing.xxs,
      borderRadius: tokens.radii.input,
      alignSelf: 'flex-start'
    }, style]} {...props}>
      <Text style={{
        color: colorScheme.text,
        fontSize: tokens.typography.fontSize.sm,
        fontWeight: tokens.typography.fontWeight.heading
      }}>
        {children}
      </Text>
    </View>
  );
};

interface DSStatTileProps {
  title: string;
  value: string | number;
  style?: object;
  [key: string]: unknown;
}

export const DSStatTile = ({ title, value, style, ...props }: DSStatTileProps) => {
  return (
    <View style={[{
      backgroundColor: colors.white,
      padding: tokens.spacing.md,
      borderRadius: tokens.radii.input,
      alignItems: 'center',
      ...tokens.shadows.sm,
    }, style]} {...props}>
      <Text style={{
        fontSize: tokens.typography.fontSize['2xl'],
        fontWeight: tokens.typography.fontWeight.bold,
        color: colors.primary,
        marginBottom: tokens.spacing.xxs
      }}>
        {value}
      </Text>
      <Text style={{
        fontSize: tokens.typography.fontSize.sm,
        color: colors.textSecondary,
        textAlign: 'center'
      }}>
        {title}
      </Text>
    </View>
  );
};

// Story components - Implemented
interface StoryHeaderProps {
  title: string;
  subtitle?: string;
  style?: object;
  [key: string]: unknown;
}

export const StoryHeader = ({ title, subtitle, style, ...props }: StoryHeaderProps) => {
  return (
    <View style={[{
      padding: 20,
      alignItems: 'center',
      backgroundColor: colors.backgroundSecondary
    }, style]} {...props}>
      <Text style={{
        fontSize: 24,
        fontWeight: 'bold',
        color: colors.primary,
        textAlign: 'center',
        marginBottom: 8
      }}>
        {title}
      </Text>
      {subtitle && (
        <Text style={{
          fontSize: 16,
          color: colors.textSecondary,
          textAlign: 'center'
        }}>
          {subtitle}
        </Text>
      )}
    </View>
  );
};

interface StoryIntroCardProps {
  content: string;
  style?: object;
  [key: string]: unknown;
}

export const StoryIntroCard = ({ content, style, ...props }: StoryIntroCardProps) => {
  return (
    <View style={[{
      backgroundColor: colors.white,
      padding: tokens.spacing.md,
      margin: tokens.spacing.md,
      borderRadius: tokens.radii.input,
      ...tokens.shadows.sm,
    }, style]} {...props}>
      <Text style={{
        fontSize: tokens.typography.fontSize.md,
        color: colors.text,
        lineHeight: tokens.typography.lineHeight.relaxed
      }}>
        {content}
      </Text>
    </View>
  );
};

interface StoryTimelineItemProps {
  date: string;
  title: string;
  description: string;
  style?: object;
  [key: string]: unknown;
}

export const StoryTimelineItem = ({ date, title, description, style, ...props }: StoryTimelineItemProps) => {
  return (
    <View style={[{
      flexDirection: 'row',
      padding: 16,
      backgroundColor: colors.white,
      marginVertical: 4,
      borderRadius: 8
    }, style]} {...props}>
      <View style={{
        width: 8,
        height: 8,
        borderRadius: 4,
        backgroundColor: colors.primary,
        marginTop: 6,
        marginRight: 12
      }} />
      <View style={{ flex: 1 }}>
        <Text style={{
          fontSize: 12,
          color: colors.textSecondary,
          marginBottom: 4
        }}>
          {date}
        </Text>
        <Text style={{
          fontSize: 16,
          fontWeight: '600',
          color: colors.text,
          marginBottom: 4
        }}>
          {title}
        </Text>
        {description && (
          <Text style={{
            fontSize: 14,
            color: colors.textSecondary,
            lineHeight: 20
          }}>
            {description}
          </Text>
        )}
      </View>
    </View>
  );
};

interface StoryFooterProps {
  onSave: () => void;
  onCancel: () => void;
  style?: object;
  [key: string]: unknown;
}

export const StoryFooter = ({ onSave, onCancel, style, ...props }: StoryFooterProps) => {
  return (
    <View style={[{
      flexDirection: 'row',
      padding: 16,
      backgroundColor: colors.backgroundSecondary,
      borderTopWidth: 1,
      borderTopColor: colors.border,
      gap: 12
    }, style]} {...props}>
      {onCancel && (
        <TouchableOpacity
          onPress={onCancel}
          style={{
            flex: 1,
            padding: 12,
            borderRadius: 8,
            borderWidth: 1,
            borderColor: colors.border,
            alignItems: 'center'
          }}
        >
          <Text style={{ color: colors.textSecondary, fontWeight: '600' }}>
            Cancel
          </Text>
        </TouchableOpacity>
      )}
      {onSave && (
        <TouchableOpacity
          onPress={onSave}
          style={{
            flex: 1,
            padding: tokens.spacing.sm,
            borderRadius: tokens.radii.xs,
            backgroundColor: colors.primary,
            alignItems: 'center'
          }}
        >
          <Text style={{ color: colors.white, fontWeight: tokens.typography.fontWeight.heading }}>
            Save
          </Text>
        </TouchableOpacity>
      )}
    </View>
  );
};

interface StoryFooterCardProps {
  children: React.ReactNode;
  style?: object;
  [key: string]: unknown;
}

export const StoryFooterCard = ({ children, style, ...props }: StoryFooterCardProps) => {
  return (
    <View style={[{
      backgroundColor: colors.white,
      padding: tokens.spacing.md,
      margin: tokens.spacing.md,
      borderRadius: tokens.radii.input,
      ...tokens.shadows.sm,
    }, style]} {...props}>
      {children}
    </View>
  );
};

// Header component - Implemented
interface HeaderProps {
  title: string;
  subtitle?: string;
  style?: object;
  [key: string]: unknown;
}

export const Header = ({ title, subtitle, style, ...props }: HeaderProps) => {
  return (
    <View style={[{
      padding: 16,
      backgroundColor: colors.primary,
      alignItems: 'center'
    }, style]} {...props}>
      <Text style={{
        fontSize: 20,
        fontWeight: 'bold',
        color: colors.white,
        textAlign: 'center'
      }}>
        {title}
      </Text>
      {subtitle && (
        <Text style={{
          fontSize: 14,
          color: colors.white,
          textAlign: 'center',
          marginTop: 4,
          opacity: 0.9
        }}>
          {subtitle}
        </Text>
      )}
    </View>
  );
};

// RelationshipActivity type and component
export interface RelationshipActivityType {
  id: string;
  title: string;
  description: string;
  type: string;
  isCompleted?: boolean;
  points?: number;
  prompts?: unknown[];
  responses?: unknown[];
}

// RelationshipActivity component - Implemented
interface RelationshipActivityProps {
  title: string;
  description: string;
  onComplete: () => void;
  style?: object;
  [key: string]: unknown;
}

export const RelationshipActivity = ({ title, description, onComplete, style, ...props }: RelationshipActivityProps) => {
  return (
    <View style={[{
      backgroundColor: colors.white,
      padding: 16,
      margin: 8,
      borderRadius: 12,
      shadowColor: colors.textGray,
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 4,
      elevation: 2
    }, style]} {...props}>
      <Text style={{
        fontSize: 18,
        fontWeight: '600',
        color: colors.text,
        marginBottom: 8
      }}>
        {title}
      </Text>
      {description && (
        <Text style={{
          fontSize: 14,
          color: colors.textSecondary,
          lineHeight: 20,
          marginBottom: 16
        }}>
          {description}
        </Text>
      )}
      {onComplete && (
        <TouchableOpacity
          onPress={onComplete}
          style={{
            backgroundColor: colors.primary,
            padding: 12,
            borderRadius: 8,
            alignItems: 'center'
          }}
        >
          <Text style={{
            color: colors.white,
            fontWeight: '600'
          }}>
            Start Activity
          </Text>
        </TouchableOpacity>
      )}
    </View>
  );
};

// Theme provider
export { GlobalThemeProvider } from '../../shared/components/common/ThemeProvider';

// Glass components - use real implementations
export { GlassView as DSGlassCard } from '../../shared/components/common/GlassView';

// Feature flags
export const FEATURE_FLAGS = {
  ENABLE_DAILY_QUESTIONS: true,
  ENABLE_MATCH_GAME: true,
  ENABLE_DATE_NIGHT_IDEAS: true,
  ENABLE_ACHIEVEMENTS: true,
  ENABLE_PARTNER_PAIRING: true,
  ENABLE_PUSH_NOTIFICATIONS: true,
  ENABLE_ANALYTICS: true,
  ENABLE_DARK_MODE: true,
  ENABLE_DEBUG_LOGGING: true,
  ENABLE_AUTHENTICATION: true,
};
