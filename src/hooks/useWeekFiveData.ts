/**
 * Week Five Data Hook
 * Manages data for the fifth week of the couple's journey - focusing on deeper connection
 */

import { useCallback, useEffect, useState } from 'react';

export interface DreamVacationPlan {
  when: string;
  where: string;
  theme: string;
  worldLocation: string;
  vibe: string;
  companions: string;
  accommodation: string;
  dailyActivities: string;
  weather: string;
  activities: string;
  food: string;
  splurgeLevel: string;
  completed: boolean;
  timestamp: number;
}

export interface WeekFiveActivity {
  id: string;
  title: string;
  description: string;
  type: 'deep_question' | 'intimacy_challenge' | 'future_planning' | 'celebration';
  day: number; // 1-7
  isCompleted: boolean;
  completedAt?: Date;
  userResponse?: string;
  partnerResponse?: string;
  points: number;
  difficulty: 'medium' | 'hard' | 'expert';
  category: 'emotional' | 'physical' | 'spiritual' | 'practical';
}

export interface WeekFiveProgress {
  totalActivities: number;
  completedActivities: number;
  currentDay: number;
  totalPoints: number;
  earnedPoints: number;
  completionPercentage: number;
  intimacyScore: number;
  connectionLevel: 'building' | 'strong' | 'deep' | 'profound';
  isWeekComplete: boolean;
}

export interface WeekFiveState {
  activities: WeekFiveActivity[];
  progress: WeekFiveProgress;
  currentActivity: WeekFiveActivity | null;
  isLoading: boolean;
  error: string | null;
  // Additional properties for backward compatibility
  completedSections?: string[];
  dreamVacationPlan?: DreamVacationPlan;
  dreamVacationAdjectives?: {
    playerOneAdjectives?: string[];
    playerTwoAdjectives?: string[];
  };
  chatPrompts?: unknown[];
  conflictStyleReflection?: unknown;
}

const sampleActivities: WeekFiveActivity[] = [
  {
    id: 'w5_d1_dq1',
    title: 'Vulnerability and Trust',
    description: 'Share a fear you have about your relationship and how your partner can help you overcome it.',
    type: 'deep_question',
    day: 1,
    isCompleted: false,
    points: 25,
    difficulty: 'hard',
    category: 'emotional'
  },
  {
    id: 'w5_d1_ic1',
    title: 'Physical Connection',
    description: 'Spend 10 minutes in comfortable silence while maintaining eye contact and holding hands.',
    type: 'intimacy_challenge',
    day: 1,
    isCompleted: false,
    points: 30,
    difficulty: 'medium',
    category: 'physical'
  },
  {
    id: 'w5_d2_fp1',
    title: 'Life Goals Alignment',
    description: 'Discuss your individual 5-year goals and find ways to support each other in achieving them.',
    type: 'future_planning',
    day: 2,
    isCompleted: false,
    points: 35,
    difficulty: 'hard',
    category: 'practical'
  },
  {
    id: 'w5_d2_dq1',
    title: 'Love Languages Deep Dive',
    description: 'Explore how your love languages have evolved since being together and what new ways you want to express love.',
    type: 'deep_question',
    day: 2,
    isCompleted: false,
    points: 25,
    difficulty: 'medium',
    category: 'emotional'
  },
  {
    id: 'w5_d3_ic1',
    title: 'Emotional Intimacy',
    description: 'Share a childhood memory that shaped who you are today and how it affects your relationship.',
    type: 'intimacy_challenge',
    day: 3,
    isCompleted: false,
    points: 30,
    difficulty: 'hard',
    category: 'emotional'
  },
  {
    id: 'w5_d4_dq1',
    title: 'Spiritual Connection',
    description: 'Discuss what gives your life meaning and how you can create shared meaning together.',
    type: 'deep_question',
    day: 4,
    isCompleted: false,
    points: 35,
    difficulty: 'expert',
    category: 'spiritual'
  },
  {
    id: 'w5_d5_fp1',
    title: 'Relationship Vision Board',
    description: 'Create a visual representation of your relationship goals and dreams for the next year.',
    type: 'future_planning',
    day: 5,
    isCompleted: false,
    points: 40,
    difficulty: 'medium',
    category: 'practical'
  },
  {
    id: 'w5_d6_ic1',
    title: 'Appreciation Ritual',
    description: 'Create a weekly ritual where you express specific appreciation for each other.',
    type: 'intimacy_challenge',
    day: 6,
    isCompleted: false,
    points: 30,
    difficulty: 'medium',
    category: 'emotional'
  },
  {
    id: 'w5_d7_c1',
    title: 'Week Five Celebration',
    description: 'Plan and execute a meaningful celebration of your deepened connection.',
    type: 'celebration',
    day: 7,
    isCompleted: false,
    points: 50,
    difficulty: 'expert',
    category: 'emotional'
  }
];

export function useWeekFiveData() {
  const [state, setState] = useState<WeekFiveState>({
    activities: [],
    progress: {
      totalActivities: 0,
      completedActivities: 0,
      currentDay: 1,
      totalPoints: 0,
      earnedPoints: 0,
      completionPercentage: 0,
      intimacyScore: 0,
      connectionLevel: 'building',
      isWeekComplete: false
    },
    currentActivity: null,
    isLoading: true,
    error: null
  });

  const loadWeekFiveData = useCallback(async () => {
    try {
      setState(prev => ({ ...prev, isLoading: true, error: null }));

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 400));

      // Load saved progress from storage
      const savedProgress = localStorage.getItem('week_five_progress');
      let activities = [...sampleActivities];

      if (savedProgress) {
        const progress = JSON.parse(savedProgress);
        activities = activities.map(activity => ({
          ...activity,
          isCompleted: progress.completedActivities?.includes(activity.id) || false,
          completedAt: progress.completedActivities?.includes(activity.id)
            ? new Date(progress.completedAt || Date.now())
            : undefined,
          userResponse: progress.responses?.[activity.id]?.user,
          partnerResponse: progress.responses?.[activity.id]?.partner
        }));
      }

      const progress = calculateProgress(activities);
      const currentActivity = getCurrentActivity(activities);

      setState(prev => ({
        ...prev,
        activities,
        progress,
        currentActivity,
        isLoading: false
      }));
    } catch (error) {
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Failed to load week five data',
        isLoading: false
      }));
    }
  }, []);

  // Load week five data on mount
  useEffect(() => {
    loadWeekFiveData();
  }, [loadWeekFiveData]);

  const calculateProgress = (activities: WeekFiveActivity[]): WeekFiveProgress => {
    const totalActivities = activities.length;
    const completedActivities = activities.filter(a => a.isCompleted).length;
    const totalPoints = activities.reduce((sum, a) => sum + a.points, 0);
    const earnedPoints = activities.filter(a => a.isCompleted).reduce((sum, a) => sum + a.points, 0);
    const completionPercentage = totalActivities > 0 ? Math.round((completedActivities / totalActivities) * 100) : 0;

    // Calculate current day based on completed activities
    const currentDay = Math.min(7, Math.floor(completedActivities / (totalActivities / 7)) + 1);

    // Calculate intimacy score based on completed activities and their categories
    const emotionalActivities = activities.filter(a => a.category === 'emotional' && a.isCompleted).length;
    const physicalActivities = activities.filter(a => a.category === 'physical' && a.isCompleted).length;
    const spiritualActivities = activities.filter(a => a.category === 'spiritual' && a.isCompleted).length;
    const practicalActivities = activities.filter(a => a.category === 'practical' && a.isCompleted).length;

    const intimacyScore = Math.round(
      (emotionalActivities * 25 + physicalActivities * 20 + spiritualActivities * 30 + practicalActivities * 15) /
      (totalActivities * 0.25)
    );

    // Determine connection level
    let connectionLevel: WeekFiveProgress['connectionLevel'] = 'building';
    if (intimacyScore >= 80) connectionLevel = 'profound';
    else if (intimacyScore >= 60) connectionLevel = 'deep';
    else if (intimacyScore >= 40) connectionLevel = 'strong';

    const isWeekComplete = completedActivities === totalActivities;

    return {
      totalActivities,
      completedActivities,
      currentDay,
      totalPoints,
      earnedPoints,
      completionPercentage,
      intimacyScore,
      connectionLevel,
      isWeekComplete
    };
  };

  const getCurrentActivity = (activities: WeekFiveActivity[]): WeekFiveActivity | null => {
    // Find the first incomplete activity
    return activities.find(activity => !activity.isCompleted) || null;
  };

  const completeActivity = async (activityId: string, userResponse?: string, partnerResponse?: string): Promise<boolean> => {
    try {
      setState(prev => ({ ...prev, isLoading: true, error: null }));

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 300));

      const updatedActivities = state.activities.map(activity =>
        activity.id === activityId
          ? {
              ...activity,
              isCompleted: true,
              completedAt: new Date(),
              userResponse,
              partnerResponse
            }
          : activity
      );

      const progress = calculateProgress(updatedActivities);
      const currentActivity = getCurrentActivity(updatedActivities);

      // Save progress to storage
      const progressData = {
        completedActivities: updatedActivities.filter(a => a.isCompleted).map(a => a.id),
        completedAt: new Date().toISOString(),
        responses: updatedActivities.reduce((acc, activity) => {
          if (activity.userResponse || activity.partnerResponse) {
            acc[activity.id] = {
              user: activity.userResponse,
              partner: activity.partnerResponse
            };
          }
          return acc;
        }, {} as Record<string, { user?: string; partner?: string }>)
      };
      localStorage.setItem('week_five_progress', JSON.stringify(progressData));

      setState(prev => ({
        ...prev,
        activities: updatedActivities,
        progress,
        currentActivity,
        isLoading: false
      }));

      return true;
    } catch (error) {
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Failed to complete activity',
        isLoading: false
      }));
      return false;
    }
  };

  const getActivitiesByDay = (day: number): WeekFiveActivity[] => {
    return state.activities.filter(activity => activity.day === day);
  };

  const getActivitiesByCategory = (category: WeekFiveActivity['category']): WeekFiveActivity[] => {
    return state.activities.filter(activity => activity.category === category);
  };

  const getActivitiesByType = (type: WeekFiveActivity['type']): WeekFiveActivity[] => {
    return state.activities.filter(activity => activity.type === type);
  };

  const getIntimacyInsights = () => {
    const { activities, progress } = state;

    const categoryProgress = {
      emotional: activities.filter(a => a.category === 'emotional' && a.isCompleted).length / activities.filter(a => a.category === 'emotional').length,
      physical: activities.filter(a => a.category === 'physical' && a.isCompleted).length / activities.filter(a => a.category === 'physical').length,
      spiritual: activities.filter(a => a.category === 'spiritual' && a.isCompleted).length / activities.filter(a => a.category === 'spiritual').length,
      practical: activities.filter(a => a.category === 'practical' && a.isCompleted).length / activities.filter(a => a.category === 'practical').length
    };

    const strongestArea = Object.entries(categoryProgress).reduce((a, b) =>
      categoryProgress[a[0] as keyof typeof categoryProgress] > categoryProgress[b[0] as keyof typeof categoryProgress] ? a : b
    )[0];

    const growthArea = Object.entries(categoryProgress).reduce((a, b) =>
      categoryProgress[a[0] as keyof typeof categoryProgress] < categoryProgress[b[0] as keyof typeof categoryProgress] ? a : b
    )[0];

    return {
      intimacyScore: progress.intimacyScore,
      connectionLevel: progress.connectionLevel,
      categoryProgress,
      strongestArea,
      growthArea,
      recommendations: getRecommendations(categoryProgress, progress.connectionLevel)
    };
  };

  const getRecommendations = (categoryProgress: Record<string, number>, connectionLevel: string): string[] => {
    const recommendations: string[] = [];

    if (categoryProgress.emotional < 0.5) {
      recommendations.push('Focus on emotional intimacy activities to deepen your connection');
    }
    if (categoryProgress.physical < 0.5) {
      recommendations.push('Incorporate more physical touch and presence exercises');
    }
    if (categoryProgress.spiritual < 0.5) {
      recommendations.push('Explore shared values and meaning-making activities');
    }
    if (categoryProgress.practical < 0.5) {
      recommendations.push('Work on practical future planning and goal alignment');
    }

    if (connectionLevel === 'building') {
      recommendations.push('Take your time with each activity and focus on quality over quantity');
    } else if (connectionLevel === 'profound') {
      recommendations.push('Consider creating your own custom intimacy challenges');
    }

    return recommendations;
  };

  const clearError = () => {
    setState(prev => ({ ...prev, error: null }));
  };

  // Additional methods for backward compatibility
  const updateDreamVacationAdjectives = (adjectives: { playerOneAdjectives?: string[]; playerTwoAdjectives?: string[] }) => {
    setState(prev => ({ ...prev, dreamVacationAdjectives: adjectives }));
  };

  const updateDreamVacationPlan = (plan: DreamVacationPlan) => {
    setState(prev => ({ ...prev, dreamVacationPlan: plan }));
  };

  const updateChatPrompt = (prompt: unknown) => {
    setState(prev => ({
      ...prev,
      chatPrompts: prev.chatPrompts ? [...prev.chatPrompts, prompt] : [prompt]
    }));
  };

  const updateConflictStyleReflection = (reflection: unknown) => {
    setState(prev => ({ ...prev, conflictStyleReflection: reflection }));
  };

  const updateCompletedSections = (sections: string[]) => {
    setState(prev => ({ ...prev, completedSections: sections }));
  };

  return {
    ...state,
    data: state, // Alias for backward compatibility - return full state
    completeActivity,
    getActivitiesByDay,
    getActivitiesByCategory,
    getActivitiesByType,
    getIntimacyInsights,
    clearError,
    refresh: loadWeekFiveData,
    // Additional methods for backward compatibility
    updateDreamVacationAdjectives,
    updateDreamVacationPlan,
    updateChatPrompt,
    updateConflictStyleReflection,
    updateCompletedSections
  };
}
