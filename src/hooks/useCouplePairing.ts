/**
 * Couple Pairing Hook
 * Manages the process of connecting two users as a couple
 */

import { useEffect, useState } from 'react';

export interface PairingCode {
  code: string;
  expiresAt: Date;
  createdBy: string;
  isActive: boolean;
}

export interface PairingRequest {
  id: string;
  fromUserId: string;
  toUserId: string;
  status: 'pending' | 'accepted' | 'rejected' | 'expired';
  createdAt: Date;
  respondedAt?: Date;
}

export interface CouplePairingState {
  isConnected: boolean;
  partnerId: string | null;
  partnerName: string | null;
  partnerEmail: string | null;
  pairingCode: PairingCode | null;
  pendingRequests: PairingRequest[];
  isLoading: boolean;
  error: string | null;
}

export function useCouplePairing() {
  const [state, setState] = useState<CouplePairingState>({
    isConnected: false,
    partnerId: null,
    partnerName: null,
    partnerEmail: null,
    pairingCode: null,
    pendingRequests: [],
    isLoading: true,
    error: null
  });

  // Load pairing state on mount
  useEffect(() => {
    loadPairingState();
  }, []);

  const loadPairingState = async () => {
    try {
      setState(prev => ({ ...prev, isLoading: true, error: null }));

      // Simulate loading from storage/API
      await new Promise(resolve => setTimeout(resolve, 500));

      // Check if already connected
      const storedPartner = localStorage.getItem('partner_info');
      if (storedPartner) {
        const partner = JSON.parse(storedPartner);
        setState(prev => ({
          ...prev,
          isConnected: true,
          partnerId: partner.id,
          partnerName: partner.name,
          partnerEmail: partner.email,
          isLoading: false
        }));
        return;
      }

      // Load pending requests
      const pendingRequests: PairingRequest[] = []; // Would load from API

      setState(prev => ({
        ...prev,
        pendingRequests,
        isLoading: false
      }));
    } catch (error) {
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Failed to load pairing state',
        isLoading: false
      }));
    }
  };

  const generatePairingCode = async (): Promise<string> => {
    try {
      setState(prev => ({ ...prev, isLoading: true, error: null }));

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 300));

      const code = Math.random().toString(36).substring(2, 8).toUpperCase();
      const pairingCode: PairingCode = {
        code,
        expiresAt: new Date(Date.now() + 15 * 60 * 1000), // 15 minutes
        createdBy: 'current_user_id',
        isActive: true
      };

      setState(prev => ({
        ...prev,
        pairingCode,
        isLoading: false
      }));

      return code;
    } catch (error) {
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Failed to generate pairing code',
        isLoading: false
      }));
      throw error;
    }
  };

  const joinWithCode = async (_code: string): Promise<boolean> => {
    try {
      setState(prev => ({ ...prev, isLoading: true, error: null }));

      // Simulate API call to validate and join with code
      await new Promise(resolve => setTimeout(resolve, 500));

      // Simulate successful pairing
      const partner = {
        id: 'partner_id',
        name: 'Partner Name',
        email: '<EMAIL>'
      };

      // Store partner info
      localStorage.setItem('partner_info', JSON.stringify(partner));

      setState(prev => ({
        ...prev,
        isConnected: true,
        partnerId: partner.id,
        partnerName: partner.name,
        partnerEmail: partner.email,
        pairingCode: null,
        isLoading: false
      }));

      return true;
    } catch (error) {
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Failed to join with code',
        isLoading: false
      }));
      return false;
    }
  };

  const sendPairingRequest = async (_email: string): Promise<boolean> => {
    try {
      setState(prev => ({ ...prev, isLoading: true, error: null }));

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 400));

      const request: PairingRequest = {
        id: `req_${Date.now()}`,
        fromUserId: 'current_user_id',
        toUserId: 'target_user_id',
        status: 'pending',
        createdAt: new Date()
      };

      setState(prev => ({
        ...prev,
        pendingRequests: [...prev.pendingRequests, request],
        isLoading: false
      }));

      return true;
    } catch (error) {
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Failed to send pairing request',
        isLoading: false
      }));
      return false;
    }
  };

  const respondToPairingRequest = async (requestId: string, accept: boolean): Promise<boolean> => {
    try {
      setState(prev => ({ ...prev, isLoading: true, error: null }));

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 300));

      setState(prev => ({
        ...prev,
        pendingRequests: prev.pendingRequests.map(req =>
          req.id === requestId
            ? { ...req, status: accept ? 'accepted' : 'rejected', respondedAt: new Date() }
            : req
        ),
        isLoading: false
      }));

      if (accept) {
        // Simulate successful pairing
        const partner = {
          id: 'partner_id',
          name: 'Partner Name',
          email: '<EMAIL>'
        };

        localStorage.setItem('partner_info', JSON.stringify(partner));

        setState(prev => ({
          ...prev,
          isConnected: true,
          partnerId: partner.id,
          partnerName: partner.name,
          partnerEmail: partner.email
        }));
      }

      return true;
    } catch (error) {
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Failed to respond to pairing request',
        isLoading: false
      }));
      return false;
    }
  };

  const disconnect = async (): Promise<boolean> => {
    try {
      setState(prev => ({ ...prev, isLoading: true, error: null }));

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 200));

      // Clear stored partner info
      localStorage.removeItem('partner_info');

      setState(prev => ({
        ...prev,
        isConnected: false,
        partnerId: null,
        partnerName: null,
        partnerEmail: null,
        isLoading: false
      }));

      return true;
    } catch (error) {
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Failed to disconnect',
        isLoading: false
      }));
      return false;
    }
  };

  const clearError = () => {
    setState(prev => ({ ...prev, error: null }));
  };

  return {
    ...state,
    generatePairingCode,
    joinWithCode,
    sendPairingRequest,
    respondToPairingRequest,
    disconnect,
    clearError,
    refresh: loadPairingState
  };
}
