/**
 * Daily Questions Hook
 * Manages daily questions for couples
 */

import { useCallback, useEffect, useState } from 'react';

export interface DailyQuestion {
  id: string;
  question: string;
  category: 'relationship' | 'personal' | 'fun' | 'deep' | 'future';
  difficulty: 'easy' | 'medium' | 'hard';
  date: Date;
  answered: boolean;
  userAnswer?: string;
  partnerAnswer?: string;
}

export interface DailyQuestionsState {
  todaysQuestion: DailyQuestion | null;
  previousQuestions: DailyQuestion[];
  streak: number;
  totalAnswered: number;
  isLoading: boolean;
  error: string | null;
}

const sampleQuestions: Omit<DailyQuestion, 'id' | 'date' | 'answered'>[] = [
  {
    question: "What's one thing you're grateful for about your partner today?",
    category: 'relationship',
    difficulty: 'easy'
  },
  {
    question: "If you could travel anywhere together, where would it be and why?",
    category: 'future',
    difficulty: 'medium'
  },
  {
    question: "What's a childhood memory that still makes you smile?",
    category: 'personal',
    difficulty: 'easy'
  },
  {
    question: "How do you envision our relationship in 5 years?",
    category: 'future',
    difficulty: 'hard'
  },
  {
    question: "What's the silliest thing that makes you laugh?",
    category: 'fun',
    difficulty: 'easy'
  }
];

export function useDailyQuestions(): DailyQuestionsState & {
  answerQuestion: (_answer: string) => Promise<void>;
  skipQuestion: () => Promise<void>;
  getQuestionHistory: (_limit?: number) => DailyQuestion[];
  refreshQuestion: () => Promise<void>;
} {
  const [state, setState] = useState<DailyQuestionsState>({
    todaysQuestion: null,
    previousQuestions: [],
    streak: 0,
    totalAnswered: 0,
    isLoading: true,
    error: null
  });

  const loadTodaysQuestion = useCallback(async () => {
    try {
      setState(prev => ({ ...prev, isLoading: true, error: null }));

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500));

      const today = new Date();
      const questionIndex = today.getDate() % sampleQuestions.length;
      const baseQuestion = sampleQuestions[questionIndex];

      const todaysQuestion: DailyQuestion = {
        ...baseQuestion,
        id: `daily_${today.toISOString().split('T')[0]}`,
        date: today,
        answered: false
      };

      // Load previous questions (simulated)
      const previousQuestions: DailyQuestion[] = [];
      for (let i = 1; i <= 7; i++) {
        const pastDate = new Date(today);
        pastDate.setDate(today.getDate() - i);
        const pastIndex = (pastDate.getDate()) % sampleQuestions.length;
        const pastQuestion = sampleQuestions[pastIndex];

        previousQuestions.push({
          ...pastQuestion,
          id: `daily_${pastDate.toISOString().split('T')[0]}`,
          date: pastDate,
          answered: Math.random() > 0.3, // 70% chance of being answered
          userAnswer: Math.random() > 0.5 ? 'Sample user answer' : undefined,
          partnerAnswer: Math.random() > 0.5 ? 'Sample partner answer' : undefined
        });
      }

      const answeredCount = previousQuestions.filter(q => q.answered).length;
      const streak = calculateStreak(previousQuestions);

      setState(prev => ({
        ...prev,
        todaysQuestion,
        previousQuestions,
        streak,
        totalAnswered: answeredCount,
        isLoading: false
      }));
    } catch (error) {
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Failed to load question',
        isLoading: false
      }));
    }
  }, []);

  // Load today's question
  useEffect(() => {
    loadTodaysQuestion();
  }, [loadTodaysQuestion]);

  const calculateStreak = (questions: DailyQuestion[]): number => {
    let streak = 0;
    const sortedQuestions = questions.sort((a, b) => b.date.getTime() - a.date.getTime());

    for (const question of sortedQuestions) {
      if (question.answered) {
        streak++;
      } else {
        break;
      }
    }

    return streak;
  };

  const answerQuestion = async (answer: string) => {
    if (!state.todaysQuestion) return;

    try {
      setState(prev => ({ ...prev, isLoading: true }));

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 300));

      const updatedQuestion: DailyQuestion = {
        ...state.todaysQuestion,
        answered: true,
        userAnswer: answer
      };

      setState(prev => ({
        ...prev,
        todaysQuestion: updatedQuestion,
        totalAnswered: prev.totalAnswered + 1,
        streak: prev.streak + 1,
        isLoading: false
      }));
    } catch (error) {
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Failed to answer question',
        isLoading: false
      }));
    }
  };

  const skipQuestion = async () => {
    if (!state.todaysQuestion) return;

    try {
      setState(prev => ({ ...prev, isLoading: true }));

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 200));

      setState(prev => ({
        ...prev,
        todaysQuestion: { ...prev.todaysQuestion!, answered: true },
        streak: 0, // Reset streak when skipping
        isLoading: false
      }));
    } catch (error) {
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Failed to skip question',
        isLoading: false
      }));
    }
  };

  const getQuestionHistory = (limit: number = 10): DailyQuestion[] => {
    return state.previousQuestions
      .sort((a, b) => b.date.getTime() - a.date.getTime())
      .slice(0, limit);
  };

  const refreshQuestion = async () => {
    await loadTodaysQuestion();
  };

  return {
    ...state,
    answerQuestion,
    skipQuestion,
    getQuestionHistory,
    refreshQuestion
  };
}
