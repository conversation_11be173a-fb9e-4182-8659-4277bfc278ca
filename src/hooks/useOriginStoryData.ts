/**
 * Origin Story Data Hook
 * Manages the couple's origin story and relationship timeline
 */

import { useState, useEffect } from 'react';

export interface StoryMilestone {
  id: string;
  title: string;
  description: string;
  date: Date;
  type: 'first_met' | 'first_date' | 'relationship_start' | 'engagement' | 'marriage' | 'anniversary' | 'custom';
  photos?: string[];
  location?: {
    name: string;
    coordinates?: {
      lat: number;
      lng: number;
    };
  };
  isPrivate: boolean;
  createdBy: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface OriginStory {
  id: string;
  coupleId: string;
  title: string;
  summary: string;
  milestones: StoryMilestone[];
  coverPhoto?: string;
  isComplete: boolean;
  visibility: 'private' | 'partner_only' | 'public';
  createdAt: Date;
  updatedAt: Date;
}

export interface OriginStoryState {
  story: OriginStory | null;
  milestones: StoryMilestone[];
  isLoading: boolean;
  error: string | null;
  hasUnsavedChanges: boolean;
}

export function useOriginStoryData(coupleId?: string) {
  const [state, setState] = useState<OriginStoryState>({
    story: null,
    milestones: [],
    isLoading: true,
    error: null,
    hasUnsavedChanges: false
  });

  // Load story data on mount
  useEffect(() => {
    if (coupleId) {
      loadStoryData(coupleId);
    } else {
      loadStoryData();
    }
  }, [coupleId]);

  const loadStoryData = async (id?: string) => {
    try {
      setState(prev => ({ ...prev, isLoading: true, error: null }));

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500));

      // Generate sample story data
      const sampleMilestones: StoryMilestone[] = [
        {
          id: 'milestone_1',
          title: 'First Met',
          description: 'We met at a coffee shop on a rainy Tuesday morning. It was love at first sight!',
          date: new Date('2020-03-15'),
          type: 'first_met',
          photos: ['/images/first_met.jpg'],
          location: {
            name: 'Central Perk Coffee Shop',
            coordinates: { lat: 40.7128, lng: -74.0060 }
          },
          isPrivate: false,
          createdBy: 'user_1',
          createdAt: new Date('2020-03-16'),
          updatedAt: new Date('2020-03-16')
        },
        {
          id: 'milestone_2',
          title: 'First Date',
          description: 'Our first official date was dinner at that little Italian place downtown.',
          date: new Date('2020-03-22'),
          type: 'first_date',
          photos: ['/images/first_date.jpg'],
          location: {
            name: 'Mama Mia\'s Italian Restaurant'
          },
          isPrivate: false,
          createdBy: 'user_1',
          createdAt: new Date('2020-03-23'),
          updatedAt: new Date('2020-03-23')
        },
        {
          id: 'milestone_3',
          title: 'Became Official',
          description: 'After two months of dating, we decided to make it official!',
          date: new Date('2020-05-15'),
          type: 'relationship_start',
          isPrivate: false,
          createdBy: 'user_2',
          createdAt: new Date('2020-05-16'),
          updatedAt: new Date('2020-05-16')
        }
      ];

      const story: OriginStory = {
        id: 'story_1',
        coupleId: id || 'couple_1',
        title: 'Our Love Story',
        summary: 'The beautiful journey of how we found each other and fell in love.',
        milestones: sampleMilestones,
        coverPhoto: '/images/couple_cover.jpg',
        isComplete: false,
        visibility: 'partner_only',
        createdAt: new Date('2020-03-16'),
        updatedAt: new Date()
      };

      setState(prev => ({
        ...prev,
        story,
        milestones: sampleMilestones,
        isLoading: false
      }));
    } catch (error) {
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Failed to load story data',
        isLoading: false
      }));
    }
  };

  const updateStory = async (updates: Partial<OriginStory>): Promise<boolean> => {
    try {
      if (!state.story) return false;

      setState(prev => ({ ...prev, hasUnsavedChanges: true }));

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 300));

      const updatedStory = {
        ...state.story,
        ...updates,
        updatedAt: new Date()
      };

      setState(prev => ({
        ...prev,
        story: updatedStory,
        hasUnsavedChanges: false
      }));

      return true;
    } catch (error) {
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Failed to update story'
      }));
      return false;
    }
  };

  const addMilestone = async (milestone: Omit<StoryMilestone, 'id' | 'createdAt' | 'updatedAt'>): Promise<boolean> => {
    try {
      setState(prev => ({ ...prev, hasUnsavedChanges: true }));

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 200));

      const newMilestone: StoryMilestone = {
        ...milestone,
        id: `milestone_${Date.now()}`,
        createdAt: new Date(),
        updatedAt: new Date()
      };

      setState(prev => ({
        ...prev,
        milestones: [...prev.milestones, newMilestone].sort((a, b) => a.date.getTime() - b.date.getTime()),
        story: prev.story ? {
          ...prev.story,
          milestones: [...prev.story.milestones, newMilestone].sort((a, b) => a.date.getTime() - b.date.getTime()),
          updatedAt: new Date()
        } : null,
        hasUnsavedChanges: false
      }));

      return true;
    } catch (error) {
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Failed to add milestone'
      }));
      return false;
    }
  };

  const updateMilestone = async (milestoneId: string, updates: Partial<StoryMilestone>): Promise<boolean> => {
    try {
      setState(prev => ({ ...prev, hasUnsavedChanges: true }));

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 200));

      setState(prev => ({
        ...prev,
        milestones: prev.milestones.map(m => 
          m.id === milestoneId 
            ? { ...m, ...updates, updatedAt: new Date() }
            : m
        ),
        story: prev.story ? {
          ...prev.story,
          milestones: prev.story.milestones.map(m => 
            m.id === milestoneId 
              ? { ...m, ...updates, updatedAt: new Date() }
              : m
          ),
          updatedAt: new Date()
        } : null,
        hasUnsavedChanges: false
      }));

      return true;
    } catch (error) {
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Failed to update milestone'
      }));
      return false;
    }
  };

  const deleteMilestone = async (milestoneId: string): Promise<boolean> => {
    try {
      setState(prev => ({ ...prev, hasUnsavedChanges: true }));

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 200));

      setState(prev => ({
        ...prev,
        milestones: prev.milestones.filter(m => m.id !== milestoneId),
        story: prev.story ? {
          ...prev.story,
          milestones: prev.story.milestones.filter(m => m.id !== milestoneId),
          updatedAt: new Date()
        } : null,
        hasUnsavedChanges: false
      }));

      return true;
    } catch (error) {
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Failed to delete milestone'
      }));
      return false;
    }
  };

  const getMilestonesByType = (type: StoryMilestone['type']): StoryMilestone[] => {
    return state.milestones.filter(milestone => milestone.type === type);
  };

  const getTimelineData = () => {
    return state.milestones
      .sort((a, b) => a.date.getTime() - b.date.getTime())
      .map(milestone => ({
        ...milestone,
        yearsSince: Math.floor((new Date().getTime() - milestone.date.getTime()) / (365.25 * 24 * 60 * 60 * 1000))
      }));
  };

  const getStoryStats = () => {
    const totalMilestones = state.milestones.length;
    const milestonesWithPhotos = state.milestones.filter(m => m.photos && m.photos.length > 0).length;
    const milestonesWithLocation = state.milestones.filter(m => m.location).length;
    const oldestMilestone = state.milestones.reduce((oldest, current) => 
      current.date < oldest.date ? current : oldest, 
      state.milestones[0]
    );
    const newestMilestone = state.milestones.reduce((newest, current) => 
      current.date > newest.date ? current : newest, 
      state.milestones[0]
    );

    return {
      totalMilestones,
      milestonesWithPhotos,
      milestonesWithLocation,
      completionPercentage: state.story?.isComplete ? 100 : Math.min(90, totalMilestones * 20),
      relationshipDuration: oldestMilestone && newestMilestone 
        ? Math.floor((newestMilestone.date.getTime() - oldestMilestone.date.getTime()) / (365.25 * 24 * 60 * 60 * 1000))
        : 0
    };
  };

  const clearError = () => {
    setState(prev => ({ ...prev, error: null }));
  };

  return {
    ...state,
    updateStory,
    addMilestone,
    updateMilestone,
    deleteMilestone,
    getMilestonesByType,
    getTimelineData,
    getStoryStats,
    clearError,
    refresh: () => loadStoryData(coupleId)
  };
}
