/**
 * Date Night Ideas Service
 * Manages date night suggestions and recommendations
 */

export interface DateNightIdea {
  id: string;
  title: string;
  description: string;
  category: 'indoor' | 'outdoor' | 'romantic' | 'adventure' | 'creative' | 'budget-friendly';
  duration: number; // in minutes
  cost: 'free' | 'low' | 'medium' | 'high';
  season?: 'spring' | 'summer' | 'fall' | 'winter' | 'any';
  tags: string[];
  rating?: number;
  isFavorite?: boolean;
}

export interface DateNightFilter {
  category?: string;
  cost?: string;
  duration?: { min: number; max: number };
  season?: string;
  tags?: string[];
}

class DateNightIdeasService {
  private ideas: DateNightIdea[] = [
    {
      id: '1',
      title: 'Cozy Movie Night',
      description: 'Create a home theater experience with your favorite films',
      category: 'indoor',
      duration: 120,
      cost: 'free',
      season: 'any',
      tags: ['relaxing', 'home', 'movies'],
      rating: 4.5
    },
    {
      id: '2',
      title: 'Sunset Picnic',
      description: 'Pack a basket and watch the sunset together',
      category: 'outdoor',
      duration: 90,
      cost: 'low',
      season: 'any',
      tags: ['romantic', 'nature', 'food'],
      rating: 4.8
    },
    {
      id: '3',
      title: 'Cooking Challenge',
      description: 'Try cooking a new cuisine together',
      category: 'creative',
      duration: 60,
      cost: 'medium',
      season: 'any',
      tags: ['cooking', 'learning', 'fun'],
      rating: 4.3
    }
  ];

  private favorites: Set<string> = new Set();

  getAllIdeas(): DateNightIdea[] {
    return this.ideas.map(idea => ({
      ...idea,
      isFavorite: this.favorites.has(idea.id)
    }));
  }

  getFilteredIdeas(filter: DateNightFilter): DateNightIdea[] {
    return this.ideas.filter(idea => {
      if (filter.category && idea.category !== filter.category) return false;
      if (filter.cost && idea.cost !== filter.cost) return false;
      if (filter.season && idea.season !== 'any' && idea.season !== filter.season) return false;
      if (filter.duration) {
        if (idea.duration < filter.duration.min || idea.duration > filter.duration.max) return false;
      }
      if (filter.tags && !filter.tags.some(tag => idea.tags.includes(tag))) return false;
      return true;
    }).map(idea => ({
      ...idea,
      isFavorite: this.favorites.has(idea.id)
    }));
  }

  getRandomIdea(filter?: DateNightFilter): DateNightIdea | null {
    const filteredIdeas = filter ? this.getFilteredIdeas(filter) : this.getAllIdeas();
    if (filteredIdeas.length === 0) return null;
    
    const randomIndex = Math.floor(Math.random() * filteredIdeas.length);
    return filteredIdeas[randomIndex];
  }

  addToFavorites(ideaId: string): void {
    this.favorites.add(ideaId);
  }

  removeFromFavorites(ideaId: string): void {
    this.favorites.delete(ideaId);
  }

  getFavorites(): DateNightIdea[] {
    return this.ideas
      .filter(idea => this.favorites.has(idea.id))
      .map(idea => ({ ...idea, isFavorite: true }));
  }

  addCustomIdea(idea: Omit<DateNightIdea, 'id'>): DateNightIdea {
    const newIdea: DateNightIdea = {
      ...idea,
      id: Math.random().toString(36).substr(2, 9)
    };
    this.ideas.push(newIdea);
    return newIdea;
  }

  getCategories(): string[] {
    return ['indoor', 'outdoor', 'romantic', 'adventure', 'creative', 'budget-friendly'];
  }

  getCostLevels(): string[] {
    return ['free', 'low', 'medium', 'high'];
  }

  getPopularTags(): string[] {
    const tagCounts = new Map<string, number>();
    this.ideas.forEach(idea => {
      idea.tags.forEach(tag => {
        tagCounts.set(tag, (tagCounts.get(tag) || 0) + 1);
      });
    });

    return Array.from(tagCounts.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 10)
      .map(([tag]) => tag);
  }

  searchIdeas(query: string): DateNightIdea[] {
    const searchTerm = query.toLowerCase();
    return this.ideas
      .filter(idea => 
        idea.title.toLowerCase().includes(searchTerm) ||
        idea.description.toLowerCase().includes(searchTerm) ||
        idea.tags.some(tag => tag.toLowerCase().includes(searchTerm))
      )
      .map(idea => ({
        ...idea,
        isFavorite: this.favorites.has(idea.id)
      }));
  }
}

// Singleton instance
export const dateNightIdeasService = new DateNightIdeasService();
