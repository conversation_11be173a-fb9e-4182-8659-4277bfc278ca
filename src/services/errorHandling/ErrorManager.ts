/**
 * Error Management System
 * Centralized error handling and recovery
 */

export enum ErrorCategory {
  _NETWORK = 'network',
  _VALIDATION = 'validation',
  _AUTHENTICATION = 'authentication',
  _AUTHORIZATION = 'authorization',
  _DATABASE = 'database',
  _BUSINESS_LOGIC = 'business_logic',
  _EXTERNAL_SERVICE = 'external_service',
  _PERMISSION = 'permission',
  _DATA = 'data',
  _UI = 'ui',
  _SYSTEM = 'system',
  _UNKNOWN = 'unknown'
}

export enum ErrorSeverity {
  _LOW = 'low',
  _MEDIUM = 'medium',
  _HIGH = 'high',
  _CRITICAL = 'critical'
}

export interface NetworkError {
  type: 'network';
  message: string;
  code: string;
  category: 'network';
  userMessage: string;
  isRetryable: boolean;
  timestamp: Date;
  context?: any;
}

export interface ValidationError {
  type: 'validation';
  message: string;
  code: string;
  category: 'validation';
  field?: string;
  isRetryable: boolean;
  timestamp: Date;
  context?: any;
}

export type AppError = NetworkError | ValidationError;

export interface ErrorStats {
  totalErrors: number;
  networkErrors: number;
  validationErrors: number;
  recoveredErrors: number;
  errorsByCategory: { [key: string]: number };
  errorsBySeverity: { [key: string]: number };
  topErrors: { code: string; count: number }[];
  lastError?: AppError;
}

class ErrorManager {
  private errors: AppError[] = [];
  private errorCounts: Map<string, number> = new Map();
  private stats: ErrorStats = {
    totalErrors: 0,
    networkErrors: 0,
    validationErrors: 0,
    recoveredErrors: 0,
    errorsByCategory: {},
    errorsBySeverity: {},
    topErrors: []
  };

  async handleError(error: AppError): Promise<void> {
    // Add context timestamp if not present
    if (!error.context) {
      error.context = {};
    }
    error.context.timestamp = new Date();

    this.errors.push(error);
    this.stats.totalErrors++;
    this.stats.lastError = error;

    // Update category stats
    this.stats.errorsByCategory[error.category] = (this.stats.errorsByCategory[error.category] || 0) + 1;

    // Update error code counts
    this.errorCounts.set(error.code, (this.errorCounts.get(error.code) || 0) + 1);

    if (error.type === 'network') {
      this.stats.networkErrors++;
    } else if (error.type === 'validation') {
      this.stats.validationErrors++;
    }

    // Update top errors
    this.updateTopErrors();

    // Log error
    console.error(`[ErrorManager] ${error.type} error:`, error.message);
  }

  private updateTopErrors(): void {
    this.stats.topErrors = Array.from(this.errorCounts.entries())
      .map(([code, count]) => ({ code, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10);
  }

  getErrorStats(): ErrorStats {
    return { ...this.stats };
  }

  getRecentErrors(limit: number = 10): AppError[] {
    return this.errors.slice(-limit);
  }

  clearErrors(): void {
    this.errors = [];
    this.errorCounts.clear();
    this.stats = {
      totalErrors: 0,
      networkErrors: 0,
      validationErrors: 0,
      recoveredErrors: 0,
      errorsByCategory: {},
      errorsBySeverity: {},
      topErrors: []
    };
  }
}

// Factory functions
export function createNetworkError(
  message: string,
  code: string = 'NETWORK_ERROR',
  isRetryable: boolean = true,
  context?: any
): NetworkError {
  return {
    type: 'network',
    message,
    code,
    category: 'network',
    userMessage: message,
    isRetryable,
    timestamp: new Date(),
    context
  };
}

export function createValidationError(
  message: string,
  field?: string,
  code?: string,
  context?: any
): ValidationError {
  const errorCode = code ? `VALIDATION_${code.toUpperCase()}` : 'VALIDATION_ERROR';
  return {
    type: 'validation',
    message,
    code: errorCode,
    category: 'validation',
    field,
    isRetryable: false,
    timestamp: new Date(),
    context
  };
}

// Singleton instance
export const errorManager = new ErrorManager();
