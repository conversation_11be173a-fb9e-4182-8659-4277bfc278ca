/**
 * Accessibility Management System
 * Handles accessibility features and compliance
 */

export enum AccessibilityRole {
  _BUTTON = 'button',
  _LINK = 'link',
  _TEXT = 'text',
  _HEADING = 'heading',
  _IMAGE = 'image',
  _LIST = 'list',
  _LISTITEM = 'listitem',
  _TEXTBOX = 'textbox',
  _CHECKBOX = 'checkbox',
  _RADIO = 'radio',
  _TAB = 'tab',
  _TABLIST = 'tablist',
  _TABPANEL = 'tabpanel',
  _DIALOG = 'dialog',
  _ALERT = 'alert',
  _MENU = 'menu',
  _MENU_ITEM = 'menuitem'
}

export interface AccessibilityProps {
  accessibilityRole?: AccessibilityRole;
  accessibilityLabel?: string;
  accessibilityHint?: string;
  accessible?: boolean;
  accessibilityState?: {
    disabled?: boolean;
    selected?: boolean;
    checked?: boolean;
    expanded?: boolean;
  };
}

export interface AccessibilitySettings {
  reduceMotion: boolean;
  highContrast: boolean;
  largeText: boolean;
  screenReader: boolean;
  voiceOver: boolean;
}

export interface ColorContrastResult {
  ratio: number;
  level: 'AA' | 'AAA' | 'FAIL';
  passes: boolean;
}

export interface AccessibilityAuditResult {
  componentName: string;
  issues: AccessibilityIssue[];
  score: number;
  recommendations: string[];
}

export interface AccessibilityIssue {
  type: 'missing_label' | 'low_contrast' | 'missing_role' | 'keyboard_trap' | 'focus_order';
  severity: 'low' | 'medium' | 'high' | 'critical';
  message: string;
  element?: string;
}

class AccessibilityManager {
  private settings: AccessibilitySettings = {
    reduceMotion: false,
    highContrast: false,
    largeText: false,
    screenReader: false,
    voiceOver: false
  };

  private announcements: string[] = [];

  getSettings(): AccessibilitySettings {
    return { ...this.settings };
  }

  updateSettings(newSettings: Partial<AccessibilitySettings>): void {
    this.settings = { ...this.settings, ...newSettings };
  }

  calculateColorContrast(foreground: string, background: string): ColorContrastResult {
    // Simplified contrast calculation
    // In a real implementation, this would use proper color space calculations
    const fgLuminance = this.getLuminance(foreground);
    const bgLuminance = this.getLuminance(background);

    const ratio = (Math.max(fgLuminance, bgLuminance) + 0.05) /
                  (Math.min(fgLuminance, bgLuminance) + 0.05);

    let level: 'AA' | 'AAA' | 'FAIL' = 'FAIL';
    if (ratio >= 7) level = 'AAA';
    else if (ratio >= 4.5) level = 'AA';

    return {
      ratio: Math.round(ratio * 100) / 100,
      level,
      passes: level !== 'FAIL'
    };
  }

  private getLuminance(color: string): number {
    // Simplified luminance calculation
    // Remove # if present
    const hex = color.replace('#', '');
    const r = parseInt(hex.substr(0, 2), 16) / 255;
    const g = parseInt(hex.substr(2, 2), 16) / 255;
    const b = parseInt(hex.substr(4, 2), 16) / 255;

    // Apply gamma correction
    const rs = r <= 0.03928 ? r / 12.92 : Math.pow((r + 0.055) / 1.055, 2.4);
    const gs = g <= 0.03928 ? g / 12.92 : Math.pow((g + 0.055) / 1.055, 2.4);
    const bs = b <= 0.03928 ? b / 12.92 : Math.pow((b + 0.055) / 1.055, 2.4);

    return 0.2126 * rs + 0.7152 * gs + 0.0722 * bs;
  }

  auditComponent(componentName: string, props: any): AccessibilityAuditResult {
    const issues: AccessibilityIssue[] = [];
    const recommendations: string[] = [];

    // Check for accessibility label
    if (!props.accessibilityLabel && !props.children) {
      issues.push({
        type: 'missing_label',
        severity: 'high',
        message: 'Component is missing accessibility label',
        element: componentName
      });
      recommendations.push('Add accessibilityLabel prop');
    }

    // Check for role
    if (!props.accessibilityRole) {
      issues.push({
        type: 'missing_role',
        severity: 'medium',
        message: 'Component is missing accessibility role',
        element: componentName
      });
      recommendations.push('Add appropriate accessibilityRole');
    }

    // Calculate score (100 - 20 points per high severity, 10 per medium, 5 per low)
    let score = 100;
    issues.forEach(issue => {
      switch (issue.severity) {
        case 'critical': score -= 30; break;
        case 'high': score -= 20; break;
        case 'medium': score -= 10; break;
        case 'low': score -= 5; break;
      }
    });

    return {
      componentName,
      issues,
      score: Math.max(0, score),
      recommendations
    };
  }

  announce(message: string): void {
    this.announcements.push(message);
    console.log(`[Accessibility] Announced: ${message}`);

    // In a real implementation, this would use platform-specific screen reader APIs
    // For now, we just log and store the announcement
  }

  getAnimationDuration(): number {
    return this.settings.reduceMotion ? 0 : 300;
  }

  getRecentAnnouncements(limit: number = 5): string[] {
    return this.announcements.slice(-limit);
  }

  clearAnnouncements(): void {
    this.announcements = [];
  }

  isScreenReaderActive(): boolean {
    return this.settings.screenReader || this.settings.voiceOver;
  }

  getFocusRingStyle(): any {
    return {
      borderWidth: this.settings.highContrast ? 3 : 2,
      borderColor: this.settings.highContrast ? '#000000' : '#007AFF',
      borderStyle: 'solid'
    };
  }
}

// Utility function
export function getAccessibilityProps(
  role: AccessibilityRole,
  label?: string,
  hint?: string,
  state?: any
): AccessibilityProps {
  return {
    accessibilityRole: role,
    accessibilityLabel: label,
    accessibilityHint: hint,
    accessible: true,
    accessibilityState: state
  };
}

// Singleton instance
export const accessibilityManager = new AccessibilityManager();
