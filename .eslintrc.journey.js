/**
 * Journey-Specific ESLint Configuration
 * 
 * Enforces architectural boundaries between journeys and ensures
 * proper import patterns in the new user-journey structure.
 */

module.exports = {
  extends: [
    'expo',
    '@react-native-community',
    'plugin:@typescript-eslint/recommended'
  ],
  
  parser: '@typescript-eslint/parser',
  
  plugins: [
    '@typescript-eslint',
    'react-hooks',
    'import'
  ],
  
  rules: {
    // General TypeScript rules
    '@typescript-eslint/no-unused-vars': ['error', { argsIgnorePattern: '^_' }],
    '@typescript-eslint/explicit-function-return-type': 'off',
    '@typescript-eslint/explicit-module-boundary-types': 'off',
    '@typescript-eslint/no-explicit-any': 'warn',
    
    // Import organization
    'import/order': [
      'error',
      {
        groups: [
          'builtin',
          'external',
          'internal',
          'parent',
          'sibling',
          'index'
        ],
        'newlines-between': 'always',
        alphabetize: {
          order: 'asc',
          caseInsensitive: true
        }
      }
    ],
    
    // React hooks
    'react-hooks/rules-of-hooks': 'error',
    'react-hooks/exhaustive-deps': 'warn',
    
    // General code quality
    'no-console': ['warn', { allow: ['warn', 'error'] }],
    'prefer-const': 'error',
    'no-var': 'error'
  },
  
  overrides: [
    // Onboarding Journey Rules
    {
      files: ['src/journeys/onboarding/**/*'],
      rules: {
        'import/no-restricted-paths': [
          'error',
          {
            zones: [
              {
                target: './src/journeys/onboarding',
                from: './src/journeys/!(onboarding)',
                except: ['../shared'],
                message: 'Onboarding journey should not import from other journeys. Use shared/ for common functionality.'
              }
            ]
          }
        ]
      }
    },
    
    // Daily Journey Rules
    {
      files: ['src/journeys/daily/**/*'],
      rules: {
        'import/no-restricted-paths': [
          'error',
          {
            zones: [
              {
                target: './src/journeys/daily',
                from: './src/journeys/!(daily)',
                except: ['../shared', '../onboarding'],
                message: 'Daily journey should only import from shared/ and onboarding/ (for auth).'
              }
            ]
          }
        ]
      }
    },
    
    // Activities Journey Rules
    {
      files: ['src/journeys/activities/**/*'],
      rules: {
        'import/no-restricted-paths': [
          'error',
          {
            zones: [
              {
                target: './src/journeys/activities',
                from: './src/journeys/!(activities)',
                except: ['../shared', '../onboarding'],
                message: 'Activities journey should only import from shared/ and onboarding/ (for auth).'
              }
            ]
          }
        ]
      }
    },
    
    // Memories Journey Rules
    {
      files: ['src/journeys/memories/**/*'],
      rules: {
        'import/no-restricted-paths': [
          'error',
          {
            zones: [
              {
                target: './src/journeys/memories',
                from: './src/journeys/!(memories)',
                except: ['../shared', '../onboarding'],
                message: 'Memories journey should only import from shared/ and onboarding/ (for auth).'
              }
            ]
          }
        ]
      }
    },
    
    // Planning Journey Rules
    {
      files: ['src/journeys/planning/**/*'],
      rules: {
        'import/no-restricted-paths': [
          'error',
          {
            zones: [
              {
                target: './src/journeys/planning',
                from: './src/journeys/!(planning)',
                except: ['../shared', '../onboarding'],
                message: 'Planning journey should only import from shared/ and onboarding/ (for auth).'
              }
            ]
          }
        ]
      }
    },
    
    // Progress Journey Rules
    {
      files: ['src/journeys/progress/**/*'],
      rules: {
        'import/no-restricted-paths': [
          'error',
          {
            zones: [
              {
                target: './src/journeys/progress',
                from: './src/journeys/!(progress)',
                except: ['../shared', '../onboarding'],
                message: 'Progress journey should only import from shared/ and onboarding/ (for auth).'
              }
            ]
          }
        ]
      }
    },
    
    // Shared Code Rules
    {
      files: ['src/shared/**/*'],
      rules: {
        'import/no-restricted-paths': [
          'error',
          {
            zones: [
              {
                target: './src/shared',
                from: './src/journeys',
                message: 'Shared code should not import from journey-specific code. Keep shared code independent.'
              }
            ]
          }
        ]
      }
    },
    
    // Test Files Rules
    {
      files: ['**/*.test.ts', '**/*.test.tsx', '**/*.spec.ts', '**/*.spec.tsx'],
      env: {
        jest: true
      },
      rules: {
        '@typescript-eslint/no-explicit-any': 'off',
        'no-console': 'off'
      }
    },
    
    // App Router Files (can import from any journey)
    {
      files: ['app/**/*'],
      rules: {
        'import/no-restricted-paths': 'off'
      }
    },
    
    // Configuration Files
    {
      files: [
        '*.config.js',
        '*.config.ts',
        'babel.config.js',
        'metro.config.js',
        'jest.config.js'
      ],
      env: {
        node: true
      },
      rules: {
        '@typescript-eslint/no-var-requires': 'off',
        'import/no-restricted-paths': 'off'
      }
    }
  ],
  
  settings: {
    'import/resolver': {
      typescript: {
        alwaysTryTypes: true,
        project: './tsconfig.json'
      },
      node: {
        extensions: ['.js', '.jsx', '.ts', '.tsx']
      }
    }
  },
  
  env: {
    'react-native/react-native': true,
    es6: true,
    node: true
  },
  
  globals: {
    __DEV__: 'readonly',
    fetch: 'readonly',
    FormData: 'readonly',
    XMLHttpRequest: 'readonly'
  }
};
