# Everlasting Us - Couple Bonding App

> **Strengthen your relationship through daily questions, activities, and meaningful conversations**

## 🌟 Overview

Everlasting Us is a comprehensive couple bonding app designed to help partners grow together through:
- **Daily Questions** - Thoughtful questions delivered daily to spark meaningful conversations
- **Weekly Modules** - Structured relationship-building activities and exercises
- **Date Night Planning** - Curated suggestions for memorable experiences
- **Milestone Tracking** - Celebrate your relationship journey and achievements
- **Photo Scrapbook** - Preserve precious memories together

## 🚀 Quick Start

### Prerequisites
- Node.js 18+
- Expo CLI (`npm install -g @expo/cli`)
- iOS Simulator (Mac) or Android Studio

### Development Setup
```bash
# Clone the repository
git clone <repository-url>
cd everlasting-us

# Install dependencies
npm install

# Start development server
npm run dev
```

Scan QR code with Expo Go app or press 'i' for iOS simulator.

### Database Setup
```bash
# Start Supabase (requires Docker)
npx supabase start

# Run migrations
npx supabase db reset
```

## 📚 Documentation

### For Developers
- **[📖 Complete Documentation](./docs/README.md)** - Start here for comprehensive guides
- **[🏗️ Developer Guide](./docs/developer-guide.md)** - Setup, architecture, and development workflow
- **[📚 API Reference](./docs/api-reference.md)** - Component and service documentation
- **[🚨 Troubleshooting](./docs/troubleshooting.md)** - Common issues and solutions

### Feature Guides
- **[💬 Daily Questions](./docs/daily-questions-feature-guide.md)** - Daily bonding questions system
- **[🎮 Match Game](./docs/match-game-migration-guide.md)** - Couple compatibility game
- **[🍽️ Meal Ideas](./docs/meal-ideas-guide.md)** - Date night meal suggestions
- **[📅 Date Night](./docs/date-night-favorites-guide.md)** - Activity planning system

### Quick References
- **[⚡ Build Guide](./docs/build-guide.md)** - Building and deployment
- **[🎨 Design System](./docs/design-system.md)** - UI components and styling
- **[❓ FAQ](./docs/faq.md)** - Frequently asked questions

## 🏗️ Architecture

### Tech Stack
- **Frontend**: React Native + Expo
- **Backend**: Supabase (PostgreSQL + Auth + Storage)
- **Language**: TypeScript
- **State Management**: React Hooks + Context
- **Styling**: Custom design system with theme support

### Key Features
- **Cross-platform** - iOS, Android, and Web support
- **Real-time sync** - Data synchronized across devices
- **Offline support** - Works without internet connection
- **Privacy-first** - Secure data handling and partner-only access
- **Gamification** - Points, streaks, and achievements

## 🎯 Core Features

### Daily Questions
- One thoughtful question per day for both partners
- 6 categories: Deep, Fun, Funny, Memories, Dreams, Gratitude
- Partner interaction with reactions and comments
- Streak tracking and milestone celebrations

### Weekly Modules
- 12 structured relationship-building activities
- Progressive difficulty and depth
- Progress tracking and completion rewards
- Integration with scrapbook and milestones

### Date Night Planning
- Curated restaurant and activity suggestions
- Personalized recommendations based on preferences
- Favorites system and completion tracking
- Integration with couple profile and preferences

## 🔧 Development

### Common Commands
```bash
npm run dev              # Start development server
npm run lint             # Check code quality
npm run build:web        # Build for web
npm run test             # Run tests
```

### Project Structure
```
├── app/                 # Expo Router pages
├── components/          # Reusable UI components
├── hooks/              # Custom React hooks
├── services/           # Business logic and API calls
├── types/              # TypeScript type definitions
├── utils/              # Utility functions and helpers
├── supabase/           # Database migrations and functions
└── docs/               # Documentation
```

## 🤝 Contributing

1. **Read** the [Developer Guide](./docs/developer-guide.md)
2. **Follow** the [Code Standards](./docs/code-standards.md)
3. **Test** your changes thoroughly
4. **Document** any new features or APIs
5. **Submit** a pull request with clear description

## 📄 License

This project is proprietary software. All rights reserved.

---

**Built with ❤️ for helping couples grow together**

*Last updated: January 2025*