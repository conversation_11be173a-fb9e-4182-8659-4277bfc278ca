
> bolt-expo-starter@1.0.0 lint
> expo lint

env: load .env
env: export EXPO_PUBLIC_SUPABASE_URL EXPO_PUBLIC_SUPABASE_ANON_KEY EXPO_PUBLIC_BYPASS_AUTH
Using legacy ESLint config. Consider upgrading to flat config.
(node:31572) [MODULE_TYPELESS_PACKAGE_JSON] Warning: Module type of file:///Users/<USER>/everlasting-us/eslint.config.js?mtime=1758046792387 is not specified and it doesn't parse as CommonJS.
Reparsing as ES module because module syntax was detected. This incurs a performance overhead.
To eliminate this warning, add "type": "module" to /Users/<USER>/everlasting-us/package.json.
(Use `node --trace-warnings ...` to show where the warning was created)

/Users/<USER>/everlasting-us/app/test-pages/TestDateNightFavoritesScreen.tsx
  88:66  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

/Users/<USER>/everlasting-us/app/test-pages/TestMilestonesScreen.tsx
  41:6   warning  React Hook useEffect has a missing dependency: 'loadData'. Either include it or remove the dependency array  react-hooks/exhaustive-deps
  65:77  warning  Unexpected any. Specify a different type                                                                     @typescript-eslint/no-explicit-any

/Users/<USER>/everlasting-us/src/components/AuthScreen.tsx
  204:16  warning  Avoid StyleSheet.create in screens; use DS templates. If you need a new pattern, add a DS component  no-restricted-syntax

/Users/<USER>/everlasting-us/src/components/shared/index.tsx
   54:22  warning  Avoid StyleSheet.create in screens; use DS templates. If you need a new pattern, add a DS component  no-restricted-syntax
   79:86  warning  Unexpected any. Specify a different type                                                             @typescript-eslint/no-explicit-any
  109:77  warning  Unexpected any. Specify a different type                                                             @typescript-eslint/no-explicit-any
  139:63  warning  Unexpected any. Specify a different type                                                             @typescript-eslint/no-explicit-any
  168:67  warning  Unexpected any. Specify a different type                                                             @typescript-eslint/no-explicit-any
  197:62  warning  Unexpected any. Specify a different type                                                             @typescript-eslint/no-explicit-any
  217:82  warning  Unexpected any. Specify a different type                                                             @typescript-eslint/no-explicit-any
  264:68  warning  Unexpected any. Specify a different type                                                             @typescript-eslint/no-explicit-any
  311:64  warning  Unexpected any. Specify a different type                                                             @typescript-eslint/no-explicit-any
  326:62  warning  Unexpected any. Specify a different type                                                             @typescript-eslint/no-explicit-any
  364:13  warning  Unexpected any. Specify a different type                                                             @typescript-eslint/no-explicit-any
  365:15  warning  Unexpected any. Specify a different type                                                             @typescript-eslint/no-explicit-any
  369:91  warning  Unexpected any. Specify a different type                                                             @typescript-eslint/no-explicit-any

/Users/<USER>/everlasting-us/src/contexts/FavoritesContext.tsx
  12:9   warning  Unexpected any. Specify a different type                                                                          @typescript-eslint/no-explicit-any
  43:6   warning  React Hook useEffect has a missing dependency: 'saveFavorites'. Either include it or remove the dependency array  react-hooks/exhaustive-deps
  52:54  warning  Unexpected any. Specify a different type                                                                          @typescript-eslint/no-explicit-any

/Users/<USER>/everlasting-us/src/contexts/SettingsContext.tsx
   54:41  warning  Unexpected any. Specify a different type                                                                         @typescript-eslint/no-explicit-any
  121:6   warning  React Hook useEffect has a missing dependency: 'saveSettings'. Either include it or remove the dependency array  react-hooks/exhaustive-deps
  178:46  warning  Unexpected any. Specify a different type                                                                         @typescript-eslint/no-explicit-any

/Users/<USER>/everlasting-us/src/hooks/useDailyQuestions.ts
  74:6  warning  React Hook useEffect has a missing dependency: 'loadTodaysQuestion'. Either include it or remove the dependency array  react-hooks/exhaustive-deps

/Users/<USER>/everlasting-us/src/hooks/useDailyQuestionsNotifications.ts
  132:7  warning  Unexpected console statement. Only these console methods are allowed: warn, error  no-console
  141:9  warning  Unexpected console statement. Only these console methods are allowed: warn, error  no-console
  150:7  warning  Unexpected console statement. Only these console methods are allowed: warn, error  no-console

/Users/<USER>/everlasting-us/src/hooks/useUserEvents.ts
   12:29  warning  Unexpected any. Specify a different type                                                                       @typescript-eslint/no-explicit-any
   69:6   warning  React Hook useEffect has a missing dependency: 'loadEvents'. Either include it or remove the dependency array  react-hooks/exhaustive-deps
  141:74  warning  Unexpected any. Specify a different type                                                                       @typescript-eslint/no-explicit-any

/Users/<USER>/everlasting-us/src/hooks/useWeekFiveData.ts
   43:23  warning  Unexpected any. Specify a different type                                                                             @typescript-eslint/no-explicit-any
   48:17  warning  Unexpected any. Specify a different type                                                                             @typescript-eslint/no-explicit-any
   49:29  warning  Unexpected any. Specify a different type                                                                             @typescript-eslint/no-explicit-any
  176:6   warning  React Hook useEffect has a missing dependency: 'loadWeekFiveData'. Either include it or remove the dependency array  react-hooks/exhaustive-deps
  399:42  warning  Unexpected any. Specify a different type                                                                             @typescript-eslint/no-explicit-any
  403:37  warning  Unexpected any. Specify a different type                                                                             @typescript-eslint/no-explicit-any
  410:54  warning  Unexpected any. Specify a different type                                                                             @typescript-eslint/no-explicit-any

/Users/<USER>/everlasting-us/src/hooks/useWeekOneData.ts
   41:19  warning  Unexpected any. Specify a different type                                                                            @typescript-eslint/no-explicit-any
   42:24  warning  Unexpected any. Specify a different type                                                                            @typescript-eslint/no-explicit-any
   43:17  warning  Unexpected any. Specify a different type                                                                            @typescript-eslint/no-explicit-any
   44:25  warning  Unexpected any. Specify a different type                                                                            @typescript-eslint/no-explicit-any
  161:6   warning  React Hook useEffect has a missing dependency: 'loadWeekOneData'. Either include it or remove the dependency array  react-hooks/exhaustive-deps
  377:46  warning  Unexpected any. Specify a different type                                                                            @typescript-eslint/no-explicit-any
  381:38  warning  Unexpected any. Specify a different type                                                                            @typescript-eslint/no-explicit-any
  385:37  warning  Unexpected any. Specify a different type                                                                            @typescript-eslint/no-explicit-any
  392:48  warning  Unexpected any. Specify a different type                                                                            @typescript-eslint/no-explicit-any

/Users/<USER>/everlasting-us/src/journeys/activities/Activities.tsx
   713:37  warning  Unexpected any. Specify a different type                                                                        @typescript-eslint/no-explicit-any
   800:6   warning  React Hook useMemo has a missing dependency: 'allActivities'. Either include it or remove the dependency array  react-hooks/exhaustive-deps
  1015:16  warning  Avoid StyleSheet.create in screens; use DS templates. If you need a new pattern, add a DS component             no-restricted-syntax

/Users/<USER>/everlasting-us/src/journeys/activities/ActivitiesRefactored.tsx
  278:37  warning  Unexpected any. Specify a different type                                                             @typescript-eslint/no-explicit-any
  575:16  warning  Avoid StyleSheet.create in screens; use DS templates. If you need a new pattern, add a DS component  no-restricted-syntax

/Users/<USER>/everlasting-us/src/journeys/activities/DateNight.tsx
   76:7   warning  Unexpected console statement. Only these console methods are allowed: warn, error                                       no-console
   82:7   warning  Unexpected console statement. Only these console methods are allowed: warn, error                                       no-console
   85:9   warning  Unexpected console statement. Only these console methods are allowed: warn, error                                       no-console
  142:13  warning  Unexpected console statement. Only these console methods are allowed: warn, error                                       no-console
  146:9   warning  Unexpected console statement. Only these console methods are allowed: warn, error                                       no-console
  148:9   warning  Unexpected console statement. Only these console methods are allowed: warn, error                                       no-console
  165:7   warning  Unexpected console statement. Only these console methods are allowed: warn, error                                       no-console
  171:7   warning  Unexpected console statement. Only these console methods are allowed: warn, error                                       no-console
  178:7   warning  Unexpected console statement. Only these console methods are allowed: warn, error                                       no-console
  182:9   warning  Unexpected console statement. Only these console methods are allowed: warn, error                                       no-console
  208:7   warning  Unexpected console statement. Only these console methods are allowed: warn, error                                       no-console
  236:6   warning  React Hook useCallback has a missing dependency: 'filterHiddenIdeas'. Either include it or remove the dependency array  react-hooks/exhaustive-deps
  249:92  warning  Unexpected any. Specify a different type                                                                                @typescript-eslint/no-explicit-any
  267:7   warning  Unexpected console statement. Only these console methods are allowed: warn, error                                       no-console
  302:7   warning  Unexpected console statement. Only these console methods are allowed: warn, error                                       no-console
  398:22  warning  Unexpected any. Specify a different type                                                                                @typescript-eslint/no-explicit-any
  410:61  warning  Unexpected any. Specify a different type                                                                                @typescript-eslint/no-explicit-any
  412:6   warning  React Hook useCallback has a missing dependency: 'renderIdeaCard'. Either include it or remove the dependency array     react-hooks/exhaustive-deps
  415:43  warning  Unexpected any. Specify a different type                                                                                @typescript-eslint/no-explicit-any
  515:51  warning  Unexpected any. Specify a different type                                                                                @typescript-eslint/no-explicit-any
  544:33  warning  Unexpected any. Specify a different type                                                                                @typescript-eslint/no-explicit-any
  572:53  warning  Unexpected any. Specify a different type                                                                                @typescript-eslint/no-explicit-any
  579:21  warning  Unexpected console statement. Only these console methods are allowed: warn, error                                       no-console
  782:27  warning  Unexpected any. Specify a different type                                                                                @typescript-eslint/no-explicit-any
  799:34  warning  Unexpected any. Specify a different type                                                                                @typescript-eslint/no-explicit-any
  808:34  warning  Unexpected any. Specify a different type                                                                                @typescript-eslint/no-explicit-any
  819:34  warning  Unexpected any. Specify a different type                                                                                @typescript-eslint/no-explicit-any
  855:30  warning  Unexpected any. Specify a different type                                                                                @typescript-eslint/no-explicit-any
  863:53  warning  Unexpected any. Specify a different type                                                                                @typescript-eslint/no-explicit-any
  905:42  warning  Unexpected any. Specify a different type                                                                                @typescript-eslint/no-explicit-any
  926:63  warning  Unexpected any. Specify a different type                                                                                @typescript-eslint/no-explicit-any
  934:33  warning  Unexpected console statement. Only these console methods are allowed: warn, error                                       no-console
  968:16  warning  Avoid StyleSheet.create in screens; use DS templates. If you need a new pattern, add a DS component                     no-restricted-syntax

/Users/<USER>/everlasting-us/src/journeys/activities/Filters.tsx
   78:61  warning  Unexpected any. Specify a different type                                                             @typescript-eslint/no-explicit-any
  102:12  warning  Unexpected any. Specify a different type                                                             @typescript-eslint/no-explicit-any
  262:16  warning  Avoid StyleSheet.create in screens; use DS templates. If you need a new pattern, add a DS component  no-restricted-syntax

/Users/<USER>/everlasting-us/src/journeys/activities/IdeaCard.tsx
  268:16  warning  Avoid StyleSheet.create in screens; use DS templates. If you need a new pattern, add a DS component  no-restricted-syntax

/Users/<USER>/everlasting-us/src/journeys/activities/Meals.tsx
   98:9   warning  Unexpected console statement. Only these console methods are allowed: warn, error                    no-console
  100:9   warning  Unexpected console statement. Only these console methods are allowed: warn, error                    no-console
  112:9   warning  Unexpected console statement. Only these console methods are allowed: warn, error                    no-console
  123:92  warning  Unexpected any. Specify a different type                                                             @typescript-eslint/no-explicit-any
  141:7   warning  Unexpected console statement. Only these console methods are allowed: warn, error                    no-console
  311:48  warning  Unexpected any. Specify a different type                                                             @typescript-eslint/no-explicit-any
  311:62  warning  Unexpected any. Specify a different type                                                             @typescript-eslint/no-explicit-any
  606:25  warning  Unexpected any. Specify a different type                                                             @typescript-eslint/no-explicit-any
  659:16  warning  Avoid StyleSheet.create in screens; use DS templates. If you need a new pattern, add a DS component  no-restricted-syntax

/Users/<USER>/everlasting-us/src/journeys/activities/activities/MatchActivity/MatchActivity.tsx
   54:25  warning  Unexpected any. Specify a different type                                                                               @typescript-eslint/no-explicit-any
   56:21  warning  Unexpected any. Specify a different type                                                                               @typescript-eslint/no-explicit-any
   58:31  warning  Unexpected any. Specify a different type                                                                               @typescript-eslint/no-explicit-any
  174:6   warning  React Hook useCallback has a missing dependency: 'completeActivity'. Either include it or remove the dependency array  react-hooks/exhaustive-deps
  351:16  warning  Avoid StyleSheet.create in screens; use DS templates. If you need a new pattern, add a DS component                    no-restricted-syntax

/Users/<USER>/everlasting-us/src/journeys/activities/containers/UniversalActivityContainer.tsx
   30:26  warning  Unexpected any. Specify a different type                                                                                   @typescript-eslint/no-explicit-any
   32:22  warning  Unexpected any. Specify a different type                                                                                   @typescript-eslint/no-explicit-any
   51:56  warning  Unexpected any. Specify a different type                                                                                   @typescript-eslint/no-explicit-any
   89:47  warning  Unexpected any. Specify a different type                                                                                   @typescript-eslint/no-explicit-any
   92:7   warning  Unexpected console statement. Only these console methods are allowed: warn, error                                          no-console
  103:7   warning  Unexpected console statement. Only these console methods are allowed: warn, error                                          no-console
  112:43  warning  Unexpected any. Specify a different type                                                                                   @typescript-eslint/no-explicit-any
  124:37  warning  Unexpected any. Specify a different type                                                                                   @typescript-eslint/no-explicit-any
  127:6   warning  React Hook useCallback has a missing dependency: 'renderInlineActivity'. Either include it or remove the dependency array  react-hooks/exhaustive-deps
  162:39  warning  Unexpected any. Specify a different type                                                                                   @typescript-eslint/no-explicit-any
  248:16  warning  Avoid StyleSheet.create in screens; use DS templates. If you need a new pattern, add a DS component                        no-restricted-syntax

/Users/<USER>/everlasting-us/src/journeys/activities/dateNightHooks.ts
  23:46  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

/Users/<USER>/everlasting-us/src/journeys/activities/dateNightIdeasService.ts
   74:44  warning  Unexpected any. Specify a different type                                           @typescript-eslint/no-explicit-any
  214:7   warning  Unexpected console statement. Only these console methods are allowed: warn, error  no-console
  222:7   warning  Unexpected console statement. Only these console methods are allowed: warn, error  no-console
  238:9   warning  Unexpected console statement. Only these console methods are allowed: warn, error  no-console
  243:7   warning  Unexpected console statement. Only these console methods are allowed: warn, error  no-console
  247:22  warning  Unexpected any. Specify a different type                                           @typescript-eslint/no-explicit-any
  264:7   warning  Unexpected console statement. Only these console methods are allowed: warn, error  no-console
  270:7   warning  Unexpected console statement. Only these console methods are allowed: warn, error  no-console
  276:7   warning  Unexpected console statement. Only these console methods are allowed: warn, error  no-console
  280:7   warning  Unexpected console statement. Only these console methods are allowed: warn, error  no-console
  381:23  warning  Unexpected any. Specify a different type                                           @typescript-eslint/no-explicit-any
  507:47  warning  Unexpected any. Specify a different type                                           @typescript-eslint/no-explicit-any

/Users/<USER>/everlasting-us/src/journeys/activities/examples/HomeScreenExample.tsx
   24:43  warning  Unexpected any. Specify a different type                                                             @typescript-eslint/no-explicit-any
   45:39  warning  Unexpected any. Specify a different type                                                             @typescript-eslint/no-explicit-any
  146:16  warning  Avoid StyleSheet.create in screens; use DS templates. If you need a new pattern, add a DS component  no-restricted-syntax

/Users/<USER>/everlasting-us/src/journeys/activities/examples/HomeScreenIntegration.tsx
   27:34  warning  Unexpected any. Specify a different type                                                             @typescript-eslint/no-explicit-any
   28:30  warning  Unexpected any. Specify a different type                                                             @typescript-eslint/no-explicit-any
   44:55  warning  Unexpected any. Specify a different type                                                             @typescript-eslint/no-explicit-any
   45:5   warning  Unexpected console statement. Only these console methods are allowed: warn, error                    no-console
   52:51  warning  Unexpected any. Specify a different type                                                             @typescript-eslint/no-explicit-any
   62:48  warning  Unexpected any. Specify a different type                                                             @typescript-eslint/no-explicit-any
   66:34  warning  Unexpected any. Specify a different type                                                             @typescript-eslint/no-explicit-any
  156:16  warning  Avoid StyleSheet.create in screens; use DS templates. If you need a new pattern, add a DS component  no-restricted-syntax

/Users/<USER>/everlasting-us/src/journeys/activities/examples/ProfileScreenIntegration.tsx
   34:34  warning  Unexpected any. Specify a different type                                                             @typescript-eslint/no-explicit-any
   35:30  warning  Unexpected any. Specify a different type                                                             @typescript-eslint/no-explicit-any
   52:55  warning  Unexpected any. Specify a different type                                                             @typescript-eslint/no-explicit-any
   53:5   warning  Unexpected console statement. Only these console methods are allowed: warn, error                    no-console
   60:51  warning  Unexpected any. Specify a different type                                                             @typescript-eslint/no-explicit-any
   67:59  warning  Unexpected any. Specify a different type                                                             @typescript-eslint/no-explicit-any
   72:42  warning  Unexpected any. Specify a different type                                                             @typescript-eslint/no-explicit-any
  181:16  warning  Avoid StyleSheet.create in screens; use DS templates. If you need a new pattern, add a DS component  no-restricted-syntax

/Users/<USER>/everlasting-us/src/journeys/activities/favoritesService.ts
   12:29  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   21:29  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   44:32  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   44:64  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  121:28  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  121:60  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  139:31  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  189:28  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  189:60  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  234:30  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

/Users/<USER>/everlasting-us/src/journeys/activities/match-game/matchGameQuestions.service.ts
  17:45  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

/Users/<USER>/everlasting-us/src/journeys/activities/match-game/matchGameSessions.service.ts
  109:22  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

/Users/<USER>/everlasting-us/src/journeys/activities/mealIdeasService.ts
   79:7   warning  Unexpected console statement. Only these console methods are allowed: warn, error  no-console
   86:7   warning  Unexpected console statement. Only these console methods are allowed: warn, error  no-console
   97:9   warning  Unexpected console statement. Only these console methods are allowed: warn, error  no-console
  103:9   warning  Unexpected console statement. Only these console methods are allowed: warn, error  no-console
  116:9   warning  Unexpected console statement. Only these console methods are allowed: warn, error  no-console
  121:9   warning  Unexpected console statement. Only these console methods are allowed: warn, error  no-console
  127:7   warning  Unexpected console statement. Only these console methods are allowed: warn, error  no-console
  142:9   warning  Unexpected console statement. Only these console methods are allowed: warn, error  no-console
  151:11  warning  Unexpected console statement. Only these console methods are allowed: warn, error  no-console
  155:9   warning  Unexpected console statement. Only these console methods are allowed: warn, error  no-console
  159:7   warning  Unexpected console statement. Only these console methods are allowed: warn, error  no-console
  182:7   warning  Unexpected console statement. Only these console methods are allowed: warn, error  no-console

/Users/<USER>/everlasting-us/src/journeys/activities/surpriseHelpers.ts
  50:5  warning  Unexpected console statement. Only these console methods are allowed: warn, error  no-console
  56:5  warning  Unexpected console statement. Only these console methods are allowed: warn, error  no-console

/Users/<USER>/everlasting-us/src/journeys/activities/useDateNightFavorites.ts
   14:69  warning  Unexpected any. Specify a different type                                                                                          @typescript-eslint/no-explicit-any
   54:6   warning  React Hook useEffect has a missing dependency: 'loadIdeas'. Either include it or remove the dependency array                      react-hooks/exhaustive-deps
   59:6   warning  React Hook useEffect has a missing dependency: 'updateIdeasWithFavoriteStatus'. Either include it or remove the dependency array  react-hooks/exhaustive-deps
   83:6   warning  React Hook useCallback has a missing dependency: 'baseFavorites'. Either include it or remove the dependency array                react-hooks/exhaustive-deps
  108:6   warning  React Hook useCallback has a missing dependency: 'baseFavorites'. Either include it or remove the dependency array                react-hooks/exhaustive-deps
  117:6   warning  React Hook useCallback has a missing dependency: 'baseFavorites'. Either include it or remove the dependency array                react-hooks/exhaustive-deps
  119:92  warning  Unexpected any. Specify a different type                                                                                          @typescript-eslint/no-explicit-any
  142:6   warning  React Hook useCallback has a missing dependency: 'baseFavorites'. Either include it or remove the dependency array                react-hooks/exhaustive-deps
  146:6   warning  React Hook useCallback has a missing dependency: 'baseFavorites'. Either include it or remove the dependency array                react-hooks/exhaustive-deps
  153:6   warning  React Hook useCallback has a missing dependency: 'baseFavorites'. Either include it or remove the dependency array                react-hooks/exhaustive-deps
  157:55  warning  Unexpected any. Specify a different type                                                                                          @typescript-eslint/no-explicit-any
  208:6   warning  React Hook useEffect has a missing dependency: 'hook'. Either include it or remove the dependency array                           react-hooks/exhaustive-deps

/Users/<USER>/everlasting-us/src/journeys/activities/useDateNightIdeasSupabase.ts
   47:6   warning  React Hook useEffect has missing dependencies: 'cleanupSubscriptions', 'loadAllData', and 'setupRealtimeSubscriptions'. Either include them or remove the dependency array  react-hooks/exhaustive-deps
  119:6   warning  React Hook useCallback has a missing dependency: 'state'. Either include it or remove the dependency array                                                                  react-hooks/exhaustive-deps
  238:27  warning  Unexpected any. Specify a different type                                                                                                                                    @typescript-eslint/no-explicit-any
  244:24  warning  Unexpected any. Specify a different type                                                                                                                                    @typescript-eslint/no-explicit-any
  253:27  warning  Unexpected any. Specify a different type                                                                                                                                    @typescript-eslint/no-explicit-any

/Users/<USER>/everlasting-us/src/journeys/activities/useDateNightPool.ts
  36:6  warning  React Hook useEffect has a missing dependency: 'loadIdeas'. Either include it or remove the dependency array  react-hooks/exhaustive-deps

/Users/<USER>/everlasting-us/src/journeys/activities/useFavorites.ts
   39:64  warning  Unexpected any. Specify a different type                                                                              @typescript-eslint/no-explicit-any
   49:63  warning  Unexpected any. Specify a different type                                                                              @typescript-eslint/no-explicit-any
   71:44  warning  Unexpected any. Specify a different type                                                                              @typescript-eslint/no-explicit-any
   71:53  warning  Unexpected any. Specify a different type                                                                              @typescript-eslint/no-explicit-any
   91:6   warning  React Hook useEffect has a missing dependency: 'loadUserFavorites'. Either include it or remove the dependency array  react-hooks/exhaustive-deps
  120:55  warning  Unexpected any. Specify a different type                                                                              @typescript-eslint/no-explicit-any
  120:66  warning  Unexpected any. Specify a different type                                                                              @typescript-eslint/no-explicit-any
  145:87  warning  Unexpected any. Specify a different type                                                                              @typescript-eslint/no-explicit-any
  215:6   warning  React Hook useCallback has a missing dependency: 'setItemLoading'. Either include it or remove the dependency array   react-hooks/exhaustive-deps
  248:86  warning  Unexpected any. Specify a different type                                                                              @typescript-eslint/no-explicit-any

/Users/<USER>/everlasting-us/src/journeys/activities/useMealIdeasSupabase.ts
   34:6  warning  React Hook useEffect has a missing dependency: 'loadMealIdeas'. Either include it or remove the dependency array    react-hooks/exhaustive-deps
   88:6  warning  React Hook useCallback has a missing dependency: 'loadMealIdeas'. Either include it or remove the dependency array  react-hooks/exhaustive-deps
  109:6  warning  React Hook useCallback has a missing dependency: 'loadMealIdeas'. Either include it or remove the dependency array  react-hooks/exhaustive-deps
  130:6  warning  React Hook useCallback has a missing dependency: 'loadMealIdeas'. Either include it or remove the dependency array  react-hooks/exhaustive-deps
  152:6  warning  React Hook useCallback has a missing dependency: 'loadMealIdeas'. Either include it or remove the dependency array  react-hooks/exhaustive-deps
  182:6  warning  React Hook useCallback has a missing dependency: 'loadMealIdeas'. Either include it or remove the dependency array  react-hooks/exhaustive-deps

/Users/<USER>/everlasting-us/src/journeys/daily/dailyQuestionsNotificationService.ts
  32:25  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

/Users/<USER>/everlasting-us/src/journeys/daily/dailyQuestionsService.ts
   82:7   warning  Unexpected console statement. Only these console methods are allowed: warn, error  no-console
   85:7   warning  Unexpected console statement. Only these console methods are allowed: warn, error  no-console
   88:80  warning  Unexpected any. Specify a different type                                           @typescript-eslint/no-explicit-any
  123:50  warning  Unexpected any. Specify a different type                                           @typescript-eslint/no-explicit-any
  131:9   warning  Unexpected console statement. Only these console methods are allowed: warn, error  no-console
  167:50  warning  Unexpected any. Specify a different type                                           @typescript-eslint/no-explicit-any
  205:50  warning  Unexpected any. Specify a different type                                           @typescript-eslint/no-explicit-any
  261:50  warning  Unexpected any. Specify a different type                                           @typescript-eslint/no-explicit-any
  290:80  warning  Unexpected any. Specify a different type                                           @typescript-eslint/no-explicit-any
  301:53  warning  Unexpected any. Specify a different type                                           @typescript-eslint/no-explicit-any
  306:56  warning  Unexpected any. Specify a different type                                           @typescript-eslint/no-explicit-any
  444:26  warning  Unexpected any. Specify a different type                                           @typescript-eslint/no-explicit-any

/Users/<USER>/everlasting-us/src/journeys/daily/streakEventService.ts
   25:28  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   42:28  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  211:52  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  284:54  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

/Users/<USER>/everlasting-us/src/journeys/daily/useDailyChallenges.ts
  110:6  warning  React Hook useEffect has a missing dependency: 'loadDailyChallenges'. Either include it or remove the dependency array  react-hooks/exhaustive-deps

/Users/<USER>/everlasting-us/src/journeys/daily/useDailyQuestions.ts
   26:29  warning  Unexpected any. Specify a different type                                           @typescript-eslint/no-explicit-any
   27:22  warning  Unexpected any. Specify a different type                                           @typescript-eslint/no-explicit-any
   28:24  warning  Unexpected any. Specify a different type                                           @typescript-eslint/no-explicit-any
   29:19  warning  Unexpected any. Specify a different type                                           @typescript-eslint/no-explicit-any
   65:3   warning  Unexpected console statement. Only these console methods are allowed: warn, error  no-console
   99:5   warning  Unexpected console statement. Only these console methods are allowed: warn, error  no-console
  102:7   warning  Unexpected console statement. Only these console methods are allowed: warn, error  no-console
  118:7   warning  Unexpected console statement. Only these console methods are allowed: warn, error  no-console
  127:7   warning  Unexpected console statement. Only these console methods are allowed: warn, error  no-console

/Users/<USER>/everlasting-us/src/journeys/daily/useGenericWeekData.ts
  66:6  warning  React Hook useEffect has a missing dependency: 'loadData'. Either include it or remove the dependency array  react-hooks/exhaustive-deps

/Users/<USER>/everlasting-us/src/journeys/daily/useWeekOneData.ts
  101:6   warning  React Hook useEffect has a missing dependency: 'loadQuestions'. Either include it or remove the dependency array  react-hooks/exhaustive-deps
  106:56  warning  Unexpected any. Specify a different type                                                                          @typescript-eslint/no-explicit-any
  184:41  warning  Unexpected any. Specify a different type                                                                          @typescript-eslint/no-explicit-any

/Users/<USER>/everlasting-us/src/journeys/memories/PhotoManager.tsx
  339:16  warning  Avoid StyleSheet.create in screens; use DS templates. If you need a new pattern, add a DS component  no-restricted-syntax

/Users/<USER>/everlasting-us/src/journeys/memories/enhancedImageStorageService.ts
  196:93  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  238:35  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

/Users/<USER>/everlasting-us/src/journeys/memories/milestoneService.ts
  342:72  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  385:46  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  385:97  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  386:48  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  386:93  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  387:36  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  387:88  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

/Users/<USER>/everlasting-us/src/journeys/memories/useMilestoneIntegration.ts
   26:35  warning  Unexpected any. Specify a different type                                                                                  @typescript-eslint/no-explicit-any
   27:15  warning  Unexpected any. Specify a different type                                                                                  @typescript-eslint/no-explicit-any
   72:6   warning  React Hook useCallback has an unnecessary dependency: 'addAchievement'. Either exclude it or remove the dependency array  react-hooks/exhaustive-deps
   79:15  warning  Unexpected any. Specify a different type                                                                                  @typescript-eslint/no-explicit-any
  162:45  warning  Unexpected any. Specify a different type                                                                                  @typescript-eslint/no-explicit-any
  186:53  warning  Unexpected any. Specify a different type                                                                                  @typescript-eslint/no-explicit-any

/Users/<USER>/everlasting-us/src/journeys/memories/useMilestones.ts
  260:6  warning  React Hook useEffect has a missing dependency: 'loadMilestones'. Either include it or remove the dependency array  react-hooks/exhaustive-deps

/Users/<USER>/everlasting-us/src/journeys/memories/useOriginStoryData.ts
  117:6   warning  React Hook useCallback has a missing dependency: 'couple'. Either include it or remove the dependency array  react-hooks/exhaustive-deps
  140:21  warning  Unexpected any. Specify a different type                                                                     @typescript-eslint/no-explicit-any
  212:39  warning  Unexpected any. Specify a different type                                                                     @typescript-eslint/no-explicit-any

/Users/<USER>/everlasting-us/src/journeys/memories/useRelationshipMilestones.ts
   30:65  warning  Unexpected any. Specify a different type                                                                                                                    @typescript-eslint/no-explicit-any
   34:43  warning  Unexpected any. Specify a different type                                                                                                                    @typescript-eslint/no-explicit-any
   36:79  warning  Unexpected any. Specify a different type                                                                                                                    @typescript-eslint/no-explicit-any
   76:6   warning  React Hook useEffect has a missing dependency: 'loadAllData'. Either include it or remove the dependency array                                              react-hooks/exhaustive-deps
   84:6   warning  React Hook useCallback has missing dependencies: 'loadMilestones', 'loadProgress', and 'loadTemplates'. Either include them or remove the dependency array  react-hooks/exhaustive-deps
  143:88  warning  Unexpected any. Specify a different type                                                                                                                    @typescript-eslint/no-explicit-any
  208:67  warning  Unexpected any. Specify a different type                                                                                                                    @typescript-eslint/no-explicit-any
  231:96  warning  Unexpected any. Specify a different type                                                                                                                    @typescript-eslint/no-explicit-any
  313:56  warning  Unexpected any. Specify a different type                                                                                                                    @typescript-eslint/no-explicit-any
  313:72  warning  Unexpected any. Specify a different type                                                                                                                    @typescript-eslint/no-explicit-any

/Users/<USER>/everlasting-us/src/journeys/memories/useTimeline.ts
  60:39  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  60:47  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  83:17  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

/Users/<USER>/everlasting-us/src/journeys/memories/useTimelineData.ts
  237:6   warning  React Hook useMemo has an unnecessary dependency: 'pointsData'. Either exclude it or remove the dependency array  react-hooks/exhaustive-deps
  241:54  warning  Unexpected any. Specify a different type                                                                          @typescript-eslint/no-explicit-any
  241:66  warning  Unexpected any. Specify a different type                                                                          @typescript-eslint/no-explicit-any

/Users/<USER>/everlasting-us/src/journeys/onboarding/AuthScreen.tsx
   63:7   warning  Unexpected console statement. Only these console methods are allowed: warn, error                    no-console
   72:7   warning  Unexpected console statement. Only these console methods are allowed: warn, error                    no-console
   90:21  warning  Unexpected any. Specify a different type                                                             @typescript-eslint/no-explicit-any
  143:7   warning  Unexpected console statement. Only these console methods are allowed: warn, error                    no-console
  150:7   warning  Unexpected console statement. Only these console methods are allowed: warn, error                    no-console
  156:21  warning  Unexpected any. Specify a different type                                                             @typescript-eslint/no-explicit-any
  199:21  warning  Unexpected any. Specify a different type                                                             @typescript-eslint/no-explicit-any
  362:16  warning  Avoid StyleSheet.create in screens; use DS templates. If you need a new pattern, add a DS component  no-restricted-syntax

/Users/<USER>/everlasting-us/src/journeys/onboarding/auth/AuthProvider.tsx
  15:19  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

/Users/<USER>/everlasting-us/src/journeys/onboarding/authPrompt.ts
  22:11  warning  Unexpected console statement. Only these console methods are allowed: warn, error  no-console

/Users/<USER>/everlasting-us/src/journeys/onboarding/useCouplePairing.ts
   87:6  warning  React Hook useEffect has missing dependencies: 'checkPairingStatus' and 'refreshCouple'. Either include them or remove the dependency array  react-hooks/exhaustive-deps
  218:6  warning  React Hook useCallback has a missing dependency: 'checkPairingStatus'. Either include it or remove the dependency array                      react-hooks/exhaustive-deps

/Users/<USER>/everlasting-us/src/journeys/onboarding/useUserProfile.ts
  34:6   warning  React Hook useEffect has a missing dependency: 'loadProfile'. Either include it or remove the dependency array  react-hooks/exhaustive-deps
  45:19  warning  Unexpected any. Specify a different type                                                                        @typescript-eslint/no-explicit-any
  47:25  warning  Unexpected any. Specify a different type                                                                        @typescript-eslint/no-explicit-any
  54:47  warning  Unexpected any. Specify a different type                                                                        @typescript-eslint/no-explicit-any

/Users/<USER>/everlasting-us/src/journeys/onboarding/userPreferencesService.ts
  13:44  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  14:36  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  15:34  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

/Users/<USER>/everlasting-us/src/journeys/planning/config.ts
  39:26  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

/Users/<USER>/everlasting-us/src/journeys/planning/useAppSettings.ts
   70:12  warning  Avoid StyleSheet.create in screens; use DS templates. If you need a new pattern, add a DS component  no-restricted-syntax
  160:44  warning  Unexpected any. Specify a different type                                                             @typescript-eslint/no-explicit-any

/Users/<USER>/everlasting-us/src/journeys/progress/eventLogger.ts
  20:28  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

/Users/<USER>/everlasting-us/src/journeys/progress/performanceOptimizationService.ts
  128:57  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  140:55  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  155:57  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  227:14  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  240:34  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  258:58  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  258:69  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  304:32  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  309:22  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  322:32  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

/Users/<USER>/everlasting-us/src/journeys/progress/useEngagementSystem.ts
   37:22  warning  Unexpected any. Specify a different type                                                                                             @typescript-eslint/no-explicit-any
   38:39  warning  Unexpected any. Specify a different type                                                                                             @typescript-eslint/no-explicit-any
   49:6   warning  React Hook useEffect has a missing dependency: 'loadEngagementData'. Either include it or remove the dependency array                react-hooks/exhaustive-deps
   56:6   warning  React Hook useEffect has a missing dependency: 'checkForNewAchievements'. Either include it or remove the dependency array           react-hooks/exhaustive-deps
   94:41  warning  Unexpected any. Specify a different type                                                                                             @typescript-eslint/no-explicit-any
  115:6   warning  React Hook useCallback has unnecessary dependencies: 'achievements' and 'level'. Either exclude them or remove the dependency array  react-hooks/exhaustive-deps
  141:55  warning  Unexpected any. Specify a different type                                                                                             @typescript-eslint/no-explicit-any
  141:67  warning  Unexpected any. Specify a different type                                                                                             @typescript-eslint/no-explicit-any

/Users/<USER>/everlasting-us/src/journeys/progress/useHomeScreen.ts
  156:9  warning  Unexpected console statement. Only these console methods are allowed: warn, error  no-console

/Users/<USER>/everlasting-us/src/journeys/progress/usePerformance.ts
   45:43  error    'args' is defined but never used. Allowed unused args must match /^_/u                                      no-unused-vars
   45:49  warning  Unexpected any. Specify a different type                                                                    @typescript-eslint/no-explicit-any
   45:59  warning  Unexpected any. Specify a different type                                                                    @typescript-eslint/no-explicit-any
   72:16  error    'prev' is defined but never used. Allowed unused args must match /^_/u                                      no-unused-vars
   72:44  error    'next' is defined but never used. Allowed unused args must match /^_/u                                      no-unused-vars
  114:27  warning  React Hook useCallback received a function whose dependencies are unknown. Pass an inline function instead  react-hooks/exhaustive-deps
  156:24  warning  Unexpected any. Specify a different type                                                                    @typescript-eslint/no-explicit-any
  170:22  warning  Unexpected any. Specify a different type                                                                    @typescript-eslint/no-explicit-any
  239:7   warning  Unexpected console statement. Only these console methods are allowed: warn, error                           no-console
  273:7   warning  Unexpected console statement. Only these console methods are allowed: warn, error                           no-console
  290:7   warning  Unexpected console statement. Only these console methods are allowed: warn, error                           no-console

/Users/<USER>/everlasting-us/src/journeys/progress/usePointsSystemSupabase.ts
   28:6   warning  React Hook useEffect has a missing dependency: 'loadUserPoints'. Either include it or remove the dependency array                                                                                             react-hooks/exhaustive-deps
   73:6   warning  React Hook useCallback has a missing dependency: 'loadUserPoints'. Either include it or remove the dependency array                                                                                           react-hooks/exhaustive-deps
   94:6   warning  React Hook useCallback has a missing dependency: 'loadUserPoints'. Either include it or remove the dependency array                                                                                           react-hooks/exhaustive-deps
  152:6   warning  React Hook useCallback has a missing dependency: 'loadUserPoints'. Either include it or remove the dependency array                                                                                           react-hooks/exhaustive-deps
  157:9   warning  The 'achievements' logical expression could make the dependencies of useCallback Hook (at line 175) change on every render. To fix this, wrap the initialization of 'achievements' in its own useMemo() Hook  react-hooks/exhaustive-deps
  157:9   warning  The 'achievements' logical expression could make the dependencies of useCallback Hook (at line 179) change on every render. To fix this, wrap the initialization of 'achievements' in its own useMemo() Hook  react-hooks/exhaustive-deps
  161:11  error    'currentLevelPoints' is assigned a value but never used. Allowed unused vars must match /^_/u                                                                                                                 no-unused-vars
  161:11  error    'currentLevelPoints' is assigned a value but never used. Allowed unused vars must match /^_/u                                                                                                                 @typescript-eslint/no-unused-vars
  183:6   warning  React Hook useCallback has a missing dependency: 'loadUserPoints'. Either include it or remove the dependency array                                                                                           react-hooks/exhaustive-deps

/Users/<USER>/everlasting-us/src/journeys/progress/useUserEvents.ts
   9:14  error    'eventName' is defined but never used. Allowed unused args must match /^_/u                                        no-unused-vars
   9:33  error    'metadata' is defined but never used. Allowed unused args must match /^_/u                                         no-unused-vars
   9:59  warning  Unexpected any. Specify a different type                                                                           @typescript-eslint/no-explicit-any
  10:14  error    'eventName' is defined but never used. Allowed unused args must match /^_/u                                        no-unused-vars
  59:6   warning  React Hook useEffect has a missing dependency: 'loadUserEvents'. Either include it or remove the dependency array  react-hooks/exhaustive-deps
  89:71  warning  Unexpected any. Specify a different type                                                                           @typescript-eslint/no-explicit-any

/Users/<USER>/everlasting-us/src/journeys/progress/userEventUtils.ts
  11:13  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

/Users/<USER>/everlasting-us/src/services/accessibility/AccessibilityManager.ts
    7:3   error    'BUTTON' is defined but never used. Allowed unused vars must match /^_/u           no-unused-vars
    8:3   error    'LINK' is defined but never used. Allowed unused vars must match /^_/u             no-unused-vars
    9:3   error    'TEXT' is defined but never used. Allowed unused vars must match /^_/u             no-unused-vars
   10:3   error    'HEADING' is defined but never used. Allowed unused vars must match /^_/u          no-unused-vars
   11:3   error    'IMAGE' is defined but never used. Allowed unused vars must match /^_/u            no-unused-vars
   12:3   error    'LIST' is defined but never used. Allowed unused vars must match /^_/u             no-unused-vars
   13:3   error    'LISTITEM' is defined but never used. Allowed unused vars must match /^_/u         no-unused-vars
   14:3   error    'TEXTBOX' is defined but never used. Allowed unused vars must match /^_/u          no-unused-vars
   15:3   error    'CHECKBOX' is defined but never used. Allowed unused vars must match /^_/u         no-unused-vars
   16:3   error    'RADIO' is defined but never used. Allowed unused vars must match /^_/u            no-unused-vars
   17:3   error    'TAB' is defined but never used. Allowed unused vars must match /^_/u              no-unused-vars
   18:3   error    'TABLIST' is defined but never used. Allowed unused vars must match /^_/u          no-unused-vars
   19:3   error    'TABPANEL' is defined but never used. Allowed unused vars must match /^_/u         no-unused-vars
   20:3   error    'DIALOG' is defined but never used. Allowed unused vars must match /^_/u           no-unused-vars
   21:3   error    'ALERT' is defined but never used. Allowed unused vars must match /^_/u            no-unused-vars
   22:3   error    'MENU' is defined but never used. Allowed unused vars must match /^_/u             no-unused-vars
   23:3   error    'MENU_ITEM' is defined but never used. Allowed unused vars must match /^_/u        no-unused-vars
  122:48  warning  Unexpected any. Specify a different type                                           @typescript-eslint/no-explicit-any
  169:5   warning  Unexpected console statement. Only these console methods are allowed: warn, error  no-console
  191:24  warning  Unexpected any. Specify a different type                                           @typescript-eslint/no-explicit-any
  205:11  warning  Unexpected any. Specify a different type                                           @typescript-eslint/no-explicit-any

/Users/<USER>/everlasting-us/src/services/auth.ts
   66:14  error    'error' is defined but never used                                                  no-unused-vars
   66:14  error    'error' is defined but never used                                                  @typescript-eslint/no-unused-vars
   94:14  error    'error' is defined but never used                                                  no-unused-vars
   94:14  error    'error' is defined but never used                                                  @typescript-eslint/no-unused-vars
  133:5   warning  Unexpected console statement. Only these console methods are allowed: warn, error  no-console
  145:24  error    'currentPassword' is defined but never used. Allowed unused args must match /^_/u  no-unused-vars
  145:24  error    'currentPassword' is defined but never used. Allowed unused args must match /^_/u  @typescript-eslint/no-unused-vars
  145:49  error    'newPassword' is defined but never used. Allowed unused args must match /^_/u      no-unused-vars
  145:49  error    'newPassword' is defined but never used. Allowed unused args must match /^_/u      @typescript-eslint/no-unused-vars
  152:5   warning  Unexpected console statement. Only these console methods are allowed: warn, error  no-console

/Users/<USER>/everlasting-us/src/services/errorHandling/ErrorManager.ts
    7:3   error    'NETWORK' is defined but never used. Allowed unused vars must match /^_/u           no-unused-vars
    8:3   error    'VALIDATION' is defined but never used. Allowed unused vars must match /^_/u        no-unused-vars
    9:3   error    'AUTHENTICATION' is defined but never used. Allowed unused vars must match /^_/u    no-unused-vars
   10:3   error    'AUTHORIZATION' is defined but never used. Allowed unused vars must match /^_/u     no-unused-vars
   11:3   error    'DATABASE' is defined but never used. Allowed unused vars must match /^_/u          no-unused-vars
   12:3   error    'BUSINESS_LOGIC' is defined but never used. Allowed unused vars must match /^_/u    no-unused-vars
   13:3   error    'EXTERNAL_SERVICE' is defined but never used. Allowed unused vars must match /^_/u  no-unused-vars
   14:3   error    'PERMISSION' is defined but never used. Allowed unused vars must match /^_/u        no-unused-vars
   15:3   error    'DATA' is defined but never used. Allowed unused vars must match /^_/u              no-unused-vars
   16:3   error    'UI' is defined but never used. Allowed unused vars must match /^_/u                no-unused-vars
   17:3   error    'SYSTEM' is defined but never used. Allowed unused vars must match /^_/u            no-unused-vars
   18:3   error    'UNKNOWN' is defined but never used. Allowed unused vars must match /^_/u           no-unused-vars
   22:3   error    'LOW' is defined but never used. Allowed unused vars must match /^_/u               no-unused-vars
   23:3   error    'MEDIUM' is defined but never used. Allowed unused vars must match /^_/u            no-unused-vars
   24:3   error    'HIGH' is defined but never used. Allowed unused vars must match /^_/u              no-unused-vars
   25:3   error    'CRITICAL' is defined but never used. Allowed unused vars must match /^_/u          no-unused-vars
   36:13  warning  Unexpected any. Specify a different type                                            @typescript-eslint/no-explicit-any
   47:13  warning  Unexpected any. Specify a different type                                            @typescript-eslint/no-explicit-any
  141:13  warning  Unexpected any. Specify a different type                                            @typescript-eslint/no-explicit-any
  159:13  warning  Unexpected any. Specify a different type                                            @typescript-eslint/no-explicit-any

/Users/<USER>/everlasting-us/src/services/imageStorageService.ts
   51:13  error  'processedImage' is assigned a value but never used. Allowed unused vars must match /^_/u  no-unused-vars
   51:13  error  'processedImage' is assigned a value but never used. Allowed unused vars must match /^_/u  @typescript-eslint/no-unused-vars
  158:5   error  'imageData' is defined but never used. Allowed unused args must match /^_/u                no-unused-vars
  158:5   error  'imageData' is defined but never used. Allowed unused args must match /^_/u                @typescript-eslint/no-unused-vars
  159:5   error  'options' is defined but never used. Allowed unused args must match /^_/u                  no-unused-vars
  159:5   error  'options' is defined but never used. Allowed unused args must match /^_/u                  @typescript-eslint/no-unused-vars

/Users/<USER>/everlasting-us/src/services/logging/EnterpriseLogger.ts
    7:3   error    'DEBUG' is defined but never used. Allowed unused vars must match /^_/u            no-unused-vars
    8:3   error    'INFO' is defined but never used. Allowed unused vars must match /^_/u             no-unused-vars
    9:3   error    'WARN' is defined but never used. Allowed unused vars must match /^_/u             no-unused-vars
   10:3   error    'ERROR' is defined but never used. Allowed unused vars must match /^_/u            no-unused-vars
   11:3   error    'FATAL' is defined but never used. Allowed unused vars must match /^_/u            no-unused-vars
   22:14  warning  Unexpected any. Specify a different type                                           @typescript-eslint/no-explicit-any
   40:14  warning  Unexpected any. Specify a different type                                           @typescript-eslint/no-explicit-any
   64:16  warning  Unexpected any. Specify a different type                                           @typescript-eslint/no-explicit-any
   92:5   warning  Unexpected console statement. Only these console methods are allowed: warn, error  no-console
  116:37  warning  Unexpected any. Specify a different type                                           @typescript-eslint/no-explicit-any
  121:36  warning  Unexpected any. Specify a different type                                           @typescript-eslint/no-explicit-any
  126:36  warning  Unexpected any. Specify a different type                                           @typescript-eslint/no-explicit-any
  131:52  warning  Unexpected any. Specify a different type                                           @typescript-eslint/no-explicit-any
  136:52  warning  Unexpected any. Specify a different type                                           @typescript-eslint/no-explicit-any
  141:77  warning  Unexpected any. Specify a different type                                           @typescript-eslint/no-explicit-any
  222:50  warning  Unexpected any. Specify a different type                                           @typescript-eslint/no-explicit-any
  226:69  warning  Unexpected any. Specify a different type                                           @typescript-eslint/no-explicit-any
  230:91  warning  Unexpected any. Specify a different type                                           @typescript-eslint/no-explicit-any

/Users/<USER>/everlasting-us/src/services/offline/OfflineManager.ts
    7:3   error    'CREATE' is defined but never used. Allowed unused vars must match /^_/u  no-unused-vars
    8:3   error    'UPDATE' is defined but never used. Allowed unused vars must match /^_/u  no-unused-vars
    9:3   error    'DELETE' is defined but never used. Allowed unused vars must match /^_/u  no-unused-vars
   10:3   error    'SYNC' is defined but never used. Allowed unused vars must match /^_/u    no-unused-vars
   16:9   warning  Unexpected any. Specify a different type                                  @typescript-eslint/no-explicit-any
   37:38  warning  Unexpected any. Specify a different type                                  @typescript-eslint/no-explicit-any
   63:38  warning  Unexpected any. Specify a different type                                  @typescript-eslint/no-explicit-any
   68:45  warning  Unexpected any. Specify a different type                                  @typescript-eslint/no-explicit-any
  105:16  error    'error' is defined but never used                                         no-unused-vars
  105:16  error    'error' is defined but never used                                         @typescript-eslint/no-unused-vars
  127:9   warning  Unexpected any. Specify a different type                                  @typescript-eslint/no-explicit-any

/Users/<USER>/everlasting-us/src/services/performanceOptimizationService.ts
   56:11  warning  Unexpected any. Specify a different type                                            @typescript-eslint/no-explicit-any
   70:5   warning  Unexpected console statement. Only these console methods are allowed: warn, error   no-console
  151:37  warning  Unexpected any. Specify a different type                                            @typescript-eslint/no-explicit-any
  203:30  warning  Unexpected any. Specify a different type                                            @typescript-eslint/no-explicit-any
  205:36  error    'orderBy' is assigned a value but never used. Allowed unused vars must match /^_/u  no-unused-vars
  205:36  error    'orderBy' is assigned a value but never used. Allowed unused vars must match /^_/u  @typescript-eslint/no-unused-vars

/Users/<USER>/everlasting-us/src/services/queryOptimizationService.ts
   16:15  error    'query' is defined but never used. Allowed unused args must match /^_/u            no-unused-vars
   17:14  error    'query' is defined but never used. Allowed unused args must match /^_/u            no-unused-vars
   23:9   warning  Unexpected any. Specify a different type                                           @typescript-eslint/no-explicit-any
   95:72  warning  Unexpected any. Specify a different type                                           @typescript-eslint/no-explicit-any
  151:50  warning  Unexpected any. Specify a different type                                           @typescript-eslint/no-explicit-any
  169:27  error    'query' is defined but never used. Allowed unused args must match /^_/u            no-unused-vars
  169:27  error    'query' is defined but never used. Allowed unused args must match /^_/u            @typescript-eslint/no-unused-vars
  184:42  warning  Unexpected any. Specify a different type                                           @typescript-eslint/no-explicit-any
  273:13  error    'result' is assigned a value but never used. Allowed unused vars must match /^_/u  no-unused-vars
  273:13  error    'result' is assigned a value but never used. Allowed unused vars must match /^_/u  @typescript-eslint/no-unused-vars
  311:13  error    'result' is assigned a value but never used. Allowed unused vars must match /^_/u  no-unused-vars
  311:13  error    'result' is assigned a value but never used. Allowed unused vars must match /^_/u  @typescript-eslint/no-unused-vars

/Users/<USER>/everlasting-us/src/services/recovery/ErrorRecoveryService.ts
  10:16  error  'error' is defined but never used. Allowed unused args must match /^_/u  no-unused-vars
  11:13  error  'error' is defined but never used. Allowed unused args must match /^_/u  no-unused-vars
  50:23  error  'error' is defined but never used. Allowed unused args must match /^_/u  no-unused-vars
  50:23  error  'error' is defined but never used. Allowed unused args must match /^_/u  @typescript-eslint/no-unused-vars
  62:23  error  'error' is defined but never used. Allowed unused args must match /^_/u  no-unused-vars
  62:23  error  'error' is defined but never used. Allowed unused args must match /^_/u  @typescript-eslint/no-unused-vars

/Users/<USER>/everlasting-us/src/services/validation/ValidationEngine.ts
    7:3   error    'EMAIL' is defined but never used. Allowed unused vars must match /^_/u            no-unused-vars
    8:3   error    'PASSWORD' is defined but never used. Allowed unused vars must match /^_/u         no-unused-vars
    9:3   error    'STRING' is defined but never used. Allowed unused vars must match /^_/u           no-unused-vars
   10:3   error    'NUMBER' is defined but never used. Allowed unused vars must match /^_/u           no-unused-vars
   11:3   error    'INTEGER' is defined but never used. Allowed unused vars must match /^_/u          no-unused-vars
   12:3   error    'BOOLEAN' is defined but never used. Allowed unused vars must match /^_/u          no-unused-vars
   13:3   error    'DATE' is defined but never used. Allowed unused vars must match /^_/u             no-unused-vars
   14:3   error    'URL' is defined but never used. Allowed unused vars must match /^_/u              no-unused-vars
   15:3   error    'PHONE' is defined but never used. Allowed unused vars must match /^_/u            no-unused-vars
   16:3   error    'ARRAY' is defined but never used. Allowed unused vars must match /^_/u            no-unused-vars
   17:3   error    'OBJECT' is defined but never used. Allowed unused vars must match /^_/u           no-unused-vars
   28:13  error    'value' is defined but never used. Allowed unused args must match /^_/u            no-unused-vars
   28:20  warning  Unexpected any. Specify a different type                                           @typescript-eslint/no-explicit-any
   38:20  warning  Unexpected any. Specify a different type                                           @typescript-eslint/no-explicit-any
   46:10  warning  Unexpected any. Specify a different type                                           @typescript-eslint/no-explicit-any
   51:36  error    Unnecessary escape character: \(                                                   no-useless-escape
   51:38  error    Unnecessary escape character: \)                                                   no-useless-escape
   54:24  warning  Unexpected any. Specify a different type                                           @typescript-eslint/no-explicit-any
   56:27  warning  Unexpected any. Specify a different type                                           @typescript-eslint/no-explicit-any
   76:47  warning  Unexpected any. Specify a different type                                           @typescript-eslint/no-explicit-any
  324:43  error    'sanitizer' is defined but never used. Allowed unused args must match /^_/u        no-unused-vars
  324:43  error    'sanitizer' is defined but never used. Allowed unused args must match /^_/u        @typescript-eslint/no-unused-vars
  324:55  error    'value' is defined but never used. Allowed unused args must match /^_/u            no-unused-vars
  324:62  warning  Unexpected any. Specify a different type                                           @typescript-eslint/no-explicit-any
  324:70  warning  Unexpected any. Specify a different type                                           @typescript-eslint/no-explicit-any
  327:5   warning  Unexpected console statement. Only these console methods are allowed: warn, error  no-console

/Users/<USER>/everlasting-us/src/shared/components/activities/ActivityRegistry.ts
  123:50  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  126:51  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

/Users/<USER>/everlasting-us/src/shared/components/activities/ChatPromptsActivity.tsx
  255:16  warning  Avoid StyleSheet.create in screens; use DS templates. If you need a new pattern, add a DS component  no-restricted-syntax

/Users/<USER>/everlasting-us/src/shared/components/activities/DateNightPlanActivity.tsx
  211:16  warning  Avoid StyleSheet.create in screens; use DS templates. If you need a new pattern, add a DS component  no-restricted-syntax

/Users/<USER>/everlasting-us/src/shared/components/activities/MatchGameActivity.tsx
  108:16  warning  Avoid StyleSheet.create in screens; use DS templates. If you need a new pattern, add a DS component  no-restricted-syntax

/Users/<USER>/everlasting-us/src/shared/components/activities/SoftStartupActivity.tsx
  229:16  warning  Avoid StyleSheet.create in screens; use DS templates. If you need a new pattern, add a DS component  no-restricted-syntax

/Users/<USER>/everlasting-us/src/shared/components/activities/WeekConfigurations.ts
  155:62  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  206:58  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

/Users/<USER>/everlasting-us/src/shared/components/activities/WouldYouRatherActivity.tsx
  110:6   warning  React Hook useCallback has a missing dependency: 'handleComplete'. Either include it or remove the dependency array  react-hooks/exhaustive-deps
  242:16  warning  Avoid StyleSheet.create in screens; use DS templates. If you need a new pattern, add a DS component                  no-restricted-syntax

/Users/<USER>/everlasting-us/src/shared/components/activities/types.ts
   15:16  error    'data' is defined but never used. Allowed unused args must match /^_/u       no-unused-vars
   17:13  error    'error' is defined but never used. Allowed unused args must match /^_/u      no-unused-vars
   19:17  warning  Unexpected any. Specify a different type                                     @typescript-eslint/no-explicit-any
   37:24  warning  Unexpected any. Specify a different type                                     @typescript-eslint/no-explicit-any
  108:14  error    'config' is defined but never used. Allowed unused args must match /^_/u     no-unused-vars
  110:17  error    'id' is defined but never used. Allowed unused args must match /^_/u         no-unused-vars
  112:29  error    'category' is defined but never used. Allowed unused args must match /^_/u   no-unused-vars
  116:17  error    'id' is defined but never used. Allowed unused args must match /^_/u         no-unused-vars
  128:20  error    'step' is defined but never used. Allowed unused args must match /^_/u       no-unused-vars
  132:24  error    'stepIndex' is defined but never used. Allowed unused args must match /^_/u  no-unused-vars
  132:43  error    'data' is defined but never used. Allowed unused args must match /^_/u       no-unused-vars
  134:13  error    'error' is defined but never used. Allowed unused args must match /^_/u      no-unused-vars
  138:32  warning  Unexpected any. Specify a different type                                     @typescript-eslint/no-explicit-any

/Users/<USER>/everlasting-us/src/shared/components/activity/ActivityContainer.tsx
   31:9   error    'width' is assigned a value but never used. Allowed unused vars must match /^_/u                                                          no-unused-vars
   31:9   error    'width' is assigned a value but never used. Allowed unused vars must match /^_/u                                                          @typescript-eslint/no-unused-vars
   96:6   warning  React Hook useEffect has missing dependencies: 'emitActivityEvent' and 'handleError'. Either include them or remove the dependency array  react-hooks/exhaustive-deps
  119:6   warning  React Hook useCallback has a missing dependency: 'emitActivityEvent'. Either include it or remove the dependency array                    react-hooks/exhaustive-deps
  137:6   warning  React Hook useCallback has a missing dependency: 'emitActivityEvent'. Either include it or remove the dependency array                    react-hooks/exhaustive-deps
  156:6   warning  React Hook useCallback has a missing dependency: 'emitActivityEvent'. Either include it or remove the dependency array                    react-hooks/exhaustive-deps
  159:93  warning  Unexpected any. Specify a different type                                                                                                  @typescript-eslint/no-explicit-any
  334:16  warning  Avoid StyleSheet.create in screens; use DS templates. If you need a new pattern, add a DS component                                       no-restricted-syntax

/Users/<USER>/everlasting-us/src/shared/components/activity/ActivityGrid.tsx
   27:21  error    'activity' is defined but never used. Allowed unused args must match /^_/u                           no-unused-vars
   29:23  error    'category' is defined but never used. Allowed unused args must match /^_/u                           no-unused-vars
  271:16  warning  Avoid StyleSheet.create in screens; use DS templates. If you need a new pattern, add a DS component  no-restricted-syntax

/Users/<USER>/everlasting-us/src/shared/components/activity/ErrorBoundary.tsx
  15:14  error    'error' is defined but never used. Allowed unused args must match /^_/u                              no-unused-vars
  15:28  error    'errorInfo' is defined but never used. Allowed unused args must match /^_/u                          no-unused-vars
  84:16  warning  Avoid StyleSheet.create in screens; use DS templates. If you need a new pattern, add a DS component  no-restricted-syntax

/Users/<USER>/everlasting-us/src/shared/components/activity/activities/LoveLanguageQuizActivity.tsx
   48:3   error    'customData' is assigned a value but never used. Allowed unused args must match /^_/u                                  no-unused-vars
   48:3   error    'customData' is assigned a value but never used. Allowed unused args must match /^_/u                                  @typescript-eslint/no-unused-vars
  218:6   warning  React Hook useCallback has a missing dependency: 'questions.length'. Either include it or remove the dependency array  react-hooks/exhaustive-deps
  221:9   error    'handleError' is assigned a value but never used. Allowed unused vars must match /^_/u                                 no-unused-vars
  221:9   error    'handleError' is assigned a value but never used. Allowed unused vars must match /^_/u                                 @typescript-eslint/no-unused-vars
  337:16  warning  Avoid StyleSheet.create in screens; use DS templates. If you need a new pattern, add a DS component                    no-restricted-syntax

/Users/<USER>/everlasting-us/src/shared/components/activity/activities/WouldYouRatherActivity.tsx
   23:9   error    'width' is assigned a value but never used. Allowed unused vars must match /^_/u                     no-unused-vars
   23:9   error    'width' is assigned a value but never used. Allowed unused vars must match /^_/u                     @typescript-eslint/no-unused-vars
   51:3   error    'customData' is assigned a value but never used. Allowed unused args must match /^_/u                no-unused-vars
   51:3   error    'customData' is assigned a value but never used. Allowed unused args must match /^_/u                @typescript-eslint/no-unused-vars
   57:10  error    'isLoading' is assigned a value but never used. Allowed unused vars must match /^_/u                 no-unused-vars
   57:10  error    'isLoading' is assigned a value but never used. Allowed unused vars must match /^_/u                 @typescript-eslint/no-unused-vars
   57:21  error    'setIsLoading' is assigned a value but never used. Allowed unused vars must match /^_/u              no-unused-vars
   57:21  error    'setIsLoading' is assigned a value but never used. Allowed unused vars must match /^_/u              @typescript-eslint/no-unused-vars
  172:9   error    'handleError' is assigned a value but never used. Allowed unused vars must match /^_/u               no-unused-vars
  172:9   error    'handleError' is assigned a value but never used. Allowed unused vars must match /^_/u               @typescript-eslint/no-unused-vars
  327:16  warning  Avoid StyleSheet.create in screens; use DS templates. If you need a new pattern, add a DS component  no-restricted-syntax

/Users/<USER>/everlasting-us/src/shared/components/common/AccessibleComponents.tsx
   38:28  error    'event' is defined but never used. Allowed unused args must match /^_/u                              no-unused-vars
   66:57  warning  Unexpected any. Specify a different type                                                             @typescript-eslint/no-explicit-any
   68:33  warning  Unexpected any. Specify a different type                                                             @typescript-eslint/no-explicit-any
   90:18  warning  Unexpected any. Specify a different type                                                             @typescript-eslint/no-explicit-any
   91:18  warning  Unexpected any. Specify a different type                                                             @typescript-eslint/no-explicit-any
  102:18  warning  Unexpected any. Specify a different type                                                             @typescript-eslint/no-explicit-any
  110:27  warning  Unexpected any. Specify a different type                                                             @typescript-eslint/no-explicit-any
  346:9   error    'safeTheme' is assigned a value but never used. Allowed unused vars must match /^_/u                 no-unused-vars
  346:9   error    'safeTheme' is assigned a value but never used. Allowed unused vars must match /^_/u                 @typescript-eslint/no-unused-vars
  398:9   error    'safeTheme' is assigned a value but never used. Allowed unused vars must match /^_/u                 no-unused-vars
  398:9   error    'safeTheme' is assigned a value but never used. Allowed unused vars must match /^_/u                 @typescript-eslint/no-unused-vars
  442:59  warning  Unexpected any. Specify a different type                                                             @typescript-eslint/no-explicit-any
  453:55  warning  Unexpected any. Specify a different type                                                             @typescript-eslint/no-explicit-any
  462:53  warning  Unexpected any. Specify a different type                                                             @typescript-eslint/no-explicit-any
  477:16  warning  Avoid StyleSheet.create in screens; use DS templates. If you need a new pattern, add a DS component  no-restricted-syntax

/Users/<USER>/everlasting-us/src/shared/components/common/AuthComponents.tsx
   23:10  error    'BlurView' is defined but never used. Allowed unused vars must match /^_/u                           no-unused-vars
   23:10  error    'BlurView' is defined but never used. Allowed unused vars must match /^_/u                           @typescript-eslint/no-unused-vars
   26:9   error    'width' is assigned a value but never used. Allowed unused vars must match /^_/u                     no-unused-vars
   26:9   error    'width' is assigned a value but never used. Allowed unused vars must match /^_/u                     @typescript-eslint/no-unused-vars
   45:11  warning  Unexpected any. Specify a different type                                                             @typescript-eslint/no-explicit-any
   98:11  warning  Unexpected any. Specify a different type                                                             @typescript-eslint/no-explicit-any
  130:18  error    'text' is defined but never used. Allowed unused args must match /^_/u                               no-unused-vars
  146:11  warning  Unexpected any. Specify a different type                                                             @typescript-eslint/no-explicit-any
  227:11  warning  Unexpected any. Specify a different type                                                             @typescript-eslint/no-explicit-any
  309:11  warning  Unexpected any. Specify a different type                                                             @typescript-eslint/no-explicit-any
  349:11  warning  Unexpected any. Specify a different type                                                             @typescript-eslint/no-explicit-any
  393:11  warning  Unexpected any. Specify a different type                                                             @typescript-eslint/no-explicit-any
  436:11  warning  Unexpected any. Specify a different type                                                             @typescript-eslint/no-explicit-any
  498:16  warning  Avoid StyleSheet.create in screens; use DS templates. If you need a new pattern, add a DS component  no-restricted-syntax

/Users/<USER>/everlasting-us/src/shared/components/common/CommonComponents.tsx
   12:11  warning  Unexpected any. Specify a different type                                                             @typescript-eslint/no-explicit-any
   28:11  warning  Unexpected any. Specify a different type                                                             @typescript-eslint/no-explicit-any
   56:18  error    'text' is defined but never used. Allowed unused args must match /^_/u                               no-unused-vars
   60:11  warning  Unexpected any. Specify a different type                                                             @typescript-eslint/no-explicit-any
   91:11  warning  Unexpected any. Specify a different type                                                             @typescript-eslint/no-explicit-any
  113:11  warning  Unexpected any. Specify a different type                                                             @typescript-eslint/no-explicit-any
  129:11  warning  Unexpected any. Specify a different type                                                             @typescript-eslint/no-explicit-any
  160:11  warning  Unexpected any. Specify a different type                                                             @typescript-eslint/no-explicit-any
  263:18  error    'text' is defined but never used. Allowed unused args must match /^_/u                               no-unused-vars
  301:11  warning  Unexpected any. Specify a different type                                                             @typescript-eslint/no-explicit-any
  319:16  warning  Avoid StyleSheet.create in screens; use DS templates. If you need a new pattern, add a DS component  no-restricted-syntax

/Users/<USER>/everlasting-us/src/shared/components/common/CoupleComponents.tsx
  131:16  warning  Avoid StyleSheet.create in screens; use DS templates. If you need a new pattern, add a DS component  no-restricted-syntax

/Users/<USER>/everlasting-us/src/shared/components/common/DailyQuestionCard.tsx
   21:11  warning  Unexpected any. Specify a different type                                                             @typescript-eslint/no-explicit-any
   51:10  error    'isSubmitting' is assigned a value but never used. Allowed unused vars must match /^_/u              no-unused-vars
   51:10  error    'isSubmitting' is assigned a value but never used. Allowed unused vars must match /^_/u              @typescript-eslint/no-unused-vars
   51:24  error    'setIsSubmitting' is assigned a value but never used. Allowed unused vars must match /^_/u           no-unused-vars
   51:24  error    'setIsSubmitting' is assigned a value but never used. Allowed unused vars must match /^_/u           @typescript-eslint/no-unused-vars
  230:16  warning  Avoid StyleSheet.create in screens; use DS templates. If you need a new pattern, add a DS component  no-restricted-syntax

/Users/<USER>/everlasting-us/src/shared/components/common/DailyQuestionsStreakCard.tsx
   21:11  warning  Unexpected any. Specify a different type                                                             @typescript-eslint/no-explicit-any
  238:16  warning  Avoid StyleSheet.create in screens; use DS templates. If you need a new pattern, add a DS component  no-restricted-syntax

/Users/<USER>/everlasting-us/src/shared/components/common/EnhancedComponents.tsx
   41:11  warning  Unexpected any. Specify a different type                                                             @typescript-eslint/no-explicit-any
  113:58  warning  Unexpected any. Specify a different type                                                             @typescript-eslint/no-explicit-any
  145:11  warning  Unexpected any. Specify a different type                                                             @typescript-eslint/no-explicit-any
  160:3   error    'variant' is assigned a value but never used. Allowed unused args must match /^_/u                   no-unused-vars
  160:3   error    'variant' is assigned a value but never used. Allowed unused args must match /^_/u                   @typescript-eslint/no-unused-vars
  227:11  warning  Unexpected any. Specify a different type                                                             @typescript-eslint/no-explicit-any
  276:18  error    'week' is defined but never used. Allowed unused args must match /^_/u                               no-unused-vars
  278:11  warning  Unexpected any. Specify a different type                                                             @typescript-eslint/no-explicit-any
  336:11  warning  Unexpected any. Specify a different type                                                             @typescript-eslint/no-explicit-any
  398:18  error    'text' is defined but never used. Allowed unused args must match /^_/u                               no-unused-vars
  414:11  warning  Unexpected any. Specify a different type                                                             @typescript-eslint/no-explicit-any
  416:20  warning  Unexpected any. Specify a different type                                                             @typescript-eslint/no-explicit-any
  485:11  warning  Unexpected any. Specify a different type                                                             @typescript-eslint/no-explicit-any
  555:19  error    'step' is defined but never used. Allowed unused args must match /^_/u                               no-unused-vars
  557:11  warning  Unexpected any. Specify a different type                                                             @typescript-eslint/no-explicit-any
  565:3   error    'totalSteps' is defined but never used. Allowed unused args must match /^_/u                         no-unused-vars
  565:3   error    'totalSteps' is defined but never used. Allowed unused args must match /^_/u                         @typescript-eslint/no-unused-vars
  607:16  warning  Avoid StyleSheet.create in screens; use DS templates. If you need a new pattern, add a DS component  no-restricted-syntax

/Users/<USER>/everlasting-us/src/shared/components/common/EnhancedWeekScreenLayout.tsx
   13:3   error    'Alert' is defined but never used. Allowed unused vars must match /^_/u                              no-unused-vars
   13:3   error    'Alert' is defined but never used. Allowed unused vars must match /^_/u                              @typescript-eslint/no-unused-vars
   22:32  error    'router' is defined but never used. Allowed unused vars must match /^_/u                             no-unused-vars
   22:32  error    'router' is defined but never used. Allowed unused vars must match /^_/u                             @typescript-eslint/no-unused-vars
   23:34  error    'Save' is defined but never used. Allowed unused vars must match /^_/u                               no-unused-vars
   23:34  error    'Save' is defined but never used. Allowed unused vars must match /^_/u                               @typescript-eslint/no-unused-vars
   52:67  warning  Unexpected any. Specify a different type                                                             @typescript-eslint/no-explicit-any
  231:16  warning  Avoid StyleSheet.create in screens; use DS templates. If you need a new pattern, add a DS component  no-restricted-syntax

/Users/<USER>/everlasting-us/src/shared/components/common/ErrorBoundary.tsx
   24:14  error    'error' is defined but never used. Allowed unused args must match /^_/u                              no-unused-vars
   24:28  error    'errorInfo' is defined but never used. Allowed unused args must match /^_/u                          no-unused-vars
  144:16  warning  Avoid StyleSheet.create in screens; use DS templates. If you need a new pattern, add a DS component  no-restricted-syntax
  210:14  error    'error' is defined but never used. Allowed unused args must match /^_/u                              no-unused-vars
  210:28  error    'errorInfo' is defined but never used. Allowed unused args must match /^_/u                          no-unused-vars
  212:27  warning  Unexpected any. Specify a different type                                                             @typescript-eslint/no-explicit-any

/Users/<USER>/everlasting-us/src/shared/components/common/FavoriteButton.tsx
  21:15  error    'isFavorited' is defined but never used. Allowed unused args must match /^_/u                        no-unused-vars
  72:16  warning  Avoid StyleSheet.create in screens; use DS templates. If you need a new pattern, add a DS component  no-restricted-syntax

/Users/<USER>/everlasting-us/src/shared/components/common/FormComponents.tsx
   18:3   error    'Alert' is defined but never used. Allowed unused vars must match /^_/u                              no-unused-vars
   18:3   error    'Alert' is defined but never used. Allowed unused vars must match /^_/u                              @typescript-eslint/no-unused-vars
   31:18  error    'text' is defined but never used. Allowed unused args must match /^_/u                               no-unused-vars
   41:11  warning  Unexpected any. Specify a different type                                                             @typescript-eslint/no-explicit-any
  100:11  warning  Unexpected any. Specify a different type                                                             @typescript-eslint/no-explicit-any
  183:11  warning  Unexpected any. Specify a different type                                                             @typescript-eslint/no-explicit-any
  216:11  warning  Unexpected any. Specify a different type                                                             @typescript-eslint/no-explicit-any
  233:16  warning  Avoid StyleSheet.create in screens; use DS templates. If you need a new pattern, add a DS component  no-restricted-syntax

/Users/<USER>/everlasting-us/src/shared/components/common/HeartToggle.tsx
   6:3   error    'AccessibilityInfo' is defined but never used. Allowed unused vars must match /^_/u                  no-unused-vars
   6:3   error    'AccessibilityInfo' is defined but never used. Allowed unused vars must match /^_/u                  @typescript-eslint/no-unused-vars
  15:14  error    'isFavorited' is defined but never used. Allowed unused args must match /^_/u                        no-unused-vars
  17:15  error    'isFavorited' is defined but never used. Allowed unused args must match /^_/u                        no-unused-vars
  95:16  warning  Avoid StyleSheet.create in screens; use DS templates. If you need a new pattern, add a DS component  no-restricted-syntax

/Users/<USER>/everlasting-us/src/shared/components/common/Input.tsx
  32:18  error  'text' is defined but never used. Allowed unused args must match /^_/u  no-unused-vars

/Users/<USER>/everlasting-us/src/shared/components/common/LayoutComponents.tsx
   39:11  warning  Unexpected any. Specify a different type                                                             @typescript-eslint/no-explicit-any
   78:11  warning  Unexpected any. Specify a different type                                                             @typescript-eslint/no-explicit-any
  108:11  warning  Unexpected any. Specify a different type                                                             @typescript-eslint/no-explicit-any
  150:11  warning  Unexpected any. Specify a different type                                                             @typescript-eslint/no-explicit-any
  164:45  error    'index' is defined but never used. Allowed unused args must match /^_/u                              no-unused-vars
  164:45  error    'index' is defined but never used. Allowed unused args must match /^_/u                              @typescript-eslint/no-unused-vars
  186:11  warning  Unexpected any. Specify a different type                                                             @typescript-eslint/no-explicit-any
  227:11  warning  Unexpected any. Specify a different type                                                             @typescript-eslint/no-explicit-any
  268:11  warning  Unexpected any. Specify a different type                                                             @typescript-eslint/no-explicit-any
  305:11  warning  Unexpected any. Specify a different type                                                             @typescript-eslint/no-explicit-any
  330:11  warning  Unexpected any. Specify a different type                                                             @typescript-eslint/no-explicit-any
  359:16  warning  Avoid StyleSheet.create in screens; use DS templates. If you need a new pattern, add a DS component  no-restricted-syntax

/Users/<USER>/everlasting-us/src/shared/components/common/LazyWrapper.tsx
   25:11  warning  Unexpected any. Specify a different type                                                             @typescript-eslint/no-explicit-any
   37:3   error    'threshold' is assigned a value but never used. Allowed unused args must match /^_/u                 no-unused-vars
   37:3   error    'threshold' is assigned a value but never used. Allowed unused args must match /^_/u                 @typescript-eslint/no-unused-vars
   38:3   error    'rootMargin' is assigned a value but never used. Allowed unused args must match /^_/u                no-unused-vars
   38:3   error    'rootMargin' is assigned a value but never used. Allowed unused args must match /^_/u                @typescript-eslint/no-unused-vars
   81:18  error    'isIntersecting' is defined but never used. Allowed unused args must match /^_/u                     no-unused-vars
   96:22  warning  Unexpected any. Specify a different type                                                             @typescript-eslint/no-explicit-any
  146:11  warning  Unexpected any. Specify a different type                                                             @typescript-eslint/no-explicit-any
  154:3   error    'source' is defined but never used. Allowed unused args must match /^_/u                             no-unused-vars
  154:3   error    'source' is defined but never used. Allowed unused args must match /^_/u                             @typescript-eslint/no-unused-vars
  164:9   error    'handleLoad' is assigned a value but never used. Allowed unused vars must match /^_/u                no-unused-vars
  164:9   error    'handleLoad' is assigned a value but never used. Allowed unused vars must match /^_/u                @typescript-eslint/no-unused-vars
  169:9   error    'handleError' is assigned a value but never used. Allowed unused vars must match /^_/u               no-unused-vars
  169:9   error    'handleError' is assigned a value but never used. Allowed unused vars must match /^_/u               @typescript-eslint/no-unused-vars
  221:27  warning  Unexpected any. Specify a different type                                                             @typescript-eslint/no-explicit-any
  240:16  warning  Avoid StyleSheet.create in screens; use DS templates. If you need a new pattern, add a DS component  no-restricted-syntax

/Users/<USER>/everlasting-us/src/shared/components/common/StoryComponents.tsx
   18:3   error    'ScrollView' is defined but never used. Allowed unused vars must match /^_/u                         no-unused-vars
   18:3   error    'ScrollView' is defined but never used. Allowed unused vars must match /^_/u                         @typescript-eslint/no-unused-vars
   44:11  warning  Unexpected any. Specify a different type                                                             @typescript-eslint/no-explicit-any
   71:20  error    'field' is defined but never used. Allowed unused args must match /^_/u                              no-unused-vars
   71:27  warning  Unexpected any. Specify a different type                                                             @typescript-eslint/no-explicit-any
   71:32  error    'value' is defined but never used. Allowed unused args must match /^_/u                              no-unused-vars
   72:17  error    'field' is defined but never used. Allowed unused args must match /^_/u                              no-unused-vars
   72:24  warning  Unexpected any. Specify a different type                                                             @typescript-eslint/no-explicit-any
   72:29  error    'photo' is defined but never used. Allowed unused args must match /^_/u                              no-unused-vars
   72:36  warning  Unexpected any. Specify a different type                                                             @typescript-eslint/no-explicit-any
   73:20  error    'field' is defined but never used. Allowed unused args must match /^_/u                              no-unused-vars
   73:27  warning  Unexpected any. Specify a different type                                                             @typescript-eslint/no-explicit-any
   73:32  error    'photoId' is defined but never used. Allowed unused args must match /^_/u                            no-unused-vars
   78:19  error    'field' is defined but never used. Allowed unused args must match /^_/u                              no-unused-vars
   78:26  warning  Unexpected any. Specify a different type                                                             @typescript-eslint/no-explicit-any
   78:31  error    'value' is defined but never used. Allowed unused args must match /^_/u                              no-unused-vars
   79:16  error    'field' is defined but never used. Allowed unused args must match /^_/u                              no-unused-vars
   79:23  warning  Unexpected any. Specify a different type                                                             @typescript-eslint/no-explicit-any
   79:28  error    'photo' is defined but never used. Allowed unused args must match /^_/u                              no-unused-vars
   79:35  warning  Unexpected any. Specify a different type                                                             @typescript-eslint/no-explicit-any
   80:19  error    'field' is defined but never used. Allowed unused args must match /^_/u                              no-unused-vars
   80:26  warning  Unexpected any. Specify a different type                                                             @typescript-eslint/no-explicit-any
   80:31  error    'photoId' is defined but never used. Allowed unused args must match /^_/u                            no-unused-vars
  220:33  warning  Unexpected any. Specify a different type                                                             @typescript-eslint/no-explicit-any
  229:38  warning  Unexpected any. Specify a different type                                                             @typescript-eslint/no-explicit-any
  288:29  warning  Unexpected any. Specify a different type                                                             @typescript-eslint/no-explicit-any
  297:34  warning  Unexpected any. Specify a different type                                                             @typescript-eslint/no-explicit-any
  343:3   error    'totalSections' is defined but never used. Allowed unused args must match /^_/u                      no-unused-vars
  343:3   error    'totalSections' is defined but never used. Allowed unused args must match /^_/u                      @typescript-eslint/no-unused-vars
  347:3   error    'onSkip' is defined but never used. Allowed unused args must match /^_/u                             no-unused-vars
  347:3   error    'onSkip' is defined but never used. Allowed unused args must match /^_/u                             @typescript-eslint/no-unused-vars
  422:22  error    'mode' is assigned a value but never used. Allowed unused args must match /^_/u                      no-unused-vars
  422:22  error    'mode' is assigned a value but never used. Allowed unused args must match /^_/u                      @typescript-eslint/no-unused-vars
  453:16  warning  Avoid StyleSheet.create in screens; use DS templates. If you need a new pattern, add a DS component  no-restricted-syntax

/Users/<USER>/everlasting-us/src/shared/components/common/StoryData.ts
   20:52  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  155:53  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  167:48  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

/Users/<USER>/everlasting-us/src/shared/components/common/StreakDisplay.tsx
   61:5   error    'loadingActivity' is assigned a value but never used. Allowed unused vars must match /^_/u           no-unused-vars
   61:5   error    'loadingActivity' is assigned a value but never used. Allowed unused vars must match /^_/u           @typescript-eslint/no-unused-vars
   62:5   error    'activityError' is assigned a value but never used. Allowed unused vars must match /^_/u             no-unused-vars
   62:5   error    'activityError' is assigned a value but never used. Allowed unused vars must match /^_/u             @typescript-eslint/no-unused-vars
  170:16  warning  Avoid StyleSheet.create in screens; use DS templates. If you need a new pattern, add a DS component  no-restricted-syntax

/Users/<USER>/everlasting-us/src/shared/components/common/SurpriseMe.tsx
   19:3   error    'Gift' is defined but never used. Allowed unused vars must match /^_/u                               no-unused-vars
   19:3   error    'Gift' is defined but never used. Allowed unused vars must match /^_/u                               @typescript-eslint/no-unused-vars
   24:9   error    'width' is assigned a value but never used. Allowed unused vars must match /^_/u                     no-unused-vars
   24:9   error    'width' is assigned a value but never used. Allowed unused vars must match /^_/u                     @typescript-eslint/no-unused-vars
   49:19  error    'item' is defined but never used. Allowed unused args must match /^_/u                               no-unused-vars
   50:17  error    'item' is defined but never used. Allowed unused args must match /^_/u                               no-unused-vars
   51:17  error    'item' is defined but never used. Allowed unused args must match /^_/u                               no-unused-vars
   60:11  warning  Unexpected any. Specify a different type                                                             @typescript-eslint/no-explicit-any
   69:3   error    'title' is assigned a value but never used. Allowed unused args must match /^_/u                     no-unused-vars
   69:3   error    'title' is assigned a value but never used. Allowed unused args must match /^_/u                     @typescript-eslint/no-unused-vars
   75:3   error    'showLoadingState' is assigned a value but never used. Allowed unused args must match /^_/u          no-unused-vars
   75:3   error    'showLoadingState' is assigned a value but never used. Allowed unused args must match /^_/u          @typescript-eslint/no-unused-vars
  152:51  error    'index' is defined but never used. Allowed unused args must match /^_/u                              no-unused-vars
  152:51  error    'index' is defined but never used. Allowed unused args must match /^_/u                              @typescript-eslint/no-unused-vars
  154:11  error    'isMeal' is assigned a value but never used. Allowed unused vars must match /^_/u                    no-unused-vars
  154:11  error    'isMeal' is assigned a value but never used. Allowed unused vars must match /^_/u                    @typescript-eslint/no-unused-vars
  309:16  warning  Avoid StyleSheet.create in screens; use DS templates. If you need a new pattern, add a DS component  no-restricted-syntax

/Users/<USER>/everlasting-us/src/shared/components/common/Templates.tsx
   51:55  warning  Unexpected any. Specify a different type                                                             @typescript-eslint/no-explicit-any
   59:10  warning  Unexpected any. Specify a different type                                                             @typescript-eslint/no-explicit-any
   64:86  warning  Unexpected any. Specify a different type                                                             @typescript-eslint/no-explicit-any
   70:42  warning  Unexpected any. Specify a different type                                                             @typescript-eslint/no-explicit-any
   83:61  error    'v' is defined but never used. Allowed unused args must match /^_/u                                  no-unused-vars
  123:9   error    'safeTheme' is assigned a value but never used. Allowed unused vars must match /^_/u                 no-unused-vars
  123:9   error    'safeTheme' is assigned a value but never used. Allowed unused vars must match /^_/u                 @typescript-eslint/no-unused-vars
  198:84  error    'v' is defined but never used. Allowed unused args must match /^_/u                                  no-unused-vars
  216:16  warning  Avoid StyleSheet.create in screens; use DS templates. If you need a new pattern, add a DS component  no-restricted-syntax

/Users/<USER>/everlasting-us/src/shared/components/common/ThemeProvider.tsx
  25:18  error    'mode' is defined but never used. Allowed unused args must match /^_/u  no-unused-vars
  64:10  warning  Unexpected any. Specify a different type                                @typescript-eslint/no-explicit-any

/Users/<USER>/everlasting-us/src/shared/components/common/Toast.tsx
   36:15  error    'id' is defined but never used. Allowed unused args must match /^_/u                                                                                            no-unused-vars
   79:6   warning  React Hook useEffect has missing dependencies: 'duration', 'handleDismiss', 'opacityAnim', and 'slideAnim'. Either include them or remove the dependency array  react-hooks/exhaustive-deps
  182:15  error    'id' is defined but never used. Allowed unused args must match /^_/u                                                                                            no-unused-vars
  213:16  warning  Avoid StyleSheet.create in screens; use DS templates. If you need a new pattern, add a DS component                                                             no-restricted-syntax

/Users/<USER>/everlasting-us/src/shared/components/common/TopTabs.tsx
  15:14  error    'key' is defined but never used. Allowed unused args must match /^_/u                                no-unused-vars
  56:51  warning  Unexpected any. Specify a different type                                                             @typescript-eslint/no-explicit-any
  79:16  warning  Avoid StyleSheet.create in screens; use DS templates. If you need a new pattern, add a DS component  no-restricted-syntax

/Users/<USER>/everlasting-us/src/shared/components/common/WeekScreenLayout.tsx
   27:9   error    'width' is assigned a value but never used. Allowed unused vars must match /^_/u                     no-unused-vars
   27:9   error    'width' is assigned a value but never used. Allowed unused vars must match /^_/u                     @typescript-eslint/no-unused-vars
   45:15  warning  Unexpected any. Specify a different type                                                             @typescript-eslint/no-explicit-any
   49:20  error    'step' is defined but never used. Allowed unused args must match /^_/u                               no-unused-vars
   53:24  error    'activityId' is defined but never used. Allowed unused args must match /^_/u                         no-unused-vars
   53:44  error    'data' is defined but never used. Allowed unused args must match /^_/u                               no-unused-vars
   53:50  warning  Unexpected any. Specify a different type                                                             @typescript-eslint/no-explicit-any
   55:13  error    'error' is defined but never used. Allowed unused args must match /^_/u                              no-unused-vars
   59:32  warning  Unexpected any. Specify a different type                                                             @typescript-eslint/no-explicit-any
   65:26  error    'index' is defined but never used. Allowed unused args must match /^_/u                              no-unused-vars
   92:3   error    'onActivityComplete' is defined but never used. Allowed unused args must match /^_/u                 no-unused-vars
   92:3   error    'onActivityComplete' is defined but never used. Allowed unused args must match /^_/u                 @typescript-eslint/no-unused-vars
   93:3   error    'onError' is defined but never used. Allowed unused args must match /^_/u                            no-unused-vars
   93:3   error    'onError' is defined but never used. Allowed unused args must match /^_/u                            @typescript-eslint/no-unused-vars
   94:3   error    'onNavigateBack' is defined but never used. Allowed unused args must match /^_/u                     no-unused-vars
   94:3   error    'onNavigateBack' is defined but never used. Allowed unused args must match /^_/u                     @typescript-eslint/no-unused-vars
  107:10  error    'activityData' is assigned a value but never used. Allowed unused vars must match /^_/u              no-unused-vars
  107:10  error    'activityData' is assigned a value but never used. Allowed unused vars must match /^_/u              @typescript-eslint/no-unused-vars
  107:24  error    'setActivityData' is assigned a value but never used. Allowed unused vars must match /^_/u           no-unused-vars
  107:24  error    'setActivityData' is assigned a value but never used. Allowed unused vars must match /^_/u           @typescript-eslint/no-unused-vars
  107:67  warning  Unexpected any. Specify a different type                                                             @typescript-eslint/no-explicit-any
  122:13  error    'result' is assigned a value but never used. Allowed unused vars must match /^_/u                    no-unused-vars
  122:13  error    'result' is assigned a value but never used. Allowed unused vars must match /^_/u                    @typescript-eslint/no-unused-vars
  137:15  error    'finalResult' is assigned a value but never used. Allowed unused vars must match /^_/u               no-unused-vars
  137:15  error    'finalResult' is assigned a value but never used. Allowed unused vars must match /^_/u               @typescript-eslint/no-unused-vars
  146:14  error    'error' is defined but never used                                                                    no-unused-vars
  146:14  error    'error' is defined but never used                                                                    @typescript-eslint/no-unused-vars
  246:16  warning  Avoid StyleSheet.create in screens; use DS templates. If you need a new pattern, add a DS component  no-restricted-syntax

/Users/<USER>/everlasting-us/src/shared/components/features/MilestoneForm.tsx
  12:13  error    'milestoneId' is defined but never used. Allowed unused args must match /^_/u                              no-unused-vars
  12:34  error    'data' is defined but never used. Allowed unused args must match /^_/u                                     no-unused-vars
  12:55  warning  Unexpected any. Specify a different type                                                                   @typescript-eslint/no-explicit-any
  27:59  warning  Unexpected any. Specify a different type                                                                   @typescript-eslint/no-explicit-any
  52:6   warning  React Hook useEffect has a missing dependency: 'errors'. Either include it or remove the dependency array  react-hooks/exhaustive-deps
  54:55  warning  Unexpected any. Specify a different type                                                                   @typescript-eslint/no-explicit-any

/Users/<USER>/everlasting-us/src/shared/components/features/MilestoneFormField.tsx
   10:29  warning  Unexpected any. Specify a different type                                                      @typescript-eslint/no-explicit-any
   10:39  error    'photos' is defined but never used. Allowed unused args must match /^_/u                      no-unused-vars
   10:39  error    'photos' is defined but never used. Allowed unused args must match /^_/u                      @typescript-eslint/no-unused-vars
   10:47  error    'onPhotosChange' is defined but never used. Allowed unused args must match /^_/u              no-unused-vars
   10:47  error    'onPhotosChange' is defined but never used. Allowed unused args must match /^_/u              @typescript-eslint/no-unused-vars
   10:63  error    'maxPhotos' is defined but never used. Allowed unused args must match /^_/u                   no-unused-vars
   10:63  error    'maxPhotos' is defined but never used. Allowed unused args must match /^_/u                   @typescript-eslint/no-unused-vars
   10:74  error    'disabled' is defined but never used. Allowed unused args must match /^_/u                    no-unused-vars
   10:74  error    'disabled' is defined but never used. Allowed unused args must match /^_/u                    @typescript-eslint/no-unused-vars
   14:26  warning  Unexpected any. Specify a different type                                                      @typescript-eslint/no-explicit-any
   31:10  warning  Unexpected any. Specify a different type                                                      @typescript-eslint/no-explicit-any
   32:14  error    'value' is defined but never used. Allowed unused args must match /^_/u                       no-unused-vars
   32:21  warning  Unexpected any. Specify a different type                                                      @typescript-eslint/no-explicit-any
  241:14  error    'value' is defined but never used. Allowed unused args must match /^_/u                       no-unused-vars
  302:14  error    'value' is defined but never used. Allowed unused args must match /^_/u                       no-unused-vars
  305:36  error    'error' is defined but never used. Allowed unused args must match /^_/u                       no-unused-vars
  305:36  error    'error' is defined but never used. Allowed unused args must match /^_/u                       @typescript-eslint/no-unused-vars
  306:9   error    'handlePhotoAdd' is assigned a value but never used. Allowed unused vars must match /^_/u     no-unused-vars
  306:9   error    'handlePhotoAdd' is assigned a value but never used. Allowed unused vars must match /^_/u     @typescript-eslint/no-unused-vars
  310:9   error    'handlePhotoRemove' is assigned a value but never used. Allowed unused vars must match /^_/u  no-unused-vars
  310:9   error    'handlePhotoRemove' is assigned a value but never used. Allowed unused vars must match /^_/u  @typescript-eslint/no-unused-vars
  328:14  error    'value' is defined but never used. Allowed unused args must match /^_/u                       no-unused-vars
  331:45  error    'error' is defined but never used. Allowed unused args must match /^_/u                       no-unused-vars
  331:45  error    'error' is defined but never used. Allowed unused args must match /^_/u                       @typescript-eslint/no-unused-vars

/Users/<USER>/everlasting-us/src/shared/components/features/MilestoneList.tsx
  11:22  error    'milestone' is defined but never used. Allowed unused args must match /^_/u  no-unused-vars
  11:50  error    'template' is defined but never used. Allowed unused args must match /^_/u   no-unused-vars
  35:99  warning  Unexpected any. Specify a different type                                     @typescript-eslint/no-explicit-any

/Users/<USER>/everlasting-us/src/shared/components/features/PointsDisplay.tsx
   78:3   error    'levelProgress' is assigned a value but never used. Allowed unused args must match /^_/u                                                                                                                                                                                                                                  no-unused-vars
   78:3   error    'levelProgress' is assigned a value but never used. Allowed unused args must match /^_/u                                                                                                                                                                                                                                  @typescript-eslint/no-unused-vars
  195:6   warning  React Hook useEffect has missing dependencies: 'confettiAnim', 'onCelebrationComplete', 'opacityAnim', 'scaleAnim', and 'slideAnim'. Either include them or remove the dependency array. If 'onCelebrationComplete' changes too often, find the parent component that defines it and wrap that definition in useCallback  react-hooks/exhaustive-deps
  349:16  warning  Avoid StyleSheet.create in screens; use DS templates. If you need a new pattern, add a DS component                                                                                                                                                                                                                       no-restricted-syntax

/Users/<USER>/everlasting-us/src/shared/components/game/GameContainer.tsx
   31:9   error    'width' is assigned a value but never used. Allowed unused vars must match /^_/u                                                          no-unused-vars
   31:9   error    'width' is assigned a value but never used. Allowed unused vars must match /^_/u                                                          @typescript-eslint/no-unused-vars
   96:6   warning  React Hook useEffect has missing dependencies: 'emitActivityEvent' and 'handleError'. Either include them or remove the dependency array  react-hooks/exhaustive-deps
  119:6   warning  React Hook useCallback has a missing dependency: 'emitActivityEvent'. Either include it or remove the dependency array                    react-hooks/exhaustive-deps
  137:6   warning  React Hook useCallback has a missing dependency: 'emitActivityEvent'. Either include it or remove the dependency array                    react-hooks/exhaustive-deps
  156:6   warning  React Hook useCallback has a missing dependency: 'emitActivityEvent'. Either include it or remove the dependency array                    react-hooks/exhaustive-deps
  159:93  warning  Unexpected any. Specify a different type                                                                                                  @typescript-eslint/no-explicit-any
  334:16  warning  Avoid StyleSheet.create in screens; use DS templates. If you need a new pattern, add a DS component                                       no-restricted-syntax

/Users/<USER>/everlasting-us/src/shared/components/layout/ErrorHandler.tsx
   30:14  error    'error' is defined but never used. Allowed unused args must match /^_/u                              no-unused-vars
   30:28  error    'errorInfo' is defined but never used. Allowed unused args must match /^_/u                          no-unused-vars
   65:37  warning  Unexpected any. Specify a different type                                                             @typescript-eslint/no-explicit-any
  365:16  warning  Avoid StyleSheet.create in screens; use DS templates. If you need a new pattern, add a DS component  no-restricted-syntax

/Users/<USER>/everlasting-us/src/shared/components/layout/HamburgerMenu.tsx
   15:15  warning  Unexpected any. Specify a different type                                                             @typescript-eslint/no-explicit-any
  232:16  warning  Avoid StyleSheet.create in screens; use DS templates. If you need a new pattern, add a DS component  no-restricted-syntax

/Users/<USER>/everlasting-us/src/shared/components/layout/LoadingStateManager.tsx
   16:3   error    'IDLE' is defined but never used. Allowed unused vars must match /^_/u                                                         no-unused-vars
   17:3   error    'LOADING' is defined but never used. Allowed unused vars must match /^_/u                                                      no-unused-vars
   18:3   error    'SUCCESS' is defined but never used. Allowed unused vars must match /^_/u                                                      no-unused-vars
   19:3   error    'ERROR' is defined but never used. Allowed unused vars must match /^_/u                                                        no-unused-vars
   20:3   error    'REFRESHING' is defined but never used. Allowed unused vars must match /^_/u                                                   no-unused-vars
   21:3   error    'LOADING_MORE' is defined but never used. Allowed unused vars must match /^_/u                                                 no-unused-vars
   25:3   error    'SPINNER' is defined but never used. Allowed unused vars must match /^_/u                                                      no-unused-vars
   26:3   error    'SKELETON' is defined but never used. Allowed unused vars must match /^_/u                                                     no-unused-vars
   27:3   error    'PROGRESS' is defined but never used. Allowed unused vars must match /^_/u                                                     no-unused-vars
   28:3   error    'SHIMMER' is defined but never used. Allowed unused vars must match /^_/u                                                      no-unused-vars
   29:3   error    'PULSE' is defined but never used. Allowed unused vars must match /^_/u                                                        no-unused-vars
   30:3   error    'DOTS' is defined but never used. Allowed unused vars must match /^_/u                                                         no-unused-vars
   57:16  error    'key' is defined but never used. Allowed unused args must match /^_/u                                                          no-unused-vars
   57:29  error    'config' is defined but never used. Allowed unused args must match /^_/u                                                       no-unused-vars
   58:16  error    'key' is defined but never used. Allowed unused args must match /^_/u                                                          no-unused-vars
   58:29  error    'data' is defined but never used. Allowed unused args must match /^_/u                                                         no-unused-vars
   59:14  error    'key' is defined but never used. Allowed unused args must match /^_/u                                                          no-unused-vars
   59:27  error    'error' is defined but never used. Allowed unused args must match /^_/u                                                        no-unused-vars
   60:17  error    'key' is defined but never used. Allowed unused args must match /^_/u                                                          no-unused-vars
   60:30  error    'progress' is defined but never used. Allowed unused args must match /^_/u                                                     no-unused-vars
   61:18  error    'key' is defined but never used. Allowed unused args must match /^_/u                                                          no-unused-vars
   62:15  error    'key' is defined but never used. Allowed unused args must match /^_/u                                                          no-unused-vars
   63:21  error    'key' is defined but never used. Allowed unused args must match /^_/u                                                          no-unused-vars
  237:11  warning  Unexpected any. Specify a different type                                                                                       @typescript-eslint/no-explicit-any
  247:11  error    'theme' is assigned a value but never used. Allowed unused vars must match /^_/u                                               no-unused-vars
  247:11  error    'theme' is assigned a value but never used. Allowed unused vars must match /^_/u                                               @typescript-eslint/no-unused-vars
  289:59  warning  Unexpected any. Specify a different type                                                                                       @typescript-eslint/no-explicit-any
  303:6   warning  React Hook useEffect has a missing dependency: 'spinValue'. Either include it or remove the dependency array                   react-hooks/exhaustive-deps
  327:42  warning  Unexpected any. Specify a different type                                                                                       @typescript-eslint/no-explicit-any
  347:6   warning  React Hook useEffect has a missing dependency: 'opacity'. Either include it or remove the dependency array                     react-hooks/exhaustive-deps
  374:41  warning  Unexpected any. Specify a different type                                                                                       @typescript-eslint/no-explicit-any
  388:6   warning  React Hook useEffect has a missing dependency: 'translateX'. Either include it or remove the dependency array                  react-hooks/exhaustive-deps
  409:11  warning  Unexpected any. Specify a different type                                                                                       @typescript-eslint/no-explicit-any
  420:6   warning  React Hook useEffect has a missing dependency: 'progressWidth'. Either include it or remove the dependency array               react-hooks/exhaustive-deps
  450:56  warning  Unexpected any. Specify a different type                                                                                       @typescript-eslint/no-explicit-any
  468:6   warning  React Hook useEffect has missing dependencies: 'dot1', 'dot2', and 'dot3'. Either include them or remove the dependency array  react-hooks/exhaustive-deps
  501:39  warning  Unexpected any. Specify a different type                                                                                       @typescript-eslint/no-explicit-any
  521:6   warning  React Hook useEffect has a missing dependency: 'scale'. Either include it or remove the dependency array                       react-hooks/exhaustive-deps
  543:16  warning  Avoid StyleSheet.create in screens; use DS templates. If you need a new pattern, add a DS component                            no-restricted-syntax

/Users/<USER>/everlasting-us/src/shared/components/layout/UnifiedErrorBoundary.tsx
  372:16  warning  Avoid StyleSheet.create in screens; use DS templates. If you need a new pattern, add a DS component  no-restricted-syntax

/Users/<USER>/everlasting-us/src/shared/components/screens/PerformanceDashboard.tsx
   23:42  warning  Unexpected any. Specify a different type                                                             @typescript-eslint/no-explicit-any
   24:44  warning  Unexpected any. Specify a different type                                                             @typescript-eslint/no-explicit-any
  300:16  warning  Avoid StyleSheet.create in screens; use DS templates. If you need a new pattern, add a DS component  no-restricted-syntax

/Users/<USER>/everlasting-us/src/shared/components/screens/PrivacyPolicy.tsx
  170:16  warning  Avoid StyleSheet.create in screens; use DS templates. If you need a new pattern, add a DS component  no-restricted-syntax

/Users/<USER>/everlasting-us/src/shared/components/screens/Security.tsx
  139:16  warning  Avoid StyleSheet.create in screens; use DS templates. If you need a new pattern, add a DS component  no-restricted-syntax

/Users/<USER>/everlasting-us/src/shared/hooks/useFavorites.ts
  23:17  error  'itemId' is defined but never used. Allowed unused args must match /^_/u       no-unused-vars
  23:33  error  'itemType' is defined but never used. Allowed unused args must match /^_/u     no-unused-vars
  23:61  error  'sourceTable' is defined but never used. Allowed unused args must match /^_/u  no-unused-vars
  23:83  error  'metadata' is defined but never used. Allowed unused args must match /^_/u     no-unused-vars
  24:20  error  'itemId' is defined but never used. Allowed unused args must match /^_/u       no-unused-vars
  24:36  error  'itemType' is defined but never used. Allowed unused args must match /^_/u     no-unused-vars
  25:20  error  'itemId' is defined but never used. Allowed unused args must match /^_/u       no-unused-vars
  25:36  error  'itemType' is defined but never used. Allowed unused args must match /^_/u     no-unused-vars
  25:64  error  'sourceTable' is defined but never used. Allowed unused args must match /^_/u  no-unused-vars
  25:86  error  'metadata' is defined but never used. Allowed unused args must match /^_/u     no-unused-vars
  26:17  error  'itemId' is defined but never used. Allowed unused args must match /^_/u       no-unused-vars
  26:33  error  'itemType' is defined but never used. Allowed unused args must match /^_/u     no-unused-vars
  27:24  error  'itemType' is defined but never used. Allowed unused args must match /^_/u     no-unused-vars
  28:22  error  'itemType' is defined but never used. Allowed unused args must match /^_/u     no-unused-vars

/Users/<USER>/everlasting-us/src/shared/hooks/useHeartToggle.ts
  10:16  error  'isFavorited' is defined but never used. Allowed unused args must match /^_/u  no-unused-vars
  12:14  error  'error' is defined but never used. Allowed unused args must match /^_/u        no-unused-vars
  21:12  error  'newState' is defined but never used. Allowed unused args must match /^_/u     no-unused-vars
  23:18  error  'isFavorited' is defined but never used. Allowed unused args must match /^_/u  no-unused-vars
  30:14  error  'isFavorited' is defined but never used. Allowed unused args must match /^_/u  no-unused-vars
  85:7   error  Unnecessary catch clause                                                       no-useless-catch

/Users/<USER>/everlasting-us/src/shared/hooks/useOptimizedData.ts
   44:17  warning  Unexpected any. Specify a different type                                                                                                                                                      @typescript-eslint/no-explicit-any
   50:32  warning  Unexpected any. Specify a different type                                                                                                                                                      @typescript-eslint/no-explicit-any
  282:6   warning  React Hook useEffect was passed a dependency list that is not an array literal. This means we can't statically verify whether you've passed the correct dependencies                          react-hooks/exhaustive-deps
  282:6   warning  React Hook useEffect has missing dependencies: 'cleanup', 'enablePrefetch', 'fetchData', 'prefetch', 'prefetchKeys', and 'setupRealtime'. Either include them or remove the dependency array  react-hooks/exhaustive-deps
  302:18  error    'ids' is defined but never used. Allowed unused args must match /^_/u                                                                                                                         no-unused-vars
  307:12  error    'newIds' is defined but never used. Allowed unused args must match /^_/u                                                                                                                      no-unused-vars
  308:15  error    'removeIds' is defined but never used. Allowed unused args must match /^_/u                                                                                                                   no-unused-vars

/Users/<USER>/everlasting-us/src/shared/hooks/userPreferencesService.ts
  13:44   warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  14:36   warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  15:34   warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  41:219  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  42:179  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  43:169  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

/Users/<USER>/everlasting-us/src/shared/services/accessibility/accessibilityManager.ts
  190:5   warning  Unexpected console statement. Only these console methods are allowed: warn, error  no-console
  242:48  warning  Unexpected any. Specify a different type                                           @typescript-eslint/no-explicit-any
  243:19  warning  Unexpected any. Specify a different type                                           @typescript-eslint/no-explicit-any

/Users/<USER>/everlasting-us/src/shared/services/activity/activityAnalyticsService.ts
   17:29  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   50:24  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   51:11  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  272:31  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  307:12  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

/Users/<USER>/everlasting-us/src/shared/services/activity/activityEventService.ts
  111:33  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  112:37  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  127:32  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  132:43  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  133:32  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  162:33  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  163:37  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  179:32  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  184:43  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  185:32  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  218:32  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  223:43  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  224:32  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  259:35  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  264:46  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  265:35  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

/Users/<USER>/everlasting-us/src/shared/services/activity/activityRegistry.ts
  298:19  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  299:19  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

/Users/<USER>/everlasting-us/src/shared/services/contexts/SettingsContext.tsx
   24:48  error    'key' is defined but never used. Allowed unused args must match /^_/u                                             no-unused-vars
   24:56  error    'value' is defined but never used. Allowed unused args must match /^_/u                                           no-unused-vars
  188:6   warning  React Hook useEffect has a missing dependency: 'applySettings'. Either include it or remove the dependency array  react-hooks/exhaustive-deps

/Users/<USER>/everlasting-us/src/shared/services/data/dataService.ts
    8:9   warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   18:9   warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   29:17  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   92:50  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  167:31  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  231:45  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  242:76  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  281:34  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  307:33  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  307:53  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  323:31  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  343:33  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  362:32  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  369:32  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

/Users/<USER>/everlasting-us/src/shared/services/favoritesService.ts
  17:29  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  27:18  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

/Users/<USER>/everlasting-us/src/shared/services/features/pointsSystemService.ts
  101:51  error    'activityType' is defined but never used. Allowed unused args must match /^_/u  no-unused-vars
  101:51  error    'activityType' is defined but never used. Allowed unused args must match /^_/u  @typescript-eslint/no-unused-vars
  161:48  warning  Unexpected any. Specify a different type                                        @typescript-eslint/no-explicit-any
  244:38  warning  Unexpected any. Specify a different type                                        @typescript-eslint/no-explicit-any

/Users/<USER>/everlasting-us/src/shared/services/game/gameRegistry.ts
  298:19  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  299:19  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

/Users/<USER>/everlasting-us/src/shared/services/logging/enterpriseLogger.ts
    8:3   error    'DEBUG' is defined but never used. Allowed unused vars must match /^_/u            no-unused-vars
    9:3   error    'INFO' is defined but never used. Allowed unused vars must match /^_/u             no-unused-vars
   10:3   error    'WARN' is defined but never used. Allowed unused vars must match /^_/u             no-unused-vars
   11:3   error    'ERROR' is defined but never used. Allowed unused vars must match /^_/u            no-unused-vars
   12:3   error    'FATAL' is defined but never used. Allowed unused vars must match /^_/u            no-unused-vars
   19:28  warning  Unexpected any. Specify a different type                                           @typescript-eslint/no-explicit-any
   29:85  warning  Unexpected any. Specify a different type                                           @typescript-eslint/no-explicit-any
   51:7   warning  Unexpected console statement. Only these console methods are allowed: warn, error  no-console
   55:51  warning  Unexpected any. Specify a different type                                           @typescript-eslint/no-explicit-any
   60:50  warning  Unexpected any. Specify a different type                                           @typescript-eslint/no-explicit-any
   65:50  warning  Unexpected any. Specify a different type                                           @typescript-eslint/no-explicit-any
   70:51  warning  Unexpected any. Specify a different type                                           @typescript-eslint/no-explicit-any
   75:51  warning  Unexpected any. Specify a different type                                           @typescript-eslint/no-explicit-any
   80:75  warning  Unexpected any. Specify a different type                                           @typescript-eslint/no-explicit-any
  123:64  warning  Unexpected any. Specify a different type                                           @typescript-eslint/no-explicit-any
  124:65  warning  Unexpected any. Specify a different type                                           @typescript-eslint/no-explicit-any
  125:89  warning  Unexpected any. Specify a different type                                           @typescript-eslint/no-explicit-any

/Users/<USER>/everlasting-us/src/shared/services/middleware/securityMiddleware.ts
  140:48  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  181:78  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  214:49  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  275:29  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

/Users/<USER>/everlasting-us/src/shared/services/offline/offlineManager.ts
    8:3   error    'CREATE' is defined but never used. Allowed unused vars must match /^_/u           no-unused-vars
    9:3   error    'UPDATE' is defined but never used. Allowed unused vars must match /^_/u           no-unused-vars
   10:3   error    'DELETE' is defined but never used. Allowed unused vars must match /^_/u           no-unused-vars
   11:3   error    'SYNC' is defined but never used. Allowed unused vars must match /^_/u             no-unused-vars
   18:9   warning  Unexpected any. Specify a different type                                           @typescript-eslint/no-explicit-any
   36:24  error    'status' is defined but never used. Allowed unused args must match /^_/u           no-unused-vars
   79:36  error    'status' is defined but never used. Allowed unused args must match /^_/u           no-unused-vars
  153:5   warning  Unexpected console statement. Only these console methods are allowed: warn, error  no-console
  159:5   warning  Unexpected console statement. Only these console methods are allowed: warn, error  no-console

/Users/<USER>/everlasting-us/src/shared/services/performanceMonitor.ts
   34:66  warning  Unexpected any. Specify a different type                                          @typescript-eslint/no-explicit-any
   35:24  warning  Unexpected any. Specify a different type                                          @typescript-eslint/no-explicit-any
   96:64  error    'success' is defined but never used. Allowed unused args must match /^_/u         no-unused-vars
   96:82  error    'size' is defined but never used. Allowed unused args must match /^_/u            no-unused-vars
  118:19  error    'componentCount' is defined but never used. Allowed unused args must match /^_/u  no-unused-vars
  144:31  error    'T' is defined but never used. Allowed unused vars must match /^_/u               no-unused-vars
  144:31  error    'T' is defined but never used. Allowed unused vars must match /^_/u               @typescript-eslint/no-unused-vars
  144:45  warning  Unexpected any. Specify a different type                                          @typescript-eslint/no-explicit-any
  144:59  warning  Unexpected any. Specify a different type                                          @typescript-eslint/no-explicit-any
  159:43  warning  Unexpected any. Specify a different type                                          @typescript-eslint/no-explicit-any
  183:36  warning  Unexpected any. Specify a different type                                          @typescript-eslint/no-explicit-any
  254:53  warning  Unexpected any. Specify a different type                                          @typescript-eslint/no-explicit-any
  255:33  warning  Unexpected any. Specify a different type                                          @typescript-eslint/no-explicit-any

/Users/<USER>/everlasting-us/src/shared/services/recovery/errorRecoveryService.ts
   54:11  warning  Unexpected console statement. Only these console methods are allowed: warn, error  no-console
   75:54  warning  Unexpected any. Specify a different type                                           @typescript-eslint/no-explicit-any
   97:60  warning  Unexpected any. Specify a different type                                           @typescript-eslint/no-explicit-any
  105:18  error    'error' is defined but never used                                                  no-unused-vars
  105:18  error    'error' is defined but never used                                                  @typescript-eslint/no-unused-vars
  121:18  error    'error' is defined but never used                                                  no-unused-vars
  121:18  error    'error' is defined but never used                                                  @typescript-eslint/no-unused-vars
  121:25  error    Unreachable code                                                                   no-unreachable
  139:7   warning  Unexpected console statement. Only these console methods are allowed: warn, error  no-console

/Users/<USER>/everlasting-us/src/shared/services/storage/cacheManager.ts
   46:46  warning  Unexpected any. Specify a different type                               @typescript-eslint/no-explicit-any
  190:18  error    'compressionError' is defined but never used                           no-unused-vars
  190:18  error    'compressionError' is defined but never used                           @typescript-eslint/no-unused-vars
  268:66  warning  Unexpected any. Specify a different type                               @typescript-eslint/no-explicit-any
  282:20  error    'key' is defined but never used. Allowed unused args must match /^_/u  no-unused-vars
  282:44  warning  Unexpected any. Specify a different type                               @typescript-eslint/no-explicit-any
  363:49  warning  Unexpected any. Specify a different type                               @typescript-eslint/no-explicit-any
  391:44  warning  Unexpected any. Specify a different type                               @typescript-eslint/no-explicit-any
  391:58  warning  Unexpected any. Specify a different type                               @typescript-eslint/no-explicit-any
  420:30  warning  Unexpected any. Specify a different type                               @typescript-eslint/no-explicit-any

/Users/<USER>/everlasting-us/src/shared/services/storage/hybridStorageService.ts
  191:58   warning  Unexpected any. Specify a different type                                           @typescript-eslint/no-explicit-any
  195:69   warning  Unexpected any. Specify a different type                                           @typescript-eslint/no-explicit-any
  195:102  warning  Unexpected any. Specify a different type                                           @typescript-eslint/no-explicit-any
  198:69   warning  Unexpected any. Specify a different type                                           @typescript-eslint/no-explicit-any
  198:102  warning  Unexpected any. Specify a different type                                           @typescript-eslint/no-explicit-any
  201:69   warning  Unexpected any. Specify a different type                                           @typescript-eslint/no-explicit-any
  201:102  warning  Unexpected any. Specify a different type                                           @typescript-eslint/no-explicit-any
  204:69   warning  Unexpected any. Specify a different type                                           @typescript-eslint/no-explicit-any
  204:102  warning  Unexpected any. Specify a different type                                           @typescript-eslint/no-explicit-any
  207:69   warning  Unexpected any. Specify a different type                                           @typescript-eslint/no-explicit-any
  207:102  warning  Unexpected any. Specify a different type                                           @typescript-eslint/no-explicit-any
  210:69   warning  Unexpected any. Specify a different type                                           @typescript-eslint/no-explicit-any
  210:102  warning  Unexpected any. Specify a different type                                           @typescript-eslint/no-explicit-any
  213:69   warning  Unexpected any. Specify a different type                                           @typescript-eslint/no-explicit-any
  213:102  warning  Unexpected any. Specify a different type                                           @typescript-eslint/no-explicit-any
  216:69   warning  Unexpected any. Specify a different type                                           @typescript-eslint/no-explicit-any
  216:102  warning  Unexpected any. Specify a different type                                           @typescript-eslint/no-explicit-any
  219:69   warning  Unexpected any. Specify a different type                                           @typescript-eslint/no-explicit-any
  219:102  warning  Unexpected any. Specify a different type                                           @typescript-eslint/no-explicit-any
  222:70   warning  Unexpected any. Specify a different type                                           @typescript-eslint/no-explicit-any
  222:103  warning  Unexpected any. Specify a different type                                           @typescript-eslint/no-explicit-any
  225:70   warning  Unexpected any. Specify a different type                                           @typescript-eslint/no-explicit-any
  225:103  warning  Unexpected any. Specify a different type                                           @typescript-eslint/no-explicit-any
  228:70   warning  Unexpected any. Specify a different type                                           @typescript-eslint/no-explicit-any
  228:103  warning  Unexpected any. Specify a different type                                           @typescript-eslint/no-explicit-any
  235:11   warning  Unexpected console statement. Only these console methods are allowed: warn, error  no-console
  244:36   warning  Unexpected any. Specify a different type                                           @typescript-eslint/no-explicit-any
  248:58   warning  Unexpected any. Specify a different type                                           @typescript-eslint/no-explicit-any

/Users/<USER>/everlasting-us/src/shared/services/storage/offlineManager.ts
   16:3   error  'IDLE' is defined but never used. Allowed unused vars must match /^_/u          no-unused-vars
   17:3   error  'SYNCING' is defined but never used. Allowed unused vars must match /^_/u       no-unused-vars
   18:3   error  'SUCCESS' is defined but never used. Allowed unused vars must match /^_/u       no-unused-vars
   19:3   error  'ERROR' is defined but never used. Allowed unused vars must match /^_/u         no-unused-vars
   20:3   error  'CONFLICT' is defined but never used. Allowed unused vars must match /^_/u      no-unused-vars
   24:3   error  'CREATE' is defined but never used. Allowed unused vars must match /^_/u        no-unused-vars
   25:3   error  'UPDATE' is defined but never used. Allowed unused vars must match /^_/u        no-unused-vars
   26:3   error  'DELETE' is defined but never used. Allowed unused vars must match /^_/u        no-unused-vars
   27:3   error  'BATCH' is defined but never used. Allowed unused vars must match /^_/u         no-unused-vars
   31:3   error  'CLIENT_WINS' is defined but never used. Allowed unused vars must match /^_/u   no-unused-vars
   32:3   error  'SERVER_WINS' is defined but never used. Allowed unused vars must match /^_/u   no-unused-vars
   33:3   error  'MERGE' is defined but never used. Allowed unused vars must match /^_/u         no-unused-vars
   34:3   error  'USER_DECIDES' is defined but never used. Allowed unused vars must match /^_/u  no-unused-vars
  106:31  error  'status' is defined but never used. Allowed unused args must match /^_/u        no-unused-vars
  106:51  error  'result' is defined but never used. Allowed unused args must match /^_/u        no-unused-vars
  107:34  error  'state' is defined but never used. Allowed unused args must match /^_/u         no-unused-vars
  227:37  error  'status' is defined but never used. Allowed unused args must match /^_/u        no-unused-vars
  227:57  error  'result' is defined but never used. Allowed unused args must match /^_/u        no-unused-vars
  235:40  error  'state' is defined but never used. Allowed unused args must match /^_/u         no-unused-vars

/Users/<USER>/everlasting-us/src/shared/services/supabase/client.ts
  7:27  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

/Users/<USER>/everlasting-us/src/shared/services/system/accessibilityManager.ts
   14:3   error    'A' is defined but never used. Allowed unused vars must match /^_/u          no-unused-vars
   15:3   error    'AA' is defined but never used. Allowed unused vars must match /^_/u         no-unused-vars
   16:3   error    'AAA' is defined but never used. Allowed unused vars must match /^_/u        no-unused-vars
   20:3   error    'BUTTON' is defined but never used. Allowed unused vars must match /^_/u     no-unused-vars
   21:3   error    'LINK' is defined but never used. Allowed unused vars must match /^_/u       no-unused-vars
   22:3   error    'TEXT' is defined but never used. Allowed unused vars must match /^_/u       no-unused-vars
   23:3   error    'HEADING' is defined but never used. Allowed unused vars must match /^_/u    no-unused-vars
   24:3   error    'IMAGE' is defined but never used. Allowed unused vars must match /^_/u      no-unused-vars
   25:3   error    'LIST' is defined but never used. Allowed unused vars must match /^_/u       no-unused-vars
   26:3   error    'LIST_ITEM' is defined but never used. Allowed unused vars must match /^_/u  no-unused-vars
   27:3   error    'TAB' is defined but never used. Allowed unused vars must match /^_/u        no-unused-vars
   28:3   error    'TAB_LIST' is defined but never used. Allowed unused vars must match /^_/u   no-unused-vars
   29:3   error    'TAB_PANEL' is defined but never used. Allowed unused vars must match /^_/u  no-unused-vars
   30:3   error    'DIALOG' is defined but never used. Allowed unused vars must match /^_/u     no-unused-vars
   31:3   error    'ALERT' is defined but never used. Allowed unused vars must match /^_/u      no-unused-vars
   32:3   error    'MENU' is defined but never used. Allowed unused vars must match /^_/u       no-unused-vars
   33:3   error    'MENU_ITEM' is defined but never used. Allowed unused vars must match /^_/u  no-unused-vars
   34:3   error    'SEARCH' is defined but never used. Allowed unused vars must match /^_/u     no-unused-vars
   35:3   error    'FORM' is defined but never used. Allowed unused vars must match /^_/u       no-unused-vars
   36:3   error    'CHECKBOX' is defined but never used. Allowed unused vars must match /^_/u   no-unused-vars
   37:3   error    'RADIO' is defined but never used. Allowed unused vars must match /^_/u      no-unused-vars
   38:3   error    'SWITCH' is defined but never used. Allowed unused vars must match /^_/u     no-unused-vars
   39:3   error    'SLIDER' is defined but never used. Allowed unused vars must match /^_/u     no-unused-vars
   40:3   error    'PROGRESS' is defined but never used. Allowed unused vars must match /^_/u   no-unused-vars
   65:28  error    'event' is defined but never used. Allowed unused args must match /^_/u      no-unused-vars
  103:27  error    'settings' is defined but never used. Allowed unused args must match /^_/u   no-unused-vars
  243:12  warning  Unexpected any. Specify a different type                                     @typescript-eslint/no-explicit-any
  244:5   error    'children' is defined but never used. Allowed unused args must match /^_/u   no-unused-vars
  244:5   error    'children' is defined but never used. Allowed unused args must match /^_/u   @typescript-eslint/no-unused-vars
  244:16  warning  Unexpected any. Specify a different type                                     @typescript-eslint/no-explicit-any
  356:37  warning  Unexpected any. Specify a different type                                     @typescript-eslint/no-explicit-any
  370:33  error    'settings' is defined but never used. Allowed unused args must match /^_/u   no-unused-vars
  385:37  warning  Unexpected any. Specify a different type                                     @typescript-eslint/no-explicit-any
  385:43  warning  Unexpected any. Specify a different type                                     @typescript-eslint/no-explicit-any

/Users/<USER>/everlasting-us/src/shared/services/system/configurationManager.ts
   14:3   error    'DEVELOPMENT' is defined but never used. Allowed unused vars must match /^_/u                   no-unused-vars
   15:3   error    'STAGING' is defined but never used. Allowed unused vars must match /^_/u                       no-unused-vars
   16:3   error    'PRODUCTION' is defined but never used. Allowed unused vars must match /^_/u                    no-unused-vars
   17:3   error    'TEST' is defined but never used. Allowed unused vars must match /^_/u                          no-unused-vars
  116:7   error    'ConfigurationSchema' is assigned a value but never used. Allowed unused vars must match /^_/u  no-unused-vars
  116:7   error    'ConfigurationSchema' is assigned a value but never used. Allowed unused vars must match /^_/u  @typescript-eslint/no-unused-vars
  116:43  warning  Unexpected any. Specify a different type                                                        @typescript-eslint/no-explicit-any
  139:27  error    'config' is defined but never used. Allowed unused args must match /^_/u                        no-unused-vars
  238:33  error    'config' is defined but never used. Allowed unused args must match /^_/u                        no-unused-vars
  349:45  warning  Unexpected any. Specify a different type                                                        @typescript-eslint/no-explicit-any
  378:31  warning  Unexpected any. Specify a different type                                                        @typescript-eslint/no-explicit-any

/Users/<USER>/everlasting-us/src/shared/services/system/errorRecoveryService.ts
   15:3   error  'RETRY' is defined but never used. Allowed unused vars must match /^_/u                 no-unused-vars
   16:3   error  'FALLBACK' is defined but never used. Allowed unused vars must match /^_/u              no-unused-vars
   17:3   error  'REDIRECT' is defined but never used. Allowed unused vars must match /^_/u              no-unused-vars
   18:3   error  'REFRESH' is defined but never used. Allowed unused vars must match /^_/u               no-unused-vars
   19:3   error  'OFFLINE_MODE' is defined but never used. Allowed unused vars must match /^_/u          no-unused-vars
   20:3   error  'CACHE_FALLBACK' is defined but never used. Allowed unused vars must match /^_/u        no-unused-vars
   21:3   error  'ALTERNATIVE_ENDPOINT' is defined but never used. Allowed unused vars must match /^_/u  no-unused-vars
   22:3   error  'USER_INTERVENTION' is defined but never used. Allowed unused vars must match /^_/u     no-unused-vars
   26:3   error  'LOW' is defined but never used. Allowed unused vars must match /^_/u                   no-unused-vars
   27:3   error  'MEDIUM' is defined but never used. Allowed unused vars must match /^_/u                no-unused-vars
   28:3   error  'HIGH' is defined but never used. Allowed unused vars must match /^_/u                  no-unused-vars
   29:3   error  'CRITICAL' is defined but never used. Allowed unused vars must match /^_/u              no-unused-vars
  459:39  error  'error' is defined but never used. Allowed unused args must match /^_/u                 no-unused-vars
  459:39  error  'error' is defined but never used. Allowed unused args must match /^_/u                 @typescript-eslint/no-unused-vars
  473:46  error  'plan' is defined but never used. Allowed unused args must match /^_/u                  no-unused-vars
  473:46  error  'plan' is defined but never used. Allowed unused args must match /^_/u                  @typescript-eslint/no-unused-vars

/Users/<USER>/everlasting-us/src/shared/services/system/errorReportingService.ts
   13:29  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   46:33  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  185:47  error    Unnecessary escape character: \/          no-useless-escape
  204:54  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  204:76  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  207:37  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  246:38  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  246:44  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  249:22  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  279:14  error    'error' is defined but never used         no-unused-vars
  279:14  error    'error' is defined but never used         @typescript-eslint/no-unused-vars
  408:14  error    'error' is defined but never used         no-unused-vars
  408:14  error    'error' is defined but never used         @typescript-eslint/no-unused-vars
  458:38  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  460:14  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  460:56  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  460:70  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  473:59  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

/Users/<USER>/everlasting-us/src/shared/services/system/queryOptimizationService.ts
   11:13   warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   12:16   warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   13:14   warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   19:16   warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   20:14   warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   26:12   warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   27:17   warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   28:23   warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   29:16   warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   52:22   warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   80:58   warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   88:79   warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  138:76   warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  145:61   warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  198:115  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  243:59   warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  299:71   warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  313:24   warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

/Users/<USER>/everlasting-us/src/shared/services/system/securityValidationService.ts
  341:24  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

/Users/<USER>/everlasting-us/src/shared/services/system/simpleErrorService.ts
   16:29  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   31:29  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   56:14  error    'error' is defined but never used         no-unused-vars
   56:14  error    'error' is defined but never used         @typescript-eslint/no-unused-vars
   70:14  error    'error' is defined but never used         no-unused-vars
   70:14  error    'error' is defined but never used         @typescript-eslint/no-unused-vars
   84:14  error    'error' is defined but never used         no-unused-vars
   84:14  error    'error' is defined but never used         @typescript-eslint/no-unused-vars
  103:33  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  215:47  error    Unnecessary escape character: \/          no-useless-escape
  235:37  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  278:14  error    'error' is defined but never used         no-unused-vars
  278:14  error    'error' is defined but never used         @typescript-eslint/no-unused-vars
  422:14  error    'error' is defined but never used         no-unused-vars
  422:14  error    'error' is defined but never used         @typescript-eslint/no-unused-vars

/Users/<USER>/everlasting-us/src/shared/services/system/subscriptionManager.ts
   14:14  error    'payload' is defined but never used. Allowed unused args must match /^_/u  no-unused-vars
   14:54  warning  Unexpected any. Specify a different type                                   @typescript-eslint/no-explicit-any
   15:14  error    'error' is defined but never used. Allowed unused args must match /^_/u    no-unused-vars
  109:19  warning  Unexpected any. Specify a different type                                   @typescript-eslint/no-explicit-any
  117:50  warning  Unexpected any. Specify a different type                                   @typescript-eslint/no-explicit-any

/Users/<USER>/everlasting-us/src/shared/types/activity.types.ts
   63:31  warning  Unexpected any. Specify a different type                                      @typescript-eslint/no-explicit-any
   74:24  warning  Unexpected any. Specify a different type                                      @typescript-eslint/no-explicit-any
   75:29  warning  Unexpected any. Specify a different type                                      @typescript-eslint/no-explicit-any
   85:28  warning  Unexpected any. Specify a different type                                      @typescript-eslint/no-explicit-any
  107:24  warning  Unexpected any. Specify a different type                                      @typescript-eslint/no-explicit-any
  109:29  warning  Unexpected any. Specify a different type                                      @typescript-eslint/no-explicit-any
  131:17  error    'activityId' is defined but never used. Allowed unused args must match /^_/u  no-unused-vars
  146:31  warning  Unexpected any. Specify a different type                                      @typescript-eslint/no-explicit-any
  164:24  warning  Unexpected any. Specify a different type                                      @typescript-eslint/no-explicit-any
  189:24  warning  Unexpected any. Specify a different type                                      @typescript-eslint/no-explicit-any

/Users/<USER>/everlasting-us/src/shared/types/auth.types.ts
   56:42  error    'event' is defined but never used. Allowed unused args must match /^_/u    no-unused-vars
   56:69  error    'session' is defined but never used. Allowed unused args must match /^_/u  no-unused-vars
  119:37  warning  Unexpected any. Specify a different type                                   @typescript-eslint/no-explicit-any
  127:36  warning  Unexpected any. Specify a different type                                   @typescript-eslint/no-explicit-any
  142:19  warning  Unexpected any. Specify a different type                                   @typescript-eslint/no-explicit-any
  143:54  warning  Unexpected any. Specify a different type                                   @typescript-eslint/no-explicit-any

/Users/<USER>/everlasting-us/src/shared/types/game.types.ts
   62:31  warning  Unexpected any. Specify a different type                                      @typescript-eslint/no-explicit-any
   73:24  warning  Unexpected any. Specify a different type                                      @typescript-eslint/no-explicit-any
   74:29  warning  Unexpected any. Specify a different type                                      @typescript-eslint/no-explicit-any
   84:28  warning  Unexpected any. Specify a different type                                      @typescript-eslint/no-explicit-any
  106:24  warning  Unexpected any. Specify a different type                                      @typescript-eslint/no-explicit-any
  108:29  warning  Unexpected any. Specify a different type                                      @typescript-eslint/no-explicit-any
  130:17  error    'activityId' is defined but never used. Allowed unused args must match /^_/u  no-unused-vars
  145:31  warning  Unexpected any. Specify a different type                                      @typescript-eslint/no-explicit-any
  163:24  warning  Unexpected any. Specify a different type                                      @typescript-eslint/no-explicit-any
  188:24  warning  Unexpected any. Specify a different type                                      @typescript-eslint/no-explicit-any

/Users/<USER>/everlasting-us/src/shared/types/index.ts
   10:34  warning  Unexpected any. Specify a different type                                @typescript-eslint/no-explicit-any
   25:18  warning  Unexpected any. Specify a different type                                @typescript-eslint/no-explicit-any
  103:10  warning  Unexpected any. Specify a different type                                @typescript-eslint/no-explicit-any
  120:29  warning  Unexpected any. Specify a different type                                @typescript-eslint/no-explicit-any
  178:42  error    'args' is defined but never used. Allowed unused args must match /^_/u  no-unused-vars
  178:87  error    'args' is defined but never used. Allowed unused args must match /^_/u  no-unused-vars

/Users/<USER>/everlasting-us/src/shared/types/matchGame.types.ts
  134:22  error    'params' is defined but never used. Allowed unused args must match /^_/u       no-unused-vars
  135:24  error    'params' is defined but never used. Allowed unused args must match /^_/u       no-unused-vars
  136:26  error    'category' is defined but never used. Allowed unused args must match /^_/u     no-unused-vars
  136:44  error    'count' is defined but never used. Allowed unused args must match /^_/u        no-unused-vars
  137:28  error    'difficulty' is defined but never used. Allowed unused args must match /^_/u   no-unused-vars
  137:68  error    'count' is defined but never used. Allowed unused args must match /^_/u        no-unused-vars
  139:19  error    'question_id' is defined but never used. Allowed unused args must match /^_/u  no-unused-vars
  143:18  error    'answer' is defined but never used. Allowed unused args must match /^_/u       no-unused-vars
  144:17  error    'user_id' is defined but never used. Allowed unused args must match /^_/u      no-unused-vars
  144:34  error    'question_id' is defined but never used. Allowed unused args must match /^_/u  no-unused-vars
  145:18  error    'user_id' is defined but never used. Allowed unused args must match /^_/u      no-unused-vars
  146:20  error    'answer_id' is defined but never used. Allowed unused args must match /^_/u    no-unused-vars
  146:39  error    'answer_text' is defined but never used. Allowed unused args must match /^_/u  no-unused-vars
  147:20  error    'answer_id' is defined but never used. Allowed unused args must match /^_/u    no-unused-vars
  148:28  error    'user_id' is defined but never used. Allowed unused args must match /^_/u      no-unused-vars
  152:17  error    'session' is defined but never used. Allowed unused args must match /^_/u      no-unused-vars
  153:14  error    'session_id' is defined but never used. Allowed unused args must match /^_/u   no-unused-vars
  154:19  error    'user_id' is defined but never used. Allowed unused args must match /^_/u      no-unused-vars
  154:36  error    'limit' is defined but never used. Allowed unused args must match /^_/u        no-unused-vars
  155:17  error    'session_id' is defined but never used. Allowed unused args must match /^_/u   no-unused-vars
  155:37  error    'updates' is defined but never used. Allowed unused args must match /^_/u      no-unused-vars
  156:19  error    'session_id' is defined but never used. Allowed unused args must match /^_/u   no-unused-vars
  157:17  error    'session_id' is defined but never used. Allowed unused args must match /^_/u   no-unused-vars
  161:14  error    'result' is defined but never used. Allowed unused args must match /^_/u       no-unused-vars
  162:21  error    'session_id' is defined but never used. Allowed unused args must match /^_/u   no-unused-vars
  163:16  error    'user_id' is defined but never used. Allowed unused args must match /^_/u      no-unused-vars
  164:18  error    'couple_id' is defined but never used. Allowed unused args must match /^_/u    no-unused-vars
  165:20  error    'user_id' is defined but never used. Allowed unused args must match /^_/u      no-unused-vars
  186:13  warning  Unexpected any. Specify a different type                                       @typescript-eslint/no-explicit-any
  188:86  warning  Unexpected any. Specify a different type                                       @typescript-eslint/no-explicit-any

/Users/<USER>/everlasting-us/src/shared/utils/colors.ts
  435:53  error  'systemColorScheme' is defined but never used. Allowed unused args must match /^_/u  no-unused-vars
  435:53  error  'systemColorScheme' is defined but never used. Allowed unused args must match /^_/u  @typescript-eslint/no-unused-vars

/Users/<USER>/everlasting-us/src/shared/utils/errorHandler.ts
  11:3  error  'NETWORK' is defined but never used. Allowed unused vars must match /^_/u         no-unused-vars
  12:3  error  'AUTHENTICATION' is defined but never used. Allowed unused vars must match /^_/u  no-unused-vars
  13:3  error  'VALIDATION' is defined but never used. Allowed unused vars must match /^_/u      no-unused-vars
  14:3  error  'SYSTEM' is defined but never used. Allowed unused vars must match /^_/u          no-unused-vars
  15:3  error  'UI' is defined but never used. Allowed unused vars must match /^_/u              no-unused-vars
  16:3  error  'PERMISSION' is defined but never used. Allowed unused vars must match /^_/u      no-unused-vars
  17:3  error  'STORAGE' is defined but never used. Allowed unused vars must match /^_/u         no-unused-vars

/Users/<USER>/everlasting-us/src/shared/utils/featureFlags.ts
  192:7  warning  Unexpected console statement. Only these console methods are allowed: warn, error  no-console

/Users/<USER>/everlasting-us/src/shared/utils/healthChecks.ts
   13:28  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  167:35  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  213:35  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  259:35  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  302:35  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  345:35  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  385:35  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  428:35  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

/Users/<USER>/everlasting-us/src/shared/utils/journeyAnalytics.ts
   18:29  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   51:31  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   94:31  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  123:31  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  159:30  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

/Users/<USER>/everlasting-us/src/shared/utils/logger.ts
   17:10  warning  Unexpected any. Specify a different type                                           @typescript-eslint/no-explicit-any
   44:33  warning  Unexpected any. Specify a different type                                           @typescript-eslint/no-explicit-any
   53:32  warning  Unexpected any. Specify a different type                                           @typescript-eslint/no-explicit-any
   62:32  warning  Unexpected any. Specify a different type                                           @typescript-eslint/no-explicit-any
   71:34  warning  Unexpected any. Specify a different type                                           @typescript-eslint/no-explicit-any
   80:56  warning  Unexpected any. Specify a different type                                           @typescript-eslint/no-explicit-any
  103:7   warning  Unexpected console statement. Only these console methods are allowed: warn, error  no-console
  199:47  warning  Unexpected any. Specify a different type                                           @typescript-eslint/no-explicit-any
  200:46  warning  Unexpected any. Specify a different type                                           @typescript-eslint/no-explicit-any
  201:46  warning  Unexpected any. Specify a different type                                           @typescript-eslint/no-explicit-any
  202:48  warning  Unexpected any. Specify a different type                                           @typescript-eslint/no-explicit-any

/Users/<USER>/everlasting-us/src/shared/utils/secureStorage.ts
  145:37  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

/Users/<USER>/everlasting-us/src/shared/utils/sharedStyles.ts
   7:51  warning  Avoid StyleSheet.create in screens; use DS templates. If you need a new pattern, add a DS component  no-restricted-syntax
  39:29  warning  Avoid StyleSheet.create in screens; use DS templates. If you need a new pattern, add a DS component  no-restricted-syntax
  60:28  warning  Avoid StyleSheet.create in screens; use DS templates. If you need a new pattern, add a DS component  no-restricted-syntax
  77:29  warning  Avoid StyleSheet.create in screens; use DS templates. If you need a new pattern, add a DS component  no-restricted-syntax
  99:27  warning  Avoid StyleSheet.create in screens; use DS templates. If you need a new pattern, add a DS component  no-restricted-syntax

/Users/<USER>/everlasting-us/src/shared/utils/theme/index.ts
   66:13  error  'mode' is defined but never used. Allowed unused args must match /^_/u           no-unused-vars
  120:3   error  'customOpacity' is defined but never used. Allowed unused args must match /^_/u  no-unused-vars
  120:3   error  'customOpacity' is defined but never used. Allowed unused args must match /^_/u  @typescript-eslint/no-unused-vars

/Users/<USER>/everlasting-us/src/utils/constants.ts
  307:28  error  Unnecessary escape character: \(  no-useless-escape
  307:30  error  Unnecessary escape character: \)  no-useless-escape

/Users/<USER>/everlasting-us/src/utils/onboardingStorage.ts
   11:10  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   55:58  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   83:45  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  161:32  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  166:46  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  190:53  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  200:54  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

/Users/<USER>/everlasting-us/src/utils/toast.ts
   33:24  error  'toasts' is defined but never used. Allowed unused args must match /^_/u  no-unused-vars
  104:24  error  'toasts' is defined but never used. Allowed unused args must match /^_/u  no-unused-vars
  142:26  error  'toasts' is defined but never used. Allowed unused args must match /^_/u  no-unused-vars
  184:15  error  'id' is defined but never used. Allowed unused args must match /^_/u      no-unused-vars

✖ 1287 problems (502 errors, 785 warnings)

