# Database Documentation

## 📊 **Quick Reference**

### **Main Documentation**
- **[Schema Reference](./docs/schema-reference.md)** - 📋 Table overview and relationships
- **[Architecture Rationale](./docs/architecture-rationale.md)** - 🏗️ Design decisions and performance strategy
- **[Complete Field Reference](./docs/complete-field-reference.md)** - 📝 Detailed field specifications

### **Setup & Operations**
- **[Setup Guide](./docs/setup-guide.md)** - 🗄️ Supabase configuration and environment setup
- **[Migration Guide](./docs/migration-guide.md)** - 🚀 How to run database migrations
- **[migrations/](./migrations/)** - SQL migration scripts for schema changes

## 🚀 **Quick Start**

### **For Developers**
1. **Understanding the schema**: Start with [Schema Reference](./docs/schema-reference.md)
2. **Design context**: Read [Architecture Rationale](./docs/architecture-rationale.md) 
3. **Field details**: Reference [Complete Field Reference](./docs/complete-field-reference.md)

### **For Database Changes**
1. Create new migration file in `migrations/`
2. Update schema reference documentation
3. Test migration on development database
4. Update TypeScript types if needed

## 🔗 **Key Tables Summary**

| Category | Tables | Purpose |
|----------|--------|---------|
| **Core** | `couples`, `profiles`, `user_events` | User management & activity tracking |
| **Content** | `daily_questions`, `date_night_ideas_*`, `meal_ideas_*` | App content and activities |
| **Timeline** | `timeline_events`, `timeline_photos`, `origin_story` | Memory and milestone tracking |
| **Games** | `match_game_*`, `points_system`, `favorites` | Gamification features |
| **System** | `error_logs`, `onboarding_progress` | Analytics and monitoring |

---
*All database documentation centralized in this folder for easy reference*
