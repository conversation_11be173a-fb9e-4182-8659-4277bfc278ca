# 🗄️ Supabase Setup Guide

> **Complete guide to setting up Supabase backend for Nestled**

## 📋 Contents

1. [Quick Setup](#-quick-setup)
2. [Database Setup](#-database-setup)
3. [Authentication Setup](#-authentication-setup)
4. [Environment Configuration](#-environment-configuration)
5. [Testing](#-testing)
6. [Production Setup](#-production-setup)
7. [Troubleshooting](#-troubleshooting)

## ⚡ Quick Setup

### 1. Create Environment File
```bash
touch .env
```

### 2. Add Supabase Configuration
```env
EXPO_PUBLIC_SUPABASE_URL=https://your-project-id.supabase.co
EXPO_PUBLIC_SUPABASE_ANON_KEY=your_anon_key_here
```

### 3. Get Your Keys
1. Go to [supabase.com](https://supabase.com)
2. Sign in and select your project
3. Go to **Settings** → **API**
4. Copy:
   - **Project URL** → `EXPO_PUBLIC_SUPABASE_URL`
   - **anon public** key → `EXPO_PUBLIC_SUPABASE_ANON_KEY`

### 4. Restart Development Server
```bash
npx expo start --clear
```

## 🗄️ Database Setup

### 1. Run Database Schema
1. Go to SQL Editor in your Supabase dashboard
2. Copy contents of `supabase-schema.sql` from this project
3. Paste into SQL Editor and click "Run"

### 2. Verify Tables Created
Check Table Editor for these tables:
- `profiles` - User profile information
- `weekly_data` - Weekly relationship activities
- `origin_story` - Couple's origin story
- `points_system` - Points and achievements
- `date_night_ideas` - Date night suggestions
- `meal_voting` - Meal voting history
- `scrapbook` - Photo and memory entries

## 🔐 Authentication Setup

### 1. Configure Authentication
1. Go to Authentication > Settings
2. Enable Email authentication
3. Configure site URL (development: `http://localhost:8081`)
4. Add redirect URLs for your app

### 2. Email Templates (Optional)
- Customize templates in Authentication > Email Templates
- Update confirmation and reset password emails

## ⚙️ Environment Configuration

### Security Notes
✅ **Safe to use in client-side code**: These are PUBLIC environment variables
✅ **Already in .gitignore**: Your `.env` file won't be committed to git
✅ **Anon key is safe**: Designed for public access with Row Level Security

### Example .env file:
```env
EXPO_PUBLIC_SUPABASE_URL=https://abcdefghijklmnop.supabase.co
EXPO_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************.example_signature_here
```

## 🧪 Testing

### 1. Test Authentication
- App shows AuthScreen when no user is logged in
- Try creating a new account
- Try signing in with existing credentials

### 2. Test Data Sync
- Create some data in the app
- Check Supabase dashboard to see data in tables

## 🚀 Production Setup

### 1. Update Environment Variables
- Use environment variables instead of hardcoded values
- Set `EXPO_PUBLIC_SUPABASE_URL` and `EXPO_PUBLIC_SUPABASE_ANON_KEY`

### 2. Configure Production URLs
- Update Supabase project settings with production URLs
- Configure proper redirect URLs for production app

## 🛡️ Security Features

- **Row Level Security (RLS)** enabled on all tables
- **Users can only access their own data**
- **Secure authentication** with JWT tokens
- **Automatic timestamp updates**
- **Data validation and constraints**

## 🚨 Troubleshooting

### Common Issues

1. **Authentication not working**
   - Check Supabase URL and anon key
   - Verify email authentication is enabled
   - Check redirect URLs in Supabase settings

2. **Data not syncing**
   - Check internet connection
   - Verify user is authenticated
   - Check browser console for errors

3. **Database errors**
   - Ensure schema was run successfully
   - Check RLS policies are in place
   - Verify table permissions

### Still Getting Errors?
- Make sure there are no spaces around the `=` in your `.env` file
- URL should be `https://your-project-id.supabase.co`
- Anon key should be a long JWT token (100+ characters)

## 📁 Files Created/Modified

- `utils/supabase.ts` - Supabase client configuration
- `services/authService.ts` - Authentication service
- `services/dataService.ts` - Data management service
- `services/hybridStorageService.ts` - Hybrid local/cloud storage
- `components/AuthScreen.tsx` - Authentication UI
- `supabase-schema.sql` - Database schema

Your Supabase backend is now ready to use! 🎉