# Database Schema Reference

## 📊 **Core Application Tables**

### **👥 User & Couple Management**
- **`couples`** - Couple relationships and pairing codes
- **`profiles`** - Partner names, icons, profile pictures, relationship dates
- **`pairing_attempts`** - Tracking couple connection attempts
- **`user_preferences`** - User-specific settings and preferences

### **📝 Content & Activities**
- **`daily_questions`** - Question bank for daily prompts
- **`daily_question_responses`** - User answers to daily questions
- **`daily_question_schedule`** - Scheduled questions by date
- **`date_night_ideas_global`** - System-provided date night ideas
- **`date_night_ideas_user`** - User-created date night ideas
- **`meal_ideas_global`** - System-provided meal ideas
- **`meal_ideas_users`** - User meal ideas and completion status

### **🎯 Milestones & Timeline**
- **`milestone_templates`** - Template definitions for milestones
- **`couple_milestones`** - Couple-specific milestone progress
- **`timeline_events`** - Major relationship events and milestones
- **`timeline_photos`** - Photos associated with timeline events

### **📖 Stories & Memory**
- **`couple_stories`** - Couple story data and completion status
- **`origin_story`** - How couples met, first kiss, etc. with photos
- **`scrapbook`** - Memory entries and photo collections
- **`weekly_data`** - Weekly journey data and progress

### **🎮 Gamification**
- **`match_game_questions`** - Questions for the matching game
- **`match_game_sessions`** - Game session tracking
- **`match_game_results`** - Individual question results
- **`match_game_user_answers`** - User answers for matching
- **`points_system`** - User points, levels, and achievements
- **`user_favorites`** - User favorited items across the app

### **📊 Analytics & Monitoring**
- **`user_events`** - Central activity tracking table
- **`onboarding_progress`** - User onboarding step completion
- **`error_logs`** - Application error tracking
- **`error_stats`** - Aggregated error statistics

## 🔧 **System Tables**
- **`auth.*`** - Supabase authentication tables
- **`storage.*`** - File storage management
- **`realtime.*`** - Real-time messaging system
- **`extensions.*`** - Database extensions and stats

## 🔗 **Key Relationships**
- `couples.id` → `user_events.couple_id`
- `couples.id` → `timeline_events.couple_id`
- `milestone_templates.id` → `couple_milestones.milestone_template_id`
- `timeline_events.id` → `timeline_photos.timeline_event_id`
- `auth.users.id` → `profiles.id`, `user_events.user_id`, etc.

## 📚 **Related Documentation**
- **[Architecture Rationale](./architecture-rationale.md)** - Design decisions and performance considerations
- **[Complete Field Reference](./complete-field-reference.md)** - Detailed field specifications for all tables
- **[Migration Scripts](../migrations/)** - SQL migration files

---
*Quick reference for developers - see related docs for complete details*
