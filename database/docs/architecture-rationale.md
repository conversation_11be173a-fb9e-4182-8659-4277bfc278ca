# Database Schema Architecture Rationale

## 🎯 **Design Philosophy: Separation of Concerns with Seamless Integration**

### **Core Architectural Decisions**

## 1. **Hybrid Table Strategy**

### **Why Keep `origin_story` Separate?**
✅ **Optimized for Structure**: Form-based editing with known fields  
✅ **Performance**: Single row per couple, fast reads/writes  
✅ **Data Integrity**: Structured validation for milestone dates  
✅ **User Experience**: Dedicated UI flows for story creation  

### **Why Transform `scrapbook` to `timeline_events`?**
✅ **Horizontal Scaling**: Each event = separate row (unlimited growth)  
✅ **Flexible Schema**: JSONB metadata adapts to any event type  
✅ **Query Performance**: Optimized indexes for timeline operations  
✅ **Future-Proof**: New event types require no schema changes  

## 2. **Timeline Events Table Design**

### **Event Type Classification**
```sql
event_type IN (
  'milestone',        -- From origin_story (first kiss, meeting, etc.)
  'date_night',       -- App activities (restaurants, movies)
  'meal',            -- Cooking together, meal planning
  'weekly_activity', -- Relationship challenges completed
  'achievement',     -- Points, levels, badges earned
  'custom',          -- User-created memories
  'anniversary',     -- Auto-generated recurring events
  'photo_memory'     -- Photo-focused timeline entries
)
```

**Rationale**: Enables efficient filtering, different UI treatments, and analytics.

### **Source Tracking System**
```sql
source_type IN (
  'origin_story',    -- Auto-imported milestones
  'app_activity',    -- Generated from app usage
  'user_created',    -- Manual entries
  'system_generated' -- Anniversaries, reminders
)
```

**Benefits**:
- **Audit Trail**: Know where each event originated
- **Sync Logic**: Prevent duplicate imports
- **Data Quality**: Different validation rules per source
- **User Trust**: Transparency in data sources

## 3. **Metadata Strategy: JSONB for Maximum Flexibility**

### **Why JSONB Over Separate Columns?**
✅ **Schema Evolution**: Add new fields without migrations  
✅ **Event Diversity**: Different events need different data  
✅ **Performance**: GIN indexes enable fast JSON queries  
✅ **Developer Experience**: Easy to work with in code  

### **Sample Metadata Structures**
```json
// Milestone Event
{
  "milestone_type": "first_meeting",
  "location": "Central Park, NYC",
  "photos": ["photo1.jpg"],
  "imported_from_origin_story": true
}

// App Activity Event  
{
  "activity_type": "date_night",
  "restaurant": "Italian Bistro",
  "rating": 5,
  "app_activity_id": "date_123"
}

// Custom Memory
{
  "location": "Beach House",
  "tags": ["vacation", "romantic"],
  "notes": "Perfect weekend getaway"
}
```

## 4. **Photo Storage Strategy**

### **Separate `timeline_photos` Table**
**Why Not Store in Metadata?**
✅ **Performance**: Avoid large JSONB objects  
✅ **Querying**: Efficient photo-specific queries  
✅ **Ordering**: Explicit display_order for galleries  
✅ **Metadata**: Rich photo attributes (size, dimensions)  

## 5. **Indexing Strategy for Timeline Queries**

### **Primary Timeline Index**
```sql
CREATE INDEX idx_timeline_events_couple_date 
ON timeline_events(couple_id, event_date DESC);
```
**Optimizes**: Main timeline view (most recent first)

### **Type-Specific Queries**
```sql
CREATE INDEX idx_timeline_events_type 
ON timeline_events(couple_id, event_type);
```
**Optimizes**: Filtered views (only milestones, only photos, etc.)

### **Metadata Search**
```sql
CREATE INDEX idx_timeline_events_metadata 
ON timeline_events USING GIN (metadata);
```
**Optimizes**: Search within event details, tags, locations

## 6. **Data Synchronization Strategy**

### **Origin Story → Timeline Events**
```sql
-- Automatic sync trigger (conceptual)
WHEN origin_story.first_meeting_date IS UPDATED
  → CREATE/UPDATE timeline_events WHERE source_type = 'origin_story'
```

**Benefits**:
- **Single Source of Truth**: Origin story remains authoritative
- **Timeline Completeness**: All milestones appear in timeline
- **Consistency**: Changes sync automatically

## 7. **Scalability Considerations**

### **Horizontal Scaling Ready**
- **Partitioning**: Can partition by couple_id or date ranges
- **Sharding**: Events naturally shard by couple
- **Archiving**: Old events can be archived by date

### **Query Performance**
- **Pagination**: Efficient LIMIT/OFFSET with date ordering
- **Filtering**: Compound indexes support complex filters
- **Aggregation**: Fast counts and statistics

## 8. **Security & Privacy**

### **Row Level Security**
```sql
-- Only couple members can see their events
WHERE couple_id IN (
  SELECT id FROM couples 
  WHERE partner1_user_id = auth.uid() OR partner2_user_id = auth.uid()
)
```

### **Data Isolation**
- **Couple-Scoped**: All data isolated by couple_id
- **Partner Attribution**: Track who created/modified what
- **Visibility Controls**: Events can be hidden without deletion

## 9. **Migration Strategy**

### **Phase 1**: Create new tables alongside existing
### **Phase 2**: Migrate existing scrapbook data
### **Phase 3**: Implement sync logic for origin_story
### **Phase 4**: Update application code
### **Phase 5**: Remove old scrapbook table

## 10. **Future Extensibility**

### **New Event Types**
- Add to `event_type` enum
- Define metadata structure
- No schema changes needed

### **Rich Media Support**
- Videos: Add to timeline_photos (rename to timeline_media)
- Audio: Metadata can store audio file references
- Documents: Flexible metadata supports any file type

### **Advanced Features**
- **Collaborative Editing**: `last_updated_by` tracks changes
- **Event Reactions**: Separate table can reference timeline_events
- **Event Comments**: Timeline becomes social within couple
- **AI Insights**: Metadata enables ML analysis of relationship patterns

## 📊 **Performance Benchmarks (Projected)**

| Operation | Current | Optimized |
|-----------|---------|-----------|
| Load Timeline (30 days) | ~200ms | ~50ms |
| Add New Event | ~100ms | ~25ms |
| Search Events | ~500ms | ~75ms |
| Photo Gallery Load | ~300ms | ~60ms |

## 🎯 **Success Metrics**

1. **Scalability**: Support 10K+ events per couple
2. **Performance**: <100ms for all timeline queries
3. **Flexibility**: Add new event types without downtime
4. **Consistency**: 100% sync between origin_story and timeline
5. **User Experience**: Rich, fast, intuitive timeline interface
