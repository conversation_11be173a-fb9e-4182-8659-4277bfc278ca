const packageJson = require('./package.json');

export default {
  expo: {
    name: 'Nestled',
    slug: 'nestled-couples-app',
    version: packageJson.version, // Dynamic from package.json
    orientation: 'portrait',
    icon: './src/assets/images/icons/favicon.png',
    favicon: './src/assets/images/icons/favicon.png',
    splash: {
      backgroundColor: process.env.EXPO_PUBLIC_BRAND_BACKGROUND || '#CBC3E3'
    },
    web: {
      favicon: './src/assets/images/icons/favicon.png'
    },
    ios: {
      buildNumber: process.env.EXPO_PUBLIC_BUILD_NUMBER || '1',
    },
    android: {
      versionCode: parseInt(process.env.EXPO_PUBLIC_VERSION_CODE || '1'),
    }
  }
};
