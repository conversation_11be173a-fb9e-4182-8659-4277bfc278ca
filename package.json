{"name": "bolt-expo-starter", "main": "expo-router/entry", "version": "1.0.0", "private": true, "scripts": {"dev": "NODE_ENV=development EXPO_NO_TELEMETRY=1 expo start", "build:web": "NODE_ENV=production expo export --platform web", "build:ios": "NODE_ENV=production expo build:ios", "build:android": "NODE_ENV=production expo build:android", "build:production": "NODE_ENV=production eas build --platform all --profile production", "lint": "expo lint", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:performance": "jest __tests__/performance/", "test:onboarding": "jest --testPathPattern=journeys/onboarding", "test:daily": "jest --testPathPattern=journeys/daily", "test:activities": "jest --testPathPattern=journeys/activities", "test:memories": "jest --testPathPattern=journeys/memories", "test:planning": "jest --testPathPattern=journeys/planning", "test:progress": "jest --testPathPattern=journeys/progress", "test:journeys": "jest --testPathPattern=journeys/", "test:shared": "jest --testPathPattern=shared/", "security:audit": "npm audit --audit-level=moderate", "security:check": "npm audit && npm outdated", "test:security": "npm run lint && npm run security:audit", "debug": "./debug-app.sh", "debug:win": "debug-app.bat"}, "dependencies": {"@expo/ngrok": "^4.1.3", "@expo/vector-icons": "^14.1.0", "@lucide/lab": "^0.1.2", "@react-native-async-storage/async-storage": "^2.2.0", "@react-native-community/datetimepicker": "^8.4.5", "@react-native-community/netinfo": "^11.4.1", "@react-native-picker/picker": "^2.11.2", "@react-navigation/bottom-tabs": "^7.4.7", "@react-navigation/core": "^7.12.4", "@react-navigation/native": "^7.1.17", "@supabase/supabase-js": "^2.57.0", "@types/qrcode": "^1.5.5", "expo": "~53.0.22", "expo-blur": "~14.1.5", "expo-camera": "~16.1.11", "expo-constants": "~17.1.7", "expo-crypto": "^14.1.5", "expo-font": "~13.3.2", "expo-haptics": "~14.1.4", "expo-image-picker": "^16.1.4", "expo-linear-gradient": "~14.1.5", "expo-linking": "~7.1.7", "expo-router": "~5.1.5", "expo-secure-store": "~14.2.4", "expo-splash-screen": "~0.30.10", "expo-status-bar": "~2.2.3", "expo-symbols": "~0.4.5", "expo-system-ui": "~5.0.11", "expo-web-browser": "~14.2.0", "lucide-react-native": "^0.460.0", "nanoid": "^3.3.7", "qrcode": "^1.5.4", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.5", "react-native-gesture-handler": "~2.24.0", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-svg": "15.11.2", "react-native-url-polyfill": "^2.0.0", "react-native-web": "^0.20.0", "react-native-webview": "13.13.5", "zod": "^4.1.8"}, "devDependencies": {"@babel/core": "^7.25.2", "@eslint/js": "^9.35.0", "@testing-library/jest-native": "^5.4.3", "@testing-library/react-native": "^13.3.3", "@types/jest": "^30.0.0", "@types/react": "~19.0.10", "@typescript-eslint/eslint-plugin": "^8.44.0", "@typescript-eslint/parser": "^8.44.0", "eslint": "^9.0.0", "eslint-config-expo": "~9.2.0", "eslint-plugin-import": "^2.32.0", "eslint-plugin-react-hooks": "^5.2.0", "jest": "^30.1.3", "jest-environment-jsdom": "^30.1.2", "supabase": "^2.40.7", "ts-jest": "^29.4.1", "typescript": "~5.8.3"}, "resolutions": {"nanoid": "^3.3.7"}, "overrides": {"nanoid": "^3.3.7", "lucide-react-native": {"react": "$react"}}}