/**
 * Index Screen - Optimized
 *
 * Optimized version with cleaner routing logic and better error handling.
 * Demonstrates improved maintainability and user experience.
 *
 * <AUTHOR> Us Team
 * @version 2.0.0
 */

import { Redirect } from 'expo-router';
import React, { useCallback, useEffect, useState } from 'react';
import { ActivityIndicator, StyleSheet, Text, View } from 'react-native';
import { useAuth } from '../src/journeys/onboarding/useAuth';
import { ScreenLayout } from '../src/shared/components/common/LayoutComponents';
import { useGlobalTheme } from '../src/shared/components/common/ThemeProvider';
import { ErrorScreen } from '../src/shared/components/layout/ErrorHandler';
import { setupGlobalErrorHandling, simpleErrorService } from '../src/shared/services/system/simpleErrorService';
import { FEATURE_FLAGS } from '../src/utils/constants';

// Import shared components

type AppState = 'loading' | 'login' | 'main' | 'error';

export default function Index() {
  const { isAuthenticated, isLoading, isInitialized } = useAuth();
  const { currentTheme, isDarkMode: _isDarkMode } = useGlobalTheme();
  const [appState, setAppState] = useState<AppState>('loading');
  const [error, setError] = useState<string | null>(null);

  // Conditional debug logging removed

  const determineAppState = useCallback(async (): Promise<AppState> => {
    try {
      // Skip onboarding entirely and check authentication based on feature flag
      if (FEATURE_FLAGS.ENABLE_AUTHENTICATION) {
        if (isAuthenticated) {
          return 'main';
        }
        return 'login';
      } else {
        // Guest mode or authentication disabled
        console.warn('Authentication is disabled. This should only be used in development.');
        return 'main';
      }
    } catch (err) {
      console.error('Error determining app state:', err);
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      setError(errorMessage);

      // Report the error
      await simpleErrorService.reportError(err as Error, {
        component: 'IndexScreen',
        action: 'determineAppState',
        userId: isAuthenticated ? 'authenticated' : 'unauthenticated',
      });

      return 'error';
    }
  }, [isAuthenticated]);

  const initializeApp = useCallback(async () => {
    if (!isInitialized || isLoading) {
      return;
    }

    const newState = await determineAppState();
    setAppState(newState);
  }, [isInitialized, isLoading, determineAppState]);

  const handleRetry = useCallback(() => {
    setError(null);
    setAppState('loading');
    initializeApp();
  }, [initializeApp]);

  useEffect(() => {
    // Set up global error handling
    setupGlobalErrorHandling();
  }, []);

  useEffect(() => {
    initializeApp();
  }, [initializeApp]);

  // Error state
  if (appState === 'error' && error) {
    return (
      <ErrorScreen
        error={error}
        onRetry={handleRetry}
      />
    );
  }

  // Loading state
  if (appState === 'loading' || !isInitialized || isLoading) {
    return (
      <ScreenLayout>
        <View style={[styles.loadingContainer, { backgroundColor: currentTheme.background }]}>
          <ActivityIndicator size="large" color={currentTheme.primary} />
          <Text style={[styles.loadingText, { color: currentTheme.text }]}>Loading...</Text>
        </View>
      </ScreenLayout>
    );
  }

  // Route based on app state
  switch (appState) {
    case 'login':
      return <Redirect href="/auth/welcome" />;

    case 'main':
      return <Redirect href="/(tabs)" />;

    default:
      return <Redirect href="/(tabs)" />;
  }
}

// eslint-disable-next-line no-restricted-syntax
const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    // Color will be applied dynamically via theme
  },
});
