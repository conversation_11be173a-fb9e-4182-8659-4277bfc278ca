import { router } from 'expo-router';
import { ArrowLeft } from 'lucide-react-native';
import React from 'react';
import { StyleSheet, TouchableOpacity, View } from 'react-native';
// AuthScreen component removed - this route is no longer used
import { DSHeaderBar } from '../src/components/shared';
import { colors } from '../src/shared/utils/colors';

export default function Auth() {
  const handleBackPress = () => {
    router.back();
  };

  const _handleAuthSuccess = () => {
    router.replace('/(tabs)');
  };

  return (
    <View style={styles.container}>
      <DSHeaderBar
        title="Create Account"
        tone="neutral"
        left={
          <TouchableOpacity onPress={handleBackPress}>
            <ArrowLeft size={24} color={colors.white} />
          </TouchableOpacity>
        }
      />

      {/* AuthScreen component removed - this route is no longer used */}
    </View>
  );
}

// eslint-disable-next-line no-restricted-syntax
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
});
