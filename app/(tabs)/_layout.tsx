import { Tabs } from 'expo-router';
import { Chrome as Home, BookOpen, Calendar, Gamepad2 } from 'lucide-react-native';
import { useGlobalTheme } from '../../src/shared/components/common/ThemeProvider';
import { tokens } from '../../src/shared/utils/theme';

export default function TabLayout() {
  const { currentTheme } = useGlobalTheme();

  return (
    <Tabs
      screenOptions={{
        headerShown: false,
        tabBarActiveTintColor: currentTheme.primary,
        tabBarInactiveTintColor: currentTheme.textSecondary,
        tabBarStyle: {
          backgroundColor: currentTheme.surface,
          borderTopWidth: tokens.borders.thin,
          borderTopColor: currentTheme.border,
          paddingTop: tokens.spacing.xs,
          paddingBottom: tokens.spacing.xs,
          height: tokens.sizes.layout.tabBar,
        },
        tabBarLabelStyle: {
          fontSize: tokens.typography.fontSize.sm,
          fontWeight: tokens.typography.fontWeight.medium,
          marginTop: tokens.spacing.xxs,
          color: currentTheme.text,
        },
      }}>
      <Tabs.Screen
        name="index"
        options={{
          title: 'Home',
          tabBarIcon: ({ size, color }) => (
            <Home size={size} color={color} />
          ),
        }}
      />
      <Tabs.Screen
        name="modules"
        options={{
          title: 'Modules',
          tabBarIcon: ({ size, color }) => (
            <BookOpen size={size} color={color} />
          ),
        }}
      />
      <Tabs.Screen
        name="date-night"
        options={{
          title: 'Date Night',
          tabBarIcon: ({ size, color }) => (
            <Calendar size={size} color={color} />
          ),
        }}
      />
      <Tabs.Screen
        name="activities"
        options={{
          title: 'Activities',
          tabBarIcon: ({ size, color }) => (
            <Gamepad2 size={size} color={color} />
          ),
        }}
      />
    </Tabs>
  );
}