import React from 'react';
import { SafeAreaView, StatusBar } from 'react-native';
import DateNight from '../../src/journeys/activities/DateNight';
import HamburgerMenu from '../../src/shared/components/layout/HamburgerMenu';
import { colors } from '../../src/shared/utils/colors';

export default function DateNightScreen() {
  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: colors.background }}>
      <StatusBar barStyle="dark-content" backgroundColor={colors.background} />
      <HamburgerMenu position="top-right" />
      <DateNight />
    </SafeAreaView>
  );
}
