/**
 * Activities History Route
 *
 * Shows the history of completed activities
 * Route: /activities/history
 */

import { router } from 'expo-router';
import { ArrowLeft, CheckCircle, Clock } from 'lucide-react-native';
import React from 'react';
import { ScrollView, StyleSheet, Text, TouchableOpacity, View } from 'react-native';

export default function ActivitiesHistoryScreen() {
  const handleGoBack = () => {
    router.back();
  };

  // Placeholder data - replace with actual activity history
  const activityHistory = [
    { id: 1, name: 'Date Night Planning', completedAt: '2024-01-15', type: 'Planning' },
    { id: 2, name: 'Love Language Quiz', completedAt: '2024-01-14', type: 'Assessment' },
    { id: 3, name: 'Memory Lane', completedAt: '2024-01-13', type: 'Reflection' },
  ];

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity onPress={handleGoBack} style={styles.backButton}>
          <ArrowLeft size={24} color="#393939" />
        </TouchableOpacity>
        <Text style={styles.title}>Activity History</Text>
      </View>

      <ScrollView style={styles.content}>
        <Text style={styles.subtitle}>Your Completed Activities</Text>

        {activityHistory.map((activity) => (
          <View key={activity.id} style={styles.activityCard}>
            <View style={styles.activityHeader}>
              <CheckCircle size={20} color="#10B981" />
              <Text style={styles.activityName}>{activity.name}</Text>
            </View>
            <Text style={styles.activityType}>{activity.type}</Text>
            <View style={styles.activityFooter}>
              <Clock size={16} color="#6B7280" />
              <Text style={styles.completedDate}>Completed on {activity.completedAt}</Text>
            </View>
          </View>
        ))}

        <TouchableOpacity
          style={styles.button}
          onPress={() => router.push('/(tabs)/activities')}
        >
          <Text style={styles.buttonText}>Back to Activities</Text>
        </TouchableOpacity>
      </ScrollView>
    </View>
  );
}

// eslint-disable-next-line no-restricted-syntax
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FAFAFA',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  backButton: {
    marginRight: 16,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#393939',
  },
  content: {
    flex: 1,
    padding: 20,
  },
  subtitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#393939',
    marginBottom: 20,
  },
  activityCard: {
    backgroundColor: '#FFFFFF',
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  activityHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  activityName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#393939',
    marginLeft: 8,
  },
  activityType: {
    fontSize: 14,
    color: '#9CAF88',
    marginBottom: 8,
  },
  activityFooter: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  completedDate: {
    fontSize: 12,
    color: '#6B7280',
    marginLeft: 4,
  },
  button: {
    backgroundColor: '#9CAF88',
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
    marginTop: 20,
  },
  buttonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
});
