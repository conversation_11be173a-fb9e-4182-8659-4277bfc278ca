/**
 * Email Verification Screen
 * 
 * Screen for handling email verification tokens and providing
 * user feedback during the verification process.
 * 
 * @version 3.0.0
 * <AUTHOR> Us Team
 */

import { router, useLocalSearchParams } from 'expo-router';
import { CheckCircle, XCircle, Mail } from 'lucide-react-native';
import React, { useState, useEffect } from 'react';
import { StyleSheet, Text, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import {
  AuthScreenLayout,
  AuthHeader,
  AuthButton,
  AuthLink,
} from '../../src/components/auth/AuthComponents';
import { GlassView } from '../../src/shared/components/common/GlassView';
import { useAuth } from '../../src/hooks/useAuth';
import { brandColors } from '../../src/shared/utils/colors';
import { logger } from '../../src/shared/utils/logger';

type VerificationState = 'verifying' | 'success' | 'error' | 'expired';

export default function VerifyEmailScreen() {
  const { token } = useLocalSearchParams<{ token?: string }>();
  const [verificationState, setVerificationState] = useState<VerificationState>('verifying');
  const [errorMessage, setErrorMessage] = useState('');

  const { verifyEmail } = useAuth();

  useEffect(() => {
    if (token) {
      handleVerifyEmail(token);
    } else {
      setVerificationState('error');
      setErrorMessage('Invalid verification link');
    }
  }, [token]);

  const handleVerifyEmail = async (verificationToken: string) => {
    try {
      setVerificationState('verifying');
      
      await verifyEmail(verificationToken);
      
      logger.info('Email verification successful');
      setVerificationState('success');
    } catch (error) {
      logger.error('Email verification error:', error);
      
      const errorMessage = error instanceof Error ? error.message : 'Verification failed';
      
      if (errorMessage.includes('expired') || errorMessage.includes('invalid')) {
        setVerificationState('expired');
        setErrorMessage('This verification link has expired or is invalid');
      } else {
        setVerificationState('error');
        setErrorMessage(errorMessage);
      }
    }
  };

  const handleContinueToLogin = () => {
    router.replace('/auth/login');
  };

  const handleResendVerification = () => {
    // TODO: Implement resend verification email
    router.push('/auth/signup');
  };

  const renderContent = () => {
    switch (verificationState) {
      case 'verifying':
        return (
          <View style={styles.contentContainer}>
            <Mail size={80} color="#FFFFFF" style={styles.icon} />
            
            <AuthHeader
              title="Verifying Your Email"
              subtitle="Please wait while we verify your email address..."
              emoji=""
            />

            <GlassView style={styles.messageContainer}>
              <Text style={styles.messageText}>
                This should only take a moment.
              </Text>
            </GlassView>
          </View>
        );

      case 'success':
        return (
          <View style={styles.contentContainer}>
            <CheckCircle size={80} color="#10B981" style={styles.icon} />
            
            <AuthHeader
              title="Email Verified!"
              subtitle="Your email has been successfully verified. You can now sign in to your account."
              emoji=""
            />

            <GlassView style={styles.messageContainer}>
              <Text style={styles.successTitle}>Welcome to Nestled!</Text>
              <Text style={styles.messageText}>
                Your account is now active and ready to use. Start building stronger connections with your partner today.
              </Text>
            </GlassView>

            <View style={styles.actionsContainer}>
              <AuthButton
                title="Continue to Sign In"
                onPress={handleContinueToLogin}
                variant="primary"
              />
            </View>
          </View>
        );

      case 'expired':
        return (
          <View style={styles.contentContainer}>
            <XCircle size={80} color="#F59E0B" style={styles.icon} />
            
            <AuthHeader
              title="Link Expired"
              subtitle="This verification link has expired or is no longer valid."
              emoji=""
            />

            <GlassView style={styles.messageContainer}>
              <Text style={styles.errorTitle}>What happened?</Text>
              <Text style={styles.messageText}>
                Verification links expire after 24 hours for security reasons. Don't worry - you can request a new one.
              </Text>
            </GlassView>

            <View style={styles.actionsContainer}>
              <AuthButton
                title="Get New Verification Link"
                onPress={handleResendVerification}
                variant="primary"
              />

              <AuthLink
                text="Already verified?"
                linkText="Sign In"
                onPress={handleContinueToLogin}
              />
            </View>
          </View>
        );

      case 'error':
      default:
        return (
          <View style={styles.contentContainer}>
            <XCircle size={80} color="#EF4444" style={styles.icon} />
            
            <AuthHeader
              title="Verification Failed"
              subtitle="We couldn't verify your email address."
              emoji=""
            />

            <GlassView style={styles.messageContainer}>
              <Text style={styles.errorTitle}>Error Details</Text>
              <Text style={styles.messageText}>
                {errorMessage || 'An unexpected error occurred during verification.'}
              </Text>
            </GlassView>

            <View style={styles.actionsContainer}>
              <AuthButton
                title="Try Again"
                onPress={handleResendVerification}
                variant="primary"
              />

              <AuthLink
                text="Need help?"
                linkText="Contact Support"
                onPress={() => {
                  // TODO: Implement support contact
                  console.log('Contact support');
                }}
              />
            </View>
          </View>
        );
    }
  };

  return (
    <SafeAreaView style={styles.safeArea}>
      <AuthScreenLayout backgroundColor={brandColors.sageGreen}>
        {renderContent()}
      </AuthScreenLayout>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: brandColors.sageGreen,
  },
  contentContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  icon: {
    marginBottom: 24,
  },
  messageContainer: {
    padding: 24,
    borderRadius: 16,
    marginVertical: 32,
    width: '100%',
  },
  messageText: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.8)',
    textAlign: 'center',
    lineHeight: 20,
  },
  successTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#10B981',
    marginBottom: 12,
    textAlign: 'center',
  },
  errorTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#EF4444',
    marginBottom: 12,
    textAlign: 'center',
  },
  actionsContainer: {
    width: '100%',
    alignItems: 'center',
  },
});
