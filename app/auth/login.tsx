/**
 * Login Screen
 *
 * Secure login screen with biometric authentication support,
 * input validation, and comprehensive error handling.
 *
 * @version 3.0.0
 * <AUTHOR> Us Team
 */

import { router } from 'expo-router';
import { ArrowLeft, Fingerprint, Lock, Mail } from 'lucide-react-native';
import React, { useEffect, useState } from 'react';
import { Alert, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import {
    AuthButton,
    AuthDivider,
    AuthHeader,
    AuthInput,
    AuthLink,
    AuthScreenLayout,
    SocialButton,
} from '../../src/components/auth/AuthComponents';
import { useAuth } from '../../src/hooks/useAuth';
import { brandColors } from '../../src/shared/utils/colors';
import { logger } from '../../src/shared/utils/logger';

export default function LoginScreen() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [errors, setErrors] = useState<{ email?: string; password?: string; general?: string }>({});
  const [isLoading, setIsLoading] = useState(false);

  const {
    signIn,
    signInWithBiometric,
    signInWithGoogle,
    signInWithApple,
    isBiometricEnabled,
    biometricType,
  } = useAuth();

  // Clear errors when user starts typing
  useEffect(() => {
    if (errors.email && email) setErrors(prev => ({ ...prev, email: undefined }));
  }, [email, errors.email]);

  useEffect(() => {
    if (errors.password && password) setErrors(prev => ({ ...prev, password: undefined }));
  }, [password, errors.password]);

  const validateForm = () => {
    const newErrors: typeof errors = {};

    if (!email.trim()) {
      newErrors.email = 'Email is required';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
      newErrors.email = 'Please enter a valid email address';
    }

    if (!password.trim()) {
      newErrors.password = 'Password is required';
    } else if (password.length < 8) {
      newErrors.password = 'Password must be at least 8 characters';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleLogin = async () => {
    if (!validateForm()) return;

    try {
      setIsLoading(true);
      setErrors({});

      await signIn(email.trim().toLowerCase(), password);

      logger.info('Login successful');
      router.replace('/(tabs)');
    } catch (error) {
      logger.error('Login error:', error);

      const errorMessage = error instanceof Error ? error.message : 'Login failed';

      // Handle specific error types
      if (errorMessage.includes('Invalid login credentials')) {
        setErrors({ general: 'Invalid email or password. Please try again.' });
      } else if (errorMessage.includes('Email not confirmed')) {
        setErrors({ general: 'Please verify your email address before signing in.' });
      } else if (errorMessage.includes('Too many requests')) {
        setErrors({ general: 'Too many login attempts. Please try again later.' });
      } else {
        setErrors({ general: errorMessage });
      }
    } finally {
      setIsLoading(false);
    }
  };

  const handleBiometricLogin = async () => {
    try {
      setIsLoading(true);
      setErrors({});

      await signInWithBiometric();

      logger.info('Biometric login successful');
      router.replace('/(tabs)');
    } catch (error) {
      logger.error('Biometric login error:', error);

      const errorMessage = error instanceof Error ? error.message : 'Biometric login failed';
      setErrors({ general: errorMessage });
    } finally {
      setIsLoading(false);
    }
  };

  const handleGoogleSignIn = async () => {
    try {
      setIsLoading(true);
      setErrors({});

      await signInWithGoogle();

      logger.info('Google sign in successful');
      router.replace('/(tabs)');
    } catch (error) {
      logger.error('Google sign in error:', error);

      const errorMessage = error instanceof Error ? error.message : 'Google sign in failed';
      if (!errorMessage.includes('not yet implemented')) {
        setErrors({ general: errorMessage });
      } else {
        Alert.alert('Coming Soon', 'Google Sign-In will be available in a future update.');
      }
    } finally {
      setIsLoading(false);
    }
  };

  const handleAppleSignIn = async () => {
    try {
      setIsLoading(true);
      setErrors({});

      await signInWithApple();

      logger.info('Apple sign in successful');
      router.replace('/(tabs)');
    } catch (error) {
      logger.error('Apple sign in error:', error);

      const errorMessage = error instanceof Error ? error.message : 'Apple sign in failed';
      if (!errorMessage.includes('not yet implemented')) {
        setErrors({ general: errorMessage });
      } else {
        Alert.alert('Coming Soon', 'Apple Sign-In will be available in a future update.');
      }
    } finally {
      setIsLoading(false);
    }
  };

  const handleForgotPassword = () => {
    router.push('/auth/forgot-password');
  };

  const handleSignUp = () => {
    router.push('/auth/signup');
  };

  const handleBack = () => {
    router.back();
  };

  return (
    <SafeAreaView style={styles.safeArea}>
      <AuthScreenLayout backgroundColor={brandColors.sageGreen}>
        {/* Back Button */}
        <TouchableOpacity style={styles.backButton} onPress={handleBack}>
          <ArrowLeft size={24} color="#FFFFFF" />
        </TouchableOpacity>

        {/* Header */}
        <AuthHeader
          title="Welcome Back"
          subtitle="Sign in to continue your journey together"
          emoji="💕"
        />

        {/* Form */}
        <View style={styles.formContainer}>
          <AuthInput
            value={email}
            onChangeText={setEmail}
            placeholder="Enter your email"
            label="Email"
            type="email"
            leftIcon={<Mail size={20} color="rgba(255, 255, 255, 0.7)" />}
            error={errors.email}
          />

          <AuthInput
            value={password}
            onChangeText={setPassword}
            placeholder="Enter your password"
            label="Password"
            type="password"
            leftIcon={<Lock size={20} color="rgba(255, 255, 255, 0.7)" />}
            error={errors.password}
          />

          {/* Forgot Password Link */}
          <TouchableOpacity style={styles.forgotPassword} onPress={handleForgotPassword}>
            <AuthLink
              text=""
              linkText="Forgot Password?"
              onPress={handleForgotPassword}
            />
          </TouchableOpacity>

          {/* General Error */}
          {errors.general && (
            <View style={styles.errorContainer}>
              <Text style={styles.errorText}>{errors.general}</Text>
            </View>
          )}

          {/* Login Button */}
          <AuthButton
            title="Sign In"
            onPress={handleLogin}
            variant="primary"
            loading={isLoading}
            disabled={isLoading}
          />

          {/* Biometric Login */}
          {isBiometricEnabled && (
            <AuthButton
              title={`Sign in with ${biometricType}`}
              onPress={handleBiometricLogin}
              variant="outline"
              leftIcon={<Fingerprint size={20} color="#FFFFFF" />}
              loading={isLoading}
              disabled={isLoading}
            />
          )}

          <AuthDivider />

          {/* Social Login */}
          <SocialButton
            provider="apple"
            onPress={handleAppleSignIn}
            loading={isLoading}
            disabled={isLoading}
          />

          <SocialButton
            provider="google"
            onPress={handleGoogleSignIn}
            loading={isLoading}
            disabled={isLoading}
          />

          {/* Sign Up Link */}
          <AuthLink
            text="Don't have an account?"
            linkText="Sign Up"
            onPress={handleSignUp}
          />
        </View>
      </AuthScreenLayout>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: brandColors.sageGreen,
  },
  backButton: {
    position: 'absolute',
    top: 60,
    left: 24,
    zIndex: 1,
    padding: 8,
  },
  formContainer: {
    flex: 1,
    justifyContent: 'center',
  },
  forgotPassword: {
    alignSelf: 'flex-end',
    marginBottom: 24,
  },
  errorContainer: {
    backgroundColor: 'rgba(239, 68, 68, 0.1)',
    borderRadius: 8,
    padding: 12,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: 'rgba(239, 68, 68, 0.3)',
  },
  errorText: {
    color: '#EF4444',
    fontSize: 14,
    textAlign: 'center',
  },
});
