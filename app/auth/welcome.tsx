/**
 * Welcome Screen
 * 
 * Entry point for authentication flow with brand-compliant design
 * and glass-morphism effects.
 * 
 * @version 3.0.0
 * <AUTHOR> Us Team
 */

import { router } from 'expo-router';
import React from 'react';
import { StyleSheet, Text, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import {
  AuthScreenLayout,
  AuthHeader,
  AuthButton,
  AuthDivider,
  SocialButton,
} from '../../src/components/auth/AuthComponents';
import { GlassView } from '../../src/shared/components/common/GlassView';
import { brandColors } from '../../src/shared/utils/colors';
import { APP_INFO } from '../../src/utils/constants';

export default function WelcomeScreen() {
  const handleGetStarted = () => {
    router.push('/auth/signup');
  };

  const handleSignIn = () => {
    router.push('/auth/login');
  };

  const handleGoogleSignIn = () => {
    // TODO: Implement Google Sign-In
    console.log('Google Sign-In pressed');
  };

  const handleAppleSignIn = () => {
    // TODO: Implement Apple Sign-In
    console.log('Apple Sign-In pressed');
  };

  return (
    <SafeAreaView style={styles.safeArea}>
      <AuthScreenLayout backgroundColor={brandColors.sageGreen}>
        <View style={styles.container}>
          {/* Header Section */}
          <AuthHeader
            title={`Welcome to ${APP_INFO.NAME}`}
            subtitle={APP_INFO.TAGLINE}
            emoji="💕"
            style={styles.header}
          />

          {/* Feature Highlights */}
          <GlassView style={styles.featuresContainer}>
            <View style={styles.feature}>
              <Text style={styles.featureEmoji}>💬</Text>
              <Text style={styles.featureTitle}>Daily Questions</Text>
              <Text style={styles.featureDescription}>
                Deepen your connection with thoughtful daily prompts
              </Text>
            </View>
            
            <View style={styles.feature}>
              <Text style={styles.featureEmoji}>🎯</Text>
              <Text style={styles.featureTitle}>Shared Activities</Text>
              <Text style={styles.featureDescription}>
                Discover new experiences to enjoy together
              </Text>
            </View>
            
            <View style={styles.feature}>
              <Text style={styles.featureEmoji}>📖</Text>
              <Text style={styles.featureTitle}>Your Story</Text>
              <Text style={styles.featureDescription}>
                Build and preserve your unique love story
              </Text>
            </View>
          </GlassView>

          {/* Action Buttons */}
          <View style={styles.actionsContainer}>
            <AuthButton
              title="Get Started Together"
              onPress={handleGetStarted}
              variant="primary"
              style={styles.primaryButton}
            />

            <AuthButton
              title="I already have an account"
              onPress={handleSignIn}
              variant="outline"
              style={styles.secondaryButton}
            />

            <AuthDivider text="or continue with" />

            <View style={styles.socialButtonsContainer}>
              <SocialButton
                provider="apple"
                onPress={handleAppleSignIn}
                style={styles.socialButton}
              />
              
              <SocialButton
                provider="google"
                onPress={handleGoogleSignIn}
                style={styles.socialButton}
              />
            </View>
          </View>
        </View>
      </AuthScreenLayout>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: brandColors.sageGreen,
  },
  container: {
    flex: 1,
    justifyContent: 'space-between',
    paddingVertical: 20,
  },
  header: {
    marginBottom: 40,
  },
  featuresContainer: {
    padding: 24,
    borderRadius: 16,
    marginBottom: 40,
  },
  feature: {
    alignItems: 'center',
    marginBottom: 24,
  },
  featureEmoji: {
    fontSize: 32,
    marginBottom: 8,
  },
  featureTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#FFFFFF',
    marginBottom: 4,
    textAlign: 'center',
  },
  featureDescription: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.8)',
    textAlign: 'center',
    lineHeight: 20,
  },
  actionsContainer: {
    paddingTop: 20,
  },
  primaryButton: {
    marginBottom: 16,
  },
  secondaryButton: {
    marginBottom: 8,
  },
  socialButtonsContainer: {
    gap: 12,
  },
  socialButton: {
    marginBottom: 8,
  },
});
