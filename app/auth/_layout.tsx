/**
 * Authentication Layout
 * 
 * Stack navigation for authentication screens with proper
 * screen configuration and transitions.
 * 
 * @version 3.0.0
 * <AUTHOR> Us Team
 */

import { Stack } from 'expo-router';
import React from 'react';
import { StatusBar } from 'expo-status-bar';

export default function AuthLayout() {
  return (
    <>
      <Stack
        screenOptions={{
          headerShown: false,
          gestureEnabled: true,
          animation: 'slide_from_right',
        }}
      >
        <Stack.Screen
          name="welcome"
          options={{
            title: 'Welcome',
          }}
        />
        <Stack.Screen
          name="login"
          options={{
            title: 'Sign In',
          }}
        />
        <Stack.Screen
          name="signup"
          options={{
            title: 'Sign Up',
          }}
        />
        <Stack.Screen
          name="forgot-password"
          options={{
            title: 'Forgot Password',
          }}
        />
        <Stack.Screen
          name="verify-email"
          options={{
            title: 'Verify Email',
          }}
        />
      </Stack>
      <StatusBar style="light" />
    </>
  );
}
