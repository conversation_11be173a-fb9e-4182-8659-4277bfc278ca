/**
 * Forgot Password Screen
 * 
 * Password reset screen with email validation and user feedback.
 * 
 * @version 3.0.0
 * <AUTHOR> Us Team
 */

import { router } from 'expo-router';
import { ArrowLeft, Mail, CheckCircle } from 'lucide-react-native';
import React, { useState, useEffect } from 'react';
import { StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import {
  AuthScreenLayout,
  AuthHeader,
  AuthInput,
  AuthButton,
  AuthLink,
} from '../../src/components/auth/AuthComponents';
import { GlassView } from '../../src/shared/components/common/GlassView';
import { useAuth } from '../../src/hooks/useAuth';
import { brandColors } from '../../src/shared/utils/colors';
import { logger } from '../../src/shared/utils/logger';

export default function ForgotPasswordScreen() {
  const [email, setEmail] = useState('');
  const [error, setError] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isEmailSent, setIsEmailSent] = useState(false);

  const { resetPassword } = useAuth();

  // Clear error when user starts typing
  useEffect(() => {
    if (error && email) setError('');
  }, [email, error]);

  const validateEmail = (emailValue: string) => {
    if (!emailValue.trim()) {
      return 'Email is required';
    }
    if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(emailValue)) {
      return 'Please enter a valid email address';
    }
    return '';
  };

  const handleResetPassword = async () => {
    const emailError = validateEmail(email);
    if (emailError) {
      setError(emailError);
      return;
    }

    try {
      setIsLoading(true);
      setError('');

      await resetPassword(email.trim().toLowerCase());

      logger.info('Password reset email sent');
      setIsEmailSent(true);
    } catch (error) {
      logger.error('Password reset error:', error);

      const errorMessage = error instanceof Error ? error.message : 'Failed to send reset email';
      
      // Handle specific error types
      if (errorMessage.includes('User not found')) {
        setError('No account found with this email address');
      } else if (errorMessage.includes('Email not confirmed')) {
        setError('Please verify your email address first');
      } else if (errorMessage.includes('Too many requests')) {
        setError('Too many reset attempts. Please try again later.');
      } else {
        setError(errorMessage);
      }
    } finally {
      setIsLoading(false);
    }
  };

  const handleBackToLogin = () => {
    router.push('/auth/login');
  };

  const handleBack = () => {
    router.back();
  };

  const handleResendEmail = () => {
    setIsEmailSent(false);
    handleResetPassword();
  };

  if (isEmailSent) {
    return (
      <SafeAreaView style={styles.safeArea}>
        <AuthScreenLayout backgroundColor={brandColors.sageGreen}>
          {/* Back Button */}
          <TouchableOpacity style={styles.backButton} onPress={handleBack}>
            <ArrowLeft size={24} color="#FFFFFF" />
          </TouchableOpacity>

          {/* Success Content */}
          <View style={styles.successContainer}>
            <CheckCircle size={80} color="#FFFFFF" style={styles.successIcon} />
            
            <AuthHeader
              title="Check Your Email"
              subtitle={`We've sent a password reset link to ${email}`}
              emoji=""
            />

            <GlassView style={styles.instructionsContainer}>
              <Text style={styles.instructionsTitle}>What's next?</Text>
              <Text style={styles.instructionsText}>
                1. Check your email inbox (and spam folder)
              </Text>
              <Text style={styles.instructionsText}>
                2. Click the reset link in the email
              </Text>
              <Text style={styles.instructionsText}>
                3. Create a new password
              </Text>
              <Text style={styles.instructionsText}>
                4. Sign in with your new password
              </Text>
            </GlassView>

            <View style={styles.actionsContainer}>
              <AuthButton
                title="Back to Sign In"
                onPress={handleBackToLogin}
                variant="primary"
              />

              <AuthLink
                text="Didn't receive the email?"
                linkText="Resend"
                onPress={handleResendEmail}
              />
            </View>
          </View>
        </AuthScreenLayout>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.safeArea}>
      <AuthScreenLayout backgroundColor={brandColors.sageGreen}>
        {/* Back Button */}
        <TouchableOpacity style={styles.backButton} onPress={handleBack}>
          <ArrowLeft size={24} color="#FFFFFF" />
        </TouchableOpacity>

        {/* Header */}
        <AuthHeader
          title="Forgot Password?"
          subtitle="Enter your email address and we'll send you a link to reset your password"
          emoji="🔐"
        />

        {/* Form */}
        <View style={styles.formContainer}>
          <AuthInput
            value={email}
            onChangeText={setEmail}
            placeholder="Enter your email"
            label="Email"
            type="email"
            leftIcon={<Mail size={20} color="rgba(255, 255, 255, 0.7)" />}
            error={error}
          />

          <AuthButton
            title="Send Reset Link"
            onPress={handleResetPassword}
            variant="primary"
            loading={isLoading}
            disabled={isLoading}
          />

          <AuthLink
            text="Remember your password?"
            linkText="Back to Sign In"
            onPress={handleBackToLogin}
          />
        </View>
      </AuthScreenLayout>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: brandColors.sageGreen,
  },
  backButton: {
    position: 'absolute',
    top: 60,
    left: 24,
    zIndex: 1,
    padding: 8,
  },
  formContainer: {
    flex: 1,
    justifyContent: 'center',
  },
  successContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  successIcon: {
    marginBottom: 24,
  },
  instructionsContainer: {
    padding: 24,
    borderRadius: 16,
    marginVertical: 32,
    width: '100%',
  },
  instructionsTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#FFFFFF',
    marginBottom: 16,
    textAlign: 'center',
  },
  instructionsText: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.8)',
    marginBottom: 8,
    lineHeight: 20,
  },
  actionsContainer: {
    width: '100%',
    alignItems: 'center',
  },
});
