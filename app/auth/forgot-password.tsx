/**
 * Forgot Password Screen
 *
 * Password reset screen with email validation and user feedback.
 *
 * @version 3.0.0
 * <AUTHOR> Us Team
 */

import { router } from 'expo-router';
import { ArrowLeft, CheckCircle, Mail } from 'lucide-react-native';
import React, { useEffect, useState } from 'react';
import { StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import {
  AuthButton,
  AuthHeader,
  AuthInput,
  AuthLink,
  AuthScreenLayout,
} from '../../src/components/auth/AuthComponents';
import { useAuth } from '../../src/hooks/useAuth';
import { GlassView } from '../../src/shared/components/common/GlassView';
import { brandColors } from '../../src/shared/utils/colors';
import { logger } from '../../src/shared/utils/logger';

export default function ForgotPasswordScreen() {
  const [email, setEmail] = useState('');
  const [error, setError] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isEmailSent, setIsEmailSent] = useState(false);
  const [_isResending, _setIsResending] = useState(false);
  const [_resendCount, _setResendCount] = useState(0);
  const [_lastResendTime, _setLastResendTime] = useState<number | null>(null);

  const { resetPassword } = useAuth();

  // Clear error when user starts typing
  useEffect(() => {
    if (error && email) setError('');
  }, [email, error]);

  const validateEmail = (emailValue: string) => {
    if (!emailValue.trim()) {
      return 'Email is required';
    }
    if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(emailValue)) {
      return 'Please enter a valid email address';
    }
    return '';
  };

  const handleResetPassword = async () => {
    const emailError = validateEmail(email);
    if (emailError) {
      setError(emailError);
      return;
    }

    try {
      setIsLoading(true);
      setError('');

      await resetPassword(email.trim().toLowerCase());

      logger.info('Password reset email sent');
      setIsEmailSent(true);
    } catch (error) {
      logger.error('Password reset error:', error);

      const errorMessage = error instanceof Error ? error.message : 'Failed to send reset email';

      // Handle specific error types with couples-focused messaging
      if (errorMessage.includes('User not found')) {
        setError('We couldn\'t find an account with this email. Double-check the spelling or create a new account to start your journey.');
      } else if (errorMessage.includes('Email not confirmed')) {
        setError('Please verify your email address first. Check your inbox for the verification email we sent when you signed up.');
      } else if (errorMessage.includes('Too many requests')) {
        setError('We\'ve sent several reset emails recently. Please wait a few minutes before requesting another one.');
      } else if (errorMessage.includes('network') || errorMessage.includes('Network')) {
        setError('Connection issue. Please check your internet and try again.');
      } else {
        setError('Something went wrong. Please try again or contact support if the issue persists.');
      }
    } finally {
      setIsLoading(false);
    }
  };

  const handleBackToLogin = () => {
    router.push('/auth/login');
  };

  const handleBack = () => {
    router.back();
  };

  const handleResendEmail = async () => {
    // Rate limiting: prevent spam
    const now = Date.now();
    if (_lastResendTime && now - _lastResendTime < 30000) { // 30 seconds
      const remainingTime = Math.ceil((30000 - (now - _lastResendTime)) / 1000);
      setError(`Please wait ${remainingTime} seconds before requesting another email.`);
      return;
    }

    try {
      _setIsResending(true);
      setError('');

      await resetPassword(email.trim().toLowerCase());

      _setResendCount(prev => prev + 1);
      _setLastResendTime(now);

      logger.info('Password reset email resent');

      // Show success feedback
      setTimeout(() => {
        setError('');
      }, 3000);

    } catch (error) {
      logger.error('Resend password reset error:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to resend email';
      setError(`Resend failed: ${errorMessage}`);
    } finally {
      _setIsResending(false);
    }
  };

  if (isEmailSent) {
    return (
      <SafeAreaView style={styles.safeArea}>
        <AuthScreenLayout backgroundColor={brandColors.sageGreen}>
          {/* Back Button */}
          <TouchableOpacity style={styles.backButton} onPress={handleBack}>
            <ArrowLeft size={24} color="#FFFFFF" />
          </TouchableOpacity>

          {/* Success Content */}
          <View style={styles.successContainer}>
            <View style={styles.successIconContainer}>
              <CheckCircle size={64} color="#FFFFFF" style={styles.successIcon} />
              <Text style={styles.successEmoji}>💌</Text>
            </View>

            <AuthHeader
              title="Check Your Email"
              subtitle={`We've sent a secure password reset link to ${email}. Your relationship data is safe with us.`}
              emoji=""
            />

            <GlassView style={styles.instructionsContainer}>
              <Text style={styles.instructionsTitle}>What's next?</Text>
              <View style={styles.instructionsList}>
                <View style={styles.instructionItem}>
                  <View style={styles.instructionNumber}>
                    <Text style={styles.instructionNumberText}>1</Text>
                  </View>
                  <Text style={styles.instructionsText}>
                    Check your email inbox (and spam folder)
                  </Text>
                </View>
                <View style={styles.instructionItem}>
                  <View style={styles.instructionNumber}>
                    <Text style={styles.instructionNumberText}>2</Text>
                  </View>
                  <Text style={styles.instructionsText}>
                    Click the reset link in the email
                  </Text>
                </View>
                <View style={styles.instructionItem}>
                  <View style={styles.instructionNumber}>
                    <Text style={styles.instructionNumberText}>3</Text>
                  </View>
                  <Text style={styles.instructionsText}>
                    Create a new secure password
                  </Text>
                </View>
                <View style={styles.instructionItem}>
                  <View style={styles.instructionNumber}>
                    <Text style={styles.instructionNumberText}>4</Text>
                  </View>
                  <Text style={styles.instructionsText}>
                    Sign in and continue building your relationship together
                  </Text>
                </View>
              </View>

              <View style={styles.timeoutNotice}>
                <Text style={styles.timeoutText}>
                  ⏰ Reset links expire in 24 hours for your security
                </Text>
              </View>
            </GlassView>

            <View style={styles.actionsContainer}>
              <AuthButton
                title="Back to Sign In"
                onPress={handleBackToLogin}
                variant="primary"
              />

              <AuthLink
                text="Didn't receive the email?"
                linkText={_isResending ? "Sending..." : "Resend"}
                onPress={handleResendEmail}
              />
            </View>
          </View>
        </AuthScreenLayout>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.safeArea}>
      <AuthScreenLayout backgroundColor={brandColors.sageGreen}>
        {/* Back Button */}
        <TouchableOpacity style={styles.backButton} onPress={handleBack}>
          <ArrowLeft size={24} color="#FFFFFF" />
        </TouchableOpacity>

        {/* Header */}
        <AuthHeader
          title="Forgot Password?"
          subtitle="No worries! Enter your email and we'll help you get back to building your relationship together"
          emoji="🔐"
        />

        {/* Form */}
        <View style={styles.formContainer}>
          <View style={styles.inputContainer}>
            <AuthInput
              value={email}
              onChangeText={setEmail}
              placeholder="Enter your email address"
              label="Email Address"
              type="email"
              leftIcon={<Mail size={20} color="rgba(255, 255, 255, 0.7)" />}
              error={error}
            />
          </View>

          <View style={styles.buttonContainer}>
            <AuthButton
              title={isLoading ? "Sending..." : "Send Reset Link"}
              onPress={handleResetPassword}
              variant="primary"
              loading={isLoading}
              disabled={isLoading || !email.trim()}
            />
          </View>

          <View style={styles.securityNotice}>
            <Text style={styles.securityText}>
              🔒 We'll never ask for your password via email. Your relationship data stays secure.
            </Text>
          </View>

          <AuthLink
            text="Remember your password?"
            linkText="Back to Sign In"
            onPress={handleBackToLogin}
          />
        </View>
      </AuthScreenLayout>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: brandColors.sageGreen,
  },
  backButton: {
    position: 'absolute',
    top: 60,
    left: 24,
    zIndex: 1,
    padding: 8,
  },
  formContainer: {
    flex: 1,
    justifyContent: 'center',
  },
  inputContainer: {
    marginBottom: 24,
  },
  buttonContainer: {
    marginBottom: 24,
  },
  securityNotice: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 12,
    padding: 16,
    marginBottom: 24,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  securityText: {
    fontSize: 13,
    color: 'rgba(255, 255, 255, 0.8)',
    textAlign: 'center',
    lineHeight: 18,
  },
  successContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  successIconContainer: {
    alignItems: 'center',
    marginBottom: 24,
    position: 'relative',
  },
  successIcon: {
    marginBottom: 8,
  },
  successEmoji: {
    fontSize: 32,
    position: 'absolute',
    bottom: -8,
    right: -8,
    backgroundColor: brandColors.sageGreen,
    borderRadius: 20,
    width: 40,
    height: 40,
    textAlign: 'center',
    lineHeight: 40,
  },
  instructionsContainer: {
    padding: 24,
    borderRadius: 16,
    marginVertical: 32,
    width: '100%',
  },
  instructionsTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#FFFFFF',
    marginBottom: 20,
    textAlign: 'center',
  },
  instructionsList: {
    gap: 16,
  },
  instructionItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: 12,
  },
  instructionNumber: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 2,
  },
  instructionNumberText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  instructionsText: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.8)',
    lineHeight: 20,
    flex: 1,
  },
  timeoutNotice: {
    marginTop: 20,
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: 'rgba(255, 255, 255, 0.1)',
  },
  timeoutText: {
    fontSize: 12,
    color: 'rgba(255, 255, 255, 0.6)',
    textAlign: 'center',
    fontStyle: 'italic',
  },
  actionsContainer: {
    width: '100%',
    alignItems: 'center',
  },
});
