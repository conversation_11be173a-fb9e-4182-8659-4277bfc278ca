/**
 * Signup Screen
 * 
 * Secure registration screen with password strength validation,
 * comprehensive input validation, and error handling.
 * 
 * @version 3.0.0
 * <AUTHOR> Us Team
 */

import { router } from 'expo-router';
import { ArrowLeft, Mail, Lock, User } from 'lucide-react-native';
import React, { useState, useEffect } from 'react';
import { Alert, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import {
  AuthScreenLayout,
  AuthHeader,
  AuthInput,
  AuthButton,
  AuthDivider,
  SocialButton,
  AuthLink,
  PasswordStrength,
} from '../../src/components/auth/AuthComponents';
import { useAuth } from '../../src/hooks/useAuth';
import { brandColors } from '../../src/shared/utils/colors';
import { logger } from '../../src/shared/utils/logger';

export default function SignupScreen() {
  const [formData, setFormData] = useState({
    fullName: '',
    email: '',
    password: '',
    confirmPassword: '',
  });
  const [errors, setErrors] = useState<{
    fullName?: string;
    email?: string;
    password?: string;
    confirmPassword?: string;
    general?: string;
  }>({});
  const [isLoading, setIsLoading] = useState(false);

  const { signUp, signInWithGoogle, signInWithApple } = useAuth();

  // Clear errors when user starts typing
  useEffect(() => {
    if (errors.fullName && formData.fullName) {
      setErrors(prev => ({ ...prev, fullName: undefined }));
    }
  }, [formData.fullName, errors.fullName]);

  useEffect(() => {
    if (errors.email && formData.email) {
      setErrors(prev => ({ ...prev, email: undefined }));
    }
  }, [formData.email, errors.email]);

  useEffect(() => {
    if (errors.password && formData.password) {
      setErrors(prev => ({ ...prev, password: undefined }));
    }
  }, [formData.password, errors.password]);

  useEffect(() => {
    if (errors.confirmPassword && formData.confirmPassword) {
      setErrors(prev => ({ ...prev, confirmPassword: undefined }));
    }
  }, [formData.confirmPassword, errors.confirmPassword]);

  const validateForm = () => {
    const newErrors: typeof errors = {};

    // Full name validation
    if (!formData.fullName.trim()) {
      newErrors.fullName = 'Full name is required';
    } else if (formData.fullName.trim().length < 2) {
      newErrors.fullName = 'Full name must be at least 2 characters';
    }

    // Email validation
    if (!formData.email.trim()) {
      newErrors.email = 'Email is required';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = 'Please enter a valid email address';
    }

    // Password validation
    if (!formData.password) {
      newErrors.password = 'Password is required';
    } else if (formData.password.length < 8) {
      newErrors.password = 'Password must be at least 8 characters';
    } else if (!/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(formData.password)) {
      newErrors.password = 'Password must contain uppercase, lowercase, and number';
    }

    // Confirm password validation
    if (!formData.confirmPassword) {
      newErrors.confirmPassword = 'Please confirm your password';
    } else if (formData.password !== formData.confirmPassword) {
      newErrors.confirmPassword = 'Passwords do not match';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSignUp = async () => {
    if (!validateForm()) return;

    try {
      setIsLoading(true);
      setErrors({});

      await signUp({
        fullName: formData.fullName.trim(),
        email: formData.email.trim().toLowerCase(),
        password: formData.password,
        confirmPassword: formData.confirmPassword,
      });

      logger.info('Signup successful');
      
      // Show success message and redirect
      Alert.alert(
        'Account Created!',
        'Please check your email to verify your account before signing in.',
        [
          {
            text: 'OK',
            onPress: () => router.replace('/auth/login'),
          },
        ]
      );
    } catch (error) {
      logger.error('Signup error:', error);

      const errorMessage = error instanceof Error ? error.message : 'Signup failed';

      // Handle specific error types
      if (errorMessage.includes('User already registered')) {
        setErrors({ email: 'An account with this email already exists' });
      } else if (errorMessage.includes('Password should be at least')) {
        setErrors({ password: 'Password must be at least 8 characters long' });
      } else if (errorMessage.includes('Invalid email')) {
        setErrors({ email: 'Please enter a valid email address' });
      } else {
        setErrors({ general: errorMessage });
      }
    } finally {
      setIsLoading(false);
    }
  };

  const handleGoogleSignUp = async () => {
    try {
      setIsLoading(true);
      setErrors({});

      await signInWithGoogle();

      logger.info('Google sign up successful');
      router.replace('/(tabs)');
    } catch (error) {
      logger.error('Google sign up error:', error);

      const errorMessage = error instanceof Error ? error.message : 'Google sign up failed';
      if (!errorMessage.includes('not yet implemented')) {
        setErrors({ general: errorMessage });
      } else {
        Alert.alert('Coming Soon', 'Google Sign-Up will be available in a future update.');
      }
    } finally {
      setIsLoading(false);
    }
  };

  const handleAppleSignUp = async () => {
    try {
      setIsLoading(true);
      setErrors({});

      await signInWithApple();

      logger.info('Apple sign up successful');
      router.replace('/(tabs)');
    } catch (error) {
      logger.error('Apple sign up error:', error);

      const errorMessage = error instanceof Error ? error.message : 'Apple sign up failed';
      if (!errorMessage.includes('not yet implemented')) {
        setErrors({ general: errorMessage });
      } else {
        Alert.alert('Coming Soon', 'Apple Sign-Up will be available in a future update.');
      }
    } finally {
      setIsLoading(false);
    }
  };

  const handleSignIn = () => {
    router.push('/auth/login');
  };

  const handleBack = () => {
    router.back();
  };

  const updateFormData = (field: keyof typeof formData) => (value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  return (
    <SafeAreaView style={styles.safeArea}>
      <AuthScreenLayout backgroundColor={brandColors.sageGreen}>
        {/* Back Button */}
        <TouchableOpacity style={styles.backButton} onPress={handleBack}>
          <ArrowLeft size={24} color="#FFFFFF" />
        </TouchableOpacity>

        {/* Header */}
        <AuthHeader
          title="Create Your Account"
          subtitle="Join thousands of couples building stronger relationships"
          emoji="💕"
        />

        {/* Form */}
        <View style={styles.formContainer}>
          <AuthInput
            value={formData.fullName}
            onChangeText={updateFormData('fullName')}
            placeholder="Enter your full name"
            label="Full Name"
            type="text"
            leftIcon={<User size={20} color="rgba(255, 255, 255, 0.7)" />}
            error={errors.fullName}
          />

          <AuthInput
            value={formData.email}
            onChangeText={updateFormData('email')}
            placeholder="Enter your email"
            label="Email"
            type="email"
            leftIcon={<Mail size={20} color="rgba(255, 255, 255, 0.7)" />}
            error={errors.email}
          />

          <AuthInput
            value={formData.password}
            onChangeText={updateFormData('password')}
            placeholder="Create a secure password"
            label="Password"
            type="password"
            leftIcon={<Lock size={20} color="rgba(255, 255, 255, 0.7)" />}
            error={errors.password}
          />

          <PasswordStrength password={formData.password} />

          <AuthInput
            value={formData.confirmPassword}
            onChangeText={updateFormData('confirmPassword')}
            placeholder="Confirm your password"
            label="Confirm Password"
            type="password"
            leftIcon={<Lock size={20} color="rgba(255, 255, 255, 0.7)" />}
            error={errors.confirmPassword}
          />

          {/* General Error */}
          {errors.general && (
            <View style={styles.errorContainer}>
              <Text style={styles.errorText}>{errors.general}</Text>
            </View>
          )}

          {/* Sign Up Button */}
          <AuthButton
            title="Create Account"
            onPress={handleSignUp}
            variant="primary"
            loading={isLoading}
            disabled={isLoading}
          />

          <AuthDivider />

          {/* Social Sign Up */}
          <SocialButton
            provider="apple"
            onPress={handleAppleSignUp}
            loading={isLoading}
            disabled={isLoading}
          />

          <SocialButton
            provider="google"
            onPress={handleGoogleSignUp}
            loading={isLoading}
            disabled={isLoading}
          />

          {/* Sign In Link */}
          <AuthLink
            text="Already have an account?"
            linkText="Sign In"
            onPress={handleSignIn}
          />
        </View>
      </AuthScreenLayout>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: brandColors.sageGreen,
  },
  backButton: {
    position: 'absolute',
    top: 60,
    left: 24,
    zIndex: 1,
    padding: 8,
  },
  formContainer: {
    flex: 1,
    justifyContent: 'center',
  },
  errorContainer: {
    backgroundColor: 'rgba(239, 68, 68, 0.1)',
    borderRadius: 8,
    padding: 12,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: 'rgba(239, 68, 68, 0.3)',
  },
  errorText: {
    color: '#EF4444',
    fontSize: 14,
    textAlign: 'center',
  },
});
