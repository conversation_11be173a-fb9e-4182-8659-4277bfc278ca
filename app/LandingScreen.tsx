/**
 * Landing Screen - Optimized
 *
 * Optimized version using shared authentication components.
 * Demonstrates significant code reduction and improved maintainability.
 *
 * <AUTHOR> Us Team
 * @version 2.0.0
 */

import { router } from 'expo-router';
import { Camera, Heart, Star, Users } from 'lucide-react-native';
import React from 'react';
import { StyleSheet, Text, View } from 'react-native';
import { useUserEvents } from '../src/hooks/useUserEvents';
import { colors } from '../src/shared/utils/colors';

// Import shared auth components
import {
    AuthHeader,
    AuthScreenLayout,
    FeatureShowcase
} from '../src/components/shared/AuthComponents';

// Import DS components
import {
    DSAvatar,
    DSBadge,
    DSButton,
    DSGlassCard,
    DSStatTile,
} from '../src/components/shared';

export default function LandingScreen() {
  const { logEvent: _logEvent } = useUserEvents();



  const handleSignUp = async () => {
    // ONBOARDING_STARTED event removed with onboarding functionality
    router.push('/auth');
  };

  const features = [
    {
      icon: <Heart size={24} color={colors.primary} />,
      title: 'Strengthen Your Bond',
      description: 'Discover meaningful activities and conversations designed to bring you closer together.',
      color: colors.primary,
    },
    {
      icon: <Camera size={24} color={colors.secondary} />,
      title: 'Create Shared Memories',
      description: 'Capture your journey together with photos, stories, and special moments you\'ll treasure forever.',
      color: colors.secondary,
    },
    {
      icon: <Users size={24} color={colors.accent1} />,
      title: 'Grow Together',
      description: 'Build deeper connections through guided activities, games, and conversations that matter.',
      color: colors.accent1,
    },
    {
      icon: <Star size={24} color={colors.primary} />,
      title: 'Track Your Progress',
      description: 'See how your relationship grows with points, milestones, and personalized insights.',
      color: colors.primary,
    },
  ];

  return (
    <AuthScreenLayout backgroundColor={colors.primary}>
      {/* Header */}
      <AuthHeader
        title="Everlasting Us"
        subtitle="Strengthen your bond, one moment at a time"
        logo={<Text style={styles.logo}>💕</Text>}
      />

      {/* Features Section */}
      <FeatureShowcase
        title="What makes us special?"
        features={features}
      />

      {/* Demo Preview */}
      <View style={styles.demoSection}>
        <Text style={styles.sectionTitle}>See it in action</Text>
        <DSGlassCard>
          <View style={styles.demoHeader}>
            <DSAvatar size={60} emoji="👫" />
            <View style={styles.demoInfo}>
              <Text style={styles.demoTitle}>Your Journey Together</Text>
              <Text style={styles.demoSubtitle}>12 weeks of connection</Text>
              <DSBadge>Demo Mode</DSBadge>
            </View>
          </View>
          <View style={styles.demoStats}>
            <DSStatTile title="Memories" value={0} />
            <DSStatTile title="Points" value={0} />
            <DSStatTile title="Activities" value={0} />
          </View>
        </DSGlassCard>
      </View>

      {/* Call to Action */}
      <View style={styles.ctaSection}>
        <Text style={styles.ctaTitle}>Ready to start your journey?</Text>
        <Text style={styles.ctaSubtitle}>
          Try our limited demo mode or create an account to unlock all features
        </Text>
      </View>

      {/* Bottom Actions */}
      <DSGlassCard style={styles.bottomActions}>
        <DSButton
          title="Create Account"
          onPress={handleSignUp}
          variant="primary"
          style={styles.signUpButton}
        />
      </DSGlassCard>
    </AuthScreenLayout>
  );
}

// eslint-disable-next-line no-restricted-syntax
const styles = StyleSheet.create({
  logo: {
    fontSize: 80,
  },
  demoSection: {
    paddingHorizontal: 20,
    marginBottom: 40,
  },
  sectionTitle: {
    fontSize: 24,
    fontWeight: 'bold' as const,
    color: 'white',
    textAlign: 'center' as const,
    marginBottom: 24,
  },
  demoCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderRadius: 16,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  demoHeader: {
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    marginBottom: 20,
  },
  demoAvatar: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: colors.primary,
    justifyContent: 'center' as const,
    alignItems: 'center' as const,
    marginRight: 16,
  },
  demoAvatarText: {
    fontSize: 24,
  },
  demoInfo: {
    flex: 1,
  },
  demoTitle: {
    fontSize: 20,
    fontWeight: '600' as const,
    color: colors.textPrimary,
    marginBottom: 4,
  },
  demoSubtitle: {
    fontSize: 14,
    color: colors.textSecondary,
  },
  demoStats: {
    flexDirection: 'row' as const,
    justifyContent: 'space-around' as const,
  },
  statItem: {
    alignItems: 'center' as const,
  },
  statNumber: {
    fontSize: 24,
    fontWeight: 'bold' as const,
    color: colors.primary,
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    color: colors.textSecondary,
    textTransform: 'uppercase' as const,
    letterSpacing: 0.5,
  },
  ctaSection: {
    paddingHorizontal: 20,
    alignItems: 'center' as const,
    marginBottom: 20,
  },
  ctaTitle: {
    fontSize: 24,
    fontWeight: 'bold' as const,
    color: 'white',
    textAlign: 'center' as const,
    marginBottom: 8,
  },
  ctaSubtitle: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.9)',
    textAlign: 'center' as const,
    lineHeight: 22,
  },
  bottomActions: {
    position: 'absolute' as const,
    bottom: 0,
    left: 0,
    right: 0,
    paddingHorizontal: 20,
    paddingBottom: 40,
    paddingTop: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
  },

  signUpButton: {
    // Additional styles if needed
  },
});
