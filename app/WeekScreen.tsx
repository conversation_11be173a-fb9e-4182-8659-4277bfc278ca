/**
 * Universal Week Screen
 *
 * Single parameterized screen that replaces all 12 individual week screens.
 * Uses the new modular activity system for dynamic content rendering.
 */

import { router, useLocalSearchParams } from 'expo-router';
import React, { useCallback, useEffect, useState } from 'react';
import { Alert } from 'react-native';

import { useGenericWeekData, WeekDataBase } from '../src/journeys/daily/useGenericWeekData';
import { getWeekConfig } from '../src/shared/components/activities/WeekConfigurations';
import { ActivityCompletionData } from '../src/shared/components/activities/types';
import { EnhancedWeekScreenLayout } from '../src/shared/components/common/EnhancedWeekScreenLayout';
import { logger } from '../src/shared/utils/logger';

/**
 * Extended week data interface for the universal week screen
 */
interface WeekScreenData extends WeekDataBase {
  activities: Record<string, unknown>;
  weekNumber: number;
  startedAt: number;
  lastAccessedAt?: number;
  lastAccessedStep?: number;
}

/**
 * Universal Week Screen Component
 *
 * Handles all weeks (1-12) through URL parameters and configuration.
 * Route: /WeekScreen?week=1 or /WeekScreen/1
 */
export default function WeekScreen() {
  const params = useLocalSearchParams();
  const [currentStep, setCurrentStep] = useState(0);

  // Extract week number from params
  const weekNumber = parseInt((params.week as string) || '1');

  // Get week configuration
  const weekConfig = getWeekConfig(weekNumber);

  // Use generic week data hook - must be called before any conditional returns
  const {
    data,
    isLoading,
    error,
    updateData,
    updateCompletedSections: _updateCompletedSections,
    markSectionComplete,
    saveData,
    getCompletionPercentage: _getCompletionPercentage,
    isComplete: _isComplete,
  } = useGenericWeekData<WeekScreenData>({
    storageKey: `week_${weekNumber}_data`,
    defaultData: {
      completedSections: new Array(weekConfig?.activityIds?.length || 0).fill(false),
      activities: {},
      weekNumber,
      startedAt: Date.now(),
    },
    weekNumber,
  });

  // Handle activity completion
  const handleActivityComplete = useCallback(async (stepIndex: number, completionData: ActivityCompletionData) => {
    if (!weekConfig) {
      logger.error(`Week configuration not found for week ${weekNumber}`);
      return;
    }

    try {
      logger.info(`Activity completed: ${completionData.activityType} in week ${weekNumber}`);

      // Update activity data
      await updateData({
        activities: {
          ...data.activities,
          [completionData.activityType]: {
            ...completionData.data,
            completed: true,
            completedAt: completionData.completedAt,
            points: completionData.points,
          },
        },
      });

      // Mark section as complete
      await markSectionComplete(stepIndex);

      // Show completion feedback
      const isLastActivity = stepIndex === weekConfig.activityIds.length - 1;

      if (isLastActivity) {
        Alert.alert(
          'Week Complete! 🎉',
          `Congratulations! You've completed Week ${weekNumber}: ${weekConfig.title}!\n\nYou earned ${completionData.points} points!`,
          [
            {
              text: 'Continue',
              onPress: () => {
                // Navigate back to modules or next week
                router.push('/(tabs)/modules');
              },
            },
          ]
        );
      } else {
        Alert.alert(
          'Activity Complete! ✅',
          `Great job! You earned ${completionData.points} points!`,
          [{ text: 'Continue' }]
        );
      }
    } catch (error) {
      logger.error('Failed to complete activity:', error);
      Alert.alert('Error', 'Failed to save your progress. Please try again.');
    }
  }, [weekNumber, weekConfig, data.activities, updateData, markSectionComplete]);

  // Handle errors
  const handleError = useCallback((error: Error) => {
    logger.error(`Week ${weekNumber} error:`, error);
    Alert.alert('Error', error.message || 'Something went wrong. Please try again.');
  }, [weekNumber]);

  // Handle navigation back
  const handleNavigateBack = useCallback(() => {
    // Save current progress before leaving
    saveData({
      ...data,
      lastAccessedAt: Date.now(),
    }).catch(error => {
      logger.error('Failed to save progress on exit:', error);
    });

    router.back();
  }, [data, saveData]);

  // Handle step changes
  const handleStepChange = useCallback((step: number) => {
    setCurrentStep(step);

    // Update last accessed step
    updateData({
      lastAccessedStep: step,
      lastAccessedAt: Date.now(),
    }).catch(error => {
      logger.error('Failed to update step progress:', error);
    });
  }, [updateData]);

  // Initialize step from saved data
  useEffect(() => {
    if (data.lastAccessedStep !== undefined) {
      setCurrentStep(data.lastAccessedStep);
    }
  }, [data.lastAccessedStep]);

  // Check week configuration after all hooks
  if (!weekConfig) {
    Alert.alert('Error', `Week ${weekNumber} configuration not found`);
    router.back();
    return null;
  }

  // Show loading state
  if (isLoading) {
    return null; // Could add a loading spinner here
  }

  // Show error state
  if (error) {
    Alert.alert('Error', error);
    router.back();
    return null;
  }

  return (
    <EnhancedWeekScreenLayout
      weekConfig={weekConfig}
      currentStep={currentStep}
      setCurrentStep={handleStepChange}
      completedSections={data.completedSections}
      onActivityComplete={handleActivityComplete}
      onError={handleError}
      onNavigateBack={handleNavigateBack}
      initialData={data.activities || {}}
    />
  );
}

/**
 * Export for dynamic routing
 * This allows the screen to be accessed via:
 * - /WeekScreen?week=1
 * - /WeekScreen/1 (if using dynamic routes)
 */
export { WeekScreen };
