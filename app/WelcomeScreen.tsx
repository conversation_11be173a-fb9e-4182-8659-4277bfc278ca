import { ScrollView, StyleSheet, Text, TouchableOpacity, View } from 'react-native';

import { router } from 'expo-router';
import { ArrowRight, Calendar, Clock, Heart, Users } from 'lucide-react-native';
import { colors } from '../src/shared/utils/colors';

export default function WelcomeScreen() {
  const handleGetStarted = () => {
    router.push('/couple-profile');
  };

  return (
    <View style={styles.container}>
      <View
        style={[styles.headerGradient, { backgroundColor: colors.primary }]}
      >
        <View style={styles.headerContent}>
          <View style={styles.heartContainer}>
            <Heart size={48} color={colors.white} fill={colors.white} />
          </View>
          <Text style={styles.welcomeTitle}>WELCOME, LOVEBIRDS!</Text>
          <Text style={styles.tagline}>Your haven for love, laughter, and growth</Text>
        </View>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <View style={styles.introCard}>
          <Text style={styles.introText}>
            You've found your haven, a space just for the two of you. This isn't just
            a journal, it's a tool to build a strong foundation for a lasting
            relationship.
          </Text>
        </View>

        <View style={styles.instructionsCard}>
          <Text style={styles.sectionTitle}>How to use this journal:</Text>

          <View style={styles.instructionItem}>
            <View style={styles.instructionIcon}>
              <Calendar size={20} color={colors.primary} />
            </View>
            <View style={styles.instructionContent}>
              <Text style={styles.instructionTitle}>Pick a day</Text>
              <Text style={styles.instructionText}>
                Choose a day or night of the week to go through one section together.
              </Text>
            </View>
          </View>

          <View style={styles.instructionItem}>
            <View style={styles.instructionIcon}>
              <Clock size={20} color={colors.secondary} />
            </View>
            <View style={styles.instructionContent}>
              <Text style={styles.instructionTitle}>Carve out some time</Text>
              <Text style={styles.instructionText}>
                Set aside 20 to 30 minutes to focus on each other and the activity.
              </Text>
            </View>
          </View>

          <View style={styles.instructionItem}>
            <View style={styles.instructionIcon}>
              <Heart size={20} color={colors.blue} />
            </View>
            <View style={styles.instructionContent}>
              <Text style={styles.instructionTitle}>Make it a ritual</Text>
              <Text style={styles.instructionText}>
                Turn this into a regular habit, a time to connect, laugh, and grow closer.
              </Text>
            </View>
          </View>

          <View style={styles.instructionItem}>
            <View style={styles.instructionIcon}>
              <Users size={20} color={colors.success} />
            </View>
            <View style={styles.instructionContent}>
              <Text style={styles.instructionTitle}>Use your icon</Text>
              <Text style={styles.instructionText}>
                On the next page, each of you choose a heart icon. That icon is your personal space in the journal. Use it to write your reflections and responses when prompted.
              </Text>
            </View>
          </View>
        </View>

        <View style={styles.featuresCard}>
          <Text style={styles.sectionTitle}>Each section will include a:</Text>

          <View style={styles.featuresList}>
            <View style={styles.featureItem}>
              <View style={styles.featureBullet} />
              <Text style={styles.featureText}>
                <Text style={styles.featureBold}>Date Night Idea:</Text> A fresh idea for you to plan and enjoy some time that week.
              </Text>
            </View>

            <View style={styles.featureItem}>
              <View style={styles.featureBullet} />
              <Text style={styles.featureText}>
                <Text style={styles.featureBold}>Date Night Chat Topics:</Text> Questions to ask each other during your date night.
              </Text>
            </View>

            <View style={styles.featureItem}>
              <View style={styles.featureBullet} />
              <Text style={styles.featureText}>
                <Text style={styles.featureBold}>Relationship Skills Toolkit:</Text> A simple skill to help you understand, support, and communicate better.
              </Text>
            </View>

            <View style={styles.featureItem}>
              <View style={styles.featureBullet} />
              <Text style={styles.featureText}>
                <Text style={styles.featureBold}>Connection-Boosting Activity:</Text> A fun way to play, share, and bring more joy into your relationship.
              </Text>
            </View>
          </View>
        </View>

        <View style={styles.closingCard}>
          <Text style={styles.closingText}>
            So, grab your favorite drink, snuggle up, and let's get started on this
            journey of love, laughter, and growth.
          </Text>
        </View>

        <TouchableOpacity style={styles.getStartedButton} onPress={handleGetStarted}>
          <View
            style={[styles.getStartedGradient, { backgroundColor: colors.primary }]}
          >
            <Text style={styles.getStartedText}>Begin Our Journey</Text>
            <ArrowRight size={20} color={colors.white} />
          </View>
        </TouchableOpacity>
      </ScrollView>
    </View>
  );
}

// eslint-disable-next-line no-restricted-syntax
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.backgroundPrimary,
  },
  headerGradient: {
    paddingTop: 60,
    paddingBottom: 40,
  },
  headerContent: {
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  heartContainer: {
    marginBottom: 16,
  },
  welcomeTitle: {
    fontSize: 32,
    fontWeight: '800',
    color: colors.textInverse,
    textAlign: 'center',
    marginBottom: 8,
    letterSpacing: 1,
  },
  tagline: {
    fontSize: 18,
    color: colors.textInverse,
    opacity: 0.9,
    fontWeight: '500',
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  introCard: {
    backgroundColor: colors.backgroundSecondary,
    borderRadius: 16,
    padding: 24,
    marginTop: -20,
    marginBottom: 20,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 6,
  },
  introText: {
    fontSize: 16,
    color: colors.textSecondary,
    lineHeight: 24,
    textAlign: 'center',
  },
  instructionsCard: {
    backgroundColor: colors.backgroundSecondary,
    borderRadius: 16,
    padding: 24,
    marginBottom: 20,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: colors.textPrimary,
    marginBottom: 20,
    textAlign: 'center',
  },
  instructionItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 20,
  },
  instructionIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.backgroundPrimary,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 16,
  },
  instructionContent: {
    flex: 1,
  },
  instructionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.textPrimary,
    marginBottom: 4,
  },
  instructionText: {
    fontSize: 14,
    color: colors.textSecondary,
    lineHeight: 20,
  },
  featuresCard: {
    backgroundColor: colors.backgroundSecondary,
    borderRadius: 16,
    padding: 24,
    marginBottom: 20,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  featuresList: {
    marginTop: 8,
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 16,
  },
  featureBullet: {
    width: 6,
    height: 6,
    borderRadius: 3,
    backgroundColor: colors.primary,
    marginTop: 8,
    marginRight: 12,
  },
  featureText: {
    fontSize: 14,
    color: colors.textSecondary,
    lineHeight: 20,
    flex: 1,
  },
  featureBold: {
    fontWeight: '600',
    color: colors.textPrimary,
  },
  closingCard: {
    backgroundColor: colors.backgroundSecondary,
    borderRadius: 16,
    padding: 24,
    marginBottom: 32,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  closingText: {
    fontSize: 16,
    color: colors.textSecondary,
    lineHeight: 24,
    textAlign: 'center',
    fontStyle: 'italic',
  },
  getStartedButton: {
    marginBottom: 40,
    borderRadius: 16,
    overflow: 'hidden',
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 12,
    elevation: 6,
  },
  getStartedGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 20,
  },
  getStartedText: {
    color: colors.textInverse,
    fontSize: 18,
    fontWeight: '700',
    marginRight: 8,
  },
});
