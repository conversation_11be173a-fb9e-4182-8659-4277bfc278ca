#!/bin/bash

echo "🔍 Validating file migration completeness..."
echo "================================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Check for missing critical files
echo -e "\n${YELLOW}Checking for missing critical files...${NC}"
CRITICAL_FILES=(
  "src/journeys/onboarding/useAuth.ts"
  "src/journeys/onboarding/useUserProfile.ts"
  "src/journeys/onboarding/useCouplePairing.ts"
  "src/journeys/daily/useDailyQuestions.ts"
  "src/journeys/daily/useStreakData.ts"
  "src/journeys/activities/useDateNightIdeasSupabase.ts"
  "src/journeys/activities/useMatchGame.ts"
  "src/journeys/memories/useTimeline.ts"
  "src/journeys/memories/useOriginStoryData.ts"
  "src/journeys/planning/useUserPreferences.ts"
  "src/journeys/progress/usePointsSystemSupabase.ts"
  "src/journeys/progress/useUserEvents.ts"
)

MISSING_COUNT=0
for file in "${CRITICAL_FILES[@]}"; do
  if [[ ! -f "$file" ]]; then
    echo -e "${RED}❌ Missing critical file: $file${NC}"
    ((MISSING_COUNT++))
  else
    echo -e "${GREEN}✅ Found: $file${NC}"
  fi
done

# Check for duplicate files
echo -e "\n${YELLOW}Checking for duplicate files...${NC}"
DUPLICATES=$(find src/ -name "*.ts" -o -name "*.tsx" | xargs basename -a | sort | uniq -d)
if [[ -n "$DUPLICATES" ]]; then
  echo -e "${RED}❌ Found duplicate filenames:${NC}"
  echo "$DUPLICATES"
else
  echo -e "${GREEN}✅ No duplicate filenames found${NC}"
fi

# Check for empty directories
echo -e "\n${YELLOW}Checking for empty directories...${NC}"
EMPTY_DIRS=$(find src/ -type d -empty)
if [[ -n "$EMPTY_DIRS" ]]; then
  echo -e "${YELLOW}⚠️  Found empty directories:${NC}"
  echo "$EMPTY_DIRS"
else
  echo -e "${GREEN}✅ No empty directories found${NC}"
fi

# Check for nested duplications (like hooks/hooks/)
echo -e "\n${YELLOW}Checking for nested duplications...${NC}"
NESTED_DUPLICATIONS=$(find src/ -path "*/hooks/hooks/*" -o -path "*/components/components/*" -o -path "*/services/services/*" -o -path "*/types/types/*" -o -path "*/utils/utils/*")
if [[ -n "$NESTED_DUPLICATIONS" ]]; then
  echo -e "${RED}❌ Found nested duplications:${NC}"
  echo "$NESTED_DUPLICATIONS"
else
  echo -e "${GREEN}✅ No nested duplications found${NC}"
fi

# Check journey index files exist
echo -e "\n${YELLOW}Checking journey index files...${NC}"
JOURNEY_INDEXES=(
  "src/journeys/onboarding/index.ts"
  "src/journeys/daily/index.ts"
  "src/journeys/activities/index.ts"
  "src/journeys/memories/index.ts"
  "src/journeys/planning/index.ts"
  "src/journeys/progress/index.ts"
  "src/shared/index.ts"
  "src/index.ts"
)

for index_file in "${JOURNEY_INDEXES[@]}"; do
  if [[ ! -f "$index_file" ]]; then
    echo -e "${RED}❌ Missing index file: $index_file${NC}"
    ((MISSING_COUNT++))
  else
    echo -e "${GREEN}✅ Found: $index_file${NC}"
  fi
done

# Summary
echo -e "\n${YELLOW}Migration Validation Summary:${NC}"
echo "================================================"
if [[ $MISSING_COUNT -eq 0 ]]; then
  echo -e "${GREEN}✅ All critical files found - migration structure looks good!${NC}"
  exit 0
else
  echo -e "${RED}❌ Found $MISSING_COUNT missing critical files - migration needs attention${NC}"
  exit 1
fi
