#!/usr/bin/env node

/**
 * Import Path Update Script for React Native Best Practices
 *
 * Updates import paths throughout the codebase to match the new React Native structure:
 * - src/screens/
 * - src/components/
 * - src/hooks/
 * - src/services/
 * - src/types/
 * - src/utils/
 */

const fs = require('fs');
const path = require('path');

// Define the import path mappings for React Native best practices
function getImportMappings(filePath) {
  const mappings = {};

  // Common mappings for all files to new structure
  mappings['../../shared/utils/'] = '../utils/';
  mappings['../shared/utils/'] = '../utils/';
  mappings['../../utils/'] = '../utils/';
  mappings['../utils/'] = '../utils/';

  mappings['../../shared/ui/'] = '../components/';
  mappings['../shared/ui/'] = '../components/';
  mappings['../../components/'] = '../components/';
  mappings['../components/'] = '../components/';

  mappings['../../shared/data/'] = '../services/';
  mappings['../shared/data/'] = '../services/';
  mappings['../../hooks/'] = '../hooks/';
  mappings['../hooks/'] = '../hooks/';
  mappings['../../services/'] = '../services/';
  mappings['../services/'] = '../services/';

  mappings['../../types/'] = '../types/';
  mappings['../types/'] = '../types/';

  // Handle specific cases based on file location
  if (filePath.includes('src/screens/')) {
    // Screens can import from any other directory
    return mappings;
  }

  if (filePath.includes('src/components/')) {
    // Components can import utils, hooks, types, services
    return mappings;
  }

  if (filePath.includes('src/hooks/')) {
    // Hooks can import services, types, utils
    return mappings;
  }

  if (filePath.includes('src/services/')) {
    // Services can import types, utils
    return mappings;
  }

  if (filePath.includes('src/utils/')) {
    // Utils can import types
    return mappings;
  }

  return mappings;
}

// Function to update imports in a file
function updateImportsInFile(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let updated = false;

    // Get the appropriate mappings for this file
    const importMappings = getImportMappings(filePath);

    // Update each import mapping
    for (const [oldPath, newPath] of Object.entries(importMappings)) {
      const regex = new RegExp(`from ['"]${oldPath.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}`, 'g');
      if (content.match(regex)) {
        content = content.replace(regex, `from '${newPath}`);
        updated = true;
      }

      const importRegex = new RegExp(`import ['"]${oldPath.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}`, 'g');
      if (content.match(importRegex)) {
        content = content.replace(importRegex, `import '${newPath}`);
        updated = true;
      }
    }

    if (updated) {
      fs.writeFileSync(filePath, content);
      console.log(`Updated imports in: ${filePath}`);
    }
  } catch (error) {
    console.error(`Error updating ${filePath}:`, error.message);
  }
}

// Function to recursively process directories
function processDirectory(dirPath) {
  const items = fs.readdirSync(dirPath);
  
  for (const item of items) {
    const fullPath = path.join(dirPath, item);
    const stat = fs.statSync(fullPath);
    
    if (stat.isDirectory()) {
      // Skip node_modules and other irrelevant directories
      if (!['node_modules', '.git', 'dist', '.expo'].includes(item)) {
        processDirectory(fullPath);
      }
    } else if (stat.isFile() && (item.endsWith('.tsx') || item.endsWith('.ts'))) {
      updateImportsInFile(fullPath);
    }
  }
}

// Main execution
console.log('Starting import path updates for React Native best practices...');
processDirectory('./src');
console.log('Import path updates completed!');
