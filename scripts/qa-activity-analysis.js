#!/usr/bin/env node

/**
 * QA Activity Analysis Script
 * 
 * Comprehensive analysis of activity coverage and week configurations
 * for the new modular Week screen architecture.
 */

// Week configurations (extracted from WeekConfigurations.ts)
const weekConfigurations = [
  {
    weekNumber: 1,
    title: 'Getting to Know You',
    activityIds: ['match-game', 'date-night-plan', 'chat-prompts', 'soft-startup'],
    stepTitles: ['The Match Activity', 'Date Night Plan', 'Chat Prompts', 'Soft Startup'],
    pointsMultiplier: 1,
  },
  {
    weekNumber: 2,
    title: 'Celebrating Strengths',
    activityIds: ['strengths-bingo', 'date-night-plan', 'chat-prompts', 'five-to-one'],
    stepTitles: ['Strengths Bingo', 'Date Night Plan', 'Chat Prompts', '5:1 Ratio'],
    pointsMultiplier: 1,
  },
  {
    weekNumber: 3,
    title: 'Would You Rather',
    activityIds: ['would-you-rather', 'date-night-plan', 'chat-prompts', 'emotional-bank'],
    stepTitles: ['Would You Rather', 'Date Night Plan', 'Chat Prompts', 'Emotional Bank'],
    pointsMultiplier: 1,
  },
  {
    weekNumber: 4,
    title: 'Love Languages',
    activityIds: ['love-languages', 'date-night-plan', 'chat-prompts', 'appreciation'],
    stepTitles: ['Love Languages', 'Date Night Plan', 'Chat Prompts', 'Appreciation'],
    pointsMultiplier: 1,
  },
  {
    weekNumber: 5,
    title: 'Dream Together',
    activityIds: ['dream-vacation', 'date-night-plan', 'chat-prompts', 'conflict-style'],
    stepTitles: ['Dream Vacation', 'Date Night Plan', 'Chat Prompts', 'Conflict Style'],
    pointsMultiplier: 1,
  },
  {
    weekNumber: 6,
    title: 'Sharing Memories',
    activityIds: ['memory-sharing', 'memory-lane-date', 'chat-prompts', 'validation-toolkit'],
    stepTitles: ['Sharing Memories', 'Memory Lane Date', 'Chat Prompts', 'Validation Toolkit'],
    pointsMultiplier: 1,
  },
  {
    weekNumber: 7,
    title: 'Superhero Duo',
    activityIds: ['superhero-chart', 'thrift-showdown', 'chat-prompts', 'turning-toward'],
    stepTitles: ['Superhero Duo Chart', 'Thrift Shop Showdown', 'Chat Prompts', 'Turning Toward'],
    pointsMultiplier: 1,
  },
  {
    weekNumber: 8,
    title: 'Perfect Saturday',
    activityIds: ['perfect-saturday', 'blended-day', 'chat-prompts', 'conflict-mapping'],
    stepTitles: ['Perfect Saturday Game', 'Blended Perfect Day', 'Chat Prompts', 'Conflict Mapping'],
    pointsMultiplier: 1,
  },
  {
    weekNumber: 9,
    title: 'Relationship Rituals',
    activityIds: ['ritual-design', 'ritual-date', 'chat-prompts', 'stress-reducing'],
    stepTitles: ['Ritual Design', 'Ritual Date Night', 'Chat Prompts', 'Stress-Reducing'],
    pointsMultiplier: 1,
  },
  {
    weekNumber: 10,
    title: 'Adventure Planning',
    activityIds: ['adventure-bucket', 'adventure-date', 'chat-prompts', 'repair-attempts'],
    stepTitles: ['Adventure Bucket List', 'Adventure Date', 'Chat Prompts', 'Repair Attempts'],
    pointsMultiplier: 1,
  },
  {
    weekNumber: 11,
    title: 'Gratitude & Growth',
    activityIds: ['gratitude-jar', 'growth-date', 'chat-prompts', 'meaning-making'],
    stepTitles: ['Gratitude Jar', 'Growth Date Night', 'Chat Prompts', 'Meaning Making'],
    pointsMultiplier: 1,
  },
  {
    weekNumber: 12,
    title: 'Playful Tales',
    activityIds: ['build-story', 'active-date', 'chat-prompts', 'sensate-focus'],
    stepTitles: ['Build-a-Story Game', 'Get Active Date', 'Chat Prompts', 'Sensate Focus'],
    pointsMultiplier: 1.2,
  },
];

// Currently implemented activities (from index.ts)
const implementedActivities = [
  'match-game',
  'date-night-plan', 
  'chat-prompts',
  'soft-startup',
  'would-you-rather'
];

console.log('🔍 QA ACTIVITY ANALYSIS REPORT');
console.log('=====================================\n');

// Collect all unique activity IDs
const allActivityIds = new Set();
weekConfigurations.forEach(week => {
  week.activityIds.forEach(id => allActivityIds.add(id));
});

console.log(`📊 COVERAGE ANALYSIS:`);
console.log(`   Total unique activities: ${allActivityIds.size}`);
console.log(`   Implemented activities: ${implementedActivities.length}`);
console.log(`   Coverage: ${Math.round((implementedActivities.length / allActivityIds.size) * 100)}%\n`);

// Find missing activities
const missingActivities = Array.from(allActivityIds).filter(id => !implementedActivities.includes(id));

console.log(`❌ MISSING ACTIVITIES (${missingActivities.length}):`);
missingActivities.forEach(id => {
  console.log(`   • ${id}`);
});
console.log();

// Week-by-week analysis
console.log('📅 WEEK-BY-WEEK ANALYSIS:');
weekConfigurations.forEach(week => {
  const implementedCount = week.activityIds.filter(id => implementedActivities.includes(id)).length;
  const totalCount = week.activityIds.length;
  const percentage = Math.round((implementedCount / totalCount) * 100);
  
  console.log(`\n   Week ${week.weekNumber}: ${week.title}`);
  console.log(`   Coverage: ${implementedCount}/${totalCount} (${percentage}%)`);
  
  week.activityIds.forEach((activityId, index) => {
    const stepTitle = week.stepTitles[index];
    const status = implementedActivities.includes(activityId) ? '✅' : '❌';
    console.log(`     ${status} ${stepTitle} (${activityId})`);
  });
});

// Functional completeness assessment
console.log('\n🎯 FUNCTIONAL COMPLETENESS ASSESSMENT:');

const fullyFunctionalWeeks = weekConfigurations.filter(week => 
  week.activityIds.every(id => implementedActivities.includes(id))
).length;

const partiallyFunctionalWeeks = weekConfigurations.filter(week => 
  week.activityIds.some(id => implementedActivities.includes(id)) &&
  !week.activityIds.every(id => implementedActivities.includes(id))
).length;

const nonFunctionalWeeks = weekConfigurations.filter(week => 
  !week.activityIds.some(id => implementedActivities.includes(id))
).length;

console.log(`   ✅ Fully functional weeks: ${fullyFunctionalWeeks}/12 (${Math.round((fullyFunctionalWeeks/12)*100)}%)`);
console.log(`   ⚠️  Partially functional weeks: ${partiallyFunctionalWeeks}/12 (${Math.round((partiallyFunctionalWeeks/12)*100)}%)`);
console.log(`   ❌ Non-functional weeks: ${nonFunctionalWeeks}/12 (${Math.round((nonFunctionalWeeks/12)*100)}%)`);

// Points system analysis
console.log('\n💰 POINTS SYSTEM ANALYSIS:');
const defaultPoints = 20; // Assumed default
let totalPossiblePoints = 0;
let implementedPoints = 0;

weekConfigurations.forEach(week => {
  const weekPoints = week.activityIds.length * defaultPoints * (week.pointsMultiplier || 1);
  const weekImplementedPoints = week.activityIds.filter(id => implementedActivities.includes(id)).length * defaultPoints * (week.pointsMultiplier || 1);
  
  totalPossiblePoints += weekPoints;
  implementedPoints += weekImplementedPoints;
});

console.log(`   Total possible points: ${Math.round(totalPossiblePoints)}`);
console.log(`   Currently available points: ${Math.round(implementedPoints)}`);
console.log(`   Points coverage: ${Math.round((implementedPoints/totalPossiblePoints)*100)}%`);

// Critical path analysis
console.log('\n🛤️  CRITICAL PATH ANALYSIS:');
const criticalActivities = ['chat-prompts', 'date-night-plan']; // Activities that appear in most weeks
const criticalImplemented = criticalActivities.filter(id => implementedActivities.includes(id));

console.log(`   Critical activities implemented: ${criticalImplemented.length}/${criticalActivities.length}`);
criticalActivities.forEach(id => {
  const status = implementedActivities.includes(id) ? '✅' : '❌';
  const weekCount = weekConfigurations.filter(week => week.activityIds.includes(id)).length;
  console.log(`     ${status} ${id} (appears in ${weekCount} weeks)`);
});

// QA Verdict
console.log('\n🏆 QA VERDICT:');
if (fullyFunctionalWeeks >= 3 && criticalImplemented.length === criticalActivities.length) {
  console.log('   ✅ ACCEPTABLE FOR TESTING - Core functionality present');
} else if (fullyFunctionalWeeks >= 1) {
  console.log('   ⚠️  LIMITED FUNCTIONALITY - Basic testing possible');
} else {
  console.log('   ❌ INSUFFICIENT FOR TESTING - Critical activities missing');
}

console.log('\n📋 RECOMMENDATIONS:');
console.log('   1. Prioritize implementing critical activities (chat-prompts, date-night-plan)');
console.log('   2. Focus on completing Week 1 for end-to-end testing');
console.log('   3. Add graceful fallbacks for missing activities');
console.log('   4. Implement activity placeholder components');

console.log('\n🔧 NEXT STEPS:');
console.log('   • Test Week 1 end-to-end functionality');
console.log('   • Validate error handling for missing activities');
console.log('   • Verify points system integration');
console.log('   • Test navigation and state persistence');
