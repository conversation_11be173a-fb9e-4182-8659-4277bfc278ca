#!/usr/bin/env node

/**
 * API Contract Validation
 *
 * Validates that all journey exports match expected interfaces and contracts.
 * Ensures consistency across the journey architecture.
 */

import * as fs from 'fs';
import * as path from 'path';

interface JourneyExports {
  onboarding: string[];
  daily: string[];
  activities: string[];
  memories: string[];
  planning: string[];
  progress: string[];
}

interface ExportValidation {
  journey: string;
  expected: string[];
  found: string[];
  missing: string[];
  extra: string[];
  valid: boolean;
}

class ApiContractValidator {
  private readonly expectedExports: JourneyExports = {
    onboarding: [
      'useAuth',
      'useUserProfile',
      'useCouplePairing',
      'useFrameworkReady',
      'useCoupleRealtime',
      'couplePairingService',
      'userPreferencesService',
      'AuthScreen'
    ],
    daily: [
      'useDailyQuestions',
      'useDailyQuestionsNotifications',
      'useStreakData',
      'useDailyChallenges',
      'useWeekOneData',
      'useWeekTwoData',
      'useGenericWeekData',
      'dailyQuestionsService',
      'streakEventService'
    ],
    activities: [
      'useDateNightIdeasSupabase',
      'useDateNightFavorites',
      'useMatchGame',
      'useFavorites',
      'useContentFavorites',
      'dateNightIdeasService',
      'favoritesService',
      'DateNight',
      'Activities'
    ],
    memories: [
      'useOriginStoryData',
      'useTimeline',
      'useTimelineData',
      'useMilestones',
      'milestoneService',
      'imageStorageService',
      'PhotoManager'
    ],
    planning: [
      'useUserPreferences',
      'useAppSettings',
      'userPreferencesService'
    ],
    progress: [
      'usePointsSystemSupabase',
      'useEngagementSystem',
      'useUserEvents',
      'usePerformance',
      'pointsSystemService',
      'eventLogger'
    ]
  };

  /**
   * Validate all journey API contracts
   */
  async validateAllContracts(): Promise<ExportValidation[]> {
    console.log('🔍 Validating journey API contracts...\n');

    const validations: ExportValidation[] = [];

    for (const [journeyName, expectedExports] of Object.entries(this.expectedExports)) {
      const validation = await this.validateJourneyContract(journeyName, expectedExports);
      validations.push(validation);
    }

    this.displayValidationResults(validations);
    return validations;
  }

  /**
   * Validate individual journey contract
   */
  private async validateJourneyContract(
    journeyName: string,
    expectedExports: string[]
  ): Promise<ExportValidation> {
    const indexPath = path.join('src', 'journeys', journeyName, 'index.ts');

    let foundExports: string[] = [];

    if (fs.existsSync(indexPath)) {
      foundExports = await this.extractExportsFromIndex(indexPath);
    }

    const missing = expectedExports.filter(exp => !foundExports.includes(exp));
    const extra = foundExports.filter(exp => !expectedExports.includes(exp));
    const valid = missing.length === 0;

    return {
      journey: journeyName,
      expected: expectedExports,
      found: foundExports,
      missing,
      extra,
      valid
    };
  }

  /**
   * Extract exports from index.ts file
   */
  private async extractExportsFromIndex(indexPath: string): Promise<string[]> {
    try {
      const content = fs.readFileSync(indexPath, 'utf8');
      const exports: string[] = [];

      // Match named exports: export { name } from './file'
      const namedExportMatches = content.match(/export\s*\{\s*([^}]+)\s*\}/g);
      if (namedExportMatches) {
        namedExportMatches.forEach(match => {
          const names = match
            .replace(/export\s*\{\s*/, '')
            .replace(/\s*\}.*/, '')
            .split(',')
            .map(name => name.trim())
            .filter(Boolean);
          exports.push(...names);
        });
      }

      // Match direct exports: export const name = ...
      const directExportMatches = content.match(/export\s+(const|function|class|interface|type)\s+(\w+)/g);
      if (directExportMatches) {
        directExportMatches.forEach(match => {
          const nameMatch = match.match(/export\s+(?:const|function|class|interface|type)\s+(\w+)/);
          if (nameMatch) {
            exports.push(nameMatch[1]);
          }
        });
      }

      // Match default exports with alias: export { default as Name }
      const defaultAliasMatches = content.match(/export\s*\{\s*default\s+as\s+(\w+)\s*\}/g);
      if (defaultAliasMatches) {
        defaultAliasMatches.forEach(match => {
          const nameMatch = match.match(/default\s+as\s+(\w+)/);
          if (nameMatch) {
            exports.push(nameMatch[1]);
          }
        });
      }

      // Remove duplicates and sort
      return [...new Set(exports)].sort();
    } catch (error) {
      console.error(`Error reading ${indexPath}:`, error);
      return [];
    }
  }

  /**
   * Display validation results
   */
  private displayValidationResults(validations: ExportValidation[]): void {
    console.log('📊 API CONTRACT VALIDATION RESULTS');
    console.log('=' .repeat(50));

    let totalValid = 0;
    let totalInvalid = 0;

    validations.forEach(validation => {
      const status = validation.valid ? '✅' : '❌';
      console.log(`\n${status} ${validation.journey.toUpperCase()} Journey:`);

      if (validation.valid) {
        totalValid++;
        console.log(`   All ${validation.expected.length} expected exports found`);
      } else {
        totalInvalid++;

        if (validation.missing.length > 0) {
          console.log(`   Missing exports (${validation.missing.length}):`);
          validation.missing.forEach(exp => console.log(`     - ${exp}`));
        }

        if (validation.extra.length > 0) {
          console.log(`   Extra exports (${validation.extra.length}):`);
          validation.extra.forEach(exp => console.log(`     + ${exp}`));
        }
      }

      // Show found exports count
      console.log(`   Found: ${validation.found.length}/${validation.expected.length} expected exports`);
    });

    // Summary
    console.log('\n' + '='.repeat(50));
    console.log('📈 VALIDATION SUMMARY:');
    console.log(`   Valid journeys: ${totalValid}/${validations.length}`);
    console.log(`   Invalid journeys: ${totalInvalid}/${validations.length}`);

    const overallValid = totalInvalid === 0;
    const overallStatus = overallValid ? '✅ PASS' : '❌ FAIL';
    console.log(`   Overall status: ${overallStatus}`);

    if (!overallValid) {
      console.log('\n⚠️  Some journeys have missing or extra exports.');
      console.log('   Update index.ts files to match expected API contracts.');
    }
  }

  /**
   * Generate expected index.ts content for a journey
   */
  generateIndexTemplate(journeyName: string): string {
    const exports = this.expectedExports[journeyName as keyof JourneyExports];
    if (!exports) {
      throw new Error(`Unknown journey: ${journeyName}`);
    }

    const journeyTitle = journeyName.charAt(0).toUpperCase() + journeyName.slice(1);

    let template = `/**\n * ${journeyTitle} Journey - Index\n * \n * Centralized exports for all ${journeyName}-related functionality.\n * \n * <AUTHOR> Us Team\n */\n\n`;

    // Group exports by type (hooks, services, components)
    const hooks = exports.filter(exp => exp.startsWith('use'));
    const services = exports.filter(exp => exp.endsWith('Service') || exp.endsWith('service'));
    const components = exports.filter(exp => !exp.startsWith('use') && !exp.endsWith('Service') && !exp.endsWith('service'));

    if (hooks.length > 0) {
      template += '// Hooks\n';
      hooks.forEach(hook => {
        template += `export { ${hook} } from './${hook}';\n`;
      });
      template += '\n';
    }

    if (services.length > 0) {
      template += '// Services\n';
      services.forEach(service => {
        template += `export { default as ${service} } from './${service}';\n`;
      });
      template += '\n';
    }

    if (components.length > 0) {
      template += '// Components\n';
      components.forEach(component => {
        template += `export { default as ${component} } from './${component}';\n`;
      });
      template += '\n';
    }

    template += '// Types\n';
    template += `export type * from './types';\n`;

    return template;
  }

  /**
   * Check if all journey files exist
   */
  async validateFileExistence(): Promise<{ journey: string; missingFiles: string[] }[]> {
    const results: { journey: string; missingFiles: string[] }[] = [];

    for (const [journeyName, expectedExports] of Object.entries(this.expectedExports)) {
      const journeyPath = path.join('src', 'journeys', journeyName);
      const missingFiles: string[] = [];

      expectedExports.forEach((exportName: any) => {
        const possiblePaths = [
          path.join(journeyPath, `${exportName}.ts`),
          path.join(journeyPath, `${exportName}.tsx`)
        ];

        const exists = possiblePaths.some(p => fs.existsSync(p));
        if (!exists) {
          missingFiles.push(exportName);
        }
      });

      results.push({
        journey: journeyName,
        missingFiles
      });
    }

    return results;
  }
}

// Run validation if called directly
if (require.main === module) {
  const validator = new ApiContractValidator();

  validator.validateAllContracts()
    .then(results => {
      const allValid = results.every(r => r.valid);
      process.exit(allValid ? 0 : 1);
    })
    .catch(error => {
      console.error('Validation failed:', error);
      process.exit(1);
    });
}

export { ApiContractValidator, ExportValidation, JourneyExports };
