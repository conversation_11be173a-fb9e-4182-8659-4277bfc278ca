# File Migration Mapping

## Overview
This document tracks the complete file migration from technical-focused structure to user-journey architecture.

## Journey Migrations

### 🚀 Onboarding Journey
**Purpose**: Authentication, couple pairing, profile setup, getting started experience

| Original Location | New Location | Status |
|------------------|--------------|--------|
| `src/hooks/useAuth.ts` | `src/journeys/onboarding/useAuth.ts` | ✅ Moved |
| `src/hooks/useUserProfile.ts` | `src/journeys/onboarding/useUserProfile.ts` | ✅ Moved |
| `src/hooks/useCouplePairing.ts` | `src/journeys/onboarding/useCouplePairing.ts` | ✅ Moved |
| `src/hooks/useFrameworkReady.ts` | `src/journeys/onboarding/useFrameworkReady.ts` | ✅ Moved |
| `src/hooks/useCoupleRealtime.ts` | `src/journeys/onboarding/useCoupleRealtime.ts` | ✅ Moved |
| `src/services/auth/` | `src/journeys/onboarding/auth/` | ✅ Moved |
| `src/services/data/couplePairingService.ts` | `src/journeys/onboarding/couplePairingService.ts` | ✅ Moved |
| `src/components/screens/AuthScreen.tsx` | `src/journeys/onboarding/AuthScreen.tsx` | ✅ Moved |
| `src/utils/onboardingStorage.ts` | `src/journeys/onboarding/onboardingStorage.ts` | ✅ Moved |
| `src/utils/authPrompt.ts` | `src/journeys/onboarding/authPrompt.ts` | ✅ Moved |

### 📅 Daily Journey
**Purpose**: Daily questions, streaks, daily engagement, weekly challenges

| Original Location | New Location | Status |
|------------------|--------------|--------|
| `src/hooks/useDailyQuestions.ts` | `src/journeys/daily/useDailyQuestions.ts` | ✅ Moved |
| `src/hooks/useDailyQuestionsNotifications.ts` | `src/journeys/daily/useDailyQuestionsNotifications.ts` | ✅ Moved |
| `src/hooks/useStreakData.ts` | `src/journeys/daily/useStreakData.ts` | ✅ Moved |
| `src/hooks/useDailyChallenges.ts` | `src/journeys/daily/useDailyChallenges.ts` | ✅ Moved |
| `src/hooks/useWeek*Data.ts` (all) | `src/journeys/daily/useWeek*Data.ts` | ✅ Moved |
| `src/hooks/useGenericWeekData.ts` | `src/journeys/daily/useGenericWeekData.ts` | ✅ Moved |
| `src/services/data/dailyQuestionsService.ts` | `src/journeys/daily/dailyQuestionsService.ts` | ✅ Moved |
| `src/services/features/dailyQuestionsNotificationService.ts` | `src/journeys/daily/dailyQuestionsNotificationService.ts` | ✅ Moved |
| `src/services/features/streakEventService.ts` | `src/journeys/daily/streakEventService.ts` | ✅ Moved |

### 🎮 Activities Journey
**Purpose**: Date nights, games, match game, meals, activities, favorites

| Original Location | New Location | Status |
|------------------|--------------|--------|
| `src/hooks/useDateNightIdeasSupabase.ts` | `src/journeys/activities/useDateNightIdeasSupabase.ts` | ✅ Moved |
| `src/hooks/useDateNightFavorites.ts` | `src/journeys/activities/useDateNightFavorites.ts` | ✅ Moved |
| `src/hooks/useDateNightPool.ts` | `src/journeys/activities/useDateNightPool.ts` | ✅ Moved |
| `src/hooks/useMatchGame.ts` | `src/journeys/activities/useMatchGame.ts` | ✅ Moved |
| `src/hooks/useMealIdeasSupabase.ts` | `src/journeys/activities/useMealIdeasSupabase.ts` | ✅ Moved |
| `src/hooks/useFavorites.ts` | `src/journeys/activities/useFavorites.ts` | ✅ Moved |
| `src/hooks/useContentFavorites.ts` | `src/journeys/activities/useContentFavorites.ts` | ✅ Moved |
| `src/hooks/useWeeklyDateNightIntegration.ts` | `src/journeys/activities/useWeeklyDateNightIntegration.ts` | ✅ Moved |
| `src/services/data/dateNightIdeasService.ts` | `src/journeys/activities/dateNightIdeasService.ts` | ✅ Moved |
| `src/services/data/mealIdeasService.ts` | `src/journeys/activities/mealIdeasService.ts` | ✅ Moved |
| `src/services/features/favoritesService.ts` | `src/journeys/activities/favoritesService.ts` | ✅ Moved |
| `src/services/features/match-game/` | `src/journeys/activities/match-game/` | ✅ Moved |
| `src/features/date-night/` | `src/journeys/activities/` (consolidated) | ✅ Moved |
| `src/components/features/DateNight.tsx` | `src/journeys/activities/DateNight.tsx` | ✅ Moved |
| `src/components/features/Activities.tsx` | `src/journeys/activities/Activities.tsx` | ✅ Moved |
| `src/components/features/Meals.tsx` | `src/journeys/activities/Meals.tsx` | ✅ Moved |
| `src/utils/matchGameHelpers.ts` | `src/journeys/activities/matchGameHelpers.ts` | ✅ Moved |
| `src/utils/surpriseHelpers.ts` | `src/journeys/activities/surpriseHelpers.ts` | ✅ Moved |

### 💕 Memories Journey
**Purpose**: Origin story, timeline, photos, milestones, scrapbook

| Original Location | New Location | Status |
|------------------|--------------|--------|
| `src/hooks/useOriginStoryData.ts` | `src/journeys/memories/useOriginStoryData.ts` | ✅ Moved |
| `src/hooks/useTimeline.ts` | `src/journeys/memories/useTimeline.ts` | ✅ Moved |
| `src/hooks/useTimelineData.ts` | `src/journeys/memories/useTimelineData.ts` | ✅ Moved |
| `src/hooks/useMilestones.ts` | `src/journeys/memories/useMilestones.ts` | ✅ Moved |
| `src/hooks/useMilestoneIntegration.ts` | `src/journeys/memories/useMilestoneIntegration.ts` | ✅ Moved |
| `src/hooks/useRelationshipMilestones.ts` | `src/journeys/memories/useRelationshipMilestones.ts` | ✅ Moved |
| `src/services/data/milestoneService.ts` | `src/journeys/memories/milestoneService.ts` | ✅ Moved |
| `src/services/storage/imageStorageService.ts` | `src/journeys/memories/imageStorageService.ts` | ✅ Moved |
| `src/services/storage/enhancedImageStorageService.ts` | `src/journeys/memories/enhancedImageStorageService.ts` | ✅ Moved |
| `src/components/features/PhotoManager.tsx` | `src/journeys/memories/PhotoManager.tsx` | ✅ Moved |

### 📋 Planning Journey
**Purpose**: Goal setting, preferences, scheduling, configuration

| Original Location | New Location | Status |
|------------------|--------------|--------|
| `src/hooks/useUserPreferences.ts` | `src/journeys/planning/useUserPreferences.ts` | ✅ Moved |
| `src/hooks/useAppSettings.ts` | `src/journeys/planning/useAppSettings.ts` | ✅ Moved |
| `src/services/data/userPreferencesService.ts` | `src/journeys/planning/userPreferencesService.ts` | ✅ Moved |
| `src/utils/config.ts` | `src/journeys/planning/config.ts` | ✅ Moved |

### 📈 Progress Journey
**Purpose**: Points, achievements, analytics, tracking, engagement

| Original Location | New Location | Status |
|------------------|--------------|--------|
| `src/hooks/usePointsSystemSupabase.ts` | `src/journeys/progress/usePointsSystemSupabase.ts` | ✅ Moved |
| `src/hooks/useEngagementSystem.ts` | `src/journeys/progress/useEngagementSystem.ts` | ✅ Moved |
| `src/hooks/useUserEvents.ts` | `src/journeys/progress/useUserEvents.ts` | ✅ Moved |
| `src/hooks/usePerformance.ts` | `src/journeys/progress/usePerformance.ts` | ✅ Moved |
| `src/hooks/useHomeScreen.ts` | `src/journeys/progress/useHomeScreen.ts` | ✅ Moved |
| `src/services/data/pointsSystemService.ts` | `src/journeys/progress/pointsSystemService.ts` | ✅ Moved |
| `src/services/analytics/` | `src/journeys/progress/` (consolidated) | ✅ Moved |
| `src/services/system/performanceOptimizationService.ts` | `src/journeys/progress/performanceOptimizationService.ts` | ✅ Moved |
| `src/utils/userEventUtils.ts` | `src/journeys/progress/userEventUtils.ts` | ✅ Moved |

## Shared Resources Migration

### 🔧 Shared Components, Services, Types, Utils
| Original Location | New Location | Status |
|------------------|--------------|--------|
| `src/components/` (remaining) | `src/shared/components/` | ✅ Moved |
| `src/hooks/` (remaining) | `src/shared/hooks/` | ✅ Moved |
| `src/services/` (remaining) | `src/shared/services/` | ✅ Moved |
| `src/types/` | `src/shared/types/` | ✅ Moved |
| `src/utils/` (remaining) | `src/shared/utils/` | ✅ Moved |

## Assets
| Original Location | New Location | Status |
|------------------|--------------|--------|
| `src/assets/` | `src/assets/` (unchanged) | ✅ Kept in place |

## Index Files Created
- ✅ `src/journeys/onboarding/index.ts`
- ✅ `src/journeys/daily/index.ts`
- ✅ `src/journeys/activities/index.ts`
- ✅ `src/journeys/memories/index.ts`
- ✅ `src/journeys/planning/index.ts`
- ✅ `src/journeys/progress/index.ts`
- ✅ `src/shared/index.ts`
- ✅ `src/index.ts`

## Consolidations Made
- ✅ **Removed `features/date-night/`** - Consolidated into `activities/`
- ✅ **Removed `analytics/` folder** - Single file moved to `progress/`
- ✅ **Fixed nested duplications** - Cleaned up `shared/` structure

## Next Steps for Junior Developer
1. **Fix import paths** in all moved files
2. **Update relative imports** to match new structure
3. **Verify TypeScript compilation** with `npx tsc --noEmit`
4. **Test critical user flows** once compilation passes
