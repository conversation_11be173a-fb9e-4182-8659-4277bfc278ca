#!/usr/bin/env node

/**
 * Update Test Imports Script
 * 
 * Updates all test files to use the new journey-based import structure.
 * This can run independently of TypeScript compilation fixes.
 */

const fs = require('fs');
const path = require('path');
const glob = require('glob');

// Import mapping for the new journey structure
const IMPORT_MAPPINGS = {
  // Onboarding Journey
  "from '../src/hooks/useAuth'": "from '../src/journeys/onboarding/useAuth'",
  "from '../src/hooks/useUserProfile'": "from '../src/journeys/onboarding/useUserProfile'",
  "from '../src/hooks/useCouplePairing'": "from '../src/journeys/onboarding/useCouplePairing'",
  "from '../src/services/auth/": "from '../src/journeys/onboarding/auth/",
  
  // Daily Journey
  "from '../src/hooks/useDailyQuestions'": "from '../src/journeys/daily/useDailyQuestions'",
  "from '../src/hooks/useStreakData'": "from '../src/journeys/daily/useStreakData'",
  "from '../src/hooks/useDailyChallenges'": "from '../src/journeys/daily/useDailyChallenges'",
  "from '../src/services/data/dailyQuestionsService'": "from '../src/journeys/daily/dailyQuestionsService'",
  
  // Activities Journey
  "from '../src/hooks/useDateNightIdeasSupabase'": "from '../src/journeys/activities/useDateNightIdeasSupabase'",
  "from '../src/hooks/useMatchGame'": "from '../src/journeys/activities/useMatchGame'",
  "from '../src/hooks/useFavorites'": "from '../src/journeys/activities/useFavorites'",
  "from '../src/services/data/dateNightIdeasService'": "from '../src/journeys/activities/dateNightIdeasService'",
  
  // Memories Journey
  "from '../src/hooks/useTimeline'": "from '../src/journeys/memories/useTimeline'",
  "from '../src/hooks/useOriginStoryData'": "from '../src/journeys/memories/useOriginStoryData'",
  "from '../src/hooks/useMilestones'": "from '../src/journeys/memories/useMilestones'",
  "from '../src/services/data/milestoneService'": "from '../src/journeys/memories/milestoneService'",
  
  // Planning Journey
  "from '../src/hooks/useUserPreferences'": "from '../src/journeys/planning/useUserPreferences'",
  "from '../src/hooks/useAppSettings'": "from '../src/journeys/planning/useAppSettings'",
  
  // Progress Journey
  "from '../src/hooks/usePointsSystemSupabase'": "from '../src/journeys/progress/usePointsSystemSupabase'",
  "from '../src/hooks/useUserEvents'": "from '../src/journeys/progress/useUserEvents'",
  "from '../src/hooks/useEngagementSystem'": "from '../src/journeys/progress/useEngagementSystem'",
  
  // Shared Resources
  "from '../src/components/": "from '../src/shared/components/",
  "from '../src/services/": "from '../src/shared/services/",
  "from '../src/utils/": "from '../src/shared/utils/",
  "from '../src/types/": "from '../src/shared/types/",
};

function updateTestFile(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let updated = false;
    
    // Apply import mappings
    for (const [oldImport, newImport] of Object.entries(IMPORT_MAPPINGS)) {
      if (content.includes(oldImport)) {
        content = content.replace(new RegExp(oldImport.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g'), newImport);
        updated = true;
      }
    }
    
    if (updated) {
      fs.writeFileSync(filePath, content);
      console.log(`✅ Updated: ${filePath}`);
      return true;
    } else {
      console.log(`⏭️  No changes needed: ${filePath}`);
      return false;
    }
  } catch (error) {
    console.error(`❌ Error updating ${filePath}:`, error.message);
    return false;
  }
}

function main() {
  console.log('🔄 Updating test imports for journey architecture...\n');
  
  // Find all test files
  const testPatterns = [
    '__tests__/**/*.test.ts',
    '__tests__/**/*.test.tsx',
    '__tests__/**/*.spec.ts',
    '__tests__/**/*.spec.tsx',
    '**/*.test.ts',
    '**/*.test.tsx',
    '**/*.spec.ts',
    '**/*.spec.tsx'
  ];
  
  let totalFiles = 0;
  let updatedFiles = 0;
  
  testPatterns.forEach(pattern => {
    const files = glob.sync(pattern, { ignore: 'node_modules/**' });
    
    files.forEach(file => {
      totalFiles++;
      if (updateTestFile(file)) {
        updatedFiles++;
      }
    });
  });
  
  console.log(`\n📊 Test Import Update Summary:`);
  console.log(`   Total test files found: ${totalFiles}`);
  console.log(`   Files updated: ${updatedFiles}`);
  console.log(`   Files unchanged: ${totalFiles - updatedFiles}`);
  
  if (updatedFiles > 0) {
    console.log('\n✅ Test imports updated successfully!');
    console.log('   Tests are ready to run once TypeScript compilation is fixed.');
  } else {
    console.log('\n⏭️  No test import updates needed.');
  }
}

if (require.main === module) {
  main();
}

module.exports = { updateTestFile, IMPORT_MAPPINGS };
