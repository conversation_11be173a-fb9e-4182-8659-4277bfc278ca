#!/usr/bin/env node

/**
 * Comprehensive Import Fix Script for React Native Best Practices
 * 
 * This script fixes all import paths to match the new structure:
 * - src/screens/
 * - src/components/
 * - src/hooks/
 * - src/services/
 * - src/types/
 * - src/utils/
 */

const fs = require('fs');
const path = require('path');

// Define comprehensive import mappings
const importMappings = {
  // Old shared structure to new structure
  '../shared/data/': '../services/',
  '../../shared/data/': '../services/',
  '../../../shared/data/': '../services/',
  '../../../../shared/data/': '../services/',
  
  '../shared/ui/': '../components/',
  '../../shared/ui/': '../components/',
  '../../../shared/ui/': '../components/',
  '../../../../shared/ui/': '../components/',
  
  '../shared/utils/': '../utils/',
  '../../shared/utils/': '../utils/',
  '../../../shared/utils/': '../utils/',
  '../../../../shared/utils/': '../utils/',
  
  // Specific service imports
  '../services/useAuth': '../hooks/useAuth',
  '../services/useDailyQuestions': '../hooks/useDailyQuestions',
  '../services/useMatchGame': '../hooks/useMatchGame',
  '../services/useFavorites': '../hooks/useFavorites',
  '../services/useTimeline': '../hooks/useTimeline',
  '../services/useUserProfile': '../hooks/useUserProfile',
  '../services/useCouplePairing': '../hooks/useCouplePairing',
  '../services/useMilestones': '../hooks/useMilestones',
  '../services/useStreakData': '../hooks/useStreakData',
  '../services/useUserEvents': '../hooks/useUserEvents',
  '../services/useAppSettings': '../hooks/useAppSettings',
  '../services/useHeartToggle': '../hooks/useHeartToggle',
  '../services/useToast': '../hooks/useToast',
  '../services/useOptimizedData': '../hooks/useOptimizedData',
  '../services/useTimelineData': '../hooks/useTimelineData',
  '../services/useOriginStoryData': '../hooks/useOriginStoryData',
  '../services/useWeek': '../hooks/useWeek',
  '../services/useDateNight': '../hooks/useDateNight',
  '../services/useMeal': '../hooks/useMeal',
  '../services/usePerformance': '../hooks/usePerformance',
  '../services/useError': '../hooks/useError',
  '../services/useAnimation': '../hooks/useAnimation',
  '../services/useCoupleRealtime': '../hooks/useCoupleRealtime',
  '../services/useFrameworkReady': '../hooks/useFrameworkReady',
  '../services/useGenericWeekData': '../hooks/useGenericWeekData',
  '../services/useHomeScreen': '../hooks/useHomeScreen',
  '../services/useMilestoneIntegration': '../hooks/useMilestoneIntegration',
  '../services/useRelationshipMilestones': '../hooks/useRelationshipMilestones',
  '../services/useUserPreferences': '../hooks/useUserPreferences',
  '../services/useWeeklyDateNightIntegration': '../hooks/useWeeklyDateNightIntegration',
  '../services/useEngagementSystem': '../hooks/useEngagementSystem',
  '../services/useDailyQuestionsNotifications': '../hooks/useDailyQuestionsNotifications',
  '../services/useDateNightFavorites': '../hooks/useDateNightFavorites',
  '../services/useDateNightIdeasSupabase': '../hooks/useDateNightIdeasSupabase',
  '../services/useDateNightPool': '../hooks/useDateNightPool',
  '../services/useErrorReporting': '../hooks/useErrorReporting',
  '../services/useMealIdeasSupabase': '../hooks/useMealIdeasSupabase',
  '../services/usePointsSystemSupabase': '../hooks/usePointsSystemSupabase',
  
  // Type imports
  '../services/auth': '../types/auth',
  '../services/supabase': '../types/supabase',
  '../services/matchGame.types': '../types/matchGame.types',
  '../services/index': '../types/index',
  
  // Component imports from shared/ui
  '../components/shared/': '../components/',
  '../components/ui/': '../components/',
  
  // Fix relative paths within new structure
  './shared/': '../',
  '../components/shared/': '../components/',
  '../components/ui/': '../components/',
};

function updateImportsInFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    let updatedContent = content;
    let hasChanges = false;

    // Apply all import mappings
    for (const [oldPath, newPath] of Object.entries(importMappings)) {
      const regex = new RegExp(`from ['"]${oldPath.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}`, 'g');
      if (regex.test(updatedContent)) {
        updatedContent = updatedContent.replace(regex, `from '${newPath}`);
        hasChanges = true;
      }
      
      const importRegex = new RegExp(`import ['"]${oldPath.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}`, 'g');
      if (importRegex.test(updatedContent)) {
        updatedContent = updatedContent.replace(importRegex, `import '${newPath}`);
        hasChanges = true;
      }
    }

    if (hasChanges) {
      fs.writeFileSync(filePath, updatedContent);
      console.log(`Updated imports in: ${filePath}`);
    }
  } catch (error) {
    console.error(`Error processing ${filePath}:`, error.message);
  }
}

function processDirectory(dirPath) {
  const items = fs.readdirSync(dirPath);
  
  for (const item of items) {
    const fullPath = path.join(dirPath, item);
    const stat = fs.statSync(fullPath);
    
    if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
      processDirectory(fullPath);
    } else if (stat.isFile() && (item.endsWith('.ts') || item.endsWith('.tsx'))) {
      updateImportsInFile(fullPath);
    }
  }
}

// Main execution
console.log('Starting comprehensive import path fixes...');
processDirectory('./src');
console.log('Comprehensive import path fixes completed!');
