#!/usr/bin/env node

/**
 * Comprehensive Import Fix Script
 * Fixes all import paths to match the React Native best practices structure
 */

const fs = require('fs');
const path = require('path');

// Define comprehensive import mappings
const importMappings = {
  // Fix supabase client imports
  '../types/supabase/client': '../services/supabase/client',
  '../supabase/client': '../services/supabase/client',
  '../supabase': '../types/supabase',
  
  // Fix utils imports
  '../utils/colors': '../../utils/colors',
  '../utils/logger': '../../utils/logger',
  '../utils/': '../../utils/',
  
  // Fix component imports
  '../components/shared': '../../components/shared',
  '../components/milestones/MilestoneList': '../../components/milestones/MilestoneList',
  '../components/milestones/MilestoneForm': '../../components/milestones/MilestoneForm',
  '../components/SurpriseMe': '../components/shared/SurpriseMe',
  
  // Fix hook imports
  '../hooks/useMilestoneIntegration': '../../hooks/useMilestoneIntegration',
  '../hooks/useAppTheme': '../../hooks/useAppTheme',
  '../../hooks/useAppTheme': '../hooks/useAppTheme',
  
  // Fix service imports
  '../services/milestoneService': '../../services/milestoneService',
  
  // Fix auth imports
  '../auth': '../types/auth',
  
  // Fix relative path issues in different directories
  './shared/': '../',
  '../shared/': '../',
  '../../shared/': '../',
  '../../../shared/': '../',
  
  // Fix app directory imports to point to src
  '../components/': '../src/components/',
  '../hooks/': '../src/hooks/',
  '../services/': '../src/services/',
  '../utils/': '../src/utils/',
  '../types/': '../src/types/',
};

function updateImportsInFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    let updatedContent = content;
    let hasChanges = false;

    // Apply all import mappings
    for (const [oldPath, newPath] of Object.entries(importMappings)) {
      // Handle 'from' imports
      const fromRegex = new RegExp(`from ['"]${oldPath.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}['"]`, 'g');
      if (fromRegex.test(updatedContent)) {
        updatedContent = updatedContent.replace(fromRegex, `from '${newPath}'`);
        hasChanges = true;
      }
      
      // Handle direct imports
      const importRegex = new RegExp(`import ['"]${oldPath.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}['"]`, 'g');
      if (importRegex.test(updatedContent)) {
        updatedContent = updatedContent.replace(importRegex, `import '${newPath}'`);
        hasChanges = true;
      }
    }

    // Special handling for app directory files to import from src
    if (filePath.startsWith('app/') && !filePath.includes('node_modules')) {
      const appMappings = {
        "from '../../components/": "from '../src/components/",
        "from '../../hooks/": "from '../src/hooks/",
        "from '../../services/": "from '../src/services/",
        "from '../../utils/": "from '../src/utils/",
        "from '../../types/": "from '../src/types/",
        "from '../components/": "from '../src/components/",
        "from '../hooks/": "from '../src/hooks/",
        "from '../services/": "from '../src/services/",
        "from '../utils/": "from '../src/utils/",
        "from '../types/": "from '../src/types/",
      };

      for (const [oldPath, newPath] of Object.entries(appMappings)) {
        if (updatedContent.includes(oldPath)) {
          updatedContent = updatedContent.replace(new RegExp(oldPath.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g'), newPath);
          hasChanges = true;
        }
      }
    }

    if (hasChanges) {
      fs.writeFileSync(filePath, updatedContent);
      console.log(`Updated imports in: ${filePath}`);
    }
  } catch (error) {
    console.error(`Error processing ${filePath}:`, error.message);
  }
}

function processDirectory(dirPath) {
  const items = fs.readdirSync(dirPath);
  
  for (const item of items) {
    const fullPath = path.join(dirPath, item);
    const stat = fs.statSync(fullPath);
    
    if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
      processDirectory(fullPath);
    } else if (stat.isFile() && (item.endsWith('.ts') || item.endsWith('.tsx'))) {
      updateImportsInFile(fullPath);
    }
  }
}

// Main execution
console.log('Starting comprehensive import fixes...');
processDirectory('./src');
processDirectory('./app');
console.log('Comprehensive import fixes completed!');
