#!/bin/bash

echo "🔍 Everlasting Us - Auto-Debug <PERSON>ript"
echo "====================================="
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    local status=$1
    local message=$2
    if [ "$status" = "OK" ]; then
        echo -e "${GREEN}✅ $message${NC}"
    elif [ "$status" = "WARN" ]; then
        echo -e "${YELLOW}⚠️  $message${NC}"
    elif [ "$status" = "ERROR" ]; then
        echo -e "${RED}❌ $message${NC}"
    else
        echo -e "${BLUE}ℹ️  $message${NC}"
    fi
}

echo "📋 Checking System Environment..."
echo "--------------------------------"

# Check Node.js version
NODE_VERSION=$(node --version 2>/dev/null)
if [ $? -eq 0 ]; then
    print_status "OK" "Node.js: $NODE_VERSION"
else
    print_status "ERROR" "Node.js not found"
    exit 1
fi

# Check npm version
NPM_VERSION=$(npm --version 2>/dev/null)
if [ $? -eq 0 ]; then
    print_status "OK" "npm: $NPM_VERSION"
else
    print_status "ERROR" "npm not found"
    exit 1
fi

# Check Expo CLI
EXPO_VERSION=$(npx expo --version 2>/dev/null)
if [ $? -eq 0 ]; then
    print_status "OK" "Expo CLI: $EXPO_VERSION"
else
    print_status "WARN" "Expo CLI not found or not working"
fi

echo ""
echo "📁 Checking Project Structure..."
echo "-------------------------------"

# Check if we're in the right directory
if [ -f "package.json" ]; then
    print_status "OK" "Found package.json"
else
    print_status "ERROR" "package.json not found - are you in the right directory?"
    exit 1
fi

# Check app directory
if [ -d "app" ]; then
    print_status "OK" "Found app/ directory"
else
    print_status "ERROR" "app/ directory not found"
fi

# Check components directory
if [ -d "components" ]; then
    print_status "OK" "Found components/ directory"
else
    print_status "WARN" "components/ directory not found"
fi

# Check hooks directory
if [ -d "hooks" ]; then
    print_status "OK" "Found hooks/ directory"
else
    print_status "WARN" "hooks/ directory not found"
fi

echo ""
echo "📦 Checking Dependencies..."
echo "---------------------------"

# Check if node_modules exists
if [ -d "node_modules" ]; then
    print_status "OK" "node_modules/ directory exists"
else
    print_status "ERROR" "node_modules/ directory not found - run 'npm install' first"
    exit 1
fi

# Check for dependency issues
echo "Checking for dependency conflicts..."
DEPENDENCY_CHECK=$(npm ls 2>&1)
if echo "$DEPENDENCY_CHECK" | grep -q "UNMET PEER DEPENDENCY"; then
    print_status "WARN" "Found peer dependency issues"
    echo "$DEPENDENCY_CHECK" | grep "UNMET PEER DEPENDENCY"
else
    print_status "OK" "No obvious dependency conflicts found"
fi

echo ""
echo "🔧 Checking Configuration Files..."
echo "--------------------------------"

# Check metro config
if [ -f "metro.config.js" ]; then
    print_status "OK" "Found metro.config.js"
    echo "Metro config contents:"
    cat metro.config.js | head -10
    echo "..."
else
    print_status "WARN" "metro.config.js not found - using default Metro config"
fi

# Check app.json or expo.json
if [ -f "app.json" ]; then
    print_status "OK" "Found app.json"
elif [ -f "expo.json" ]; then
    print_status "OK" "Found expo.json"
else
    print_status "ERROR" "Neither app.json nor expo.json found"
fi

# Check TypeScript config
if [ -f "tsconfig.json" ]; then
    print_status "OK" "Found tsconfig.json"
else
    print_status "WARN" "tsconfig.json not found"
fi

echo ""
echo "🐛 Running Code Quality Checks..."
echo "--------------------------------"

# Check TypeScript compilation
echo "Checking TypeScript compilation..."
TS_CHECK=$(npx tsc --noEmit 2>&1)
if [ $? -eq 0 ]; then
    print_status "OK" "TypeScript compilation successful"
else
    print_status "ERROR" "TypeScript compilation failed:"
    echo "$TS_CHECK" | head -20
fi

# Check ESLint if available
if [ -f ".eslintrc.js" ] || [ -f ".eslintrc.json" ]; then
    echo "Running ESLint..."
    if npm run lint >/dev/null 2>&1; then
        print_status "OK" "ESLint passed"
    else
        print_status "WARN" "ESLint found issues"
    fi
else
    print_status "INFO" "ESLint config not found - skipping"
fi

echo ""
echo "🌐 Checking Web Build..."
echo "------------------------"

# Check if web build works
echo "Attempting web build..."
if npm run build:web >/dev/null 2>&1; then
    print_status "OK" "Web build successful"
else
    print_status "ERROR" "Web build failed"
    echo "Build error output:"
    npm run build:web 2>&1 | head -20
fi

echo ""
echo "🔌 Checking Port Usage..."
echo "------------------------"

# Check what's using port 8081
echo "Checking port 8081..."
PORT_CHECK=$(lsof -i :8081 2>/dev/null)
if [ -n "$PORT_CHECK" ]; then
    print_status "WARN" "Port 8081 is in use:"
    echo "$PORT_CHECK"
else
    print_status "OK" "Port 8081 is available"
fi

# Check what's using port 19006 (Expo web default)
echo "Checking port 19006..."
WEB_PORT_CHECK=$(lsof -i :19006 2>/dev/null)
if [ -n "$WEB_PORT_CHECK" ]; then
    print_status "WARN" "Port 19006 is in use:"
    echo "$WEB_PORT_CHECK"
else
    print_status "OK" "Port 19006 is available"
fi

echo ""
echo "📱 Checking Mobile Development..."
echo "--------------------------------"

# Check if Expo Go can start
echo "Testing Expo development server..."
EXPO_TEST=$(timeout 10s npx expo start --no-dev --minify 2>&1 || echo "TIMEOUT")
if echo "$EXPO_TEST" | grep -q "Metro waiting on"; then
    print_status "OK" "Expo development server can start"
else
    print_status "WARN" "Expo development server had issues:"
    echo "$EXPO_TEST" | head -10
fi

echo ""
echo "🧹 Cache Status..."
echo "-----------------"

# Check cache sizes
echo "Checking cache sizes..."
if [ -d ".expo" ]; then
    EXPO_CACHE_SIZE=$(du -sh .expo 2>/dev/null | cut -f1)
    print_status "INFO" "Expo cache size: $EXPO_CACHE_SIZE"
else
    print_status "INFO" "No .expo cache directory found"
fi

if [ -d "node_modules/.cache" ]; then
    NPM_CACHE_SIZE=$(du -sh node_modules/.cache 2>/dev/null | cut -f1)
    print_status "INFO" "npm cache size: $NPM_CACHE_SIZE"
else
    print_status "INFO" "No npm cache directory found"
fi

echo ""
echo "📊 Summary & Recommendations..."
echo "==============================="

echo ""
echo "🔍 Next Steps:"
echo "1. If you see any ❌ errors above, fix those first"
echo "2. If you see ⚠️ warnings, consider addressing them"
echo "3. Try starting the app: npx expo start --web"
echo "4. If still blank, check browser console for JavaScript errors"
echo "5. Try clearing caches: npx expo start --clear --reset-cache"

echo ""
echo "🌐 For Web Development:"
echo "- Use: npx expo start --web (not just npx expo start)"
echo "- This should open http://localhost:19006, not 8081"
echo "- Port 8081 is Metro bundler, 19006 is the web app"

echo ""
echo "📱 For Mobile Development:"
echo "- Use: npx expo start"
echo "- Scan QR code with Expo Go app"

echo ""
echo "🚀 Debug script completed!"
echo "Check the output above for any issues."
