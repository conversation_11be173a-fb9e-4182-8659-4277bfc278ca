#!/usr/bin/env ts-node

/**
 * Week Activities Validation Script
 *
 * Validates that all activities referenced in week configurations
 * have corresponding implementations and identifies missing components.
 */

import {
    activityRegistry,
    getAllWeekConfigs,
    validateWeekConfigurations
} from '../src/shared/components/activities';

console.log('🔍 Week Activities Validation Report\n');

// Get all week configurations
const allWeeks = getAllWeekConfigs();
console.log(`📅 Found ${allWeeks.length} week configurations\n`);

// Collect all unique activity IDs
const allActivityIds = new Set<string>();
allWeeks.forEach((week: any) => {
  week.activityIds.forEach((id: string) => allActivityIds.add(id));
});

console.log(`🎯 Total unique activities referenced: ${allActivityIds.size}\n`);

// Get currently registered activities
const registeredActivities = activityRegistry.getAllActivities();
const registeredIds = new Set(registeredActivities.map(a => a.id));

console.log(`✅ Currently registered activities: ${registeredIds.size}`);
registeredActivities.forEach(activity => {
  console.log(`   • ${activity.title} (${activity.id})`);
});
console.log();

// Find missing activities
const missingActivities = Array.from(allActivityIds).filter(id => !registeredIds.has(id));

if (missingActivities.length > 0) {
  console.log(`❌ Missing activity implementations: ${missingActivities.length}`);
  missingActivities.forEach(id => {
    console.log(`   • ${id}`);
  });
  console.log();
} else {
  console.log('✅ All activities have implementations!\n');
}

// Validate week configurations
const validation = validateWeekConfigurations(activityRegistry);

console.log('📋 Week Configuration Validation:');
if (validation.valid) {
  console.log('✅ All week configurations are valid!');
} else {
  console.log(`❌ Found ${validation.errors.length} configuration errors:`);
  validation.errors.forEach((error: string) => {
    console.log(`   • ${error}`);
  });
}
console.log();

// Show week-by-week breakdown
console.log('📊 Week-by-Week Activity Status:');
allWeeks.forEach((week: any) => {
  console.log(`\n   Week ${week.weekNumber}: ${week.title}`);
  week.activityIds.forEach((activityId: string, index: number) => {
    const stepTitle = week.stepTitles?.[index] || activityId;
    const status = registeredIds.has(activityId) ? '✅' : '❌';
    console.log(`     ${status} ${stepTitle} (${activityId})`);
  });
});

// Generate implementation recommendations
console.log('\n🛠️  Implementation Recommendations:');

if (missingActivities.length > 0) {
  console.log('\n1. Create missing activity components:');
  missingActivities.forEach(id => {
    const componentName = id.split('-').map(word =>
      word.charAt(0).toUpperCase() + word.slice(1)
    ).join('') + 'Activity';

    console.log(`   • Create ${componentName}.tsx for "${id}"`);
  });

  console.log('\n2. Register new activities in index.ts:');
  missingActivities.forEach(id => {
    const configName = id.replace(/-/g, '') + 'Config';
    console.log(`   • Import and register ${configName}`);
  });

  console.log('\n3. Update activity registry:');
  console.log('   • Add new activity configs to auto-registration');
}

console.log('\n📈 Progress Summary:');
const implementedCount = registeredIds.size;
const totalCount = allActivityIds.size;
const percentage = Math.round((implementedCount / totalCount) * 100);

console.log(`   Implemented: ${implementedCount}/${totalCount} (${percentage}%)`);
console.log(`   Remaining: ${totalCount - implementedCount} activities`);

if (percentage === 100) {
  console.log('\n🎉 All activities implemented! Ready for production!');
} else if (percentage >= 75) {
  console.log('\n🚀 Most activities implemented. Good progress!');
} else if (percentage >= 50) {
  console.log('\n⚡ Half-way there! Keep going!');
} else {
  console.log('\n🏗️  Early stage. Lots of work ahead!');
}

// Export validation results for other scripts
export const validationResults = {
  totalActivities: allActivityIds.size,
  implementedActivities: implementedCount,
  missingActivities,
  validationErrors: validation.errors,
  completionPercentage: percentage,
};

export { };
