{"typescript.preferences.includePackageJsonAutoImports": "on", "typescript.suggest.autoImports": true, "typescript.preferences.importModuleSpecifier": "relative", "typescript.suggest.completeFunctionCalls": true, "files.associations": {"**/journeys/onboarding/**/*.ts": "typescript", "**/journeys/onboarding/**/*.tsx": "typescriptreact", "**/journeys/daily/**/*.ts": "typescript", "**/journeys/daily/**/*.tsx": "typescriptreact", "**/journeys/activities/**/*.ts": "typescript", "**/journeys/activities/**/*.tsx": "typescriptreact", "**/journeys/memories/**/*.ts": "typescript", "**/journeys/memories/**/*.tsx": "typescriptreact", "**/journeys/planning/**/*.ts": "typescript", "**/journeys/planning/**/*.tsx": "typescriptreact", "**/journeys/progress/**/*.ts": "typescript", "**/journeys/progress/**/*.tsx": "typescriptreact"}, "explorer.fileNesting.enabled": true, "explorer.fileNesting.patterns": {"*.ts": "${capture}.test.ts,${capture}.spec.ts,${capture}.types.ts", "*.tsx": "${capture}.test.tsx,${capture}.spec.tsx,${capture}.types.ts", "index.ts": "*.types.ts,*.constants.ts,*.utils.ts", "package.json": "package-lock.json,yarn.lock,pnpm-lock.yaml"}, "search.exclude": {"**/node_modules": true, "**/dist": true, "**/build": true, "**/.expo": true, "**/coverage": true}, "files.exclude": {"**/.expo": true, "**/node_modules": true, "**/dist": true, "**/build": true}, "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit", "source.organizeImports": "explicit"}, "editor.formatOnSave": true, "editor.defaultFormatter": "esbenp.prettier-vscode", "emmet.includeLanguages": {"typescript": "html", "typescriptreact": "html"}, "typescript.suggest.paths": true, "typescript.preferences.quoteStyle": "single", "typescript.preferences.semicolons": "insert", "eslint.workingDirectories": ["src"], "eslint.validate": ["javascript", "javascriptreact", "typescript", "typescriptreact"], "jest.jestCommandLine": "npm test", "jest.autoRun": "off", "jest.showCoverageOnLoad": false, "workbench.colorCustomizations": {"[Default Dark+]": {"activityBar.background": "#1e1e1e", "sideBar.background": "#252526"}}, "files.watcherExclude": {"**/.git/objects/**": true, "**/.git/subtree-cache/**": true, "**/node_modules/**": true, "**/.expo/**": true, "**/dist/**": true}, "typescript.suggest.includeCompletionsForImportStatements": true, "typescript.suggest.includeCompletionsForModuleExports": true, "editor.quickSuggestions": {"other": true, "comments": false, "strings": true}, "editor.parameterHints.enabled": true, "editor.suggest.snippetsPreventQuickSuggestions": false, "git.ignoreLimitWarning": true, "terminal.integrated.defaultProfile.osx": "bash", "terminal.integrated.defaultProfile.linux": "bash", "terminal.integrated.defaultProfile.windows": "PowerShell", "debug.console.fontSize": 14, "editor.fontSize": 14, "terminal.integrated.fontSize": 13, "workbench.editor.enablePreview": false, "workbench.editor.enablePreviewFromQuickOpen": false, "breadcrumbs.enabled": true, "breadcrumbs.showFiles": true, "breadcrumbs.showSymbols": true, "editor.minimap.enabled": true, "editor.minimap.maxColumn": 120, "problems.decorations.enabled": true, "editor.rulers": [80, 120], "files.trimTrailingWhitespace": true, "files.insertFinalNewline": true, "files.trimFinalNewlines": true}