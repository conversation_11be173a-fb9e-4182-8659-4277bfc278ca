module.exports = {
  root: true,
  extends: ['expo'],
  rules: {
    // Disallow ad-hoc StyleSheet usage in app screens; prefer DS components
    'no-restricted-syntax': [
      'warn',
      {
        selector: "CallExpression[callee.object.name='StyleSheet'][callee.property.name='create']",
        message: 'Avoid StyleSheet.create in screens; use DS templates. If you need a new pattern, add a DS component.'
      }
    ],
  },
  overrides: [
    {
      files: ['app/**/*.{ts,tsx}'],
      rules: {
        'no-restricted-syntax': [
          'warn',
          {
            selector: "CallExpression[callee.object.name='StyleSheet'][callee.property.name='create']",
            message: 'Avoid StyleSheet.create in screens; use DS templates.'
          }
        ],
      }
    }
  ]
};

