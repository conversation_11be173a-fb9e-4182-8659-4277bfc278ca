-- Migration: Create user_events table with RLS and idempotent constraint
-- Purpose: Central user activity/event log used across the app
-- Date: 2025-09-09

-- Enable required extensions (safe if already enabled)
create extension if not exists "uuid-ossp";
create extension if not exists pgcrypto;

-- 1) Core table (idempotent)
create table if not exists public.user_events (
  id uuid primary key default gen_random_uuid(),
  user_id uuid not null references auth.users(id) on delete cascade,
  event_name text not null,
  metadata jsonb not null default '{}',
  created_at timestamptz not null default now()
);

-- 1a) Backfill columns if table already existed without them
alter table public.user_events
  add column if not exists metadata jsonb not null default '{}';

-- Comments (guard column existence)
comment on table public.user_events is 'Centralized per-user activity events. One-off events use unique (user_id, event_name).';
comment on column public.user_events.event_name is 'Application-defined event identifier (e.g., onboarding_started)';

do $$
begin
  if exists (
    select 1 from information_schema.columns
    where table_schema = 'public' and table_name = 'user_events' and column_name = 'metadata'
  ) then
    execute 'comment on column public.user_events.metadata is ''Optional JSON metadata for event context''';
  end if;
end
$$;

-- 2) Idempotency for one-off events
-- This matches app behavior for unique, single-occurrence events
create unique index if not exists ux_user_events_user_event
  on public.user_events (user_id, event_name);

-- 3) RLS policies: users can insert/select their own events
alter table public.user_events enable row level security;

do $$
begin
  if not exists (
    select 1 from pg_policies
    where schemaname = 'public' and tablename = 'user_events'
      and policyname = 'Users can select own events'
  ) then
    create policy "Users can select own events"
      on public.user_events for select
      using (auth.uid() = user_id);
  end if;
end
$$;

do $$
begin
  if not exists (
    select 1 from pg_policies
    where schemaname = 'public' and tablename = 'user_events'
      and policyname = 'Users can insert own events'
  ) then
    create policy "Users can insert own events"
      on public.user_events for insert
      with check (auth.uid() = user_id);
  end if;
end
$$;

-- Note: The onboarding_progress view in earlier migrations depends on user_events.
-- This migration ensures the table exists in environments where it was missing.

