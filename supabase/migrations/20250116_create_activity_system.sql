-- Create Activity System Tables
-- This migration creates the database schema for the modular activity system

-- Create activity_sessions table
CREATE TABLE IF NOT EXISTS activity_sessions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  activity_id VARCHAR(255) NOT NULL,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  couple_id UUID NOT NULL,
  status VARCHAR(50) NOT NULL DEFAULT 'not_started',
  started_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  completed_at TIMESTAMP WITH TIME ZONE,
  score INTEGER NOT NULL DEFAULT 0,
  points INTEGER NOT NULL DEFAULT 0,
  data JSONB DEFAULT '{}',
  events JSONB DEFAULT '[]',
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Create activity_events table
CREATE TABLE IF NOT EXISTS activity_events (
  id VARCHAR(255) PRIMARY KEY,
  activity_id VARCHAR(255) NOT NULL,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  couple_id UUID NOT NULL,
  event_type VARCHAR(100) NOT NULL,
  data JSONB DEFAULT '{}',
  timestamp TIMESTAMP WITH TIME ZONE NOT NULL,
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Create would_you_rather_responses table
CREATE TABLE IF NOT EXISTS would_you_rather_responses (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  session_id UUID NOT NULL REFERENCES activity_sessions(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  couple_id UUID NOT NULL,
  question_id VARCHAR(255) NOT NULL,
  partner1_choice VARCHAR(1) NOT NULL CHECK (partner1_choice IN ('A', 'B')),
  partner2_choice VARCHAR(1) NOT NULL CHECK (partner2_choice IN ('A', 'B')),
  partner1_reason TEXT,
  partner2_reason TEXT,
  is_match BOOLEAN NOT NULL DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Create love_language_quiz_results table
CREATE TABLE IF NOT EXISTS love_language_quiz_results (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  session_id UUID NOT NULL REFERENCES activity_sessions(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  couple_id UUID NOT NULL,
  words_score INTEGER NOT NULL DEFAULT 0,
  acts_score INTEGER NOT NULL DEFAULT 0,
  gifts_score INTEGER NOT NULL DEFAULT 0,
  time_score INTEGER NOT NULL DEFAULT 0,
  touch_score INTEGER NOT NULL DEFAULT 0,
  primary_love_language VARCHAR(50) NOT NULL,
  total_score INTEGER NOT NULL DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_activity_sessions_user_id ON activity_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_activity_sessions_activity_id ON activity_sessions(activity_id);
CREATE INDEX IF NOT EXISTS idx_activity_sessions_couple_id ON activity_sessions(couple_id);
CREATE INDEX IF NOT EXISTS idx_activity_sessions_status ON activity_sessions(status);
CREATE INDEX IF NOT EXISTS idx_activity_sessions_created_at ON activity_sessions(created_at);

CREATE INDEX IF NOT EXISTS idx_activity_events_user_id ON activity_events(user_id);
CREATE INDEX IF NOT EXISTS idx_activity_events_activity_id ON activity_events(activity_id);
CREATE INDEX IF NOT EXISTS idx_activity_events_couple_id ON activity_events(couple_id);
CREATE INDEX IF NOT EXISTS idx_activity_events_event_type ON activity_events(event_type);
CREATE INDEX IF NOT EXISTS idx_activity_events_timestamp ON activity_events(timestamp);

CREATE INDEX IF NOT EXISTS idx_wyr_responses_session_id ON would_you_rather_responses(session_id);
CREATE INDEX IF NOT EXISTS idx_wyr_responses_user_id ON would_you_rather_responses(user_id);
CREATE INDEX IF NOT EXISTS idx_wyr_responses_couple_id ON would_you_rather_responses(couple_id);

CREATE INDEX IF NOT EXISTS idx_ll_quiz_results_session_id ON love_language_quiz_results(session_id);
CREATE INDEX IF NOT EXISTS idx_ll_quiz_results_user_id ON love_language_quiz_results(user_id);
CREATE INDEX IF NOT EXISTS idx_ll_quiz_results_couple_id ON love_language_quiz_results(couple_id);

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at
CREATE TRIGGER update_activity_sessions_updated_at
  BEFORE UPDATE ON activity_sessions
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Enable Row Level Security (RLS)
ALTER TABLE activity_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE activity_events ENABLE ROW LEVEL SECURITY;
ALTER TABLE would_you_rather_responses ENABLE ROW LEVEL SECURITY;
ALTER TABLE love_language_quiz_results ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for activity_sessions
CREATE POLICY "Users can view their own activity sessions" ON activity_sessions
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own activity sessions" ON activity_sessions
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own activity sessions" ON activity_sessions
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own activity sessions" ON activity_sessions
  FOR DELETE USING (auth.uid() = user_id);

-- Create RLS policies for activity_events
CREATE POLICY "Users can view their own activity events" ON activity_events
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own activity events" ON activity_events
  FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Create RLS policies for would_you_rather_responses
CREATE POLICY "Users can view their own WYR responses" ON would_you_rather_responses
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own WYR responses" ON would_you_rather_responses
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own WYR responses" ON would_you_rather_responses
  FOR UPDATE USING (auth.uid() = user_id);

-- Create RLS policies for love_language_quiz_results
CREATE POLICY "Users can view their own LL quiz results" ON love_language_quiz_results
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own LL quiz results" ON love_language_quiz_results
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own LL quiz results" ON love_language_quiz_results
  FOR UPDATE USING (auth.uid() = user_id);

-- Create views for common queries
CREATE OR REPLACE VIEW user_activity_stats AS
SELECT
  user_id,
  COUNT(*) as total_sessions,
  COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_sessions,
  AVG(score) as average_score,
  SUM(points) as total_points,
  MAX(completed_at) as last_activity_date
FROM activity_sessions
GROUP BY user_id;

CREATE OR REPLACE VIEW activity_leaderboard AS
SELECT
  activity_id,
  user_id,
  couple_id,
  score,
  points,
  completed_at,
  ROW_NUMBER() OVER (PARTITION BY activity_id ORDER BY score DESC) as rank
FROM activity_sessions
WHERE status = 'completed'
ORDER BY activity_id, score DESC;

-- Create functions for common operations
CREATE OR REPLACE FUNCTION get_user_activity_summary(p_user_id UUID)
RETURNS TABLE (
  total_sessions BIGINT,
  completed_sessions BIGINT,
  total_score BIGINT,
  total_points BIGINT,
  average_score NUMERIC,
  favorite_activity VARCHAR
) AS $$
BEGIN
  RETURN QUERY
  SELECT
    COUNT(*) as total_sessions,
    COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_sessions,
    COALESCE(SUM(score), 0) as total_score,
    COALESCE(SUM(points), 0) as total_points,
    COALESCE(AVG(score), 0) as average_score,
    (
      SELECT activity_id
      FROM activity_sessions
      WHERE user_id = p_user_id
      GROUP BY activity_id
      ORDER BY COUNT(*) DESC
      LIMIT 1
    ) as favorite_activity
  FROM activity_sessions
  WHERE user_id = p_user_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant necessary permissions
GRANT USAGE ON SCHEMA public TO authenticated;
GRANT ALL ON activity_sessions TO authenticated;
GRANT ALL ON activity_events TO authenticated;
GRANT ALL ON would_you_rather_responses TO authenticated;
GRANT ALL ON love_language_quiz_results TO authenticated;
GRANT SELECT ON user_activity_stats TO authenticated;
GRANT SELECT ON activity_leaderboard TO authenticated;
GRANT EXECUTE ON FUNCTION get_user_activity_summary(UUID) TO authenticated;
