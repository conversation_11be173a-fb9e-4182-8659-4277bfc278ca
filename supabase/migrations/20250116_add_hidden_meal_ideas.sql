-- Migration: Add Hidden Meal Ideas Support
-- Adds hidden_meal_ideas field to user_preferences table
-- Date: 2025-01-16

-- Add hidden_meal_ideas column to user_preferences table
ALTER TABLE public.user_preferences 
ADD COLUMN IF NOT EXISTS hidden_meal_ideas text[] NOT NULL DEFAULT '{}';

-- Create index for performance on hidden meal ideas
CREATE INDEX IF NOT EXISTS idx_user_preferences_hidden_meal_ideas 
ON public.user_preferences USING gin (hidden_meal_ideas);

-- Add comment for documentation
COMMENT ON COLUMN public.user_preferences.hidden_meal_ideas IS 'Array of composite_ids for meal ideas the user has hidden';

-- Update the get_user_preferences function to include hidden_meal_ideas
CREATE OR REPLACE FUNCTION get_user_preferences(user_uuid uuid)
RETURNS TABLE (
  id uuid,
  user_id uuid,
  hidden_date_night_ideas text[],
  hidden_meal_ideas text[],
  favorite_categories text[],
  preferred_difficulty text,
  preferred_cost text,
  preferred_duration_min integer,
  preferred_duration_max integer,
  notification_preferences jsonb,
  privacy_settings jsonb,
  ui_preferences jsonb,
  created_at timestamptz,
  updated_at timestamptz
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Create default preferences if they don't exist
  INSERT INTO public.user_preferences (user_id)
  VALUES (user_uuid)
  ON CONFLICT (user_id) DO NOTHING;

  -- Return the preferences
  RETURN QUERY
  SELECT 
    up.id,
    up.user_id,
    up.hidden_date_night_ideas,
    up.hidden_meal_ideas,
    up.favorite_categories,
    up.preferred_difficulty,
    up.preferred_cost,
    up.preferred_duration_min,
    up.preferred_duration_max,
    up.notification_preferences,
    up.privacy_settings,
    up.ui_preferences,
    up.created_at,
    up.updated_at
  FROM public.user_preferences up
  WHERE up.user_id = user_uuid;
END;
$$;
