-- Migration: Enhance user_events table for streak tracking
-- Purpose: Add columns needed for comprehensive streak calculation
-- Date: 2025-01-16
-- Author: Everlasting Us Team

-- Enable required extensions (safe if already enabled)
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Add new columns to existing user_events table
ALTER TABLE public.user_events 
ADD COLUMN IF NOT EXISTS couple_id UUID REFERENCES public.couples(id) ON DELETE CASCADE,
ADD COLUMN IF NOT EXISTS event_category TEXT,
ADD COLUMN IF NOT EXISTS event_type TEXT,
ADD COLUMN IF NOT EXISTS streak_eligible BOOLEAN DEFAULT false,
ADD COLUMN IF NOT EXISTS points_awarded INTEGER DEFAULT 0;

-- Add comments for documentation
COMMENT ON COLUMN public.user_events.couple_id IS 'Reference to couple for shared activities';
COMMENT ON COLUMN public.user_events.event_category IS 'Category of event for streak calculation (e.g., daily_questions, games)';
COMMENT ON COLUMN public.user_events.event_type IS 'Specific type of event within category';
COMMENT ON COLUMN public.user_events.streak_eligible IS 'Whether this event counts toward streak calculation';
COMMENT ON COLUMN public.user_events.points_awarded IS 'Points awarded for this event';

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_user_events_couple_id ON public.user_events(couple_id);
CREATE INDEX IF NOT EXISTS idx_user_events_category ON public.user_events(event_category);
CREATE INDEX IF NOT EXISTS idx_user_events_streak_eligible ON public.user_events(streak_eligible);
CREATE INDEX IF NOT EXISTS idx_user_events_created_at ON public.user_events(created_at);
CREATE INDEX IF NOT EXISTS idx_user_events_user_category_streak ON public.user_events(user_id, event_category, streak_eligible) WHERE streak_eligible = true;

-- Create streak calculation function
CREATE OR REPLACE FUNCTION calculate_user_streak(
  p_user_id UUID,
  p_category TEXT DEFAULT NULL
)
RETURNS INTEGER AS $$
DECLARE
  streak_count INTEGER := 0;
  current_date DATE := CURRENT_DATE;
  check_date DATE;
  has_activity BOOLEAN;
BEGIN
  -- Check if user has any streak-eligible events
  IF p_category IS NOT NULL THEN
    -- Category-specific streak
    SELECT COUNT(*) > 0 INTO has_activity
    FROM public.user_events
    WHERE user_id = p_user_id
      AND event_category = p_category
      AND streak_eligible = true;
  ELSE
    -- Overall streak (any category)
    SELECT COUNT(*) > 0 INTO has_activity
    FROM public.user_events
    WHERE user_id = p_user_id
      AND streak_eligible = true;
  END IF;

  IF NOT has_activity THEN
    RETURN 0;
  END IF;

  -- Calculate consecutive days with activity
  check_date := current_date;
  
  WHILE TRUE LOOP
    -- Check if user has activity on this date
    IF p_category IS NOT NULL THEN
      SELECT COUNT(*) > 0 INTO has_activity
      FROM public.user_events
      WHERE user_id = p_user_id
        AND event_category = p_category
        AND streak_eligible = true
        AND DATE(created_at) = check_date;
    ELSE
      SELECT COUNT(*) > 0 INTO has_activity
      FROM public.user_events
      WHERE user_id = p_user_id
        AND streak_eligible = true
        AND DATE(created_at) = check_date;
    END IF;

    IF has_activity THEN
      streak_count := streak_count + 1;
      check_date := check_date - INTERVAL '1 day';
    ELSE
      EXIT;
    END IF;
  END LOOP;

  RETURN streak_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Add RLS policies for new columns
DO $$
BEGIN
  -- Policy for users to view their own streak events
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies
    WHERE schemaname = 'public' AND tablename = 'user_events'
      AND policyname = 'Users can view own streak events'
  ) THEN
    CREATE POLICY "Users can view own streak events"
      ON public.user_events FOR SELECT
      USING (auth.uid() = user_id);
  END IF;

  -- Policy for users to insert their own streak events
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies
    WHERE schemaname = 'public' AND tablename = 'user_events'
      AND policyname = 'Users can insert own streak events'
  ) THEN
    CREATE POLICY "Users can insert own streak events"
      ON public.user_events FOR INSERT
      WITH CHECK (auth.uid() = user_id);
  END IF;
END
$$;

-- Test the function (only in development)
-- SELECT calculate_user_streak('00000000-0000-0000-0000-000000000000');

-- ============================================================================
-- ROLLBACK SECTION
-- ============================================================================
-- To rollback this migration, run the following commands:
-- 
-- -- Drop the function
-- DROP FUNCTION IF EXISTS calculate_user_streak(UUID, TEXT);
-- 
-- -- Drop indexes
-- DROP INDEX IF EXISTS idx_user_events_user_category_streak;
-- DROP INDEX IF EXISTS idx_user_events_created_at;
-- DROP INDEX IF EXISTS idx_user_events_streak_eligible;
-- DROP INDEX IF EXISTS idx_user_events_category;
-- DROP INDEX IF EXISTS idx_user_events_couple_id;
-- 
-- -- Drop columns
-- ALTER TABLE public.user_events 
-- DROP COLUMN IF EXISTS points_awarded,
-- DROP COLUMN IF EXISTS streak_eligible,
-- DROP COLUMN IF EXISTS event_type,
-- DROP COLUMN IF EXISTS event_category,
-- DROP COLUMN IF EXISTS couple_id;
