-- Migration: Daily Questions Feature
-- Creates tables and infrastructure for daily couple bonding questions

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Daily Questions Table
CREATE TABLE IF NOT EXISTS public.daily_questions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  question_id TEXT UNIQUE NOT NULL,
  question_text TEXT NOT NULL,
  category TEXT NOT NULL CHECK (category IN ('deep', 'fun', 'funny', 'memories', 'dreams', 'gratitude')),
  tone TEXT CHECK (tone IN ('light', 'thoughtful', 'playful', 'intimate')),
  difficulty TEXT CHECK (difficulty IN ('easy', 'medium', 'deep')),
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Daily Question Responses Table
CREATE TABLE IF NOT EXISTS public.daily_question_responses (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  couple_id UUID NOT NULL REFERENCES public.couples(id) ON DELETE CASCADE,
  question_id TEXT NOT NULL REFERENCES public.daily_questions(question_id),
  response_text TEXT NOT NULL,
  question_date DATE NOT NULL,
  answered_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  is_visible_to_partner BOOLEAN DEFAULT true,
  UNIQUE(user_id, question_date)
);

-- Daily Question Schedule Table
CREATE TABLE IF NOT EXISTS public.daily_question_schedule (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  question_date DATE NOT NULL,
  question_id TEXT NOT NULL REFERENCES public.daily_questions(question_id),
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  UNIQUE(question_date)
);

-- Daily Question Reactions Table (for partner interactions)
CREATE TABLE IF NOT EXISTS public.daily_question_reactions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  response_id UUID NOT NULL REFERENCES public.daily_question_responses(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  reaction_type TEXT NOT NULL CHECK (reaction_type IN ('heart', 'laugh', 'surprise', 'love')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  UNIQUE(response_id, user_id)
);

-- Daily Question Comments Table (for partner discussions)
CREATE TABLE IF NOT EXISTS public.daily_question_comments (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  response_id UUID NOT NULL REFERENCES public.daily_question_responses(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  comment_text TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- User Preferences Table (for notification settings)
CREATE TABLE IF NOT EXISTS public.user_preferences (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  daily_questions_notifications JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  UNIQUE(user_id)
);

-- Scheduled Notifications Table (for notification management)
CREATE TABLE IF NOT EXISTS public.scheduled_notifications (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  couple_id UUID REFERENCES public.couples(id) ON DELETE CASCADE,
  type TEXT NOT NULL CHECK (type IN ('daily_question_morning', 'daily_question_evening', 'partner_response', 'streak_milestone')),
  title TEXT NOT NULL,
  body TEXT NOT NULL,
  data JSONB DEFAULT '{}',
  scheduled_for TIMESTAMP WITH TIME ZONE NOT NULL,
  sent_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_daily_questions_category ON public.daily_questions(category);
CREATE INDEX IF NOT EXISTS idx_daily_questions_active ON public.daily_questions(is_active);
CREATE INDEX IF NOT EXISTS idx_daily_questions_difficulty ON public.daily_questions(difficulty);

CREATE INDEX IF NOT EXISTS idx_daily_responses_user_date ON public.daily_question_responses(user_id, question_date);
CREATE INDEX IF NOT EXISTS idx_daily_responses_couple_date ON public.daily_question_responses(couple_id, question_date);
CREATE INDEX IF NOT EXISTS idx_daily_responses_question_date ON public.daily_question_responses(question_id, question_date);

CREATE INDEX IF NOT EXISTS idx_daily_schedule_date ON public.daily_question_schedule(question_date);
CREATE INDEX IF NOT EXISTS idx_daily_schedule_active ON public.daily_question_schedule(is_active);

CREATE INDEX IF NOT EXISTS idx_daily_reactions_response ON public.daily_question_reactions(response_id);
CREATE INDEX IF NOT EXISTS idx_daily_reactions_user ON public.daily_question_reactions(user_id);

CREATE INDEX IF NOT EXISTS idx_daily_comments_response ON public.daily_question_comments(response_id);
CREATE INDEX IF NOT EXISTS idx_daily_comments_user ON public.daily_question_comments(user_id);

CREATE INDEX IF NOT EXISTS idx_user_preferences_user ON public.user_preferences(user_id);

CREATE INDEX IF NOT EXISTS idx_scheduled_notifications_user ON public.scheduled_notifications(user_id);
CREATE INDEX IF NOT EXISTS idx_scheduled_notifications_scheduled ON public.scheduled_notifications(scheduled_for);
CREATE INDEX IF NOT EXISTS idx_scheduled_notifications_sent ON public.scheduled_notifications(sent_at);

-- Enable Row Level Security
ALTER TABLE public.daily_questions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.daily_question_responses ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.daily_question_schedule ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.daily_question_reactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.daily_question_comments ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_preferences ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.scheduled_notifications ENABLE ROW LEVEL SECURITY;

-- RLS Policies for daily_questions (read-only for all authenticated users)
CREATE POLICY "Anyone can view daily questions" ON public.daily_questions
    FOR SELECT USING (true);

-- RLS Policies for daily_question_responses (users can only access their own and partner's responses)
CREATE POLICY "Users can view their own responses" ON public.daily_question_responses
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can view partner responses" ON public.daily_question_responses
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.couples c 
            WHERE c.id = couple_id 
            AND (c.partner1_user_id = auth.uid() OR c.partner2_user_id = auth.uid())
        )
    );

CREATE POLICY "Users can insert their own responses" ON public.daily_question_responses
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own responses" ON public.daily_question_responses
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own responses" ON public.daily_question_responses
    FOR DELETE USING (auth.uid() = user_id);

-- RLS Policies for daily_question_schedule (read-only for all authenticated users)
CREATE POLICY "Anyone can view daily question schedule" ON public.daily_question_schedule
    FOR SELECT USING (true);

-- RLS Policies for daily_question_reactions (users can only access reactions on their couple's responses)
CREATE POLICY "Users can view reactions on their couple's responses" ON public.daily_question_reactions
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.daily_question_responses dqr
            JOIN public.couples c ON c.id = dqr.couple_id
            WHERE dqr.id = response_id 
            AND (c.partner1_user_id = auth.uid() OR c.partner2_user_id = auth.uid())
        )
    );

CREATE POLICY "Users can insert reactions on their couple's responses" ON public.daily_question_reactions
    FOR INSERT WITH CHECK (
        auth.uid() = user_id AND
        EXISTS (
            SELECT 1 FROM public.daily_question_responses dqr
            JOIN public.couples c ON c.id = dqr.couple_id
            WHERE dqr.id = response_id 
            AND (c.partner1_user_id = auth.uid() OR c.partner2_user_id = auth.uid())
        )
    );

CREATE POLICY "Users can update their own reactions" ON public.daily_question_reactions
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own reactions" ON public.daily_question_reactions
    FOR DELETE USING (auth.uid() = user_id);

-- RLS Policies for daily_question_comments (users can only access comments on their couple's responses)
CREATE POLICY "Users can view comments on their couple's responses" ON public.daily_question_comments
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.daily_question_responses dqr
            JOIN public.couples c ON c.id = dqr.couple_id
            WHERE dqr.id = response_id 
            AND (c.partner1_user_id = auth.uid() OR c.partner2_user_id = auth.uid())
        )
    );

CREATE POLICY "Users can insert comments on their couple's responses" ON public.daily_question_comments
    FOR INSERT WITH CHECK (
        auth.uid() = user_id AND
        EXISTS (
            SELECT 1 FROM public.daily_question_responses dqr
            JOIN public.couples c ON c.id = dqr.couple_id
            WHERE dqr.id = response_id 
            AND (c.partner1_user_id = auth.uid() OR c.partner2_user_id = auth.uid())
        )
    );

CREATE POLICY "Users can update their own comments" ON public.daily_question_comments
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own comments" ON public.daily_question_comments
    FOR DELETE USING (auth.uid() = user_id);

-- RLS Policies for user_preferences (users can only access their own preferences)
CREATE POLICY "Users can view their own preferences" ON public.user_preferences
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own preferences" ON public.user_preferences
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own preferences" ON public.user_preferences
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own preferences" ON public.user_preferences
    FOR DELETE USING (auth.uid() = user_id);

-- RLS Policies for scheduled_notifications (users can only access their own notifications)
CREATE POLICY "Users can view their own notifications" ON public.scheduled_notifications
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own notifications" ON public.scheduled_notifications
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own notifications" ON public.scheduled_notifications
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own notifications" ON public.scheduled_notifications
    FOR DELETE USING (auth.uid() = user_id);

-- Function to get today's question for a couple
CREATE OR REPLACE FUNCTION get_todays_question_for_couple(couple_id_param UUID)
RETURNS TABLE (
    question_id TEXT,
    question_text TEXT,
    category TEXT,
    tone TEXT,
    difficulty TEXT,
    question_date DATE
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        dq.question_id,
        dq.question_text,
        dq.category,
        dq.tone,
        dq.difficulty,
        dqs.question_date
    FROM public.daily_question_schedule dqs
    JOIN public.daily_questions dq ON dq.question_id = dqs.question_id
    WHERE dqs.question_date = CURRENT_DATE
    AND dqs.is_active = true
    AND dq.is_active = true;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get couple's response status for today
CREATE OR REPLACE FUNCTION get_couple_response_status(couple_id_param UUID, question_date_param DATE)
RETURNS TABLE (
    user_id UUID,
    has_answered BOOLEAN,
    response_text TEXT,
    answered_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        c.partner1_user_id as user_id,
        CASE WHEN dqr1.id IS NOT NULL THEN true ELSE false END as has_answered,
        COALESCE(dqr1.response_text, '') as response_text,
        dqr1.answered_at
    FROM public.couples c
    LEFT JOIN public.daily_question_responses dqr1 ON dqr1.user_id = c.partner1_user_id 
        AND dqr1.question_date = question_date_param
    WHERE c.id = couple_id_param
    
    UNION ALL
    
    SELECT 
        c.partner2_user_id as user_id,
        CASE WHEN dqr2.id IS NOT NULL THEN true ELSE false END as has_answered,
        COALESCE(dqr2.response_text, '') as response_text,
        dqr2.answered_at
    FROM public.couples c
    LEFT JOIN public.daily_question_responses dqr2 ON dqr2.user_id = c.partner2_user_id 
        AND dqr2.question_date = question_date_param
    WHERE c.id = couple_id_param
    AND c.partner2_user_id IS NOT NULL;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Insert sample daily questions
INSERT INTO public.daily_questions (question_id, question_text, category, tone, difficulty) VALUES
-- Deep Questions
('deep_001', 'What is one thing you''ve learned about yourself through our relationship?', 'deep', 'thoughtful', 'deep'),
('deep_002', 'If you could change one thing about how we communicate, what would it be?', 'deep', 'thoughtful', 'deep'),
('deep_003', 'What does unconditional love mean to you?', 'deep', 'intimate', 'deep'),
('deep_004', 'What is your biggest fear about our future together?', 'deep', 'intimate', 'deep'),
('deep_005', 'How do you think we''ve grown together as a couple?', 'deep', 'thoughtful', 'medium'),

-- Fun Questions
('fun_001', 'If we could have any superpower together, what would it be?', 'fun', 'playful', 'easy'),
('fun_002', 'What would be the perfect date night for you this weekend?', 'fun', 'light', 'easy'),
('fun_003', 'If we could travel anywhere right now, where would you choose?', 'fun', 'playful', 'easy'),
('fun_004', 'What''s the most adventurous thing you''d like to try together?', 'fun', 'playful', 'medium'),
('fun_005', 'If we could have dinner with any couple (real or fictional), who would it be?', 'fun', 'light', 'easy'),

-- Funny Questions
('funny_001', 'What''s the weirdest habit you have that I probably don''t know about?', 'funny', 'playful', 'easy'),
('funny_002', 'If we were characters in a sitcom, what would our catchphrase be?', 'funny', 'playful', 'easy'),
('funny_003', 'What''s the most embarrassing thing that''s happened to you this week?', 'funny', 'light', 'easy'),
('funny_004', 'If you could only eat one food for the rest of your life, what would it be?', 'funny', 'playful', 'easy'),
('funny_005', 'What''s the strangest dream you''ve had recently?', 'funny', 'light', 'easy'),

-- Memories Questions
('memories_001', 'What''s your favorite memory from our first month together?', 'memories', 'thoughtful', 'medium'),
('memories_002', 'What was the moment you knew you were falling in love with me?', 'memories', 'intimate', 'deep'),
('memories_003', 'What''s the funniest thing that happened on one of our dates?', 'memories', 'playful', 'easy'),
('memories_004', 'What''s your favorite tradition we''ve created together?', 'memories', 'thoughtful', 'medium'),
('memories_005', 'What''s the most romantic thing I''ve ever done for you?', 'memories', 'intimate', 'medium'),

-- Dreams Questions
('dreams_001', 'What''s one dream you have for our relationship in the next year?', 'dreams', 'thoughtful', 'medium'),
('dreams_002', 'If money wasn''t an issue, what would you want to do together?', 'dreams', 'playful', 'easy'),
('dreams_003', 'What kind of home do you envision us having together?', 'dreams', 'thoughtful', 'medium'),
('dreams_004', 'What''s a goal you''d like us to work on together?', 'dreams', 'thoughtful', 'medium'),
('dreams_005', 'Where do you see us in 10 years?', 'dreams', 'thoughtful', 'deep'),

-- Gratitude Questions
('gratitude_001', 'What''s one thing I did today that made you smile?', 'gratitude', 'light', 'easy'),
('gratitude_002', 'What quality do you appreciate most about me?', 'gratitude', 'thoughtful', 'medium'),
('gratitude_003', 'What''s something I''ve taught you that you''re grateful for?', 'gratitude', 'thoughtful', 'medium'),
('gratitude_004', 'How has our relationship made you a better person?', 'gratitude', 'thoughtful', 'deep'),
('gratitude_005', 'What''s one small thing I do that makes your day better?', 'gratitude', 'light', 'easy')
ON CONFLICT (question_id) DO NOTHING;

-- Create a function to schedule questions for the next 365 days
CREATE OR REPLACE FUNCTION schedule_daily_questions()
RETURNS void AS $$
DECLARE
    question_record RECORD;
    current_date DATE := CURRENT_DATE;
    end_date DATE := CURRENT_DATE + INTERVAL '365 days';
    question_counter INTEGER := 0;
BEGIN
    -- Get all active questions
    FOR question_record IN 
        SELECT question_id FROM public.daily_questions WHERE is_active = true ORDER BY RANDOM()
    LOOP
        -- Schedule this question for the current date
        INSERT INTO public.daily_question_schedule (question_date, question_id)
        VALUES (current_date, question_record.question_id)
        ON CONFLICT (question_date) DO NOTHING;
        
        -- Move to next date
        current_date := current_date + INTERVAL '1 day';
        question_counter := question_counter + 1;
        
        -- If we've reached the end date, break
        IF current_date > end_date THEN
            EXIT;
        END IF;
        
        -- If we've used all questions, reset the counter and shuffle again
        IF question_counter >= (SELECT COUNT(*) FROM public.daily_questions WHERE is_active = true) THEN
            question_counter := 0;
        END IF;
    END LOOP;
END;
$$ LANGUAGE plpgsql;

-- Schedule questions for the next 30 days as a starting point
SELECT schedule_daily_questions();

-- Update the updated_at timestamp trigger
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Add updated_at columns to tables that need them
ALTER TABLE public.daily_questions ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();
ALTER TABLE public.daily_question_responses ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();

-- Create triggers for updated_at
CREATE TRIGGER update_daily_questions_updated_at 
    BEFORE UPDATE ON public.daily_questions 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_daily_question_responses_updated_at 
    BEFORE UPDATE ON public.daily_question_responses 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_preferences_updated_at 
    BEFORE UPDATE ON public.user_preferences 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
