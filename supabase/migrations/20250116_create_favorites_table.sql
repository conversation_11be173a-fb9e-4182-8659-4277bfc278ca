-- Create favorites table for consistent favorite functionality across all features
CREATE TABLE IF NOT EXISTS public.user_favorites (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    item_id TEXT NOT NULL, -- Can be UUID or composite ID like 'global:123'
    item_type TEXT NOT NULL CHECK (item_type IN ('daily_question', 'meal_idea', 'date_night_idea', 'activity')),
    source_table TEXT, -- e.g., 'meal_ideas_global', 'date_night_ideas_global'
    metadata JSONB DEFAULT '{}', -- Additional data like title, category, etc.
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Ensure one favorite per user per item
    UNIQUE(user_id, item_id, item_type)
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_user_favorites_user_type 
    ON public.user_favorites (user_id, item_type, created_at DESC);

CREATE INDEX IF NOT EXISTS idx_user_favorites_item 
    ON public.user_favorites (item_id, item_type);

-- Enable Row Level Security
ALTER TABLE public.user_favorites ENABLE ROW LEVEL SECURITY;

-- RLS Policies (users can only access their own favorites)
CREATE POLICY "Users can view their own favorites" ON public.user_favorites
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own favorites" ON public.user_favorites
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own favorites" ON public.user_favorites
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own favorites" ON public.user_favorites
    FOR DELETE USING (auth.uid() = user_id);

-- Create trigger for updated_at
CREATE TRIGGER update_user_favorites_updated_at 
    BEFORE UPDATE ON public.user_favorites 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
