-- Create meal_ideas_global table
CREATE TABLE IF NOT EXISTS public.meal_ideas_global (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    title TEXT NOT NULL,
    description TEXT,
    category TEXT NOT NULL,
    emoji TEXT,
    difficulty TEXT CHECK (difficulty IN ('easy', 'medium', 'hard')),
    prep_time INTEGER, -- in minutes
    cook_time INTEGER, -- in minutes
    servings INTEGER,
    ingredients TEXT[],
    instructions TEXT[],
    tags TEXT[],
    link TEXT,
    source TEXT,
    week_number INTEGER,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create meal_ideas_users table
CREATE TABLE IF NOT EXISTS public.meal_ideas_users (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    idea_id UUID REFERENCES public.meal_ideas_global(id) ON DELETE SET NULL,
    title TEXT NOT NULL,
    description TEXT,
    category TEXT NOT NULL,
    emoji TEXT,
    difficulty TEXT CHECK (difficulty IN ('easy', 'medium', 'hard')),
    prep_time INTEGER, -- in minutes
    cook_time INTEGER, -- in minutes
    servings INTEGER,
    ingredients TEXT[],
    instructions TEXT[],
    tags TEXT[],
    link TEXT,
    source TEXT,
    week_number INTEGER,
    is_favorite BOOLEAN DEFAULT FALSE,
    is_completed BOOLEAN DEFAULT FALSE,
    completed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create meal_voting table for meal voting functionality
CREATE TABLE IF NOT EXISTS public.meal_voting (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    history JSONB DEFAULT '[]'::jsonb,
    preferences JSONB DEFAULT '{}'::jsonb,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id)
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_meal_global_category_week 
    ON public.meal_ideas_global (category, week_number);

CREATE INDEX IF NOT EXISTS idx_meal_global_difficulty 
    ON public.meal_ideas_global (difficulty);

CREATE INDEX IF NOT EXISTS idx_meal_users_user_category 
    ON public.meal_ideas_users (user_id, category, created_at DESC);

CREATE INDEX IF NOT EXISTS idx_meal_users_user_completed 
    ON public.meal_ideas_users (user_id, is_completed, completed_at DESC);

CREATE INDEX IF NOT EXISTS idx_meal_users_user_favorite 
    ON public.meal_ideas_users (user_id, is_favorite, created_at DESC);

-- Full-text search index for meal ideas
CREATE INDEX IF NOT EXISTS idx_meal_global_search 
    ON public.meal_ideas_global USING gin(to_tsvector('english', title || ' ' || COALESCE(description, '')));

-- Enable Row Level Security
ALTER TABLE public.meal_ideas_global ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.meal_ideas_users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.meal_voting ENABLE ROW LEVEL SECURITY;

-- RLS Policies for meal_ideas_global (read-only for all authenticated users)
CREATE POLICY "Anyone can view global meal ideas" ON public.meal_ideas_global
    FOR SELECT USING (true);

-- RLS Policies for meal_ideas_users (users can only access their own data)
CREATE POLICY "Users can view their own meal ideas" ON public.meal_ideas_users
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own meal ideas" ON public.meal_ideas_users
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own meal ideas" ON public.meal_ideas_users
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own meal ideas" ON public.meal_ideas_users
    FOR DELETE USING (auth.uid() = user_id);

-- RLS Policies for meal_voting (users can only access their own data)
CREATE POLICY "Users can view their own meal voting" ON public.meal_voting
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own meal voting" ON public.meal_voting
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own meal voting" ON public.meal_voting
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own meal voting" ON public.meal_voting
    FOR DELETE USING (auth.uid() = user_id);

-- Insert some sample global meal ideas
INSERT INTO public.meal_ideas_global (title, description, category, emoji, difficulty, prep_time, cook_time, servings, ingredients, instructions, tags, week_number) VALUES
('Spaghetti Carbonara', 'Classic Italian pasta dish with eggs, cheese, and pancetta', 'Italian', '🍝', 'medium', 15, 20, 4, 
 ARRAY['400g spaghetti', '200g pancetta', '4 large eggs', '100g pecorino cheese', 'Black pepper', 'Salt'],
 ARRAY['Cook spaghetti according to package directions', 'Fry pancetta until crispy', 'Beat eggs with cheese and pepper', 'Combine hot pasta with pancetta', 'Add egg mixture and toss quickly'],
 ARRAY['pasta', 'italian', 'comfort food'], 1),

('Chicken Stir Fry', 'Quick and healthy Asian-inspired chicken and vegetable stir fry', 'Asian', '🥢', 'easy', 10, 15, 4,
 ARRAY['500g chicken breast', '2 bell peppers', '1 broccoli head', '3 cloves garlic', '2 tbsp soy sauce', '1 tbsp sesame oil'],
 ARRAY['Cut chicken into strips', 'Heat oil in wok', 'Cook chicken until golden', 'Add vegetables and stir fry', 'Add sauce and cook until heated through'],
 ARRAY['chicken', 'vegetables', 'quick meal'], 2),

('Beef Tacos', 'Traditional Mexican beef tacos with fresh toppings', 'Mexican', '🌮', 'easy', 20, 15, 6,
 ARRAY['500g ground beef', '1 packet taco seasoning', '12 taco shells', '1 lettuce head', '2 tomatoes', '1 cup cheese', 'Sour cream'],
 ARRAY['Brown ground beef in pan', 'Add taco seasoning and water', 'Simmer for 5 minutes', 'Warm taco shells', 'Fill with beef and toppings'],
 ARRAY['mexican', 'beef', 'family meal'], 3),

('Mediterranean Quinoa Bowl', 'Healthy quinoa bowl with Mediterranean vegetables and feta', 'Mediterranean', '🥗', 'easy', 15, 20, 4,
 ARRAY['1 cup quinoa', '1 cucumber', '2 tomatoes', '1 red onion', '200g feta cheese', 'Kalamata olives', 'Olive oil', 'Lemon juice'],
 ARRAY['Cook quinoa according to package', 'Dice vegetables', 'Mix vegetables with olive oil and lemon', 'Top quinoa with vegetables and feta', 'Garnish with olives'],
 ARRAY['healthy', 'vegetarian', 'mediterranean'], 4),

('Chocolate Chip Cookies', 'Classic homemade chocolate chip cookies', 'Dessert', '🍪', 'easy', 15, 12, 24,
 ARRAY['2 cups flour', '1 cup butter', '3/4 cup brown sugar', '1/2 cup white sugar', '2 eggs', '2 cups chocolate chips', '1 tsp vanilla'],
 ARRAY['Preheat oven to 375°F', 'Cream butter and sugars', 'Add eggs and vanilla', 'Mix in flour', 'Fold in chocolate chips', 'Bake for 9-11 minutes'],
 ARRAY['dessert', 'baking', 'sweet'], 5)
ON CONFLICT DO NOTHING;

-- Update the updated_at timestamp trigger
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_meal_ideas_global_updated_at 
    BEFORE UPDATE ON public.meal_ideas_global 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_meal_ideas_users_updated_at 
    BEFORE UPDATE ON public.meal_ideas_users 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
