-- Migration: Create missing database functions
-- This migration adds the functions that the TypeScript code expects to exist

-- Function to get random date night ideas
CREATE OR REPLACE FUNCTION get_random_date_night_ideas(
  p_limit INTEGER DEFAULT 10,
  p_category VARCHAR DEFAULT NULL,
  p_cost VARCHAR DEFAULT NULL,
  p_difficulty VARCHAR DEFAULT NULL
)
RETURNS TABLE (
  id VARCHAR,
  title VARCHAR,
  description TEXT,
  category VARCHAR,
  difficulty VARCHAR,
  cost VARCHAR,
  indoor_outdoor VARCHAR,
  estimated_duration INTEGER,
  emoji VARCHAR,
  week_number INTEGER,
  created_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
  RETURN QUERY
  SELECT
    dng.id,
    dng.title,
    dng.description,
    dng.category,
    dng.difficulty,
    dng.cost,
    dng.indoor_outdoor,
    dng.estimated_duration,
    dng.emoji,
    dng.week_number,
    dng.created_at
  FROM date_night_ideas_global dng
  WHERE
    (p_category IS NULL OR dng.category = p_category)
    AND (p_cost IS NULL OR dng.cost = p_cost)
    AND (p_difficulty IS NULL OR dng.difficulty = p_difficulty)
  ORDER BY RANDOM()
  LIMIT p_limit;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get date night ideas by category
CREATE OR REPLACE FUNCTION get_date_night_ideas_by_category(
  p_category VARCHAR,
  p_limit INTEGER DEFAULT 20
)
RETURNS TABLE (
  id VARCHAR,
  title VARCHAR,
  description TEXT,
  category VARCHAR,
  difficulty VARCHAR,
  cost VARCHAR,
  indoor_outdoor VARCHAR,
  estimated_duration INTEGER,
  emoji VARCHAR,
  week_number INTEGER,
  created_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
  RETURN QUERY
  SELECT
    dng.id,
    dng.title,
    dng.description,
    dng.category,
    dng.difficulty,
    dng.cost,
    dng.indoor_outdoor,
    dng.estimated_duration,
    dng.emoji,
    dng.week_number,
    dng.created_at
  FROM date_night_ideas_global dng
  WHERE dng.category = p_category
  ORDER BY dng.title
  LIMIT p_limit;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get date night ideas by difficulty
CREATE OR REPLACE FUNCTION get_date_night_ideas_by_difficulty(
  p_difficulty VARCHAR,
  p_limit INTEGER DEFAULT 20
)
RETURNS TABLE (
  id VARCHAR,
  title VARCHAR,
  description TEXT,
  category VARCHAR,
  difficulty VARCHAR,
  cost VARCHAR,
  indoor_outdoor VARCHAR,
  estimated_duration INTEGER,
  emoji VARCHAR,
  week_number INTEGER,
  created_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
  RETURN QUERY
  SELECT
    dng.id,
    dng.title,
    dng.description,
    dng.category,
    dng.difficulty,
    dng.cost,
    dng.indoor_outdoor,
    dng.estimated_duration,
    dng.emoji,
    dng.week_number,
    dng.created_at
  FROM date_night_ideas_global dng
  WHERE dng.difficulty = p_difficulty
  ORDER BY dng.title
  LIMIT p_limit;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get user activity statistics
CREATE OR REPLACE FUNCTION get_user_activity_stats(
  p_user_id UUID
)
RETURNS TABLE (
  total_activities INTEGER,
  completed_activities INTEGER,
  total_points INTEGER,
  average_score NUMERIC,
  last_activity_date TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
  RETURN QUERY
  SELECT
    COUNT(*)::INTEGER as total_activities,
    COUNT(CASE WHEN status = 'completed' THEN 1 END)::INTEGER as completed_activities,
    COALESCE(SUM(points), 0)::INTEGER as total_points,
    COALESCE(AVG(score), 0) as average_score,
    MAX(updated_at) as last_activity_date
  FROM activity_sessions
  WHERE user_id = p_user_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get couple activity statistics
CREATE OR REPLACE FUNCTION get_couple_activity_stats(
  p_couple_id UUID
)
RETURNS TABLE (
  total_activities INTEGER,
  completed_activities INTEGER,
  total_points INTEGER,
  average_score NUMERIC,
  shared_activities TEXT[]
) AS $$
BEGIN
  RETURN QUERY
  SELECT
    COUNT(*)::INTEGER as total_activities,
    COUNT(CASE WHEN status = 'completed' THEN 1 END)::INTEGER as completed_activities,
    COALESCE(SUM(points), 0)::INTEGER as total_points,
    COALESCE(AVG(score), 0) as average_score,
    ARRAY_AGG(DISTINCT activity_id) as shared_activities
  FROM activity_sessions
  WHERE couple_id = p_couple_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get activity completion rate
CREATE OR REPLACE FUNCTION get_activity_completion_rate(
  p_activity_id VARCHAR
)
RETURNS TABLE (
  total_starts INTEGER,
  total_completions INTEGER,
  completion_rate NUMERIC
) AS $$
BEGIN
  RETURN QUERY
  SELECT
    COUNT(*)::INTEGER as total_starts,
    COUNT(CASE WHEN status = 'completed' THEN 1 END)::INTEGER as total_completions,
    CASE
      WHEN COUNT(*) > 0 THEN
        (COUNT(CASE WHEN status = 'completed' THEN 1 END)::NUMERIC / COUNT(*)::NUMERIC) * 100
      ELSE 0
    END as completion_rate
  FROM activity_sessions
  WHERE activity_id = p_activity_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get user favorites count
CREATE OR REPLACE FUNCTION get_user_favorites_count(
  p_user_id UUID
)
RETURNS TABLE (
  total_favorites INTEGER,
  favorites_by_type JSONB
) AS $$
BEGIN
  RETURN QUERY
  SELECT
    COUNT(*)::INTEGER as total_favorites,
    jsonb_object_agg(item_type, type_count) as favorites_by_type
  FROM (
    SELECT
      item_type,
      COUNT(*) as type_count
    FROM user_favorites
    WHERE user_id = p_user_id
    GROUP BY item_type
  ) type_counts;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check if user has favorited an item
CREATE OR REPLACE FUNCTION is_item_favorited(
  p_user_id UUID,
  p_item_id VARCHAR,
  p_item_type VARCHAR
)
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS(
    SELECT 1
    FROM user_favorites
    WHERE user_id = p_user_id
      AND item_id = p_item_id
      AND item_type = p_item_type
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get meal voting results
CREATE OR REPLACE FUNCTION get_meal_voting_results(
  p_couple_id UUID,
  p_meal_id VARCHAR DEFAULT NULL
)
RETURNS TABLE (
  meal_id VARCHAR,
  total_votes INTEGER,
  likes INTEGER,
  dislikes INTEGER,
  neutrals INTEGER,
  consensus VARCHAR
) AS $$
BEGIN
  RETURN QUERY
  SELECT
    mv.meal_id,
    COUNT(*)::INTEGER as total_votes,
    COUNT(CASE WHEN mv.vote_type = 'like' THEN 1 END)::INTEGER as likes,
    COUNT(CASE WHEN mv.vote_type = 'dislike' THEN 1 END)::INTEGER as dislikes,
    COUNT(CASE WHEN mv.vote_type = 'neutral' THEN 1 END)::INTEGER as neutrals,
    CASE
      WHEN COUNT(CASE WHEN mv.vote_type = 'like' THEN 1 END) > COUNT(CASE WHEN mv.vote_type = 'dislike' THEN 1 END) THEN 'like'
      WHEN COUNT(CASE WHEN mv.vote_type = 'dislike' THEN 1 END) > COUNT(CASE WHEN mv.vote_type = 'like' THEN 1 END) THEN 'dislike'
      ELSE 'neutral'
    END as consensus
  FROM meal_voting mv
  WHERE mv.couple_id = p_couple_id
    AND (p_meal_id IS NULL OR mv.meal_id = p_meal_id)
  GROUP BY mv.meal_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permissions to authenticated users
GRANT EXECUTE ON FUNCTION get_random_date_night_ideas TO authenticated;
GRANT EXECUTE ON FUNCTION get_date_night_ideas_by_category TO authenticated;
GRANT EXECUTE ON FUNCTION get_date_night_ideas_by_difficulty TO authenticated;
GRANT EXECUTE ON FUNCTION get_user_activity_stats TO authenticated;
GRANT EXECUTE ON FUNCTION get_couple_activity_stats TO authenticated;
GRANT EXECUTE ON FUNCTION get_activity_completion_rate TO authenticated;
GRANT EXECUTE ON FUNCTION get_user_favorites_count TO authenticated;
GRANT EXECUTE ON FUNCTION is_item_favorited TO authenticated;
GRANT EXECUTE ON FUNCTION get_meal_voting_results TO authenticated;
