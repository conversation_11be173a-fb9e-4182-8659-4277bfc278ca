-- Comprehensive Date Night Ideas Dataset (115+ activities)
-- This migration adds a comprehensive collection of date night activities across multiple categories

-- Additional Romantic Activities (20 ideas)
INSERT INTO public.date_night_ideas_global (title, description, category, emoji, difficulty, estimated_duration, cost, indoor_outdoor, tags) VALUES
('Candlelit Bath Together', 'Draw a warm bath with candles and essential oils', 'romantic', '🛁', 'easy', 60, 'low', 'indoor', ARRAY['romantic', 'relaxing', 'intimate']),
('Love Letter Writing', 'Write heartfelt letters to each other and exchange them', 'romantic', '💌', 'easy', 45, 'free', 'indoor', ARRAY['romantic', 'writing', 'emotional']),
('Sunrise Breakfast Date', 'Wake up early to watch the sunrise with breakfast', 'romantic', '🌅', 'medium', 90, 'low', 'outdoor', ARRAY['romantic', 'morning', 'nature']),
('Couples Massage', 'Give each other relaxing massages at home', 'romantic', '💆', 'easy', 60, 'free', 'indoor', ARRAY['romantic', 'relaxing', 'intimate']),
('Memory Lane Drive', 'Drive to places that are special to your relationship', 'romantic', '🚗', 'easy', 120, 'low', 'outdoor', ARRAY['romantic', 'memories', 'nostalgic']),
('<PERSON> Picking', 'Pick wildflowers together and create a bouquet', 'romantic', '🌸', 'easy', 90, 'free', 'outdoor', ARRAY['romantic', 'nature', 'creative']),
('Poetry Reading', 'Read romantic poetry to each other by candlelight', 'romantic', '📖', 'easy', 60, 'free', 'indoor', ARRAY['romantic', 'literature', 'intimate']),
('Couples Yoga', 'Practice yoga poses together for connection', 'romantic', '🧘', 'medium', 75, 'free', 'indoor', ARRAY['romantic', 'wellness', 'connection']),
('Slow Dancing', 'Dance slowly to your favorite songs in the living room', 'romantic', '💃', 'easy', 45, 'free', 'indoor', ARRAY['romantic', 'music', 'dancing']),
('Love Playlist Creation', 'Create a playlist of songs that represent your love', 'romantic', '🎵', 'easy', 60, 'free', 'indoor', ARRAY['romantic', 'music', 'creative']),
('Romantic Picnic Indoors', 'Set up a picnic on the living room floor', 'romantic', '🧺', 'easy', 90, 'low', 'indoor', ARRAY['romantic', 'food', 'creative']),
('Star Map Creation', 'Create a map of the stars from your first date', 'romantic', '⭐', 'medium', 120, 'low', 'indoor', ARRAY['romantic', 'creative', 'memories']),
('Couples Meditation', 'Meditate together for deeper connection', 'romantic', '🕯️', 'easy', 30, 'free', 'indoor', ARRAY['romantic', 'mindfulness', 'connection']),
('Love Story Timeline', 'Create a visual timeline of your relationship', 'romantic', '📅', 'medium', 120, 'low', 'indoor', ARRAY['romantic', 'creative', 'memories']),
('Romantic Scavenger Hunt', 'Create clues leading to romantic surprises', 'romantic', '🔍', 'medium', 90, 'low', 'indoor', ARRAY['romantic', 'games', 'surprise']),
('Couples Journaling', 'Write in journals and share your thoughts', 'romantic', '📝', 'easy', 60, 'low', 'indoor', ARRAY['romantic', 'writing', 'emotional']),
('Moonlight Walk', 'Take a peaceful walk under the moonlight', 'romantic', '🌙', 'easy', 60, 'free', 'outdoor', ARRAY['romantic', 'nature', 'peaceful']),
('Love Coupon Book', 'Create a book of romantic coupons for each other', 'romantic', '🎟️', 'easy', 90, 'low', 'indoor', ARRAY['romantic', 'creative', 'gifts']),
('Romantic Movie Marathon', 'Watch classic romantic movies with themed snacks', 'romantic', '🎬', 'easy', 240, 'low', 'indoor', ARRAY['romantic', 'movies', 'cozy']),
('Breakfast in Bed', 'Surprise each other with breakfast in bed', 'romantic', '🥞', 'easy', 60, 'low', 'indoor', ARRAY['romantic', 'food', 'surprise'])
ON CONFLICT DO NOTHING;

-- Adventure Activities (15 ideas)
INSERT INTO public.date_night_ideas_global (title, description, category, emoji, difficulty, estimated_duration, cost, indoor_outdoor, tags) VALUES
('Rock Climbing', 'Try indoor or outdoor rock climbing together', 'adventure', '🧗', 'hard', 180, 'medium', 'both', ARRAY['adventure', 'exercise', 'challenge']),
('Kayaking Adventure', 'Paddle together on a lake or calm river', 'adventure', '🛶', 'medium', 240, 'medium', 'outdoor', ARRAY['adventure', 'water', 'nature']),
('Geocaching Hunt', 'Use GPS to find hidden treasures in your area', 'adventure', '📍', 'medium', 120, 'free', 'outdoor', ARRAY['adventure', 'treasure', 'exploration']),
('Zip Lining', 'Soar through the trees on a zip line course', 'adventure', '🌲', 'hard', 180, 'high', 'outdoor', ARRAY['adventure', 'adrenaline', 'heights']),
('Cave Exploration', 'Explore local caves or caverns together', 'adventure', '🕳️', 'hard', 240, 'medium', 'outdoor', ARRAY['adventure', 'exploration', 'underground']),
('Hot Air Balloon', 'Take a romantic hot air balloon ride', 'adventure', '🎈', 'easy', 180, 'high', 'outdoor', ARRAY['adventure', 'romantic', 'views']),
('Camping Under Stars', 'Set up camp and sleep under the stars', 'adventure', '⛺', 'medium', 720, 'medium', 'outdoor', ARRAY['adventure', 'camping', 'nature']),
('Whitewater Rafting', 'Navigate rapids together on a guided trip', 'adventure', '🚣', 'hard', 300, 'high', 'outdoor', ARRAY['adventure', 'water', 'adrenaline']),
('Mountain Biking', 'Explore trails on mountain bikes', 'adventure', '🚵', 'medium', 180, 'medium', 'outdoor', ARRAY['adventure', 'biking', 'exercise']),
('Skydiving', 'Take the ultimate leap together', 'adventure', '🪂', 'hard', 240, 'high', 'outdoor', ARRAY['adventure', 'extreme', 'adrenaline']),
('Horseback Riding', 'Ride horses through scenic trails', 'adventure', '🐎', 'medium', 180, 'medium', 'outdoor', ARRAY['adventure', 'animals', 'nature']),
('Scuba Diving', 'Explore underwater worlds together', 'adventure', '🤿', 'hard', 300, 'high', 'outdoor', ARRAY['adventure', 'water', 'underwater']),
('Bungee Jumping', 'Take a thrilling bungee jump together', 'adventure', '🤸', 'hard', 120, 'high', 'outdoor', ARRAY['adventure', 'extreme', 'adrenaline']),
('ATV Adventure', 'Ride ATVs through off-road terrain', 'adventure', '🏍️', 'medium', 180, 'medium', 'outdoor', ARRAY['adventure', 'vehicles', 'off-road']),
('Paragliding', 'Soar through the sky with a paraglider', 'adventure', '🪂', 'hard', 240, 'high', 'outdoor', ARRAY['adventure', 'flying', 'views'])
ON CONFLICT DO NOTHING;

-- Creative Activities (15 ideas)
INSERT INTO public.date_night_ideas_global (title, description, category, emoji, difficulty, estimated_duration, cost, indoor_outdoor, tags) VALUES
('Pottery Making', 'Create ceramic pieces together at a pottery studio', 'creative', '🏺', 'medium', 180, 'medium', 'indoor', ARRAY['creative', 'art', 'hands-on']),
('Painting Class', 'Take a couples painting class or paint at home', 'creative', '🎨', 'easy', 120, 'medium', 'indoor', ARRAY['creative', 'art', 'learning']),
('Jewelry Making', 'Design and create jewelry pieces for each other', 'creative', '💍', 'medium', 150, 'medium', 'indoor', ARRAY['creative', 'crafts', 'gifts']),
('Candle Making', 'Create custom scented candles together', 'creative', '🕯️', 'easy', 90, 'low', 'indoor', ARRAY['creative', 'crafts', 'scents']),
('Soap Making', 'Make natural soaps with custom scents', 'creative', '🧼', 'medium', 120, 'low', 'indoor', ARRAY['creative', 'crafts', 'natural']),
('Woodworking Project', 'Build something together in a workshop', 'creative', '🔨', 'hard', 240, 'medium', 'indoor', ARRAY['creative', 'building', 'tools']),
('Scrapbooking', 'Create a scrapbook of your memories together', 'creative', '📸', 'easy', 180, 'low', 'indoor', ARRAY['creative', 'memories', 'photos']),
('Calligraphy Class', 'Learn beautiful handwriting together', 'creative', '✒️', 'medium', 120, 'low', 'indoor', ARRAY['creative', 'writing', 'art']),
('Flower Arranging', 'Create beautiful floral arrangements', 'creative', '🌺', 'easy', 90, 'medium', 'indoor', ARRAY['creative', 'flowers', 'beauty']),
('Mosaic Art', 'Create colorful mosaic art pieces', 'creative', '🎭', 'medium', 180, 'medium', 'indoor', ARRAY['creative', 'art', 'colorful']),
('Embroidery Together', 'Learn embroidery and create decorative pieces', 'creative', '🪡', 'medium', 120, 'low', 'indoor', ARRAY['creative', 'sewing', 'decorative']),
('Glass Blowing', 'Try glass blowing at a local studio', 'creative', '🫧', 'hard', 180, 'high', 'indoor', ARRAY['creative', 'glass', 'hot']),
('Origami Workshop', 'Fold intricate paper creations together', 'creative', '📜', 'medium', 90, 'free', 'indoor', ARRAY['creative', 'paper', 'precision']),
('Tie-Dye Session', 'Create colorful tie-dye clothing or accessories', 'creative', '🌈', 'easy', 120, 'low', 'outdoor', ARRAY['creative', 'colorful', 'clothing']),
('Sculpture Class', 'Learn to sculpt with clay or other materials', 'creative', '🗿', 'medium', 180, 'medium', 'indoor', ARRAY['creative', 'sculpture', 'art'])
ON CONFLICT DO NOTHING;

-- Cultural Activities (15 ideas)
INSERT INTO public.date_night_ideas_global (title, description, category, emoji, difficulty, estimated_duration, cost, indoor_outdoor, tags) VALUES
('Museum Visit', 'Explore history, science, or art museums', 'cultural', '🏛️', 'easy', 180, 'medium', 'indoor', ARRAY['cultural', 'learning', 'history']),
('Opera Night', 'Attend a live opera performance', 'cultural', '🎭', 'easy', 240, 'high', 'indoor', ARRAY['cultural', 'music', 'formal']),
('Ballet Performance', 'Watch a beautiful ballet together', 'cultural', '🩰', 'easy', 180, 'high', 'indoor', ARRAY['cultural', 'dance', 'elegant']),
('Symphony Concert', 'Listen to classical music at a symphony', 'cultural', '🎼', 'easy', 150, 'medium', 'indoor', ARRAY['cultural', 'music', 'classical']),
('Theater Show', 'Attend a local theater production', 'cultural', '🎪', 'easy', 180, 'medium', 'indoor', ARRAY['cultural', 'theater', 'performance']),
('Cultural Festival', 'Attend a local cultural festival or fair', 'cultural', '🎊', 'easy', 240, 'low', 'outdoor', ARRAY['cultural', 'festival', 'community']),
('Historical Tour', 'Take a guided tour of historical sites', 'cultural', '🏰', 'easy', 180, 'low', 'outdoor', ARRAY['cultural', 'history', 'walking']),
('Language Class', 'Learn a new language together', 'cultural', '🗣️', 'medium', 90, 'medium', 'indoor', ARRAY['cultural', 'learning', 'language']),
('Cooking Cultural Cuisine', 'Cook traditional dishes from another culture', 'cultural', '🍜', 'medium', 150, 'medium', 'indoor', ARRAY['cultural', 'cooking', 'international']),
('Documentary Night', 'Watch educational documentaries together', 'cultural', '📺', 'easy', 120, 'free', 'indoor', ARRAY['cultural', 'learning', 'documentary']),
('Book Club for Two', 'Read and discuss books together', 'cultural', '📚', 'easy', 60, 'low', 'indoor', ARRAY['cultural', 'reading', 'discussion']),
('Poetry Slam', 'Attend a local poetry reading event', 'cultural', '🎤', 'easy', 120, 'low', 'indoor', ARRAY['cultural', 'poetry', 'performance']),
('Architecture Walk', 'Explore interesting architecture in your city', 'cultural', '🏢', 'easy', 120, 'free', 'outdoor', ARRAY['cultural', 'architecture', 'walking']),
('Lecture or Talk', 'Attend educational lectures or TED talks', 'cultural', '🎓', 'easy', 90, 'low', 'indoor', ARRAY['cultural', 'learning', 'intellectual']),
('Art Gallery Opening', 'Attend art gallery openings and exhibitions', 'cultural', '🖼️', 'easy', 120, 'free', 'indoor', ARRAY['cultural', 'art', 'social'])
ON CONFLICT DO NOTHING;

-- Fun & Games Activities (15 ideas)
INSERT INTO public.date_night_ideas_global (title, description, category, emoji, difficulty, estimated_duration, cost, indoor_outdoor, tags) VALUES
('Escape Room Challenge', 'Solve puzzles together to escape themed rooms', 'fun', '🔐', 'medium', 90, 'medium', 'indoor', ARRAY['fun', 'puzzles', 'teamwork']),
('Bowling Night', 'Go bowling with snacks and friendly competition', 'fun', '🎳', 'easy', 120, 'low', 'indoor', ARRAY['fun', 'sports', 'competitive']),
('Arcade Games', 'Play classic arcade games and win prizes', 'fun', '🕹️', 'easy', 120, 'medium', 'indoor', ARRAY['fun', 'games', 'nostalgic']),
('Karaoke Night', 'Sing your hearts out at a karaoke bar or at home', 'fun', '🎤', 'easy', 120, 'medium', 'indoor', ARRAY['fun', 'music', 'singing']),
('Trivia Night', 'Test your knowledge at a local trivia night', 'fun', '🧠', 'easy', 120, 'low', 'indoor', ARRAY['fun', 'knowledge', 'competitive']),
('Laser Tag', 'Battle it out in a laser tag arena', 'fun', '🔫', 'medium', 90, 'medium', 'indoor', ARRAY['fun', 'action', 'competitive']),
('Go-Kart Racing', 'Race against each other on a go-kart track', 'fun', '🏎️', 'medium', 90, 'medium', 'outdoor', ARRAY['fun', 'racing', 'adrenaline']),
('Trampoline Park', 'Bounce around and play games at a trampoline park', 'fun', '🤸', 'medium', 90, 'medium', 'indoor', ARRAY['fun', 'exercise', 'bouncing']),
('Axe Throwing', 'Learn to throw axes at targets safely', 'fun', '🪓', 'medium', 90, 'medium', 'indoor', ARRAY['fun', 'skill', 'unique']),
('Virtual Reality Gaming', 'Explore virtual worlds together', 'fun', '🥽', 'easy', 90, 'medium', 'indoor', ARRAY['fun', 'technology', 'immersive']),
('Board Game Café', 'Play board games while enjoying coffee and snacks', 'fun', '🎲', 'easy', 180, 'medium', 'indoor', ARRAY['fun', 'games', 'strategy']),
('Comedy Show', 'Laugh together at a live comedy performance', 'fun', '😂', 'easy', 120, 'medium', 'indoor', ARRAY['fun', 'comedy', 'laughter']),
('Improv Class', 'Take an improvisational comedy class together', 'fun', '🎭', 'medium', 120, 'medium', 'indoor', ARRAY['fun', 'comedy', 'creative']),
('Scavenger Hunt', 'Create or join a city-wide scavenger hunt', 'fun', '🔍', 'medium', 180, 'low', 'outdoor', ARRAY['fun', 'exploration', 'adventure']),
('Dance Class', 'Learn a new dance style together', 'fun', '💃', 'medium', 90, 'medium', 'indoor', ARRAY['fun', 'dancing', 'learning'])
ON CONFLICT DO NOTHING;

-- Outdoor Activities (15 ideas)
INSERT INTO public.date_night_ideas_global (title, description, category, emoji, difficulty, estimated_duration, cost, indoor_outdoor, tags) VALUES
('Farmers Market Visit', 'Browse local produce and artisan goods', 'outdoor', '🥕', 'easy', 120, 'low', 'outdoor', ARRAY['outdoor', 'local', 'food']),
('Botanical Garden Walk', 'Stroll through beautiful gardens and greenhouses', 'outdoor', '🌺', 'easy', 120, 'low', 'outdoor', ARRAY['outdoor', 'nature', 'peaceful']),
('Outdoor Concert', 'Enjoy live music in a park or amphitheater', 'outdoor', '🎵', 'easy', 180, 'medium', 'outdoor', ARRAY['outdoor', 'music', 'live']),
('Food Truck Tour', 'Visit multiple food trucks for a progressive meal', 'outdoor', '🚚', 'easy', 180, 'medium', 'outdoor', ARRAY['outdoor', 'food', 'variety']),
('Outdoor Movie Night', 'Watch a movie under the stars at a drive-in', 'outdoor', '🎬', 'easy', 180, 'medium', 'outdoor', ARRAY['outdoor', 'movies', 'nostalgic']),
('Fishing Trip', 'Spend a peaceful day fishing together', 'outdoor', '🎣', 'easy', 240, 'low', 'outdoor', ARRAY['outdoor', 'peaceful', 'nature']),
('Outdoor Yoga', 'Practice yoga in a park or on the beach', 'outdoor', '🧘', 'easy', 60, 'free', 'outdoor', ARRAY['outdoor', 'wellness', 'nature']),
('Frisbee Golf', 'Play disc golf at a local course', 'outdoor', '🥏', 'easy', 120, 'low', 'outdoor', ARRAY['outdoor', 'sports', 'fun']),
('Nature Photography', 'Capture beautiful nature scenes together', 'outdoor', '📷', 'easy', 180, 'low', 'outdoor', ARRAY['outdoor', 'photography', 'creative']),
('Outdoor Sketching', 'Draw landscapes and nature scenes', 'outdoor', '✏️', 'easy', 120, 'low', 'outdoor', ARRAY['outdoor', 'art', 'peaceful']),
('Bird Watching', 'Observe and identify local bird species', 'outdoor', '🦅', 'easy', 120, 'low', 'outdoor', ARRAY['outdoor', 'nature', 'peaceful']),
('Outdoor Fitness', 'Exercise together in a park or on trails', 'outdoor', '🏃', 'medium', 90, 'free', 'outdoor', ARRAY['outdoor', 'fitness', 'health']),
('Kite Flying', 'Fly colorful kites in an open field', 'outdoor', '🪁', 'easy', 90, 'low', 'outdoor', ARRAY['outdoor', 'playful', 'wind']),
('Outdoor Cooking', 'Grill or cook over a campfire', 'outdoor', '🔥', 'medium', 120, 'medium', 'outdoor', ARRAY['outdoor', 'cooking', 'fire']),
('Sunrise/Sunset Viewing', 'Watch the sunrise or sunset from a scenic spot', 'outdoor', '🌅', 'easy', 60, 'free', 'outdoor', ARRAY['outdoor', 'romantic', 'nature'])
ON CONFLICT DO NOTHING;

-- Relaxing Activities (15 ideas)
INSERT INTO public.date_night_ideas_global (title, description, category, emoji, difficulty, estimated_duration, cost, indoor_outdoor, tags) VALUES
('Couples Spa Day', 'Book a spa day with massages and treatments', 'relaxing', '💆', 'easy', 240, 'high', 'indoor', ARRAY['relaxing', 'pampering', 'luxury']),
('Hot Springs Visit', 'Soak in natural hot springs together', 'relaxing', '♨️', 'easy', 180, 'medium', 'outdoor', ARRAY['relaxing', 'nature', 'therapeutic']),
('Meditation Retreat', 'Attend a couples meditation session', 'relaxing', '🧘', 'easy', 120, 'medium', 'indoor', ARRAY['relaxing', 'mindfulness', 'spiritual']),
('Tea Ceremony', 'Learn about and participate in a traditional tea ceremony', 'relaxing', '🍵', 'easy', 90, 'medium', 'indoor', ARRAY['relaxing', 'cultural', 'mindful']),
('Hammock Time', 'Relax together in a hammock with books or music', 'relaxing', '🏖️', 'easy', 120, 'free', 'outdoor', ARRAY['relaxing', 'peaceful', 'nature']),
('Aromatherapy Session', 'Create a relaxing atmosphere with essential oils', 'relaxing', '🌿', 'easy', 90, 'low', 'indoor', ARRAY['relaxing', 'scents', 'therapeutic']),
('Float Tank Experience', 'Try sensory deprivation floating together', 'relaxing', '🛁', 'easy', 90, 'medium', 'indoor', ARRAY['relaxing', 'unique', 'meditative']),
('Gentle Yoga Class', 'Take a restorative yoga class together', 'relaxing', '🧘', 'easy', 75, 'medium', 'indoor', ARRAY['relaxing', 'yoga', 'gentle']),
('Nature Sound Bath', 'Listen to calming nature sounds together', 'relaxing', '🎵', 'easy', 60, 'free', 'indoor', ARRAY['relaxing', 'sounds', 'meditative']),
('Cozy Reading Time', 'Read books together by a fireplace', 'relaxing', '📚', 'easy', 120, 'free', 'indoor', ARRAY['relaxing', 'reading', 'cozy']),
('Gentle Stretching', 'Do relaxing stretches together', 'relaxing', '🤸', 'easy', 45, 'free', 'indoor', ARRAY['relaxing', 'stretching', 'wellness']),
('Mindful Coloring', 'Color in adult coloring books together', 'relaxing', '🖍️', 'easy', 90, 'low', 'indoor', ARRAY['relaxing', 'creative', 'mindful']),
('Peaceful Garden Time', 'Tend to plants or sit quietly in a garden', 'relaxing', '🌱', 'easy', 90, 'free', 'outdoor', ARRAY['relaxing', 'nature', 'gardening']),
('Quiet Lakeside Time', 'Sit by a lake or pond and enjoy the tranquility', 'relaxing', '🏞️', 'easy', 120, 'free', 'outdoor', ARRAY['relaxing', 'nature', 'water']),
('Breathing Exercises', 'Practice calming breathing techniques together', 'relaxing', '💨', 'easy', 30, 'free', 'indoor', ARRAY['relaxing', 'breathing', 'wellness'])
ON CONFLICT DO NOTHING;

-- Food & Dining Activities (10 ideas)
INSERT INTO public.date_night_ideas_global (title, description, category, emoji, difficulty, estimated_duration, cost, indoor_outdoor, tags) VALUES
('Progressive Dinner', 'Visit multiple restaurants for different courses', 'food', '🍽️', 'easy', 240, 'high', 'outdoor', ARRAY['food', 'restaurants', 'variety']),
('Cooking Competition', 'Compete to create the best dish with mystery ingredients', 'food', '👨‍🍳', 'medium', 120, 'medium', 'indoor', ARRAY['food', 'cooking', 'competitive']),
('Food Festival', 'Explore different cuisines at a food festival', 'food', '🎪', 'easy', 180, 'medium', 'outdoor', ARRAY['food', 'festival', 'variety']),
('Chocolate Making', 'Learn to make artisan chocolates together', 'food', '🍫', 'medium', 150, 'medium', 'indoor', ARRAY['food', 'chocolate', 'sweet']),
('Brewery Tour', 'Tour a local brewery and taste different beers', 'food', '🍺', 'easy', 120, 'medium', 'indoor', ARRAY['food', 'beer', 'learning']),
('Farmers Market Cooking', 'Shop at farmers market then cook with fresh ingredients', 'food', '🥬', 'medium', 180, 'medium', 'both', ARRAY['food', 'fresh', 'local']),
('Sushi Making Class', 'Learn to make sushi rolls together', 'food', '🍣', 'medium', 150, 'medium', 'indoor', ARRAY['food', 'sushi', 'japanese']),
('Ice Cream Making', 'Create custom ice cream flavors at home', 'food', '🍦', 'easy', 90, 'low', 'indoor', ARRAY['food', 'dessert', 'sweet']),
('Cocktail Mixing', 'Learn to make craft cocktails together', 'food', '🍸', 'easy', 90, 'medium', 'indoor', ARRAY['food', 'drinks', 'mixology']),
('Baking Challenge', 'Bake elaborate desserts or breads together', 'food', '🧁', 'medium', 180, 'medium', 'indoor', ARRAY['food', 'baking', 'sweet'])
ON CONFLICT DO NOTHING;
