-- Migration: Performance Optimization Indexes
-- Purpose: Add critical indexes for query performance optimization
-- Date: 2025-09-15

-- Enable required extensions (safe if already enabled)
create extension if not exists "uuid-ossp";
create extension if not exists pgcrypto;

-- Performance indexes for user_events table
-- Used in onboarding progress queries and event tracking
create index if not exists idx_user_events_user_event_created
  on public.user_events (user_id, event_name, created_at desc);

create index if not exists idx_user_events_user_created
  on public.user_events (user_id, created_at desc);

create index if not exists idx_user_events_event_name
  on public.user_events (event_name);

-- Performance indexes for user_favorites table
-- Used in favorites queries across different content types
create index if not exists idx_user_favorites_user_type_created
  on public.user_favorites (user_id, item_type, created_at desc);

create index if not exists idx_user_favorites_user_item_type
  on public.user_favorites (user_id, item_id, item_type);

create index if not exists idx_user_favorites_type_created
  on public.user_favorites (item_type, created_at desc);

-- Performance indexes for timeline_events table
-- Used in timeline display and couple story features
create index if not exists idx_timeline_events_couple_date
  on public.timeline_events (couple_id, event_date desc);

create index if not exists idx_timeline_events_couple_featured
  on public.timeline_events (couple_id, is_featured, event_date desc);

create index if not exists idx_timeline_events_couple_visible
  on public.timeline_events (couple_id, is_visible, event_date desc);

create index if not exists idx_timeline_events_source
  on public.timeline_events (source_type, source_id);

-- Performance indexes for timeline_photos table
-- Used in photo display and timeline features
create index if not exists idx_timeline_photos_event_order
  on public.timeline_photos (timeline_event_id, display_order);

create index if not exists idx_timeline_photos_uploaded_by
  on public.timeline_photos (uploaded_by, created_at desc);

-- Performance indexes for couple_stories table
-- Used in story sync and couple data features
create index if not exists idx_couple_stories_updated
  on public.couple_stories (last_updated desc);

create index if not exists idx_couple_stories_updated_by
  on public.couple_stories (last_updated_by, last_updated desc);

-- Performance indexes for date_night_ideas_global table
-- Used in idea search and filtering
create index if not exists idx_date_night_global_category_week
  on public.date_night_ideas_global (category, week_number);

create index if not exists idx_date_night_global_difficulty_cost
  on public.date_night_ideas_global (difficulty, cost);

create index if not exists idx_date_night_global_indoor_outdoor
  on public.date_night_ideas_global (indoor_outdoor);

create index if not exists idx_date_night_global_source_week
  on public.date_night_ideas_global (source, week_number);

-- Full-text search index for date night ideas
create index if not exists idx_date_night_global_search
  on public.date_night_ideas_global using gin(to_tsvector('english', title || ' ' || description));

-- Performance indexes for date_night_ideas_user table
-- Used in user-specific idea queries
create index if not exists idx_date_night_user_category
  on public.date_night_ideas_user (user_id, category, created_at desc);

create index if not exists idx_date_night_user_created
  on public.date_night_ideas_user (user_id, created_at desc);

-- Performance indexes for meal_ideas_global table
-- Used in meal idea search and filtering
create index if not exists idx_meal_global_category_week
  on public.meal_ideas_global (category, week_number);

create index if not exists idx_meal_global_difficulty
  on public.meal_ideas_global (difficulty);

-- Full-text search index for meal ideas
create index if not exists idx_meal_global_search
  on public.meal_ideas_global using gin(to_tsvector('english', title || ' ' || coalesce(description, '')));

-- Performance indexes for meal_ideas_users table
-- Used in user meal idea queries
create index if not exists idx_meal_users_user_category
  on public.meal_ideas_users (user_id, category, created_at desc);

create index if not exists idx_meal_users_user_completed
  on public.meal_ideas_users (user_id, is_completed, completed_at desc);

create index if not exists idx_meal_users_user_favorite
  on public.meal_ideas_users (user_id, is_favorite, created_at desc);

-- Performance indexes for points_system table
-- Used in points and achievements queries
create index if not exists idx_points_system_level
  on public.points_system (level desc, total_points desc);

create index if not exists idx_points_system_activity
  on public.points_system (last_activity desc);

-- Performance indexes for profiles table
-- Used in profile queries and couple matching
create index if not exists idx_profiles_complete
  on public.profiles (is_complete);

create index if not exists idx_profiles_relationship_date
  on public.profiles (relationship_start_date) where relationship_start_date is not null;

-- Performance indexes for weekly_data table
-- Used in weekly progress tracking
create index if not exists idx_weekly_data_user_week
  on public.weekly_data (user_id, week_number);

create index if not exists idx_weekly_data_user_completed
  on public.weekly_data (user_id, completed_at desc) where completed_at is not null;

-- Performance indexes for origin_story table
-- Used in story data queries
create index if not exists idx_origin_story_couple_updated
  on public.origin_story (couple_id, last_updated desc) where couple_id is not null;

create index if not exists idx_origin_story_user_updated
  on public.origin_story (user_id, last_updated desc);

-- Performance indexes for scrapbook table
-- Used in scrapbook queries
create index if not exists idx_scrapbook_user_updated
  on public.scrapbook (user_id, updated_at desc);

-- Performance indexes for error_logs table
-- Used in error tracking and debugging
create index if not exists idx_error_logs_user_created
  on public.error_logs (user_id, created_at desc) where user_id is not null;

create index if not exists idx_error_logs_severity_created
  on public.error_logs (severity, created_at desc);

create index if not exists idx_error_logs_component_created
  on public.error_logs (component, created_at desc) where component is not null;

-- Performance indexes for milestone_templates table
-- Used in milestone queries
create index if not exists idx_milestone_templates_active_order
  on public.milestone_templates (is_active, display_order) where is_active = true;

create index if not exists idx_milestone_templates_category_order
  on public.milestone_templates (category, display_order);

-- Performance indexes for couple_milestones table
-- Used in milestone progress tracking
create index if not exists idx_couple_milestones_couple_completed
  on public.couple_milestones (couple_id, is_completed, completion_date desc);

create index if not exists idx_couple_milestones_template
  on public.couple_milestones (milestone_template_id);

-- Add comments for documentation
comment on index idx_user_events_user_event_created is 'Optimizes onboarding progress and event tracking queries';
comment on index idx_user_favorites_user_type_created is 'Optimizes user favorites queries by content type';
comment on index idx_timeline_events_couple_date is 'Optimizes timeline display queries';
comment on index idx_date_night_global_search is 'Enables full-text search for date night ideas';
comment on index idx_meal_global_search is 'Enables full-text search for meal ideas';

-- Analyze tables to update statistics after index creation
analyze public.user_events;
analyze public.favorites;
analyze public.timeline_events;
analyze public.timeline_photos;
analyze public.couple_stories;
analyze public.date_night_ideas_global;
analyze public.date_night_ideas_user;
analyze public.meal_ideas_global;
analyze public.meal_ideas_users;
analyze public.points_system;
analyze public.profiles;
analyze public.weekly_data;
analyze public.origin_story;
analyze public.scrapbook;
analyze public.error_logs;
analyze public.milestone_templates;
analyze public.couple_milestones;

-- Grant necessary permissions
grant usage on schema public to authenticated;

-- Log completion
select 'Performance indexes migration completed successfully!' as status;
