-- Create date_night_ideas_global table
CREATE TABLE IF NOT EXISTS public.date_night_ideas_global (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    title TEXT NOT NULL,
    description TEXT,
    category TEXT NOT NULL,
    emoji TEXT,
    difficulty TEXT CHECK (difficulty IN ('easy', 'medium', 'hard')),
    estimated_duration INTEGER, -- in minutes
    cost TEXT CHECK (cost IN ('free', 'low', 'medium', 'high')),
    indoor_outdoor TEXT CHECK (indoor_outdoor IN ('indoor', 'outdoor', 'both')),
    week_number INTEGER,
    tags TEXT[],
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create date_night_ideas_user table
CREATE TABLE IF NOT EXISTS public.date_night_ideas_user (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    idea_id UUID REFERENCES public.date_night_ideas_global(id) ON DELETE SET NULL,
    title TEXT NOT NULL,
    description TEXT,
    category TEXT NOT NULL,
    emoji TEXT,
    difficulty TEXT CHECK (difficulty IN ('easy', 'medium', 'hard')),
    estimated_duration INTEGER, -- in minutes
    cost TEXT CHECK (cost IN ('free', 'low', 'medium', 'high')),
    indoor_outdoor TEXT CHECK (indoor_outdoor IN ('indoor', 'outdoor', 'both')),
    week_number INTEGER,
    tags TEXT[],
    is_favorite BOOLEAN DEFAULT FALSE,
    is_completed BOOLEAN DEFAULT FALSE,
    completed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_date_night_global_category_week 
    ON public.date_night_ideas_global (category, week_number);

CREATE INDEX IF NOT EXISTS idx_date_night_global_difficulty 
    ON public.date_night_ideas_global (difficulty);

CREATE INDEX IF NOT EXISTS idx_date_night_users_user_category 
    ON public.date_night_ideas_user (user_id, category, created_at DESC);

CREATE INDEX IF NOT EXISTS idx_date_night_users_user_completed 
    ON public.date_night_ideas_user (user_id, is_completed, completed_at DESC);

CREATE INDEX IF NOT EXISTS idx_date_night_users_user_favorite 
    ON public.date_night_ideas_user (user_id, is_favorite, created_at DESC);

-- Enable Row Level Security
ALTER TABLE public.date_night_ideas_global ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.date_night_ideas_user ENABLE ROW LEVEL SECURITY;

-- RLS Policies for date_night_ideas_global (read-only for all authenticated users)
CREATE POLICY "Anyone can view global date night ideas" ON public.date_night_ideas_global
    FOR SELECT USING (true);

-- RLS Policies for date_night_ideas_user (users can only access their own data)
CREATE POLICY "Users can view their own date night ideas" ON public.date_night_ideas_user
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own date night ideas" ON public.date_night_ideas_user
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own date night ideas" ON public.date_night_ideas_user
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own date night ideas" ON public.date_night_ideas_user
    FOR DELETE USING (auth.uid() = user_id);

-- Insert sample global date night ideas
INSERT INTO public.date_night_ideas_global (title, description, category, emoji, difficulty, estimated_duration, cost, indoor_outdoor, tags, week_number) VALUES
('Romantic Dinner at Home', 'Cook a special meal together and dine by candlelight', 'romantic', '🕯️', 'easy', 120, 'medium', 'indoor', 
 ARRAY['romantic', 'cooking', 'intimate'], 1),

('Stargazing Adventure', 'Find a quiet spot and watch the stars together', 'outdoor', '⭐', 'easy', 90, 'free', 'outdoor',
 ARRAY['romantic', 'nature', 'peaceful'], 2),

('Game Night Challenge', 'Play board games with fun stakes and prizes', 'fun', '🎲', 'easy', 120, 'low', 'indoor',
 ARRAY['games', 'competitive', 'playful'], 3),

('Hiking & Picnic', 'Explore nature trails and enjoy a packed lunch', 'adventure', '🥾', 'medium', 180, 'low', 'outdoor',
 ARRAY['adventure', 'nature', 'exercise'], 4),

('Movie Marathon Night', 'Pick a theme and watch movies with homemade snacks', 'relaxing', '🍿', 'easy', 240, 'low', 'indoor',
 ARRAY['movies', 'relaxing', 'cozy'], 5),

('Art Gallery Visit', 'Explore local art and discuss your favorites', 'cultural', '🎨', 'easy', 120, 'medium', 'indoor',
 ARRAY['art', 'culture', 'learning'], 6),

('Cooking Class at Home', 'Learn to make a new cuisine together', 'creative', '👨‍🍳', 'medium', 150, 'medium', 'indoor',
 ARRAY['cooking', 'learning', 'creative'], 7),

('Beach Day Adventure', 'Spend the day by the water with activities', 'outdoor', '🏖️', 'easy', 300, 'low', 'outdoor',
 ARRAY['beach', 'sun', 'relaxing'], 8),

('Wine Tasting Evening', 'Sample different wines and learn about them', 'sophisticated', '🍷', 'easy', 120, 'medium', 'indoor',
 ARRAY['wine', 'sophisticated', 'learning'], 9),

('Mini Golf & Ice Cream', 'Play mini golf followed by ice cream treats', 'fun', '⛳', 'easy', 90, 'low', 'outdoor',
 ARRAY['games', 'sweet', 'playful'], 10),

('Spa Night at Home', 'Create a relaxing spa experience together', 'relaxing', '🧖‍♀️', 'easy', 120, 'low', 'indoor',
 ARRAY['relaxing', 'pampering', 'intimate'], 11),

('Photography Walk', 'Explore your city and capture beautiful moments', 'creative', '📸', 'easy', 120, 'free', 'outdoor',
 ARRAY['photography', 'creative', 'exploration'], 12)
ON CONFLICT DO NOTHING;

-- Create triggers for updated_at
CREATE TRIGGER update_date_night_ideas_global_updated_at 
    BEFORE UPDATE ON public.date_night_ideas_global 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_date_night_ideas_user_updated_at 
    BEFORE UPDATE ON public.date_night_ideas_user 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
