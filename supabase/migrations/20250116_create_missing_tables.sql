-- Migration: Create missing tables for activity system and user features
-- This migration adds the tables that the TypeScript code expects to exist

-- Create activity_sessions table
CREATE TABLE IF NOT EXISTS activity_sessions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  activity_id VARCHAR(255) NOT NULL,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  couple_id UUID NOT NULL,
  status VARCHAR(50) NOT NULL DEFAULT 'not_started',
  started_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  completed_at TIMESTAMP WITH TIME ZONE,
  score INTEGER NOT NULL DEFAULT 0,
  points INTEGER NOT NULL DEFAULT 0,
  data JSONB DEFAULT '{}',
  events JSONB DEFAULT '[]',
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Create activity_events table
CREATE TABLE IF NOT EXISTS activity_events (
  id VARCHAR(255) PRIMARY KEY,
  activity_id VARCHAR(255) NOT NULL,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  couple_id UUID NOT NULL,
  event_type VARCHAR(100) NOT NULL,
  data JSONB DEFAULT '{}',
  timestamp TIMESTAMP WITH TIME ZONE NOT NULL,
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Create user_favorites table
CREATE TABLE IF NOT EXISTS user_favorites (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  item_id VARCHAR(255) NOT NULL,
  item_type VARCHAR(100) NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  UNIQUE(user_id, item_id, item_type)
);

-- Create meal_voting table
CREATE TABLE IF NOT EXISTS meal_voting (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  couple_id UUID NOT NULL,
  meal_id VARCHAR(255) NOT NULL,
  vote_type VARCHAR(50) NOT NULL, -- 'like', 'dislike', 'neutral'
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  UNIQUE(user_id, meal_id)
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_activity_sessions_user_id ON activity_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_activity_sessions_couple_id ON activity_sessions(couple_id);
CREATE INDEX IF NOT EXISTS idx_activity_sessions_activity_id ON activity_sessions(activity_id);
CREATE INDEX IF NOT EXISTS idx_activity_sessions_status ON activity_sessions(status);

CREATE INDEX IF NOT EXISTS idx_activity_events_user_id ON activity_events(user_id);
CREATE INDEX IF NOT EXISTS idx_activity_events_couple_id ON activity_events(couple_id);
CREATE INDEX IF NOT EXISTS idx_activity_events_activity_id ON activity_events(activity_id);
CREATE INDEX IF NOT EXISTS idx_activity_events_event_type ON activity_events(event_type);

CREATE INDEX IF NOT EXISTS idx_user_favorites_user_id ON user_favorites(user_id);
CREATE INDEX IF NOT EXISTS idx_user_favorites_item_type ON user_favorites(item_type);

CREATE INDEX IF NOT EXISTS idx_meal_voting_user_id ON meal_voting(user_id);
CREATE INDEX IF NOT EXISTS idx_meal_voting_couple_id ON meal_voting(couple_id);

-- Enable Row Level Security (RLS)
ALTER TABLE activity_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE activity_events ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_favorites ENABLE ROW LEVEL SECURITY;
ALTER TABLE meal_voting ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for activity_sessions
CREATE POLICY "Users can view their own activity sessions" ON activity_sessions
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own activity sessions" ON activity_sessions
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own activity sessions" ON activity_sessions
  FOR UPDATE USING (auth.uid() = user_id);

-- Create RLS policies for activity_events
CREATE POLICY "Users can view their own activity events" ON activity_events
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own activity events" ON activity_events
  FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Create RLS policies for user_favorites
CREATE POLICY "Users can view their own favorites" ON user_favorites
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own favorites" ON user_favorites
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own favorites" ON user_favorites
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own favorites" ON user_favorites
  FOR DELETE USING (auth.uid() = user_id);

-- Create RLS policies for meal_voting
CREATE POLICY "Users can view their own meal votes" ON meal_voting
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own meal votes" ON meal_voting
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own meal votes" ON meal_voting
  FOR UPDATE USING (auth.uid() = user_id);

-- Create updated_at trigger function if it doesn't exist
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at
CREATE TRIGGER update_activity_sessions_updated_at
  BEFORE UPDATE ON activity_sessions
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_favorites_updated_at
  BEFORE UPDATE ON user_favorites
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_meal_voting_updated_at
  BEFORE UPDATE ON meal_voting
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
