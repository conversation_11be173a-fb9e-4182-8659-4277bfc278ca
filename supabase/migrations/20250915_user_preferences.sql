-- Migration: User Preferences System
-- Creates table for storing user preferences including hidden date night ideas
-- Date: 2025-09-15

-- Enable required extensions (safe if already enabled)
create extension if not exists "uuid-ossp";
create extension if not exists pgcrypto;

-- Create user_preferences table for storing various user preferences
create table if not exists public.user_preferences (
  id uuid primary key default gen_random_uuid(),
  user_id uuid not null references auth.users(id) on delete cascade,
  
  -- Date night preferences
  hidden_date_night_ideas text[] not null default '{}', -- Array of composite_ids of hidden ideas
  favorite_categories text[] not null default '{}', -- User's preferred categories
  preferred_difficulty text null check (preferred_difficulty in ('easy', 'medium', 'hard')),
  preferred_cost text null check (preferred_cost in ('free', 'low', 'medium', 'high')),
  preferred_duration_min integer null check (preferred_duration_min > 0),
  preferred_duration_max integer null check (preferred_duration_max > 0),
  
  -- General app preferences
  notification_preferences jsonb not null default '{}',
  privacy_settings jsonb not null default '{}',
  ui_preferences jsonb not null default '{}',
  
  -- <PERSON><PERSON><PERSON>
  created_at timestamptz not null default now(),
  updated_at timestamptz not null default now()
);

-- Create indexes for performance
create index if not exists idx_user_preferences_user_id on public.user_preferences (user_id);
create index if not exists idx_user_preferences_hidden_ideas on public.user_preferences using gin (hidden_date_night_ideas);
create index if not exists idx_user_preferences_categories on public.user_preferences using gin (favorite_categories);

-- Create trigger to update updated_at timestamp
create or replace function update_user_preferences_updated_at()
returns trigger as $$
begin
  new.updated_at = now();
  return new;
end;
$$ language plpgsql;

create trigger user_preferences_updated_at
  before update on public.user_preferences
  for each row
  execute function update_user_preferences_updated_at();

-- Enable Row Level Security
alter table public.user_preferences enable row level security;

-- RLS Policies: Users can only access their own preferences
do $$
begin
  if not exists (
    select 1 from pg_policies
    where schemaname = 'public' and tablename = 'user_preferences'
      and policyname = 'Users can view own preferences'
  ) then
    create policy "Users can view own preferences"
      on public.user_preferences for select
      using (auth.uid() = user_id);
  end if;
end
$$;

do $$
begin
  if not exists (
    select 1 from pg_policies
    where schemaname = 'public' and tablename = 'user_preferences'
      and policyname = 'Users can insert own preferences'
  ) then
    create policy "Users can insert own preferences"
      on public.user_preferences for insert
      with check (auth.uid() = user_id);
  end if;
end
$$;

do $$
begin
  if not exists (
    select 1 from pg_policies
    where schemaname = 'public' and tablename = 'user_preferences'
      and policyname = 'Users can update own preferences'
  ) then
    create policy "Users can update own preferences"
      on public.user_preferences for update
      using (auth.uid() = user_id);
  end if;
end
$$;

-- Helper function to get or create user preferences
create or replace function get_user_preferences(user_uuid uuid)
returns public.user_preferences
language plpgsql
security definer
as $$
declare
  prefs public.user_preferences;
begin
  -- Try to get existing preferences
  select * into prefs
  from public.user_preferences
  where user_id = user_uuid;
  
  -- If no preferences exist, create default ones
  if not found then
    insert into public.user_preferences (user_id)
    values (user_uuid)
    returning * into prefs;
  end if;
  
  return prefs;
end;
$$;

-- Helper function to hide a date night idea
create or replace function hide_date_night_idea(user_uuid uuid, idea_composite_id text)
returns boolean
language plpgsql
security definer
as $$
declare
  prefs_exist boolean;
begin
  -- Check if user preferences exist
  select exists(
    select 1 from public.user_preferences where user_id = user_uuid
  ) into prefs_exist;
  
  -- Create preferences if they don't exist
  if not prefs_exist then
    insert into public.user_preferences (user_id, hidden_date_night_ideas)
    values (user_uuid, array[idea_composite_id]);
  else
    -- Add to hidden ideas if not already hidden
    update public.user_preferences
    set hidden_date_night_ideas = array_append(
      array_remove(hidden_date_night_ideas, idea_composite_id),
      idea_composite_id
    ),
    updated_at = now()
    where user_id = user_uuid;
  end if;
  
  return true;
end;
$$;

-- Helper function to unhide a date night idea
create or replace function unhide_date_night_idea(user_uuid uuid, idea_composite_id text)
returns boolean
language plpgsql
security definer
as $$
begin
  update public.user_preferences
  set hidden_date_night_ideas = array_remove(hidden_date_night_ideas, idea_composite_id),
      updated_at = now()
  where user_id = user_uuid;
  
  return true;
end;
$$;

-- Helper function to check if an idea is hidden
create or replace function is_idea_hidden(user_uuid uuid, idea_composite_id text)
returns boolean
language plpgsql
security definer
as $$
declare
  is_hidden boolean := false;
begin
  select idea_composite_id = any(hidden_date_night_ideas)
  into is_hidden
  from public.user_preferences
  where user_id = user_uuid;
  
  return coalesce(is_hidden, false);
end;
$$;

-- Grant permissions
grant usage on schema public to authenticated;
grant all on public.user_preferences to authenticated;
grant execute on function get_user_preferences(uuid) to authenticated;
grant execute on function hide_date_night_idea(uuid, text) to authenticated;
grant execute on function unhide_date_night_idea(uuid, text) to authenticated;
grant execute on function is_idea_hidden(uuid, text) to authenticated;

-- Add comments for documentation
comment on table public.user_preferences is 'Stores user preferences including hidden date night ideas and app settings';
comment on column public.user_preferences.hidden_date_night_ideas is 'Array of composite_ids for date night ideas the user has hidden';
comment on column public.user_preferences.favorite_categories is 'User preferred date night categories';
comment on column public.user_preferences.notification_preferences is 'JSON object storing notification settings';
comment on column public.user_preferences.privacy_settings is 'JSON object storing privacy settings';
comment on column public.user_preferences.ui_preferences is 'JSON object storing UI customization preferences';
