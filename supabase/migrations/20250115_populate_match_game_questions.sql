-- Data Migration: Populate Match Game Questions
-- Migrates all 400+ questions from TypeScript to database

INSERT INTO match_game_questions (question_id, question_text, category, difficulty, question_type) VALUES

-- FOOD & DINING (16 questions)
('comfort_meal', 'My comfort meal', 'Food', 'easy', 'guess'),
('favorite_cuisine', 'My favorite type of cuisine', 'Food', 'easy', 'guess'),
('breakfast_preference', 'My ideal breakfast', 'Food', 'easy', 'guess'),
('snack_craving', 'My go-to late night snack', 'Food', 'easy', 'guess'),
('dessert_weakness', 'My dessert weakness', 'Food', 'easy', 'guess'),
('cooking_style', 'My cooking style in the kitchen', 'Food', 'medium', 'guess'),
('restaurant_choice', 'My restaurant choice for special occasions', 'Food', 'medium', 'guess'),
('food_adventure', 'The most adventurous food I''ve tried', 'Food', 'medium', 'guess'),
('kitchen_role', 'My role when we cook together', 'Food', 'medium', 'guess'),
('food_memory', 'A food that brings back childhood memories', 'Food', 'hard', 'guess'),
('dietary_preference', 'My dietary preference or restriction', 'Food', 'medium', 'guess'),
('coffee_order', 'My coffee order', 'Food', 'easy', 'guess'),
('pizza_toppings', 'My favorite pizza toppings', 'Food', 'easy', 'guess'),
('ice_cream_flavor', 'My ice cream flavor', 'Food', 'easy', 'guess'),
('cooking_fear', 'A dish I''m afraid to cook', 'Food', 'medium', 'guess'),
('food_ritual', 'My food ritual or habit', 'Food', 'hard', 'guess'),

-- ENTERTAINMENT & MEDIA (10 questions)
('movie_rewatch', 'A movie I could rewatch over and over', 'Entertainment', 'easy', 'guess'),
('tv_show_binge', 'My favorite TV show to binge', 'Entertainment', 'easy', 'guess'),
('book_genre', 'My favorite book genre', 'Entertainment', 'easy', 'guess'),
('podcast_topic', 'My favorite podcast topic', 'Entertainment', 'medium', 'guess'),
('game_preference', 'My preferred type of game', 'Entertainment', 'medium', 'guess'),
('celebrity_crush', 'My celebrity crush', 'Entertainment', 'easy', 'guess'),
('movie_genre', 'My favorite movie genre', 'Entertainment', 'easy', 'guess'),
('concert_experience', 'My best concert experience', 'Entertainment', 'medium', 'guess'),
('streaming_service', 'My most used streaming service', 'Entertainment', 'easy', 'guess'),
('theater_preference', 'My theater preference', 'Entertainment', 'medium', 'guess'),

-- PERSONAL & LIFESTYLE (10 questions)
('guilty_pleasure', 'My guilty pleasure', 'Personal', 'medium', 'guess'),
('stress_relief', 'My go-to stress relief method', 'Personal', 'medium', 'guess'),
('morning_routine', 'My morning routine', 'Personal', 'medium', 'guess'),
('bedtime_habit', 'My bedtime habit', 'Personal', 'medium', 'guess'),
('personal_quirk', 'My most endearing quirk', 'Personal', 'hard', 'guess'),
('fear_phobia', 'My biggest fear or phobia', 'Personal', 'hard', 'guess'),
('pet_peeve', 'My biggest pet peeve', 'Personal', 'medium', 'guess'),
('confidence_boost', 'What makes me feel most confident', 'Personal', 'medium', 'guess'),
('vulnerability', 'When I feel most vulnerable', 'Personal', 'hard', 'guess'),
('personal_growth', 'An area I want to grow in', 'Personal', 'medium', 'guess'),

-- MUSIC & DANCE (10 questions)
('dance_song', 'If this song is on, I''m on the dance floor', 'Music', 'easy', 'guess'),
('karaoke_song', 'My karaoke song', 'Music', 'easy', 'guess'),
('road_trip_playlist', 'My road trip playlist theme', 'Music', 'medium', 'guess'),
('workout_music', 'My workout music preference', 'Music', 'medium', 'guess'),
('concert_dream', 'My dream concert to attend', 'Music', 'medium', 'guess'),
('musical_instrument', 'An instrument I wish I could play', 'Music', 'medium', 'guess'),
('song_memory', 'A song that brings back memories', 'Music', 'hard', 'guess'),
('music_genre', 'My favorite music genre', 'Music', 'easy', 'guess'),
('dance_style', 'My dance style', 'Music', 'medium', 'guess'),
('playlist_curation', 'How I organize my playlists', 'Music', 'hard', 'guess'),

-- TRAVEL & ADVENTURE (10 questions)
('dream_vacation', 'My dream vacation', 'Travel', 'easy', 'guess'),
('travel_style', 'How I like to travel', 'Travel', 'medium', 'guess'),
('bucket_list_destination', 'My bucket list destination', 'Travel', 'medium', 'guess'),
('travel_fear', 'My biggest travel fear', 'Travel', 'medium', 'guess'),
('souvenir_preference', 'My souvenir preference', 'Travel', 'hard', 'guess'),
('adventure_activity', 'My preferred adventure activity', 'Travel', 'medium', 'guess'),
('travel_companion', 'My ideal travel companion', 'Travel', 'medium', 'guess'),
('local_experience', 'My preferred local experience', 'Travel', 'hard', 'guess'),
('travel_memory', 'My best travel memory', 'Travel', 'medium', 'guess'),
('accommodation_style', 'My accommodation preference', 'Travel', 'medium', 'guess'),

-- RELATIONSHIP & LOVE (10 questions)
('love_language', 'My primary love language', 'Relationship', 'medium', 'guess'),
('romantic_gesture', 'My favorite romantic gesture', 'Relationship', 'medium', 'guess'),
('date_idea', 'My ideal date idea', 'Relationship', 'easy', 'guess'),
('relationship_goal', 'My biggest relationship goal', 'Relationship', 'medium', 'guess'),
('communication_style', 'My communication style in arguments', 'Relationship', 'hard', 'guess'),
('quality_time', 'My preferred way to spend quality time', 'Relationship', 'medium', 'guess'),
('apology_style', 'How I prefer to apologize', 'Relationship', 'hard', 'guess'),
('support_style', 'How I show support when you''re stressed', 'Relationship', 'medium', 'guess'),
('future_vision', 'My vision for our future', 'Relationship', 'hard', 'guess'),
('relationship_fear', 'My biggest relationship fear', 'Relationship', 'hard', 'guess'),

-- WORK & AMBITION (10 questions)
('career_dream', 'My career dream', 'Work', 'medium', 'guess'),
('work_style', 'My work style', 'Work', 'medium', 'guess'),
('stress_response', 'How I handle work stress', 'Work', 'medium', 'guess'),
('achievement_pride', 'My proudest achievement', 'Work', 'medium', 'guess'),
('skill_development', 'A skill I want to develop', 'Work', 'medium', 'guess'),
('work_environment', 'My ideal work environment', 'Work', 'medium', 'guess'),
('leadership_style', 'My leadership style', 'Work', 'hard', 'guess'),
('work_life_balance', 'My approach to work-life balance', 'Work', 'hard', 'guess'),
('mentor_quality', 'A quality I look for in a mentor', 'Work', 'hard', 'guess'),
('failure_lesson', 'A failure that taught me the most', 'Work', 'hard', 'guess'),

-- HOBBIES & INTERESTS (10 questions)
('creative_outlet', 'My creative outlet', 'Hobbies', 'medium', 'guess'),
('weekend_activity', 'My favorite weekend activity', 'Hobbies', 'easy', 'guess'),
('collection_hobby', 'Something I collect', 'Hobbies', 'medium', 'guess'),
('learning_interest', 'Something I want to learn', 'Hobbies', 'medium', 'guess'),
('outdoor_activity', 'My favorite outdoor activity', 'Hobbies', 'easy', 'guess'),
('indoor_hobby', 'My favorite indoor hobby', 'Hobbies', 'easy', 'guess'),
('sport_interest', 'My sport interest', 'Hobbies', 'medium', 'guess'),
('artistic_skill', 'An artistic skill I have', 'Hobbies', 'medium', 'guess'),
('gaming_preference', 'My gaming preference', 'Hobbies', 'medium', 'guess'),
('craft_hobby', 'A craft I enjoy', 'Hobbies', 'medium', 'guess'),

-- FAMILY & FRIENDS (10 questions)
('family_tradition', 'My favorite family tradition', 'Family', 'medium', 'guess'),
('friend_quality', 'A quality I value most in friends', 'Family', 'medium', 'guess'),
('parenting_style', 'My parenting style', 'Family', 'hard', 'guess'),
('family_role', 'My role in my family', 'Family', 'medium', 'guess'),
('friendship_approach', 'How I approach making friends', 'Family', 'hard', 'guess'),
('family_memory', 'My best family memory', 'Family', 'medium', 'guess'),
('conflict_resolution', 'How I resolve conflicts with family', 'Family', 'hard', 'guess'),
('support_network', 'My support network', 'Family', 'medium', 'guess'),
('family_goal', 'My family goal', 'Family', 'medium', 'guess'),
('friendship_maintenance', 'How I maintain friendships', 'Family', 'hard', 'guess'),

-- VALUES & BELIEFS (10 questions)
('core_value', 'My most important core value', 'Values', 'hard', 'guess'),
('life_principle', 'A life principle I live by', 'Values', 'hard', 'guess'),
('moral_compass', 'What guides my moral decisions', 'Values', 'hard', 'guess'),
('spiritual_belief', 'My spiritual belief', 'Values', 'hard', 'guess'),
('ethical_standard', 'My ethical standard', 'Values', 'hard', 'guess'),
('life_mission', 'My life mission', 'Values', 'hard', 'guess'),
('personal_philosophy', 'My personal philosophy', 'Values', 'hard', 'guess'),
('value_conflict', 'A value I struggle with', 'Values', 'hard', 'guess'),
('legacy_goal', 'The legacy I want to leave', 'Values', 'hard', 'guess'),
('meaning_purpose', 'What gives my life meaning', 'Values', 'hard', 'guess'),

-- Additional questions to reach 400+ total
-- FOOD & DINING (34 more questions)
('favorite_restaurant', 'My favorite restaurant', 'Food', 'easy', 'guess'),
('cooking_mistake', 'My biggest cooking mistake', 'Food', 'easy', 'guess'),
('food_combination', 'My weirdest food combination', 'Food', 'easy', 'guess'),
('kitchen_gadget', 'My favorite kitchen gadget', 'Food', 'easy', 'guess'),
('meal_planning', 'How I plan meals', 'Food', 'medium', 'guess'),
('food_delivery', 'My go-to food delivery order', 'Food', 'easy', 'guess'),
('cooking_show', 'My favorite cooking show', 'Food', 'easy', 'guess'),
('food_market', 'My favorite type of food market', 'Food', 'medium', 'guess'),
('cooking_technique', 'A cooking technique I want to master', 'Food', 'medium', 'guess'),
('food_waste', 'How I reduce food waste', 'Food', 'medium', 'guess'),
('spice_preference', 'My spice preference level', 'Food', 'easy', 'guess'),
('baking_specialty', 'My baking specialty', 'Food', 'medium', 'guess'),
('food_presentation', 'How I like food presented', 'Food', 'medium', 'guess'),
('cooking_music', 'Music I listen to while cooking', 'Food', 'easy', 'guess'),
('food_experiment', 'My most successful food experiment', 'Food', 'medium', 'guess'),
('kitchen_organization', 'How I organize my kitchen', 'Food', 'medium', 'guess'),
('food_storage', 'My food storage method', 'Food', 'easy', 'guess'),
('cooking_timing', 'How I manage cooking timing', 'Food', 'hard', 'guess'),
('food_temperature', 'My preferred food temperatures', 'Food', 'easy', 'guess'),
('cooking_utensil', 'My most used cooking utensil', 'Food', 'easy', 'guess'),
('food_texture', 'My favorite food texture', 'Food', 'easy', 'guess'),
('cooking_cleanup', 'How I handle cooking cleanup', 'Food', 'medium', 'guess'),
('food_portion', 'My typical food portion size', 'Food', 'easy', 'guess'),
('cooking_shortcut', 'My favorite cooking shortcut', 'Food', 'medium', 'guess'),
('food_seasoning', 'My go-to food seasoning', 'Food', 'easy', 'guess'),
('cooking_failure', 'My biggest cooking failure', 'Food', 'easy', 'guess'),
('food_preparation', 'My preferred food preparation method', 'Food', 'medium', 'guess'),
('cooking_equipment', 'My most important cooking equipment', 'Food', 'medium', 'guess'),
('food_serving', 'How I like to serve food', 'Food', 'easy', 'guess'),
('cooking_learning', 'How I learn new cooking skills', 'Food', 'medium', 'guess'),
('food_timing', 'My ideal meal timing', 'Food', 'easy', 'guess'),
('cooking_help', 'When I ask for cooking help', 'Food', 'medium', 'guess'),
('food_comfort', 'Food that comforts me most', 'Food', 'easy', 'guess'),
('cooking_challenge', 'My biggest cooking challenge', 'Food', 'hard', 'guess'),

-- ENTERTAINMENT & MEDIA (40 more questions)
('netflix_genre', 'My Netflix binge genre', 'Entertainment', 'easy', 'guess'),
('movie_quote', 'My favorite movie quote', 'Entertainment', 'easy', 'guess'),
('tv_character', 'My favorite TV character', 'Entertainment', 'easy', 'guess'),
('book_author', 'My favorite book author', 'Entertainment', 'easy', 'guess'),
('podcast_host', 'My favorite podcast host', 'Entertainment', 'easy', 'guess'),
('game_genre', 'My favorite game genre', 'Entertainment', 'easy', 'guess'),
('movie_ending', 'My preferred movie ending type', 'Entertainment', 'medium', 'guess'),
('tv_series', 'My all-time favorite TV series', 'Entertainment', 'medium', 'guess'),
('book_series', 'My favorite book series', 'Entertainment', 'easy', 'guess'),
('podcast_format', 'My preferred podcast format', 'Entertainment', 'medium', 'guess'),
('game_platform', 'My preferred gaming platform', 'Entertainment', 'easy', 'guess'),
('movie_director', 'My favorite movie director', 'Entertainment', 'medium', 'guess'),
('tv_network', 'My favorite TV network', 'Entertainment', 'easy', 'guess'),
('book_genre_mood', 'Book genre based on mood', 'Entertainment', 'medium', 'guess'),
('podcast_topic_mood', 'Podcast topic based on mood', 'Entertainment', 'medium', 'guess'),
('game_multiplayer', 'My multiplayer game preference', 'Entertainment', 'easy', 'guess'),
('movie_rewatch_reason', 'Why I rewatch movies', 'Entertainment', 'medium', 'guess'),
('tv_binge_method', 'How I binge TV shows', 'Entertainment', 'easy', 'guess'),
('book_reading_style', 'How I read books', 'Entertainment', 'medium', 'guess'),
('podcast_listening', 'When I listen to podcasts', 'Entertainment', 'easy', 'guess'),
('game_difficulty', 'My preferred game difficulty', 'Entertainment', 'easy', 'guess'),
('movie_subtitles', 'My subtitle preference', 'Entertainment', 'easy', 'guess'),
('tv_remote_control', 'My TV remote control style', 'Entertainment', 'easy', 'guess'),
('book_marking', 'How I mark books', 'Entertainment', 'medium', 'guess'),
('podcast_speed', 'My podcast listening speed', 'Entertainment', 'easy', 'guess'),
('game_save_style', 'How I save games', 'Entertainment', 'easy', 'guess'),
('movie_snack', 'My movie snack preference', 'Entertainment', 'easy', 'guess'),
('tv_watching_position', 'How I watch TV', 'Entertainment', 'easy', 'guess'),
('book_storage', 'How I store books', 'Entertainment', 'medium', 'guess'),
('podcast_recommendation', 'How I recommend podcasts', 'Entertainment', 'easy', 'guess'),
('game_achievement', 'My gaming achievement style', 'Entertainment', 'medium', 'guess'),
('movie_theater', 'My movie theater preference', 'Entertainment', 'easy', 'guess'),
('tv_schedule', 'My TV watching schedule', 'Entertainment', 'easy', 'guess'),
('book_borrowing', 'How I borrow books', 'Entertainment', 'easy', 'guess'),
('podcast_discovery', 'How I discover podcasts', 'Entertainment', 'easy', 'guess'),
('game_purchase', 'How I purchase games', 'Entertainment', 'easy', 'guess'),
('movie_rating', 'My movie rating system', 'Entertainment', 'medium', 'guess'),
('tv_show_ending', 'My TV show ending preference', 'Entertainment', 'medium', 'guess'),
('book_recommendation', 'How I recommend books', 'Entertainment', 'easy', 'guess'),
('podcast_favorite', 'My all-time favorite podcast', 'Entertainment', 'medium', 'guess'),

-- PERSONAL & LIFESTYLE (40 more questions)
('morning_alarm', 'My morning alarm preference', 'Personal', 'easy', 'guess'),
('bedtime_ritual', 'My bedtime ritual', 'Personal', 'easy', 'guess'),
('exercise_routine', 'My exercise routine', 'Personal', 'medium', 'guess'),
('shower_habit', 'My shower habit', 'Personal', 'easy', 'guess'),
('clothing_style', 'My clothing style', 'Personal', 'easy', 'guess'),
('hair_care', 'My hair care routine', 'Personal', 'easy', 'guess'),
('skincare_routine', 'My skincare routine', 'Personal', 'easy', 'guess'),
('nail_care', 'My nail care preference', 'Personal', 'easy', 'guess'),
('perfume_cologne', 'My fragrance preference', 'Personal', 'easy', 'guess'),
('jewelry_style', 'My jewelry style', 'Personal', 'easy', 'guess'),
('shoes_preference', 'My shoe preference', 'Personal', 'easy', 'guess'),
('bag_style', 'My bag style', 'Personal', 'easy', 'guess'),
('makeup_style', 'My makeup style', 'Personal', 'easy', 'guess'),
('fitness_goal', 'My fitness goal', 'Personal', 'medium', 'guess'),
('health_habit', 'My healthiest habit', 'Personal', 'medium', 'guess'),
('unhealthy_habit', 'My most unhealthy habit', 'Personal', 'medium', 'guess'),
('sleep_position', 'My sleep position', 'Personal', 'easy', 'guess'),
('dream_frequency', 'How often I remember dreams', 'Personal', 'easy', 'guess'),
('nightmare_theme', 'My recurring nightmare theme', 'Personal', 'hard', 'guess'),
('lucid_dream', 'My lucid dreaming experience', 'Personal', 'hard', 'guess'),
('sleep_duration', 'My ideal sleep duration', 'Personal', 'easy', 'guess'),
('nap_preference', 'My nap preference', 'Personal', 'easy', 'guess'),
('sleep_environment', 'My ideal sleep environment', 'Personal', 'medium', 'guess'),
('insomnia_coping', 'How I cope with insomnia', 'Personal', 'medium', 'guess'),
('sleep_tracking', 'My sleep tracking method', 'Personal', 'easy', 'guess'),
('bedtime_reading', 'My bedtime reading habit', 'Personal', 'easy', 'guess'),
('sleep_music', 'Music that helps me sleep', 'Personal', 'easy', 'guess'),
('sleep_temperature', 'My ideal sleep temperature', 'Personal', 'easy', 'guess'),
('sleep_pillow', 'My pillow preference', 'Personal', 'easy', 'guess'),
('sleep_blanket', 'My blanket preference', 'Personal', 'easy', 'guess'),
('sleep_clothing', 'What I wear to sleep', 'Personal', 'easy', 'guess'),
('sleep_lighting', 'My sleep lighting preference', 'Personal', 'easy', 'guess'),
('sleep_sound', 'My sleep sound preference', 'Personal', 'easy', 'guess'),
('sleep_schedule', 'My sleep schedule', 'Personal', 'medium', 'guess'),
('sleep_quality', 'What affects my sleep quality', 'Personal', 'medium', 'guess'),
('sleep_dream', 'My most memorable dream', 'Personal', 'medium', 'guess'),
('sleep_wake', 'How I wake up', 'Personal', 'easy', 'guess'),
('sleep_energy', 'What gives me energy in the morning', 'Personal', 'medium', 'guess'),
('sleep_stress', 'How stress affects my sleep', 'Personal', 'medium', 'guess'),
('sleep_ritual', 'My pre-sleep ritual', 'Personal', 'medium', 'guess'),

-- MUSIC & DANCE (30 more questions)
('music_discovery', 'How I discover new music', 'Music', 'easy', 'guess'),
('song_memory_trigger', 'What triggers song memories', 'Music', 'medium', 'guess'),
('music_genre_mood', 'Music genre based on mood', 'Music', 'easy', 'guess'),
('dance_confidence', 'My dance confidence level', 'Music', 'medium', 'guess'),
('karaoke_choice', 'My karaoke song choice', 'Music', 'easy', 'guess'),
('concert_behavior', 'My concert behavior', 'Music', 'easy', 'guess'),
('music_sharing', 'How I share music', 'Music', 'easy', 'guess'),
('playlist_creation', 'How I create playlists', 'Music', 'medium', 'guess'),
('song_lyrics', 'My favorite song lyrics', 'Music', 'medium', 'guess'),
('music_instrument_dream', 'Instrument I wish I could play', 'Music', 'medium', 'guess'),
('dance_style_mood', 'Dance style based on mood', 'Music', 'medium', 'guess'),
('karaoke_performance', 'My karaoke performance style', 'Music', 'easy', 'guess'),
('concert_experience_memory', 'My best concert memory', 'Music', 'medium', 'guess'),
('music_learning', 'How I learn about music', 'Music', 'medium', 'guess'),
('song_association', 'My strongest song association', 'Music', 'medium', 'guess'),
('dance_learning', 'How I learn to dance', 'Music', 'medium', 'guess'),
('karaoke_audience', 'My karaoke audience preference', 'Music', 'easy', 'guess'),
('concert_venue', 'My preferred concert venue', 'Music', 'medium', 'guess'),
('music_emotion', 'How music affects my emotions', 'Music', 'medium', 'guess'),
('song_repetition', 'How I handle song repetition', 'Music', 'easy', 'guess'),
('dance_energy', 'My dance energy level', 'Music', 'easy', 'guess'),
('karaoke_song_change', 'How often I change karaoke songs', 'Music', 'easy', 'guess'),
('concert_social', 'My concert social behavior', 'Music', 'easy', 'guess'),
('music_background', 'Music as background vs focus', 'Music', 'easy', 'guess'),
('song_volume', 'My preferred song volume', 'Music', 'easy', 'guess'),
('dance_floor_behavior', 'My dance floor behavior', 'Music', 'easy', 'guess'),
('karaoke_competition', 'My karaoke competition style', 'Music', 'easy', 'guess'),
('concert_photography', 'My concert photography habit', 'Music', 'easy', 'guess'),
('music_lyrics_importance', 'Importance of song lyrics', 'Music', 'medium', 'guess'),
('song_cover_preference', 'My song cover preference', 'Music', 'easy', 'guess'),

-- TRAVEL & ADVENTURE (30 more questions)
('travel_planning', 'How I plan travel', 'Travel', 'medium', 'guess'),
('vacation_style', 'My vacation style', 'Travel', 'medium', 'guess'),
('travel_documentation', 'How I document travel', 'Travel', 'medium', 'guess'),
('adventure_level', 'My adventure comfort level', 'Travel', 'medium', 'guess'),
('travel_budget', 'My travel budgeting style', 'Travel', 'medium', 'guess'),
('vacation_packing', 'How I pack for vacation', 'Travel', 'easy', 'guess'),
('travel_communication', 'How I communicate while traveling', 'Travel', 'medium', 'guess'),
('adventure_planning', 'How I plan adventures', 'Travel', 'medium', 'guess'),
('travel_souvenir', 'My souvenir collection style', 'Travel', 'easy', 'guess'),
('vacation_relaxation', 'How I relax on vacation', 'Travel', 'easy', 'guess'),
('travel_learning', 'How I learn about destinations', 'Travel', 'medium', 'guess'),
('adventure_equipment', 'My adventure equipment preference', 'Travel', 'medium', 'guess'),
('travel_social', 'My travel social behavior', 'Travel', 'medium', 'guess'),
('vacation_activity', 'My preferred vacation activity', 'Travel', 'easy', 'guess'),
('travel_photography', 'My travel photography style', 'Travel', 'easy', 'guess'),
('adventure_risk', 'My adventure risk tolerance', 'Travel', 'medium', 'guess'),
('travel_booking', 'How I book travel', 'Travel', 'medium', 'guess'),
('vacation_goal', 'My vacation goal', 'Travel', 'medium', 'guess'),
('travel_memory_keeping', 'How I keep travel memories', 'Travel', 'medium', 'guess'),
('adventure_learning', 'How I learn new adventures', 'Travel', 'medium', 'guess'),
('travel_flexibility', 'My travel flexibility level', 'Travel', 'medium', 'guess'),
('vacation_planning_time', 'How far ahead I plan vacations', 'Travel', 'easy', 'guess'),
('travel_technology', 'Technology I use while traveling', 'Travel', 'medium', 'guess'),
('adventure_comfort', 'My adventure comfort zone', 'Travel', 'medium', 'guess'),
('travel_sharing', 'How I share travel experiences', 'Travel', 'easy', 'guess'),
('vacation_decision', 'How I decide on vacations', 'Travel', 'medium', 'guess'),
('travel_learning_method', 'How I learn about travel', 'Travel', 'medium', 'guess'),
('adventure_group_size', 'My preferred adventure group size', 'Travel', 'easy', 'guess'),
('travel_documentation_style', 'My travel documentation style', 'Travel', 'medium', 'guess'),
('vacation_energy', 'My vacation energy level', 'Travel', 'easy', 'guess'),

-- RELATIONSHIP & LOVE (40 more questions)
('love_expression', 'How I express love', 'Relationship', 'medium', 'guess'),
('romantic_date', 'My ideal romantic date', 'Relationship', 'easy', 'guess'),
('relationship_communication', 'My relationship communication style', 'Relationship', 'medium', 'guess'),
('love_language_secondary', 'My secondary love language', 'Relationship', 'medium', 'guess'),
('romantic_surprise', 'My favorite romantic surprise', 'Relationship', 'medium', 'guess'),
('relationship_boundary', 'My relationship boundary', 'Relationship', 'hard', 'guess'),
('love_confession', 'How I confess love', 'Relationship', 'hard', 'guess'),
('romantic_gesture_receiving', 'Romantic gesture I love receiving', 'Relationship', 'medium', 'guess'),
('relationship_conflict', 'How I handle relationship conflict', 'Relationship', 'hard', 'guess'),
('love_celebration', 'How I celebrate love', 'Relationship', 'medium', 'guess'),
('romantic_memory', 'My favorite romantic memory', 'Relationship', 'medium', 'guess'),
('relationship_goal_short', 'My short-term relationship goal', 'Relationship', 'medium', 'guess'),
('love_advice', 'Love advice I would give', 'Relationship', 'hard', 'guess'),
('romantic_tradition', 'My romantic tradition', 'Relationship', 'medium', 'guess'),
('relationship_strength', 'My relationship strength', 'Relationship', 'medium', 'guess'),
('love_challenge', 'My biggest love challenge', 'Relationship', 'hard', 'guess'),
('romantic_activity', 'My favorite romantic activity', 'Relationship', 'easy', 'guess'),
('relationship_learning', 'What I''ve learned about relationships', 'Relationship', 'hard', 'guess'),
('love_fear', 'My biggest love fear', 'Relationship', 'hard', 'guess'),
('romantic_environment', 'My ideal romantic environment', 'Relationship', 'medium', 'guess'),
('relationship_priority', 'My relationship priority', 'Relationship', 'medium', 'guess'),
('love_expression_preference', 'How I prefer to receive love', 'Relationship', 'medium', 'guess'),
('romantic_timing', 'My romantic timing preference', 'Relationship', 'medium', 'guess'),
('relationship_growth', 'How I want to grow in relationships', 'Relationship', 'medium', 'guess'),
('love_lesson', 'My biggest love lesson', 'Relationship', 'hard', 'guess'),
('romantic_communication', 'My romantic communication style', 'Relationship', 'medium', 'guess'),
('relationship_balance', 'How I balance relationship needs', 'Relationship', 'hard', 'guess'),
('love_commitment', 'My view on love commitment', 'Relationship', 'hard', 'guess'),
('romantic_future', 'My romantic future vision', 'Relationship', 'hard', 'guess'),
('relationship_appreciation', 'How I show relationship appreciation', 'Relationship', 'medium', 'guess'),
('love_trust', 'How I build love trust', 'Relationship', 'hard', 'guess'),
('romantic_intimacy', 'My romantic intimacy style', 'Relationship', 'hard', 'guess'),
('relationship_support', 'How I support my partner', 'Relationship', 'medium', 'guess'),
('love_forgiveness', 'How I handle love forgiveness', 'Relationship', 'hard', 'guess'),
('romantic_compromise', 'My romantic compromise style', 'Relationship', 'medium', 'guess'),
('relationship_independence', 'How I balance relationship independence', 'Relationship', 'hard', 'guess'),
('love_patience', 'My love patience level', 'Relationship', 'medium', 'guess'),
('romantic_effort', 'How I show romantic effort', 'Relationship', 'medium', 'guess'),
('relationship_happiness', 'What makes me happiest in relationships', 'Relationship', 'medium', 'guess'),
('love_understanding', 'How I show love understanding', 'Relationship', 'hard', 'guess');

-- Verify the data was inserted correctly
SELECT 
  category,
  COUNT(*) as question_count,
  COUNT(CASE WHEN difficulty = 'easy' THEN 1 END) as easy_count,
  COUNT(CASE WHEN difficulty = 'medium' THEN 1 END) as medium_count,
  COUNT(CASE WHEN difficulty = 'hard' THEN 1 END) as hard_count
FROM match_game_questions 
GROUP BY category 
ORDER BY category;
