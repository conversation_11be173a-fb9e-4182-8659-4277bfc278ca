-- Match Game Database Migration
-- Creates tables for persistent match game questions, user answers, sessions, and results

-- Match game questions (migrate from TypeScript)
CREATE TABLE match_game_questions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  question_id TEXT UNIQUE NOT NULL,
  question_text TEXT NOT NULL,
  category TEXT NOT NULL,
  difficulty TEXT CHECK (difficulty IN ('easy', 'medium', 'hard')),
  question_type TEXT CHECK (question_type IN ('guess', 'text', 'list')),
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- User answers to questions
CREATE TABLE match_game_user_answers (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  question_id TEXT REFERENCES match_game_questions(question_id),
  answer_text TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  UNIQUE(user_id, question_id)
);

-- Game sessions between couples
CREATE TABLE match_game_sessions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  couple_id UUID REFERENCES couples(id) ON DELETE CASCADE,
  total_questions INTEGER DEFAULT 0,
  correct_matches INTEGER DEFAULT 0,
  completed BOOLEAN DEFAULT false,
  session_type TEXT CHECK (session_type IN ('weekly', 'custom', 'practice')) DEFAULT 'weekly',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Individual question results within sessions
CREATE TABLE match_game_results (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  session_id UUID REFERENCES match_game_sessions(id) ON DELETE CASCADE,
  question_id TEXT REFERENCES match_game_questions(question_id),
  partner1_user_id UUID REFERENCES auth.users(id),
  partner2_user_id UUID REFERENCES auth.users(id),
  partner1_answer TEXT,
  partner2_answer TEXT,
  partner1_guess TEXT,
  partner2_guess TEXT,
  partner1_correct BOOLEAN,
  partner2_correct BOOLEAN,
  answered_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  UNIQUE(session_id, question_id)
);

-- Indexes for performance
CREATE INDEX idx_match_game_questions_category ON match_game_questions(category);
CREATE INDEX idx_match_game_questions_difficulty ON match_game_questions(difficulty);
CREATE INDEX idx_match_game_questions_active ON match_game_questions(is_active);
CREATE INDEX idx_match_game_user_answers_user_id ON match_game_user_answers(user_id);
CREATE INDEX idx_match_game_user_answers_question_id ON match_game_user_answers(question_id);
CREATE INDEX idx_match_game_sessions_couple_id ON match_game_sessions(couple_id);
CREATE INDEX idx_match_game_sessions_created_at ON match_game_sessions(created_at);
CREATE INDEX idx_match_game_results_session_id ON match_game_results(session_id);
CREATE INDEX idx_match_game_results_question_id ON match_game_results(question_id);

-- Row Level Security (RLS) policies
ALTER TABLE match_game_questions ENABLE ROW LEVEL SECURITY;
ALTER TABLE match_game_user_answers ENABLE ROW LEVEL SECURITY;
ALTER TABLE match_game_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE match_game_results ENABLE ROW LEVEL SECURITY;

-- Questions are readable by all authenticated users
CREATE POLICY "Questions are viewable by authenticated users" ON match_game_questions
  FOR SELECT USING (auth.role() = 'authenticated');

-- User answers are private to the user
CREATE POLICY "Users can view their own answers" ON match_game_user_answers
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own answers" ON match_game_user_answers
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own answers" ON match_game_user_answers
  FOR UPDATE USING (auth.uid() = user_id);

-- Sessions are viewable by couple members
CREATE POLICY "Couple members can view their sessions" ON match_game_sessions
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM couples 
      WHERE couples.id = match_game_sessions.couple_id 
      AND (couples.partner1_id = auth.uid() OR couples.partner2_id = auth.uid())
    )
  );

CREATE POLICY "Couple members can create sessions" ON match_game_sessions
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1 FROM couples 
      WHERE couples.id = match_game_sessions.couple_id 
      AND (couples.partner1_id = auth.uid() OR couples.partner2_id = auth.uid())
    )
  );

CREATE POLICY "Couple members can update their sessions" ON match_game_sessions
  FOR UPDATE USING (
    EXISTS (
      SELECT 1 FROM couples 
      WHERE couples.id = match_game_sessions.couple_id 
      AND (couples.partner1_id = auth.uid() OR couples.partner2_id = auth.uid())
    )
  );

-- Results are viewable by couple members
CREATE POLICY "Couple members can view their results" ON match_game_results
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM match_game_sessions 
      JOIN couples ON couples.id = match_game_sessions.couple_id
      WHERE match_game_sessions.id = match_game_results.session_id 
      AND (couples.partner1_id = auth.uid() OR couples.partner2_id = auth.uid())
    )
  );

CREATE POLICY "Couple members can insert results" ON match_game_results
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1 FROM match_game_sessions 
      JOIN couples ON couples.id = match_game_sessions.couple_id
      WHERE match_game_sessions.id = match_game_results.session_id 
      AND (couples.partner1_id = auth.uid() OR couples.partner2_id = auth.uid())
    )
  );

CREATE POLICY "Couple members can update results" ON match_game_results
  FOR UPDATE USING (
    EXISTS (
      SELECT 1 FROM match_game_sessions 
      JOIN couples ON couples.id = match_game_sessions.couple_id
      WHERE match_game_sessions.id = match_game_results.session_id 
      AND (couples.partner1_id = auth.uid() OR couples.partner2_id = auth.uid())
    )
  );

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Triggers for updated_at
CREATE TRIGGER update_match_game_questions_updated_at BEFORE UPDATE ON match_game_questions FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_match_game_user_answers_updated_at BEFORE UPDATE ON match_game_user_answers FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_match_game_sessions_updated_at BEFORE UPDATE ON match_game_sessions FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Function to automatically update session stats when results are added
CREATE OR REPLACE FUNCTION update_session_stats()
RETURNS TRIGGER AS $$
BEGIN
  -- Update session statistics
  UPDATE match_game_sessions 
  SET 
    total_questions = (
      SELECT COUNT(*) FROM match_game_results 
      WHERE session_id = NEW.session_id
    ),
    correct_matches = (
      SELECT COUNT(*) FROM match_game_results 
      WHERE session_id = NEW.session_id 
      AND (partner1_correct = true OR partner2_correct = true)
    ),
    updated_at = now()
  WHERE id = NEW.session_id;
  
  RETURN NEW;
END;
$$ language 'plpgsql';

-- Trigger to update session stats when results are modified
CREATE TRIGGER update_session_stats_trigger 
  AFTER INSERT OR UPDATE OR DELETE ON match_game_results 
  FOR EACH ROW EXECUTE FUNCTION update_session_stats();

-- Function to get random questions with filtering
CREATE OR REPLACE FUNCTION get_random_match_game_questions(
  question_count INTEGER DEFAULT 10,
  category_filter TEXT DEFAULT NULL,
  difficulty_filter TEXT DEFAULT NULL,
  exclude_answered_by_user UUID DEFAULT NULL
)
RETURNS TABLE (
  id UUID,
  question_id TEXT,
  question_text TEXT,
  category TEXT,
  difficulty TEXT,
  question_type TEXT
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    q.id,
    q.question_id,
    q.question_text,
    q.category,
    q.difficulty,
    q.question_type
  FROM match_game_questions q
  WHERE q.is_active = true
    AND (category_filter IS NULL OR q.category = category_filter)
    AND (difficulty_filter IS NULL OR q.difficulty = difficulty_filter)
    AND (exclude_answered_by_user IS NULL OR q.question_id NOT IN (
      SELECT ua.question_id 
      FROM match_game_user_answers ua 
      WHERE ua.user_id = exclude_answered_by_user
    ))
  ORDER BY RANDOM()
  LIMIT question_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get balanced questions (40% easy, 40% medium, 20% hard)
CREATE OR REPLACE FUNCTION get_balanced_match_game_questions(
  question_count INTEGER DEFAULT 10,
  category_filter TEXT DEFAULT NULL,
  exclude_answered_by_user UUID DEFAULT NULL
)
RETURNS TABLE (
  id UUID,
  question_id TEXT,
  question_text TEXT,
  category TEXT,
  difficulty TEXT,
  question_type TEXT
) AS $$
DECLARE
  easy_count INTEGER;
  medium_count INTEGER;
  hard_count INTEGER;
BEGIN
  easy_count := CEIL(question_count * 0.4);
  medium_count := CEIL(question_count * 0.4);
  hard_count := question_count - easy_count - medium_count;
  
  RETURN QUERY
  (
    -- Easy questions
    SELECT * FROM get_random_match_game_questions(
      easy_count, 
      category_filter, 
      'easy', 
      exclude_answered_by_user
    )
  )
  UNION ALL
  (
    -- Medium questions
    SELECT * FROM get_random_match_game_questions(
      medium_count, 
      category_filter, 
      'medium', 
      exclude_answered_by_user
    )
  )
  UNION ALL
  (
    -- Hard questions
    SELECT * FROM get_random_match_game_questions(
      hard_count, 
      category_filter, 
      'hard', 
      exclude_answered_by_user
    )
  )
  ORDER BY RANDOM();
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
