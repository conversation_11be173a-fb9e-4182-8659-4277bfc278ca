const { getDefaultConfig } = require('expo/metro-config');

const config = getDefaultConfig(__dirname);

// Disable SSR for web to avoid AsyncStorage issues
config.transformer.getTransformOptions = async () => ({
  transform: {
    experimentalImportSupport: false,
    inlineRequires: true,
  },
});

// Add platform-specific extensions for web
config.resolver.platforms = ['web', 'ios', 'android', 'native'];

module.exports = config;
