/**
 * Performance Optimization Test Suite
 * Comprehensive tests for enterprise-grade performance features
 */

import { cacheManager } from '../../src/services/cacheManager';
import { performanceMonitor } from '../../src/services/performanceMonitor';
import { performanceOptimizationService } from '../../src/services/performanceOptimizationService';
import { subscriptionManager } from '../../src/services/subscriptionManager';

// Mock Supabase
jest.mock('../../lib/supabase/client', () => ({
  supabase: {
    from: jest.fn(() => ({
      insert: jest.fn().mockResolvedValue({ data: [], error: null }),
      update: jest.fn(() => ({
        eq: jest.fn().mockResolvedValue({ data: [], error: null })
      })),
      delete: jest.fn(() => ({
        in: jest.fn().mockResolvedValue({ data: [], error: null })
      })),
      select: jest.fn(() => ({
        in: jest.fn(() => ({
          limit: jest.fn().mockResolvedValue({ data: [], error: null })
        })),
        order: jest.fn(() => ({
          limit: jest.fn().mockResolvedValue({ data: [], error: null })
        })),
        eq: jest.fn(() => ({
          order: jest.fn(() => ({
            limit: jest.fn().mockResolvedValue({ data: [], error: null })
          }))
        })),
        gt: jest.fn(() => ({
          order: jest.fn(() => ({
            limit: jest.fn().mockResolvedValue({ data: [], error: null })
          }))
        })),
        lt: jest.fn(() => ({
          order: jest.fn(() => ({
            limit: jest.fn().mockResolvedValue({ data: [], error: null })
          }))
        }))
      }))
    })),
    channel: jest.fn(() => ({
      on: jest.fn().mockReturnThis(),
      subscribe: jest.fn((callback: any) => {
        callback('SUBSCRIBED');
        return Promise.resolve();
      }),
      unsubscribe: jest.fn().mockResolvedValue(undefined)
    })),
    realtime: {
      onOpen: jest.fn(),
      onClose: jest.fn(),
      onError: jest.fn()
    }
  }
}));

describe('Performance Optimization Service', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Batch Operations', () => {
    test('should execute batch insert operations efficiently', async () => {
      const testData = Array.from({ length: 250 }, (_, i) => ({
        id: `test-${i}`,
        name: `Test Item ${i}`,
        value: i
      }));

      const result = await performanceOptimizationService.executeBatchOperation({
        operation: 'insert',
        table: 'test_table',
        data: testData,
        batchSize: 100
      });

      expect(result).toBe(true);

      // Should have made 3 batch calls (250 items / 100 batch size = 3 batches)
      const metrics = performanceOptimizationService.getPerformanceMetrics();
      expect(metrics.totalQueries).toBeGreaterThan(0);
    });

    test('should handle batch operation failures gracefully', async () => {
      // Mock a failure
      const mockSupabase = require('../../lib/supabase/client').supabase;
      mockSupabase.from.mockReturnValue({
        insert: jest.fn().mockRejectedValue(new Error('Database error'))
      });

      const testData = [{ id: 'test-1', name: 'Test' }];

      const result = await performanceOptimizationService.executeBatchOperation({
        operation: 'insert',
        table: 'test_table',
        data: testData
      });

      expect(result).toBe(false);
    });

    test('should optimize batch sizes for large datasets', async () => {
      const largeDataset = Array.from({ length: 1000 }, (_, i) => ({
        id: `large-${i}`,
        data: `Large dataset item ${i}`
      }));

      const startTime = Date.now();

      const result = await performanceOptimizationService.executeBatchOperation({
        operation: 'insert',
        table: 'large_table',
        data: largeDataset,
        batchSize: 50 // Smaller batches for large datasets
      });

      const duration = Date.now() - startTime;

      expect(result).toBe(true);
      expect(duration).toBeLessThan(5000); // Should complete within 5 seconds
    });
  });

  describe('Related Data Fetching', () => {
    test('should fetch related data efficiently to prevent N+1 queries', async () => {
      const mainIds = ['id1', 'id2', 'id3'];
      const relations = [
        { table: 'related_table_1', foreignKey: 'main_id', select: '*' },
        { table: 'related_table_2', foreignKey: 'main_id', select: 'id, name', limit: 10 }
      ];

      const result = await performanceOptimizationService.fetchRelatedData(
        'main_table',
        ['related_table_1', 'related_table_2']
      );

      expect(result.success).toBe(true);
      expect(result.entityId).toBe('main_table');
      expect(result.relations).toHaveProperty('related_table_1');
      expect(result.relations).toHaveProperty('related_table_2');
    });

    test('should validate UUIDs before fetching related data', async () => {
      const invalidIds = ['invalid-id', 'not-a-uuid'];
      const relations = [
        { table: 'test_table', foreignKey: 'main_id' }
      ];

      await expect(
        performanceOptimizationService.fetchRelatedData('main_table', ['related_table_1'])
      ).rejects.toThrow('No valid IDs provided');
    });
  });

  describe('Optimized Pagination', () => {
    test('should implement cursor-based pagination for large datasets', async () => {
      const result = await performanceOptimizationService.paginateOptimized({
        table: 'test_table',
        page: 1,
        pageSize: 20,
        orderBy: 'created_at'
      });

      expect(result).toHaveProperty('data');
      expect(result).toHaveProperty('hasMore');
      expect(result.data).toBeInstanceOf(Array);
    });

    test('should handle pagination with filters', async () => {
      const result = await performanceOptimizationService.paginateOptimized({
        table: 'test_table',
        page: 1,
        pageSize: 50,
        filters: { status: 'active', category: 'test' },
        orderBy: 'updated_at'
      });

      expect(result).toHaveProperty('data');
      expect(result).toHaveProperty('hasMore');
    });

    test('should provide next cursor for pagination', async () => {
      // Mock data with more items than limit
      const mockData = Array.from({ length: 21 }, (_, i) => ({
        id: `item-${i}`,
        created_at: new Date(Date.now() - i * 1000).toISOString()
      }));

      const mockSupabase = require('../../lib/supabase/client').supabase;
      mockSupabase.from.mockReturnValue({
        select: jest.fn().mockReturnThis(),
        order: jest.fn().mockReturnThis(),
        limit: jest.fn().mockResolvedValue({ data: mockData, error: null })
      });

      const result = await performanceOptimizationService.paginateOptimized({
        table: 'test_table',
        page: 1,
        pageSize: 20
      });

      expect(result.hasMore).toBe(true);
      expect(result.nextCursor).toBeDefined();
      expect(result.data).toHaveLength(20);
    });
  });

  describe('Performance Metrics', () => {
    test('should record and retrieve performance metrics', () => {
      // Execute some operations to generate metrics
      performanceOptimizationService.getPerformanceMetrics();

      const metrics = performanceOptimizationService.getPerformanceMetrics();

      expect(metrics).toHaveProperty('averageQueryTime');
      expect(metrics).toHaveProperty('slowQueries');
      expect(metrics).toHaveProperty('totalQueries');
      expect(metrics.averageQueryTime).toBeGreaterThanOrEqual(0);
    });

    test('should identify slow queries', async () => {
      // Simulate a slow operation
      const slowOperation = new Promise(resolve => setTimeout(resolve, 1500));
      await slowOperation;

      const metrics = performanceOptimizationService.getPerformanceMetrics();

      // Check if slow queries are being tracked
      expect(metrics).toHaveProperty('slowQueries');
      expect(Array.isArray(metrics.slowQueries)).toBe(true);
    });

    test('should clean up old metrics to prevent memory leaks', () => {
      // Generate some metrics
      for (let i = 0; i < 100; i++) {
        performanceOptimizationService.getPerformanceMetrics();
      }

      performanceOptimizationService.clearMetrics();

      const metrics = performanceOptimizationService.getPerformanceMetrics();
      expect(metrics.totalQueries).toBeLessThan(100);
    });
  });
});

describe('Cache Manager', () => {
  beforeEach(() => {
    cacheManager.clear();
  });

  describe('Basic Cache Operations', () => {
    test('should store and retrieve cached data', async () => {
      const testData = { id: 1, name: 'Test Data' };
      const cacheKey = 'test-key';

      await cacheManager.set(cacheKey, testData);
      const retrieved = await cacheManager.get(cacheKey);

      expect(retrieved).toEqual(testData);
    });

    test('should respect TTL and expire cached data', async () => {
      const testData = { id: 1, name: 'Test Data' };
      const cacheKey = 'test-ttl-key';

      await cacheManager.set(cacheKey, testData, { ttl: 100 }); // 100ms TTL

      // Should be available immediately
      let retrieved = await cacheManager.get(cacheKey);
      expect(retrieved).toEqual(testData);

      // Wait for expiration
      await new Promise(resolve => setTimeout(resolve, 150));

      // Should be expired
      retrieved = await cacheManager.get(cacheKey);
      expect(retrieved).toBeNull();
    });

    test('should handle cache invalidation by tags', async () => {
      await cacheManager.set('key1', { data: 'test1' }, { tags: ['user:123', 'posts'] });
      await cacheManager.set('key2', { data: 'test2' }, { tags: ['user:123', 'comments'] });
      await cacheManager.set('key3', { data: 'test3' }, { tags: ['user:456', 'posts'] });

      // Invalidate by tag
      const invalidated = cacheManager.invalidate('user:123', true);
      expect(invalidated).toBe(2);

      // Check that tagged items are gone
      expect(await cacheManager.get('key1')).toBeNull();
      expect(await cacheManager.get('key2')).toBeNull();
      expect(await cacheManager.get('key3')).not.toBeNull();
    });
  });

  describe('Advanced Cache Features', () => {
    test('should compress large cache entries', async () => {
      const largeData = {
        content: 'x'.repeat(20000), // 20KB of data
        metadata: { size: 'large', compressed: true }
      };

      await cacheManager.set('large-key', largeData, { compress: true });
      const retrieved = await cacheManager.get('large-key');

      expect(retrieved).toEqual(largeData);
    });

    test('should implement cache warming strategy', async () => {
      const keys = ['warm1', 'warm2', 'warm3'];
      const dataLoader = jest.fn().mockImplementation((key: any) =>
        Promise.resolve({ key, data: `warmed-${key}` })
      );

      await cacheManager.warmCache({
        keys,
        dataLoader,
        priority: 'high'
      });

      // Check that all keys were warmed
      for (const key of keys) {
        const cached = await cacheManager.get(key);
        expect(cached).toEqual({ key, data: `warmed-${key}` });
      }

      expect(dataLoader).toHaveBeenCalledTimes(keys.length);
    });

    test('should provide accurate cache metrics', async () => {
      // Generate some cache activity
      await cacheManager.set('metric1', { data: 'test1' });
      await cacheManager.set('metric2', { data: 'test2' });
      await cacheManager.get('metric1'); // Hit
      await cacheManager.get('nonexistent'); // Miss

      const metrics = cacheManager.getMetrics();

      expect(metrics.totalEntries).toBe(2);
      expect(metrics.totalHits).toBeGreaterThan(0);
      expect(metrics.totalMisses).toBeGreaterThan(0);
      expect(metrics.hitRate).toBeGreaterThan(0);
    });
  });

  describe('Memory Management', () => {
    test('should evict entries when cache is full', async () => {
      // Create a cache manager with small limits for testing
      const testCache = require('../../services/cacheManager').CacheManager.getInstance({
        maxEntries: 3,
        maxSize: 1 // 1MB
      });

      // Fill cache beyond limit
      await testCache.set('key1', { data: 'test1' });
      await testCache.set('key2', { data: 'test2' });
      await testCache.set('key3', { data: 'test3' });
      await testCache.set('key4', { data: 'test4' }); // Should trigger eviction

      const metrics = testCache.getMetrics();
      expect(metrics.evictionCount).toBeGreaterThan(0);
    });
  });
});

describe('Subscription Manager', () => {
  describe('Subscription Lifecycle', () => {
    test('should create and manage subscriptions', () => {
      const callback = jest.fn();
      const subscriptionId = subscriptionManager.subscribe({
        table: 'test_table',
        event: 'INSERT',
        callback
      });

      expect(subscriptionId).toBeDefined();
      expect(typeof subscriptionId).toBe('string');

      // Clean up
      subscriptionManager.unsubscribe(subscriptionId);
    });

    test('should handle subscription errors gracefully', () => {
      const callback = jest.fn();
      const errorHandler = jest.fn();

      const subscriptionId = subscriptionManager.subscribe({
        table: 'test_table',
        event: 'UPDATE',
        callback,
        onError: errorHandler
      });

      expect(subscriptionId).toBeDefined();

      // Clean up
      subscriptionManager.unsubscribe(subscriptionId);
    });

    test('should provide subscription health metrics', () => {
      const callback = jest.fn();

      // Create multiple subscriptions
      const sub1 = subscriptionManager.subscribe({ table: 'table1', callback });
      const sub2 = subscriptionManager.subscribe({ table: 'table2', callback });

      const health = subscriptionManager.getHealthMetrics();

      expect(health.totalSubscriptions).toBe(2);
      expect(health).toHaveProperty('activeSubscriptions');
      expect(health).toHaveProperty('connectionState');

      // Clean up
      subscriptionManager.unsubscribe(sub1);
      subscriptionManager.unsubscribe(sub2);
    });
  });

  describe('Memory Leak Prevention', () => {
    test('should clean up inactive subscriptions', () => {
      const callback = jest.fn();

      // Create subscription
      const subscriptionId = subscriptionManager.subscribe({
        table: 'test_table',
        callback
      });

      // Verify it exists
      const initialMetrics = subscriptionManager.getSubscriptionMetrics();
      expect(initialMetrics.length).toBeGreaterThan(0);

      // Unsubscribe
      const success = subscriptionManager.unsubscribe(subscriptionId);
      expect(success).toBe(true);

      // Verify cleanup
      const finalMetrics = subscriptionManager.getSubscriptionMetrics();
      expect(finalMetrics.length).toBe(initialMetrics.length - 1);
    });
  });
});

describe('Performance Monitor', () => {
  describe('Metric Recording', () => {
    test('should record performance metrics', () => {
      performanceMonitor.recordMetric('test.metric', 100);

      const summary = performanceMonitor.getPerformanceSummary();
      expect(summary.metrics.length).toBeGreaterThan(0);
    });

    test('should track component performance', () => {
      const tracker = performanceMonitor.trackComponent('TestComponent');

      tracker.startRender();
      // Simulate some work
      for (let i = 0; i < 1000; i++) {
        Math.random();
      }
      tracker.endRender();

      const summary = performanceMonitor.getPerformanceSummary();
      expect(summary.components.length).toBeGreaterThan(0);

      const testComponent = summary.components.find(c => c.componentName === 'TestComponent');
      expect(testComponent).toBeDefined();
      expect(testComponent?.renderTime).toBeGreaterThan(0);
    });

    test('should track network request performance', async () => {
      const tracker = performanceMonitor.trackNetworkRequest('/api/test');

      tracker.start();
      // Simulate network delay
      await new Promise(resolve => setTimeout(resolve, 100));
      tracker.end(true, 1024);

      const summary = performanceMonitor.getPerformanceSummary();
      expect(summary.network.requestCount).toBeGreaterThan(0);
    });
  });

  describe('Performance Insights', () => {
    test('should generate performance insights and recommendations', () => {
      // Generate some metrics to analyze
      performanceMonitor.recordMetric('slow.query', 2000);
      performanceMonitor.recordMetric('cache.miss', 1);

      const insights = performanceMonitor.getPerformanceInsights();

      expect(insights).toHaveProperty('insights');
      expect(insights).toHaveProperty('recommendations');
      expect(insights).toHaveProperty('score');
      expect(Array.isArray(insights.insights)).toBe(true);
      expect(Array.isArray(insights.recommendations)).toBe(true);
      expect(typeof insights.score).toBe('number');
    });

    test('should export performance data for analysis', () => {
      const exportData = performanceMonitor.exportPerformanceData();

      expect(typeof exportData).toBe('object');
      expect(exportData).toHaveProperty('timestamp');
      expect(exportData).toHaveProperty('summary');
      expect(exportData).toHaveProperty('insights');
    });
  });
});

describe('Integration Tests', () => {
  test('should work together seamlessly', async () => {
    // Test the integration of all performance services
    const testData = { id: 'integration-test', data: 'test-data' };

    // Cache some data
    await cacheManager.set('integration-key', testData);

    // Record some metrics
    performanceMonitor.recordMetric('integration.test', 50);

    // Create a subscription
    const subscriptionId = subscriptionManager.subscribe({
      table: 'integration_table',
      callback: () => {}
    });

    // Get comprehensive metrics
    const cacheMetrics = cacheManager.getMetrics();
    const subscriptionHealth = subscriptionManager.getHealthMetrics();
    const performanceSummary = performanceMonitor.getPerformanceSummary();

    expect(cacheMetrics.totalEntries).toBeGreaterThan(0);
    expect(subscriptionHealth.totalSubscriptions).toBeGreaterThan(0);
    expect(performanceSummary.metrics.length).toBeGreaterThan(0);

    // Clean up
    subscriptionManager.unsubscribe(subscriptionId);
    cacheManager.clear();
  });
});
