/**
 * Integration Testing for Production Readiness
 * Tests integration with actual Supabase backend and production scenarios
 */

import { cacheManager } from '../../src/services/cacheManager';
import { performanceMonitor } from '../../src/services/performanceMonitor';
import { performanceOptimizationService } from '../../src/services/performanceOptimizationService';
import { subscriptionManager } from '../../src/services/subscriptionManager';

// Use real Supabase client for integration tests
jest.unmock('../../lib/supabase/client');

describe('Production Integration Testing', () => {
  beforeAll(() => {
    // Start performance monitoring for integration tests
    performanceMonitor.startMonitoring();
  });

  afterAll(() => {
    performanceMonitor.stopMonitoring();
  });

  beforeEach(() => {
    cacheManager.clear();
  });

  describe('Real Database Integration', () => {
    test('should handle real database operations gracefully', async () => {
      const testData = [{
        id: `integration-test-${Date.now()}`,
        name: 'Integration Test Item',
        created_at: new Date().toISOString()
      }];

      // This will use the real Supabase client
      const result = await performanceOptimizationService.executeBatchOperation({
        operation: 'insert',
        table: 'test_integration', // Assuming this table exists or will gracefully fail
        data: testData,
        batchSize: 1
      });

      // Should handle both success and failure gracefully
      expect(result).toHaveProperty('success');
      expect(result).toHaveProperty('processedCount');
      expect(result).toHaveProperty('errors');
      expect(result).toHaveProperty('duration');

      if (!result.success) {
        // If table doesn't exist, should have meaningful error
        expect(result.errors.length).toBeGreaterThan(0);
        expect(result.errors[0]).toHaveProperty('code');
        expect(result.errors[0]).toHaveProperty('message');
      }
    });

    test('should handle network timeouts gracefully', async () => {
      const startTime = Date.now();

      // Create a large dataset that might timeout
      const largeDataset = Array.from({ length: 1000 }, (_, i) => ({
        id: `timeout-test-${i}`,
        data: 'x'.repeat(10000), // Large data to potentially cause timeout
        timestamp: Date.now()
      }));

      try {
        const result = await performanceOptimizationService.executeBatchOperation({
          operation: 'insert',
          table: 'timeout_test',
          data: largeDataset,
          batchSize: 100
        });

        const duration = Date.now() - startTime;

        // Should complete within reasonable time or fail gracefully
        expect(duration).toBeLessThan(30000); // 30 seconds max
        expect(result).toHaveProperty('success');

      } catch (error) {
        // Should handle timeouts gracefully
        expect(error).toBeInstanceOf(Error);
        const duration = Date.now() - startTime;
        expect(duration).toBeLessThan(35000); // Should timeout within 35 seconds
      }
    });
  });

  describe('Real-time Subscription Integration', () => {
    test('should handle real subscription lifecycle', async () => {
      const callbackMock = jest.fn();
      const errorMock = jest.fn();

      const subscriptionId = subscriptionManager.subscribe({
        table: 'user_events', // Assuming this table exists
        event: 'INSERT',
        callback: callbackMock,
        onError: errorMock,
        autoReconnect: true
      });

      expect(subscriptionId).toBeTruthy();

      // Wait for subscription to initialize
      await new Promise(resolve => setTimeout(resolve, 1000));

      const healthMetrics = subscriptionManager.getHealthMetrics();
      expect(healthMetrics.totalSubscriptions).toBeGreaterThan(0);

      // Cleanup
      subscriptionManager.unsubscribe(subscriptionId);

      // Wait for cleanup
      await new Promise(resolve => setTimeout(resolve, 500));

      const finalMetrics = subscriptionManager.getHealthMetrics();
      expect(finalMetrics.totalSubscriptions).toBe(healthMetrics.totalSubscriptions - 1);
    });

    test('should handle subscription errors gracefully', async () => {
      const errorMock = jest.fn();

      // Try to subscribe to non-existent table
      const subscriptionId = subscriptionManager.subscribe({
        table: 'non_existent_table_12345',
        event: 'INSERT',
        callback: jest.fn(),
        onError: errorMock,
        autoReconnect: false
      });

      expect(subscriptionId).toBeTruthy();

      // Wait for potential error
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Should handle gracefully without crashing
      const healthMetrics = subscriptionManager.getHealthMetrics();
      expect(healthMetrics).toHaveProperty('totalSubscriptions');

      // Cleanup
      subscriptionManager.unsubscribe(subscriptionId);
    });
  });

  describe('Cache Persistence Integration', () => {
    test('should persist cache data correctly', async () => {
      const testKey = 'integration-cache-test';
      const testData = {
        message: 'Integration test data',
        timestamp: Date.now(),
        complex: {
          nested: {
            data: [1, 2, 3, 4, 5]
          }
        }
      };

      // Set data with persistence
      await cacheManager.set(testKey, testData, {
        ttl: 60000,
        tags: ['integration', 'test']
      });

      // Retrieve data
      const retrieved = await cacheManager.get(testKey);
      expect(retrieved).toEqual(testData);

      // Check metrics
      const metrics = cacheManager.getMetrics();
      expect(metrics.totalEntries).toBeGreaterThan(0);
      expect(metrics.hitRate).toBeGreaterThanOrEqual(0);
    });

    test('should handle cache compression correctly', async () => {
      const largeData = {
        content: 'x'.repeat(50000), // 50KB of data
        metadata: {
          size: 50000,
          compressed: true,
          timestamp: Date.now()
        }
      };

      const testKey = 'compression-test';

      await cacheManager.set(testKey, largeData, {
        ttl: 30000,
        compress: true
      });

      const retrieved = await cacheManager.get(testKey);
      expect(retrieved).toEqual(largeData);

      const metrics = cacheManager.getMetrics();
      expect(metrics.compressionRatio).toBeGreaterThanOrEqual(0);
    });
  });

  describe('Performance Monitoring Integration', () => {
    test('should collect real performance metrics', async () => {
      const componentTracker = performanceMonitor.trackComponent('IntegrationTestComponent');

      // Simulate component lifecycle
      componentTracker.startRender();
      await new Promise(resolve => setTimeout(resolve, 50)); // Simulate render time
      componentTracker.endRender();

      componentTracker.recordMount(25);
      componentTracker.recordUpdate();

      // Record some network metrics
      const networkTracker = performanceMonitor.trackNetworkRequest('/api/integration-test');
      networkTracker.start();
      await new Promise(resolve => setTimeout(resolve, 100)); // Simulate network delay
      networkTracker.end(true, 1024);

      // Get performance insights
      const insights = performanceMonitor.getPerformanceInsights();
      expect(insights).toHaveProperty('insights');
      expect(insights).toHaveProperty('recommendations');
      expect(insights).toHaveProperty('score');
      expect(insights.score).toBeGreaterThanOrEqual(0);
      expect(insights.score).toBeLessThanOrEqual(100);
    });

    test('should generate meaningful performance reports', async () => {
      // Generate some performance data
      for (let i = 0; i < 10; i++) {
        performanceMonitor.recordMetric(`integration-metric-${i}`, Math.random() * 100);
      }

      const summary = performanceMonitor.getPerformanceSummary();
      expect(summary).toHaveProperty('totalMetrics');
      expect(summary).toHaveProperty('averageResponseTime');
      expect(summary).toHaveProperty('slowQueries');

      const exportData = performanceMonitor.exportPerformanceData();
      expect(exportData).toBeTruthy();
      expect(typeof exportData).toBe('object');

      // Should have required properties
      expect(exportData).toHaveProperty('timestamp');
      expect(exportData).toHaveProperty('summary');
      expect(exportData).toHaveProperty('insights');
    });
  });

  describe('Memory Management Integration', () => {
    test('should manage memory efficiently under sustained load', async () => {
      const initialMemory = process.memoryUsage();

      // Create sustained load
      const operations = [];

      for (let i = 0; i < 100; i++) {
        operations.push(
          cacheManager.set(`memory-test-${i}`, {
            data: 'x'.repeat(1000),
            index: i,
            timestamp: Date.now()
          })
        );
      }

      await Promise.all(operations);

      // Force garbage collection if available
      if (global.gc) {
        global.gc();
      }

      const finalMemory = process.memoryUsage();
      const memoryIncrease = (finalMemory.heapUsed - initialMemory.heapUsed) / 1024 / 1024;

      // Should not leak significant memory
      expect(memoryIncrease).toBeLessThan(10); // Less than 10MB increase

      // Cache should be managing memory properly
      const metrics = cacheManager.getMetrics();
      expect(metrics.totalSize).toBeGreaterThan(0);
      expect(metrics.totalEntries).toBeGreaterThan(0);
    });
  });

  describe('Error Recovery Integration', () => {
    test('should recover from service failures gracefully', async () => {
      // Test cache recovery
      cacheManager.clear();
      await cacheManager.set('recovery-test', 'test-data');

      // Simulate service restart
      const newData = await cacheManager.get('recovery-test');
      expect(newData).toBeTruthy();

      // Test subscription recovery
      const subscriptionId = subscriptionManager.subscribe({
        table: 'recovery_test',
        event: 'INSERT',
        callback: jest.fn(),
        onError: jest.fn(),
        autoReconnect: true
      });

      expect(subscriptionId).toBeTruthy();

      // Cleanup
      subscriptionManager.unsubscribe(subscriptionId);
    });
  });

  describe('Production Configuration Validation', () => {
    test('should validate production-ready configurations', () => {
      // Check cache configuration
      const cacheMetrics = cacheManager.getMetrics();
      expect(cacheMetrics).toHaveProperty('totalSize');
      expect(cacheMetrics).toHaveProperty('hitRate');

      // Check subscription limits
      const healthMetrics = subscriptionManager.getHealthMetrics();
      expect(healthMetrics).toHaveProperty('totalSubscriptions');
      expect(healthMetrics).toHaveProperty('connectionState');

      // Check performance monitoring
      const performanceSummary = performanceMonitor.getPerformanceSummary();
      expect(performanceSummary).toHaveProperty('totalMetrics');
      expect(performanceSummary).toHaveProperty('averageResponseTime');
    });

    test('should handle production-scale data volumes', async () => {
      const startTime = Date.now();

      // Test with production-scale data
      const productionData = Array.from({ length: 5000 }, (_, i) => ({
        id: `prod-${i}`,
        user_id: `user-${Math.floor(i / 100)}`,
        event_type: ['login', 'logout', 'action', 'view'][i % 4],
        timestamp: new Date(Date.now() - Math.random() * 86400000).toISOString(),
        metadata: {
          session_id: `session-${Math.floor(i / 50)}`,
          ip_address: `192.168.1.${(i % 255) + 1}`,
          user_agent: 'Mozilla/5.0 (compatible; IntegrationTest/1.0)'
        }
      }));

      const result = await performanceOptimizationService.executeBatchOperation({
        operation: 'insert',
        table: 'production_scale_test',
        data: productionData,
        batchSize: 250
      });

      const duration = Date.now() - startTime;

      // Should handle production scale efficiently
      expect(duration).toBeLessThan(15000); // 15 seconds max
      expect(result).toHaveProperty('success');
      expect(result.processedCount).toBe(5000);
    });
  });
});
