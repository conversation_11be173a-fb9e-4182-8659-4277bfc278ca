/**
 * Jest Test Setup
 * Global test configuration and mocks
 */

// Mock React Native modules
jest.mock('react-native', () => ({
  Platform: { OS: 'ios' },
  StyleSheet: {
    create: (styles: any) => styles,
  },
  View: 'View',
  Text: 'Text',
  TouchableOpacity: 'TouchableOpacity',
  ScrollView: 'ScrollView',
  Alert: {
    alert: jest.fn(),
  },
}));

// Mock Expo modules
jest.mock('expo-secure-store', () => ({
  setItemAsync: jest.fn().mockResolvedValue(undefined),
  getItemAsync: jest.fn().mockResolvedValue(null),
  deleteItemAsync: jest.fn().mockResolvedValue(undefined),
}));

jest.mock('@react-native-async-storage/async-storage', () => ({
  setItem: jest.fn().mockResolvedValue(undefined),
  getItem: jest.fn().mockResolvedValue(null),
  removeItem: jest.fn().mockResolvedValue(undefined),
  clear: jest.fn().mockResolvedValue(undefined),
}));

// Mock global objects
global.performance = {
  now: jest.fn(() => Date.now()),
  memory: {
    usedJSHeapSize: 1024 * 1024 * 50, // 50MB
    totalJSHeapSize: 1024 * 1024 * 100, // 100MB
    jsHeapSizeLimit: 1024 * 1024 * 500, // 500MB
  },
} as any;

global.crypto = {
  getRandomValues: jest.fn((arr) => {
    for (let i = 0; i < arr.length; i++) {
      arr[i] = Math.floor(Math.random() * 256);
    }
    return arr;
  }),
  subtle: {
    importKey: jest.fn().mockResolvedValue({}),
    encrypt: jest.fn().mockResolvedValue(new ArrayBuffer(32)),
    decrypt: jest.fn().mockResolvedValue(new ArrayBuffer(32)),
  },
} as any;

// Mock localStorage for web environment
global.localStorage = {
  getItem: jest.fn().mockReturnValue(null),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
  length: 0,
  key: jest.fn().mockReturnValue(null),
} as any;

// Mock navigator
global.navigator = {
  connection: {
    effectiveType: '4g',
  },
} as any;

// Mock window
global.window = {
  addEventListener: jest.fn(),
  removeEventListener: jest.fn(),
} as any;

// Mock document
global.document = {
  hidden: false,
} as any;

// Mock compression streams for testing
global.CompressionStream = jest.fn().mockImplementation(() => ({
  readable: new ReadableStream(),
  writable: new WritableStream(),
}));

global.DecompressionStream = jest.fn().mockImplementation(() => ({
  readable: new ReadableStream(),
  writable: new WritableStream(),
}));

// Mock btoa/atob for base64 encoding
global.btoa = jest.fn((str) => Buffer.from(str, 'binary').toString('base64'));
global.atob = jest.fn((str) => Buffer.from(str, 'base64').toString('binary'));

// Mock Response for compression testing
global.Response = jest.fn().mockImplementation((body) => ({
  arrayBuffer: jest.fn().mockResolvedValue(new ArrayBuffer(0)),
  text: jest.fn().mockResolvedValue(''),
})) as any;

global.ReadableStream = jest.fn().mockImplementation(() => ({
  pipeThrough: jest.fn().mockReturnThis(),
})) as any;

// Increase timeout for performance tests
jest.setTimeout(30000);

// Use fake timers for better test control
jest.useFakeTimers();

// Clean up after each test
afterEach(async () => {
  jest.clearAllMocks();

  // Clear any running timers
  jest.clearAllTimers();

  // Wait for any pending promises
  await new Promise(resolve => setTimeout(resolve, 0));
});
