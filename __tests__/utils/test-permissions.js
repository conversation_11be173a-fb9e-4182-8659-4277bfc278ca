#!/usr/bin/env node

// Test script to verify permission cleanup
console.log('Testing permission cleanup...');

const fs = require('fs');
const path = require('path');

// Read app.json
const appJsonPath = path.join(__dirname, 'app.json');
const appJson = JSON.parse(fs.readFileSync(appJsonPath, 'utf8'));

console.log('\n=== iOS PERMISSIONS ===');
const iosPermissions = appJson.expo.ios.infoPlist;
const expectedIosPermissions = [
  'NSCameraUsageDescription',
  'NSPhotoLibraryUsageDescription'
];

const removedIosPermissions = [
  'NSMicrophoneUsageDescription',
  'NSContactsUsageDescription', 
  'NSLocationWhenInUseUsageDescription'
];

console.log('✅ Expected permissions present:');
expectedIosPermissions.forEach(perm => {
  if (iosPermissions[perm]) {
    console.log(`  ✓ ${perm}: ${iosPermissions[perm]}`);
  } else {
    console.log(`  ❌ MISSING: ${perm}`);
  }
});

console.log('\n✅ Unused permissions removed:');
removedIosPermissions.forEach(perm => {
  if (!iosPermissions[perm]) {
    console.log(`  ✓ REMOVED: ${perm}`);
  } else {
    console.log(`  ❌ STILL PRESENT: ${perm}`);
  }
});

console.log('\n=== ANDROID PERMISSIONS ===');
const androidPermissions = appJson.expo.android.permissions;
const expectedAndroidPermissions = [
  'CAMERA',
  'READ_EXTERNAL_STORAGE',
  'WRITE_EXTERNAL_STORAGE'
];

const removedAndroidPermissions = [
  'RECORD_AUDIO',
  'ACCESS_FINE_LOCATION',
  'ACCESS_COARSE_LOCATION',
  'READ_CONTACTS'
];

console.log('✅ Expected permissions present:');
expectedAndroidPermissions.forEach(perm => {
  if (androidPermissions.includes(perm)) {
    console.log(`  ✓ ${perm}`);
  } else {
    console.log(`  ❌ MISSING: ${perm}`);
  }
});

console.log('\n✅ Unused permissions removed:');
removedAndroidPermissions.forEach(perm => {
  if (!androidPermissions.includes(perm)) {
    console.log(`  ✓ REMOVED: ${perm}`);
  } else {
    console.log(`  ❌ STILL PRESENT: ${perm}`);
  }
});

console.log('\n=== SUMMARY ===');
const totalExpected = expectedIosPermissions.length + expectedAndroidPermissions.length;
const totalRemoved = removedIosPermissions.length + removedAndroidPermissions.length;

let presentCount = 0;
let removedCount = 0;

expectedIosPermissions.forEach(perm => {
  if (iosPermissions[perm]) presentCount++;
});

expectedAndroidPermissions.forEach(perm => {
  if (androidPermissions.includes(perm)) presentCount++;
});

removedIosPermissions.forEach(perm => {
  if (!iosPermissions[perm]) removedCount++;
});

removedAndroidPermissions.forEach(perm => {
  if (!androidPermissions.includes(perm)) removedCount++;
});

console.log(`Expected permissions present: ${presentCount}/${totalExpected}`);
console.log(`Unused permissions removed: ${removedCount}/${totalRemoved}`);

if (presentCount === totalExpected && removedCount === totalRemoved) {
  console.log('\n🎉 Permission cleanup completed successfully!');
} else {
  console.log('\n❌ Permission cleanup incomplete. Please review the issues above.');
}

console.log('\nPermission cleanup test completed!');
