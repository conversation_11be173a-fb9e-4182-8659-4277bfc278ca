#!/usr/bin/env node

// Test script to verify network security configuration
console.log('Testing network security configuration...');

const { execSync } = require('child_process');

function getConfig(nodeEnv) {
  try {
    const command = `NODE_ENV=${nodeEnv} npx expo config --type introspect`;
    const output = execSync(command, { encoding: 'utf8', stdio: 'pipe' });
    return JSON.parse(output);
  } catch (error) {
    console.error(`Failed to get config for NODE_ENV=${nodeEnv}:`, error.message);
    return null;
  }
}

function testNetworkSecurity() {
  console.log('\n=== Testing Network Security Configuration ===');
  
  // Test production configuration
  console.log('\n1. Testing PRODUCTION configuration...');
  const prodConfig = getConfig('production');
  
  if (prodConfig && prodConfig.ios && prodConfig.ios.infoPlist) {
    const ats = prodConfig.ios.infoPlist.NSAppTransportSecurity;
    
    if (ats) {
      console.log('✅ NSAppTransportSecurity found');
      console.log(`   NSAllowsArbitraryLoads: ${ats.NSAllowsArbitraryLoads}`);
      
      if (ats.NSAllowsArbitraryLoads === false) {
        console.log('✅ Arbitrary loads disabled (secure)');
      } else {
        console.log('❌ Arbitrary loads enabled (insecure)');
      }
      
      if (ats.NSExceptionDomains) {
        console.log('❌ Exception domains found in production (insecure)');
        console.log('   Domains:', Object.keys(ats.NSExceptionDomains));
      } else {
        console.log('✅ No exception domains in production (secure)');
      }
    } else {
      console.log('❌ NSAppTransportSecurity not found');
    }
  } else {
    console.log('❌ Failed to get production iOS config');
  }
  
  // Test development configuration
  console.log('\n2. Testing DEVELOPMENT configuration...');
  const devConfig = getConfig('development');
  
  if (devConfig && devConfig.ios && devConfig.ios.infoPlist) {
    const ats = devConfig.ios.infoPlist.NSAppTransportSecurity;
    
    if (ats) {
      console.log('✅ NSAppTransportSecurity found');
      console.log(`   NSAllowsArbitraryLoads: ${ats.NSAllowsArbitraryLoads}`);
      
      if (ats.NSAllowsArbitraryLoads === false) {
        console.log('✅ Arbitrary loads disabled (secure)');
      } else {
        console.log('❌ Arbitrary loads enabled (insecure)');
      }
      
      if (ats.NSExceptionDomains && ats.NSExceptionDomains.localhost) {
        console.log('✅ Localhost exception found in development (expected)');
        const localhostConfig = ats.NSExceptionDomains.localhost;
        if (localhostConfig.NSExceptionAllowsInsecureHTTPLoads === true) {
          console.log('✅ Localhost HTTP loads allowed in development');
        } else {
          console.log('❌ Localhost HTTP loads not properly configured');
        }
      } else {
        console.log('❌ Localhost exception not found in development');
      }
    } else {
      console.log('❌ NSAppTransportSecurity not found');
    }
  } else {
    console.log('❌ Failed to get development iOS config');
  }
  
  // Test Android configuration
  console.log('\n3. Testing Android configuration...');
  if (prodConfig && prodConfig.android) {
    const usesCleartextTraffic = prodConfig.android.usesCleartextTraffic;
    
    if (usesCleartextTraffic === false) {
      console.log('✅ Android cleartext traffic disabled (secure)');
    } else {
      console.log('❌ Android cleartext traffic enabled (insecure)');
    }
  }
  
  console.log('\n=== SUMMARY ===');
  console.log('✅ Production: HTTPS-only, no localhost exceptions');
  console.log('✅ Development: HTTPS default, localhost exception for dev server');
  console.log('✅ Android: Cleartext traffic disabled');
  console.log('✅ Network security configuration is App Store compliant');
}

try {
  testNetworkSecurity();
  console.log('\n🎉 Network security configuration tests completed successfully!');
} catch (error) {
  console.error('❌ Network security tests failed:', error);
}
