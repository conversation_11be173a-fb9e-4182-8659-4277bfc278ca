import { z } from 'zod';

// Example row schema (keep local to test to avoid breaking runtime)
const DateNightRow = z.object({
  id: z.string(),
  title: z.string(),
  description: z.string().default(''),
  category: z.string().optional(),
  emoji: z.string().optional(),
  cost: z.enum(['free','low','medium','high']).optional(),
  estimated_duration: z.number().int().optional(),
  indoor_outdoor: z.enum(['indoor','outdoor','both']).optional(),
});

const toIdea = (row: unknown) => {
  const r = DateNightRow.safeParse(row);
  if (!r.success) throw new Error('invalid row');
  const record = r.data;
  return {
    id: record.id,
    title: record.title,
    description: record.description,
    category: record.category,
    emoji: record.emoji,
    costLevel: record.cost,
    estimatedDuration: record.estimated_duration,
    indoorOutdoor: record.indoor_outdoor,
  };
};

describe('DateNight zod parsing', () => {
  it('parses a valid row', () => {
    const idea = toIdea({
      id: '1', title: 'Picnic', description: 'In the park',
      cost: 'low', estimated_duration: 90, indoor_outdoor: 'outdoor'
    });
    expect(idea.title).toBe('Picnic');
    expect(idea.costLevel).toBe('low');
    expect(idea.indoorOutdoor).toBe('outdoor');
  });

  it('throws on invalid row', () => {
    expect(() => toIdea({ id: 1 } as any)).toThrow('invalid row');
  });
});

