# Modular Activity System

The modular activity system allows you to create self-contained activity components that can be dynamically rendered on any screen throughout the relationship app. Each activity manages its own state, data, and Supabase integration while following standardized interfaces.

## Core Concepts

- **Activities are portable, standalone components** that can be embedded anywhere
- **Any screen can pull in and display any activity component** using the universal container
- **Activities screen becomes a container** that orchestrates these modular activities
- **Each activity manages its own state, data, and Supabase integration**

## Architecture

### 1. Activity Registry System
- Central registry of all available activities
- Each activity registers itself with metadata (name, description, category, etc.)
- Registry allows dynamic discovery and rendering of activities
- Support for filtering/categorizing activities

### 2. Universal Activity Container
- Wrapper component that can render any registered activity
- Handles common functionality (loading states, error boundaries, etc.)
- Provides consistent styling and layout patterns
- Manages activity lifecycle events

### 3. Event System
- Activities fire standardized events when played, completed, or updated
- Common event schema: `{ activityId, userId, action, data, timestamp }`
- Events allow tracking across all screens where activities appear

## Quick Start

### 1. Using an Activity

```tsx
import { ActivityContainer } from '@/shared/components/activity';

function MyScreen() {
  return (
    <ActivityContainer
      activityId="would-you-rather"
      userId={userId}
      coupleId={coupleId}
      onComplete={(result) => {
        console.log('Activity completed:', result);
      }}
      onExit={() => {
        console.log('User exited activity');
      }}
      onError={(error) => {
        console.log('Activity error:', error);
      }}
    />
  );
}
```

### 2. Registering Activities

```tsx
import { registerAllActivities } from '@/shared/components/activity';

// Register all available activities
registerAllActivities();
```

## Creating a New Activity

### 1. Create the Activity Component

```tsx
// src/shared/components/activity/activities/MyActivity.tsx
import React, { useState, useCallback } from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { ActivityProps, ActivityResult, ActivityError } from '@/shared/types/activity.types';

export function MyActivity({
  activityId,
  userId,
  coupleId,
  onComplete,
  onExit,
  onError,
  theme = 'default',
  customData = {}
}: ActivityProps) {
  const [score, setScore] = useState(0);

  const handleComplete = useCallback(() => {
    const result: ActivityResult = {
      activityId,
      userId,
      coupleId,
      score,
      points: 25,
      completedAt: new Date().toISOString(),
      data: {
        // Your activity-specific data
      }
    };

    onComplete(result);
  }, [activityId, userId, coupleId, score, onComplete]);

  return (
    <View style={styles.container}>
      <Text style={styles.title}>My Activity</Text>
      <TouchableOpacity onPress={handleComplete}>
        <Text>Complete Activity</Text>
      </TouchableOpacity>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
  },
});
```

### 2. Register the Activity

```tsx
// src/shared/components/activity/activities/index.ts
import { activityRegistry } from '@/shared/services/activity/activityRegistry';
import { MyActivity } from './MyActivity';

const myActivity: ActivityComponent = {
  id: 'my-activity',
  name: 'My Activity',
  category: 'getting-to-know',
  metadata: {
    id: 'my-activity',
    name: 'My Activity',
    description: 'A fun activity to get to know each other better.',
    category: 'getting-to-know',
    difficulty: 'easy',
    duration: '10-15 min',
    players: 2,
    icon: <SomeIcon size={24} color="#FFFFFF" />,
    theme: 'playful',
    tags: ['fun', 'conversation'],
    isAvailable: true,
    version: '1.0.0',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  component: MyActivity,
  supabaseTable: 'my_activity_responses'
};

// Register the activity
activityRegistry.registerActivity(myActivity);
```

### 3. Create Database Table (Optional)

If your activity needs to store data, create a corresponding Supabase table:

```sql
-- supabase/migrations/YYYYMMDD_create_my_activity_table.sql
CREATE TABLE IF NOT EXISTS my_activity_responses (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  session_id UUID NOT NULL REFERENCES activity_sessions(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  couple_id UUID NOT NULL,
  -- Your activity-specific fields
  response_data JSONB DEFAULT '{}',
  score INTEGER NOT NULL DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Enable RLS
ALTER TABLE my_activity_responses ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
CREATE POLICY "Users can view their own responses" ON my_activity_responses
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own responses" ON my_activity_responses
  FOR INSERT WITH CHECK (auth.uid() = user_id);
```

## Activity Interface

### ActivityProps

```tsx
interface ActivityProps {
  activityId: string;           // Unique identifier for the activity
  userId: string;              // Current user ID
  coupleId: string;            // Couple/relationship ID
  onComplete: (result: ActivityResult) => void;  // Called when activity completes
  onExit: () => void;          // Called when user exits activity
  onError: (error: ActivityError) => void;       // Called when error occurs
  theme?: ActivityTheme;       // Visual theme ('default', 'romantic', 'playful', 'reflective')
  customData?: Record<string, any>;  // Custom data passed to activity
}
```

### ActivityResult

```tsx
interface ActivityResult {
  activityId: string;
  userId: string;
  coupleId: string;
  score: number;               // Activity score (0-100)
  points: number;              // Points earned
  completedAt: string;         // ISO timestamp
  data: Record<string, any>;   // Activity-specific data
  metadata?: Record<string, any>;  // Additional metadata
}
```

## Event System

Activities can emit standardized events for tracking and analytics:

```tsx
import { activityEventService } from '@/shared/services/activity/activityEventService';

// Emit an event
await activityEventService.emitEvent({
  id: `${activityId}-${Date.now()}`,
  activityId,
  userId,
  coupleId,
  eventType: 'question_answered',
  data: { questionId: 'q1', answer: 'A' },
  timestamp: new Date().toISOString()
});

// Subscribe to events
const unsubscribe = activityEventService.subscribe('activity_completed', (event) => {
  console.log('Activity completed:', event);
});
```

## Available Activities

### Would You Rather
- **ID**: `would-you-rather`
- **Category**: Getting to Know
- **Difficulty**: Easy
- **Duration**: 10-15 minutes
- **Description**: Fun dilemmas that reveal preferences and spark interesting conversations.

### Love Language Quiz
- **ID**: `love-language-quiz`
- **Category**: Getting to Know
- **Difficulty**: Medium
- **Duration**: 15-20 minutes
- **Description**: Discover your love languages and how to express love better.

## Best Practices

### 1. State Management
- Use local state for activity-specific data
- Keep state minimal and focused
- Use callbacks to communicate with parent components

### 2. Error Handling
- Always handle errors gracefully
- Use the `onError` callback for critical errors
- Provide fallback UI for error states

### 3. Performance
- Lazy load activity components when possible
- Optimize images and assets
- Use React.memo for expensive components

### 4. Accessibility
- Provide proper labels and descriptions
- Support keyboard navigation
- Use semantic HTML elements

### 5. Theming
- Support the provided theme system
- Use theme colors consistently
- Provide fallbacks for missing theme values

## Database Schema

The activity system uses several database tables:

- `activity_sessions`: Tracks activity sessions and progress
- `activity_events`: Logs all activity events for analytics
- `would_you_rather_responses`: Stores Would You Rather responses
- `love_language_quiz_results`: Stores Love Language Quiz results

See the migration file for complete schema details.

## Troubleshooting

### Common Issues

1. **Activity not found**: Make sure the activity is registered with the registry
2. **Database errors**: Check RLS policies and user permissions
3. **Event not firing**: Verify event service is properly initialized
4. **Styling issues**: Check theme colors and component styles

### Debug Mode

Enable debug logging by setting the log level:

```tsx
import { logger } from '@/shared/utils/logger';

// Set debug level
logger.setLevel('debug');
```

## Contributing

When adding new activities:

1. Follow the existing patterns and interfaces
2. Add proper TypeScript types
3. Include comprehensive error handling
4. Write tests for your activity
5. Update documentation
6. Add database migrations if needed

## Examples

See the example activities in `src/shared/components/activity/activities/` for reference implementations.
