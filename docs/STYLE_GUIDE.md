### Engagement Patterns
```typescript
// Progress tracking
interface StreakData {
  currentStreak: number
  longestStreak: number
  allowsGracePeriod: boolean
}

// Activity states
type ActivityStatus = 'not_started' | 'in_progress' | 'completed' | 'skipped'

// Notification tones
type NotificationTone = 'encouraging' | 'neutral' | 'gentle'
```

### Privacy & Consent
```typescript
// Data visibility
interface DataPrivacy {
  visibleToPartner: boolean
  requiresMutualConsent: boolean
  userCanToggle: boolean
}

// Response sharing
interface ResponseControl {
  defaultVisibility: 'private' | 'shared'
  canChangeAfterSubmission: boolean
}
```# EVERLASTING-US Code Style Guide

## File & Folder Naming Conventions

### Directory Names
- **Feature directories**: kebab-case (`daily-questions/`, `match-game/`, `date-night/`)
- **Utility directories**: camelCase (`auth/`, `storage/`, `utils/`)
- **Component group directories**: kebab-case (`design-system/`, `error-handling/`)

### File Naming

#### React Components
```
PascalCase + .tsx extension
✅ Button.tsx
✅ UserProfile.tsx
✅ TimelineEvent.tsx
❌ button.tsx
❌ user-profile.tsx
```

#### React Screens/Pages
```
PascalCase + Screen suffix + .tsx
✅ OnboardingScreen.tsx
✅ TimelineScreen.tsx
✅ DailyQuestionScreen.tsx
❌ onboarding.tsx
❌ timeline-screen.tsx
```

#### Hooks
```
camelCase starting with 'use' + .ts
✅ useAuth.ts
✅ useCoupleData.ts
✅ useTimeline.ts
❌ UseAuth.ts
❌ use-auth.ts
```

#### Services & Utilities
```
camelCase + .ts extension
✅ authService.ts
✅ storageManager.ts
✅ dateUtils.ts
❌ AuthService.ts
❌ auth-service.ts
```

#### Types
```
camelCase + .types.ts suffix
✅ auth.types.ts
✅ database.types.ts
✅ api.types.ts
❌ AuthTypes.ts
❌ auth-types.ts
```

#### Test Files
```
Match source file name + .test.ts/.test.tsx
✅ Button.test.tsx
✅ authService.test.ts
✅ useAuth.test.ts
❌ button.test.tsx
❌ auth-service.test.ts
```

## Folder Structure

### Source Organization
```
src/
├── journeys/              # User experience flows
│   ├── onboarding/        # Getting started flow
│   ├── daily/             # Daily questions & check-ins
│   ├── activities/        # Match games, quizzes
│   ├── memories/          # Timeline, scrapbook
│   ├── planning/          # Date nights, meals
│   └── progress/          # Achievements, insights
├── shared/                # Reusable across features
│   ├── components/        # UI components
│   ├── data/             # API, database, types
│   ├── hooks/            # Cross-feature hooks
│   ├── services/         # Business logic
│   ├── utils/            # Helper functions
│   └── types/            # Global type definitions
└── app/                  # Expo Router pages (thin wrappers)
```

### Journey Structure
```
journeys/[feature]/
├── components/           # Feature-specific components
├── screens/             # Full screen components
├── hooks/               # Feature-specific hooks
├── services/            # Feature business logic
├── types/               # Feature type definitions
└── index.ts             # Clean exports
```

## Code Organization

### Import Order
```typescript
// 1. React & React Native
import React from 'react'
import { View, Text } from 'react-native'

// 2. Third-party libraries
import { supabase } from '@supabase/supabase-js'

// 3. Shared utilities (from outer directories)
import { Button } from '../../../shared/components/Button'
import { useAuth } from '../../../shared/hooks/useAuth'

// 4. Local imports (same directory)
import { ProfileForm } from './ProfileForm'
import { useProfile } from './useProfile'
```

### Component Structure
```typescript
interface UserProfileProps {
  userId: string
  onUpdate?: () => void
}

export function UserProfile({ userId, onUpdate }: UserProfileProps) {
  // Hooks first
  const { user, loading } = useAuth()
  const { profile, updateProfile } = useProfile(userId)

  // Event handlers
  const handleUpdate = () => {
    // logic here
    onUpdate?.()
  }

  // Conditional renders
  if (loading) return <LoadingSpinner />

  // Main render
  return (
    <View>
      {/* component content */}
    </View>
  )
}
```

### Hook Structure
```typescript
interface UseAuthReturn {
  user: User | null
  loading: boolean
  signIn: (email: string, password: string) => Promise<void>
  signOut: () => Promise<void>
}

export function useAuth(): UseAuthReturn {
  // State declarations
  const [user, setUser] = useState<User | null>(null)
  const [loading, setLoading] = useState(true)

  // Effects
  useEffect(() => {
    // effect logic
  }, [])

  // Handlers
  const signIn = async (email: string, password: string) => {
    // logic
  }

  // Return object
  return {
    user,
    loading,
    signIn,
    signOut
  }
}
```

## TypeScript Standards

### Interface Naming
```typescript
// Component props
interface ButtonProps {
  title: string
  onPress?: () => void
}

// Service return types
interface AuthServiceReturn {
  user: User | null
  error: string | null
}

// Database types
interface TimelineEvent {
  id: string
  title: string
  date: Date
}
```

### Type Exports
```typescript
// Always export types that might be used elsewhere
export interface User {
  id: string
  name: string
}

export type AuthStatus = 'authenticated' | 'loading' | 'unauthenticated'
```

## Relationship App Specific Conventions

### Partner Identification
```typescript
// Always use userId for tracking, determine partner role via helper
interface CoupleAction {
  userId: string        // Who performed the action
  coupleId: string      // Which couple
  action: string        // What they did
  timestamp: Date       // When
}

// Helper to determine partner role
function getPartnerRole(userId: string, coupleId: string): 'partner1' | 'partner2'
```

### Activity Naming
```typescript
// Use "activities" not "games"
✅ ActivityScreen.tsx
✅ useActivities.ts
✅ activityService.ts
❌ GameScreen.tsx
❌ useGames.ts
```

### Event Logging
```typescript
// Consistent event naming for analytics
logUserAction('daily_question_answered', { questionId, answer })
logUserAction('milestone_completed', { milestoneId })
logUserAction('photo_uploaded', { eventId, photoCount })
```

## Forbidden Patterns

### File Organization
```typescript
❌ Multiple components in one file
❌ Mixing hooks and components in same file
❌ Deep nesting (more than 3 levels in journeys)
❌ Circular dependencies
❌ Default exports (use named exports)
```

### TypeScript
```typescript
❌ any type usage
❌ Non-null assertion operator (!)
❌ Ignoring TypeScript errors with @ts-ignore
❌ Implicit any returns
```

### React Native
```typescript
❌ Inline styles for complex components
❌ Direct AsyncStorage usage (use storage service)
❌ Unhandled promise rejections
❌ Missing error boundaries
```

## Tools & Enforcement

### Required Tools
- ESLint with TypeScript rules
- Prettier for code formatting
- TypeScript strict mode enabled
- React Native TypeScript template

### File Templates
Use consistent file templates for new components, hooks, and services to maintain patterns across the codebase.
