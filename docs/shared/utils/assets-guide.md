# Assets Guide

## 📁 **Current Asset Structure**

```
src/assets/
├── index.ts                    # Centralized asset exports
└── images/
    ├── icons/                  # App icons and favicons
    │   └── favicon.png         # Web favicon ✅
    ├── splash/                 # Splash screen images
    ├── app-store/             # iOS App Store assets
    └── play-store/            # Google Play Store assets
```

## ✅ **Current Assets**
- `favicon.png` - Web favicon (existing)

## 🚨 **Missing Critical Assets**

## 📱 **Usage in Code**

Import assets using the centralized index:

```typescript
// Import all assets
import assets, { icons, getAppIcon } from '../assets';

// Use specific assets
const favicon = icons.favicon;
const appIcon = getAppIcon();

// In components
<Image source={icons.favicon} style={styles.icon} />
```

## ❌ **Missing Assets (Required for App Store Submission)**

### iOS App Store Assets
- `splash.png` - Splash screen image (1024x1024px recommended)
- `icon-foreground.png` - Adaptive icon foreground for Android
- `app-store/` directory with:
  - `app-icon-1024.png` - App Store icon (1024x1024px, PNG, no transparency)
  - `screenshots-iphone-67/` - iPhone 6.7" screenshots (1290x2796px)
  - `screenshots-iphone-65/` - iPhone 6.5" screenshots (1242x2688px)
  - `screenshots-ipad/` - iPad Pro screenshots (2048x2732px)

### Google Play Store Assets
- `play-store/` directory with:
  - `app-icon-512.png` - Play Store icon (512x512px, PNG, no transparency)
  - `feature-graphic.png` - Feature graphic (1024x500px, PNG/JPG)
  - `screenshots-phone/` - Phone screenshots (1080x1920px or similar)
  - `screenshots-tablet/` - Tablet screenshots (1200x1920px or similar)

## Asset Creation Guidelines

### App Icons
- Use high-quality, recognizable design
- Ensure icon looks good at small sizes
- Follow platform-specific guidelines
- No text or fine details
- Use brand colors consistently

### Screenshots
- Show key app features and functionality
- Use real content, not placeholder text
- Include different app states (logged in, activities, etc.)
- Ensure text is readable at display size
- Follow platform screenshot guidelines

### Feature Graphics (Google Play)
- Highlight main app value proposition
- Include app name and key benefits
- Use high contrast for readability
- Follow Google Play design guidelines

## Next Steps
1. Create or commission professional app icons
2. Take screenshots on real devices
3. Design feature graphics
4. Optimize all images for web/mobile
5. Test assets on different screen sizes
