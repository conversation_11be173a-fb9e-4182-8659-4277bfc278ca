# 🎨 Design System

> **Complete design system guide for the Nestled app**

## 📋 Table of Contents

1. [Design Philosophy](#-design-philosophy)
2. [Color System](#-color-system)
3. [Typography](#-typography)
4. [Spacing & Layout](#-spacing--layout)
5. [Components](#-components)
6. [Icons & Imagery](#-icons--imagery)
7. [Theming](#-theming)
8. [Usage Guidelines](#-usage-guidelines)

## 🎯 Design Philosophy

### Core Principles
The Nestled design system is built on these foundational principles:

#### **Relationship-First Design**
- **Warm, inviting colors** that feel personal and intimate
- **Playful interactions** that make relationship building fun
- **Progress visualization** that celebrates growth
- **Privacy-first** approach to personal data

#### **Accessibility & Inclusion**
- **High contrast ratios** for readability
- **Large touch targets** for easy interaction
- **Clear visual hierarchy** for intuitive navigation
- **Support for screen readers** and assistive technologies

#### **Consistency & Scalability**
- **Unified visual language** across all screens
- **Reusable components** for efficient development
- **Scalable design tokens** for easy maintenance
- **Clear documentation** for team alignment

## 🎨 Color System

### Primary Colors
The color palette is designed to evoke warmth, love, and growth:

```typescript
// Primary Colors
const colors = {
  primary: {
    50: '#fef2f2',   // Lightest
    100: '#fee2e2',
    200: '#fecaca',
    300: '#fca5a5',
    400: '#f87171',
    500: '#ef4444',  // Main primary
    600: '#dc2626',
    700: '#b91c1c',
    800: '#991b1b',
    900: '#7f1d1d',  // Darkest
  },
  
  secondary: {
    50: '#f0fdfa',
    100: '#ccfbf1',
    200: '#99f6e4',
    300: '#5eead4',
    400: '#2dd4bf',
    500: '#14b8a6',  // Main secondary
    600: '#0d9488',
    700: '#0f766e',
    800: '#115e59',
    900: '#134e4a',
  },
  
  accent: {
    50: '#eff6ff',
    100: '#dbeafe',
    200: '#bfdbfe',
    300: '#93c5fd',
    400: '#60a5fa',
    500: '#3b82f6',  // Main accent
    600: '#2563eb',
    700: '#1d4ed8',
    800: '#1e40af',
    900: '#1e3a8a',
  }
};
```

### Semantic Colors
Colors with specific meanings and use cases:

```typescript
const semanticColors = {
  // Status Colors
  success: '#10b981',    // Green - Success states
  warning: '#f59e0b',    // Amber - Warning states
  error: '#ef4444',      // Red - Error states
  info: '#3b82f6',       // Blue - Information states
  
  // Neutral Colors
  gray: {
    50: '#f9fafb',
    100: '#f3f4f6',
    200: '#e5e7eb',
    300: '#d1d5db',
    400: '#9ca3af',
    500: '#6b7280',
    600: '#4b5563',
    700: '#374151',
    800: '#1f2937',
    900: '#111827',
  },
  
  // Background Colors
  background: {
    primary: '#ffffff',     // Main background
    secondary: '#f9fafb',   // Secondary background
    tertiary: '#f3f4f6',    // Tertiary background
  },
  
  // Text Colors
  text: {
    primary: '#111827',     // Main text
    secondary: '#6b7280',   // Secondary text
    tertiary: '#9ca3af',    // Tertiary text
    inverse: '#ffffff',     // Text on dark backgrounds
  }
};
```

### Gradient System
Pre-defined gradients for visual interest:

```typescript
const gradients = {
  warm: ['#ff6b6b', '#ffa726'],           // Warm, romantic
  cool: ['#4ecdc4', '#45b7d1'],           // Cool, calming
  sunset: ['#ff9a9e', '#fecfef'],         // Sunset, dreamy
  ocean: ['#667eea', '#764ba2'],          // Ocean, deep
  forest: ['#56ab2f', '#a8e6cf'],         // Forest, natural
  fire: ['#ff416c', '#ff4b2b'],           // Fire, passionate
};
```

### Dark Mode Colors
Colors optimized for dark mode:

```typescript
const darkModeColors = {
  background: {
    primary: '#0f172a',     // Dark background
    secondary: '#1e293b',   // Secondary dark background
    tertiary: '#334155',    // Tertiary dark background
  },
  
  text: {
    primary: '#f8fafc',     // Light text
    secondary: '#cbd5e1',   // Secondary light text
    tertiary: '#94a3b8',    // Tertiary light text
  },
  
  // Adjusted semantic colors for dark mode
  success: '#34d399',
  warning: '#fbbf24',
  error: '#f87171',
  info: '#60a5fa',
};
```

## 📝 Typography

### Font System
The app uses system fonts for optimal performance and native feel:

```typescript
const typography = {
  // Font Families
  fontFamily: {
    regular: 'System',      // Default system font
    medium: 'System',       // Medium weight
    bold: 'System',         // Bold weight
    mono: 'Courier New',    // Monospace for code
  },
  
  // Font Sizes
  fontSize: {
    xs: 12,      // Extra small
    sm: 14,      // Small
    base: 16,    // Base (default)
    lg: 18,      // Large
    xl: 20,      // Extra large
    '2xl': 24,   // 2X large
    '3xl': 30,   // 3X large
    '4xl': 36,   // 4X large
    '5xl': 48,   // 5X large
  },
  
  // Font Weights
  fontWeight: {
    normal: '400',
    medium: '500',
    semibold: '600',
    bold: '700',
    extrabold: '800',
  },
  
  // Line Heights
  lineHeight: {
    tight: 1.25,
    normal: 1.5,
    relaxed: 1.75,
  },
  
  // Letter Spacing
  letterSpacing: {
    tight: -0.025,
    normal: 0,
    wide: 0.025,
  }
};
```

### Text Styles
Pre-defined text styles for consistent typography:

```typescript
const textStyles = {
  // Headings
  h1: {
    fontSize: 36,
    fontWeight: '700',
    lineHeight: 1.25,
    letterSpacing: -0.025,
  },
  
  h2: {
    fontSize: 30,
    fontWeight: '600',
    lineHeight: 1.3,
    letterSpacing: -0.025,
  },
  
  h3: {
    fontSize: 24,
    fontWeight: '600',
    lineHeight: 1.35,
    letterSpacing: 0,
  },
  
  h4: {
    fontSize: 20,
    fontWeight: '500',
    lineHeight: 1.4,
    letterSpacing: 0,
  },
  
  // Body Text
  body: {
    fontSize: 16,
    fontWeight: '400',
    lineHeight: 1.5,
    letterSpacing: 0,
  },
  
  bodySmall: {
    fontSize: 14,
    fontWeight: '400',
    lineHeight: 1.5,
    letterSpacing: 0,
  },
  
  // UI Text
  button: {
    fontSize: 16,
    fontWeight: '500',
    lineHeight: 1.25,
    letterSpacing: 0.025,
  },
  
  caption: {
    fontSize: 12,
    fontWeight: '400',
    lineHeight: 1.4,
    letterSpacing: 0,
  },
  
  // Special Text
  label: {
    fontSize: 14,
    fontWeight: '500',
    lineHeight: 1.25,
    letterSpacing: 0.025,
  },
  
  link: {
    fontSize: 16,
    fontWeight: '500',
    lineHeight: 1.25,
    letterSpacing: 0,
    textDecorationLine: 'underline',
  }
};
```

## 📐 Spacing & Layout

### Spacing Scale
Consistent spacing system based on 4px grid:

```typescript
const spacing = {
  0: 0,      // No spacing
  1: 4,      // 4px
  2: 8,      // 8px
  3: 12,     // 12px
  4: 16,     // 16px
  5: 20,     // 20px
  6: 24,     // 24px
  8: 32,     // 32px
  10: 40,    // 40px
  12: 48,    // 48px
  16: 64,    // 64px
  20: 80,    // 80px
  24: 96,    // 96px
  32: 128,   // 128px
};
```

### Layout System
Consistent layout patterns and containers:

```typescript
const layout = {
  // Container Widths
  container: {
    sm: 640,    // Small screens
    md: 768,    // Medium screens
    lg: 1024,   // Large screens
    xl: 1280,   // Extra large screens
  },
  
  // Screen Padding
  screenPadding: {
    horizontal: 16,  // Left/right padding
    vertical: 24,    // Top/bottom padding
  },
  
  // Component Spacing
  componentSpacing: {
    xs: 8,      // Small components
    sm: 12,     // Medium components
    md: 16,     // Large components
    lg: 24,     // Extra large components
  },
  
  // Border Radius
  borderRadius: {
    none: 0,
    sm: 4,
    md: 8,
    lg: 12,
    xl: 16,
    full: 9999,
  },
  
  // Shadows
  shadow: {
    sm: {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: 0.05,
      shadowRadius: 2,
      elevation: 1,
    },
    md: {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 4,
      elevation: 2,
    },
    lg: {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: 0.15,
      shadowRadius: 8,
      elevation: 4,
    },
  }
};
```

## 🧩 Components

### Button System
Comprehensive button component with multiple variants:

```typescript
interface ButtonProps {
  variant: 'primary' | 'secondary' | 'outline' | 'ghost';
  size: 'small' | 'medium' | 'large';
  disabled?: boolean;
  loading?: boolean;
  fullWidth?: boolean;
  children: React.ReactNode;
  onPress: () => void;
}

const buttonStyles = {
  // Variants
  primary: {
    backgroundColor: colors.primary[500],
    borderColor: colors.primary[500],
    color: colors.text.inverse,
  },
  
  secondary: {
    backgroundColor: colors.secondary[500],
    borderColor: colors.secondary[500],
    color: colors.text.inverse,
  },
  
  outline: {
    backgroundColor: 'transparent',
    borderColor: colors.primary[500],
    color: colors.primary[500],
  },
  
  ghost: {
    backgroundColor: 'transparent',
    borderColor: 'transparent',
    color: colors.primary[500],
  },
  
  // Sizes
  small: {
    paddingHorizontal: 12,
    paddingVertical: 8,
    fontSize: 14,
  },
  
  medium: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
  },
  
  large: {
    paddingHorizontal: 24,
    paddingVertical: 16,
    fontSize: 18,
  }
};
```

### Card System
Flexible card components for content organization:

```typescript
interface CardProps {
  variant: 'default' | 'elevated' | 'outlined';
  padding: 'none' | 'small' | 'medium' | 'large';
  children: React.ReactNode;
}

const cardStyles = {
  default: {
    backgroundColor: colors.background.primary,
    borderRadius: layout.borderRadius.lg,
    padding: spacing[4],
  },
  
  elevated: {
    backgroundColor: colors.background.primary,
    borderRadius: layout.borderRadius.lg,
    padding: spacing[4],
    ...layout.shadow.md,
  },
  
  outlined: {
    backgroundColor: colors.background.primary,
    borderRadius: layout.borderRadius.lg,
    padding: spacing[4],
    borderWidth: 1,
    borderColor: colors.gray[200],
  }
};
```

### Input System
Consistent form input components:

```typescript
interface InputProps {
  variant: 'default' | 'filled' | 'outlined';
  size: 'small' | 'medium' | 'large';
  error?: boolean;
  disabled?: boolean;
  placeholder?: string;
  value: string;
  onChangeText: (text: string) => void;
}

const inputStyles = {
  default: {
    borderWidth: 1,
    borderColor: colors.gray[300],
    borderRadius: layout.borderRadius.md,
    paddingHorizontal: spacing[3],
    paddingVertical: spacing[2],
    backgroundColor: colors.background.primary,
  },
  
  filled: {
    borderWidth: 0,
    borderRadius: layout.borderRadius.md,
    paddingHorizontal: spacing[3],
    paddingVertical: spacing[2],
    backgroundColor: colors.gray[100],
  },
  
  outlined: {
    borderWidth: 2,
    borderColor: colors.primary[500],
    borderRadius: layout.borderRadius.md,
    paddingHorizontal: spacing[3],
    paddingVertical: spacing[2],
    backgroundColor: colors.background.primary,
  }
};
```

## 🎭 Icons & Imagery

### Icon System
Consistent icon usage throughout the app:

```typescript
// Icon sizes
const iconSizes = {
  xs: 12,
  sm: 16,
  md: 20,
  lg: 24,
  xl: 32,
  '2xl': 48,
};

// Icon colors (semantic)
const iconColors = {
  primary: colors.primary[500],
  secondary: colors.secondary[500],
  success: colors.success,
  warning: colors.warning,
  error: colors.error,
  muted: colors.gray[400],
};
```

### Image Guidelines
Consistent image handling and optimization:

```typescript
// Image aspect ratios
const imageAspectRatios = {
  square: 1,           // 1:1
  landscape: 1.5,      // 3:2
  portrait: 0.75,      // 3:4
  wide: 2,             // 2:1
};

// Image sizes
const imageSizes = {
  thumbnail: 64,
  small: 128,
  medium: 256,
  large: 512,
  xlarge: 1024,
};
```

## 🌓 Theming

### Theme Provider
Centralized theme management:

```typescript
interface Theme {
  colors: ColorPalette;
  typography: Typography;
  spacing: Spacing;
  layout: Layout;
  gradients: GradientPalette;
}

const lightTheme: Theme = {
  colors: lightColors,
  typography,
  spacing,
  layout,
  gradients,
};

const darkTheme: Theme = {
  colors: darkColors,
  typography,
  spacing,
  layout,
  gradients,
};

// Theme context
const ThemeContext = createContext<{
  theme: Theme;
  isDarkMode: boolean;
  toggleTheme: () => void;
}>({
  theme: lightTheme,
  isDarkMode: false,
  toggleTheme: () => {},
});
```

### Theme Usage
How to use themes in components:

```typescript
import { useTheme } from '../hooks/useTheme';

const MyComponent = () => {
  const { theme, isDarkMode } = useTheme();
  
  return (
    <View style={{
      backgroundColor: theme.colors.background.primary,
      padding: theme.spacing[4],
    }}>
      <Text style={{
        color: theme.colors.text.primary,
        fontSize: theme.typography.fontSize.lg,
      }}>
        Hello World
      </Text>
    </View>
  );
};
```

## 📏 Usage Guidelines

### Do's and Don'ts

#### ✅ Do's
- **Use design tokens** instead of hardcoded values
- **Follow the spacing scale** for consistent layouts
- **Use semantic colors** for status and meaning
- **Test on both light and dark themes**
- **Ensure sufficient contrast** for accessibility
- **Use consistent typography** throughout the app

#### ❌ Don'ts
- **Don't use arbitrary colors** outside the design system
- **Don't mix different spacing values** in the same component
- **Don't ignore accessibility** requirements
- **Don't use too many different fonts** or sizes
- **Don't create custom components** without checking existing ones
- **Don't ignore the design system** for "quick fixes"

### Accessibility Guidelines

#### Color Contrast
- **Normal text**: Minimum 4.5:1 contrast ratio
- **Large text**: Minimum 3:1 contrast ratio
- **UI components**: Minimum 3:1 contrast ratio

#### Touch Targets
- **Minimum size**: 44x44 points
- **Recommended size**: 48x48 points
- **Spacing**: Minimum 8 points between targets

#### Typography
- **Minimum font size**: 16 points for body text
- **Line height**: Minimum 1.4 for readability
- **Font weight**: Use medium (500) or higher for important text

### Performance Guidelines

#### Images
- **Optimize images** before including in the app
- **Use appropriate formats** (WebP for web, PNG for transparency)
- **Implement lazy loading** for large image lists
- **Provide fallbacks** for failed image loads

#### Colors and Gradients
- **Use system colors** when possible for better performance
- **Limit gradient usage** to avoid performance impact
- **Test on lower-end devices** for performance validation

---

## 🎯 Implementation

### Getting Started
1. **Import the design system**:
   ```typescript
   import { colors, typography, spacing } from '../design-system';
   ```

2. **Use the theme provider**:
   ```typescript
   <ThemeProvider>
     <App />
   </ThemeProvider>
   ```

3. **Follow the guidelines** in this document

4. **Test on multiple devices** and themes

### Customization
The design system is designed to be flexible while maintaining consistency. When customizing:

1. **Extend existing tokens** rather than replacing them
2. **Document custom additions** for team reference
3. **Test customizations** across the entire app
4. **Consider accessibility** implications of changes

---

*This design system provides the foundation for creating a beautiful, consistent, and accessible user experience in the Nestled app. Follow these guidelines to maintain design quality and user satisfaction.*
