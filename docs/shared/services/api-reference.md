# 📚 API Reference

> **Complete reference for components, hooks, services, and utilities**

## 📋 Contents

1. [Components](#-components)
2. [Hooks](#-hooks)
3. [Services](#-services)
4. [Utilities](#-utilities)
5. [Types](#-types)
6. [Constants](#-constants)

## 🧩 Components

### Shared Components (`components/shared/`)

#### GradientCard
Reusable gradient card component with optional press functionality.

```typescript
interface GradientCardProps {
  gradientColors: string[];
  pressable?: boolean;
  onPress?: () => void;
  children: React.ReactNode;
  style?: ViewStyle;
  borderRadius?: number;
  padding?: number;
}

<GradientCard
  gradientColors={['#ff6b6b', '#4ecdc4']}
  pressable={true}
  onPress={() => console.log('Card pressed')}
>
  <Text>Card Content</Text>
</GradientCard>
```

**Props:**
- `gradientColors` - Array of colors for the gradient
- `pressable` - Whether the card is pressable (default: false)
- `onPress` - Callback when card is pressed
- `children` - Content to display inside the card
- `style` - Additional styles to apply
- `borderRadius` - Border radius for the card (default: 12)
- `padding` - Internal padding (default: 16)

#### StatCard
Displays metrics with icons and labels.

```typescript
interface StatCardProps {
  icon: React.ReactNode;
  label: string;
  value: string | number;
  color?: string;
  onPress?: () => void;
}

<StatCard
  icon={<Heart size={24} color="#ff6b6b" />}
  label="Date Nights"
  value={12}
  color="#ff6b6b"
  onPress={() => navigateToDateNights()}
/>
```

**Props:**
- `icon` - Icon component to display
- `label` - Text label for the stat
- `value` - Numeric or string value to display
- `color` - Color theme for the card
- `onPress` - Optional press handler

#### ScreenLayout
Consistent screen layout wrapper with header and content areas.

```typescript
interface ScreenLayoutProps {
  title?: string;
  subtitle?: string;
  showBackButton?: boolean;
  onBackPress?: () => void;
  children: React.ReactNode;
  headerRight?: React.ReactNode;
}

<ScreenLayout
  title="Date Night"
  subtitle="Plan your perfect evening"
  showBackButton={true}
  onBackPress={() => router.back()}
  headerRight={<SettingsIcon />}
>
  <DateNightContent />
</ScreenLayout>
```

### UI Components (`components/ui/`)

#### DSButton
Design system button component with multiple variants.

```typescript
interface DSButtonProps {
  title: string;
  onPress: () => void;
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost';
  size?: 'small' | 'medium' | 'large';
  disabled?: boolean;
  loading?: boolean;
  icon?: React.ReactNode;
  fullWidth?: boolean;
}

<DSButton
  title="Save Changes"
  onPress={handleSave}
  variant="primary"
  size="medium"
  icon={<Save size={16} />}
  fullWidth={true}
/>
```

#### DSTextInput
Design system text input with validation and styling.

```typescript
interface DSTextInputProps {
  label?: string;
  placeholder?: string;
  value: string;
  onChangeText: (text: string) => void;
  error?: string;
  multiline?: boolean;
  numberOfLines?: number;
  secureTextEntry?: boolean;
  keyboardType?: KeyboardTypeOptions;
  autoCapitalize?: 'none' | 'sentences' | 'words' | 'characters';
}

<DSTextInput
  label="Email Address"
  placeholder="Enter your email"
  value={email}
  onChangeText={setEmail}
  error={emailError}
  keyboardType="email-address"
  autoCapitalize="none"
/>
```

### Feature Components

#### DateNight Components (`components/date-night/`)

##### DateNight
Main date night planning component.

```typescript
interface DateNightProps {
  onIdeaSelect: (idea: DateNightIdea) => void;
  onFavoriteToggle: (idea: DateNightIdea) => void;
  favorites: DateNightIdea[];
  filters?: DateNightFilters;
}

<DateNight
  onIdeaSelect={(idea) => navigateToDetails(idea)}
  onFavoriteToggle={toggleFavorite}
  favorites={userFavorites}
  filters={{ category: 'restaurant', price: '$$' }}
/>
```

##### IdeaCard
Individual date night idea card.

```typescript
interface IdeaCardProps {
  idea: DateNightIdea;
  isFavorite: boolean;
  onPress: () => void;
  onFavoritePress: () => void;
  showFavoriteButton?: boolean;
}

<IdeaCard
  idea={restaurantIdea}
  isFavorite={isInFavorites}
  onPress={() => selectIdea(restaurantIdea)}
  onFavoritePress={() => toggleFavorite(restaurantIdea)}
  showFavoriteButton={true}
/>
```

#### Milestone Components (`components/milestones/`)

##### MilestoneCard
Displays individual milestone information.

```typescript
interface MilestoneCardProps {
  milestone: Milestone;
  onEdit?: () => void;
  onDelete?: () => void;
  editable?: boolean;
}

<MilestoneCard
  milestone={firstKissMilestone}
  onEdit={() => editMilestone(firstKissMilestone)}
  onDelete={() => deleteMilestone(firstKissMilestone.id)}
  editable={true}
/>
```

## 🪝 Hooks

### Authentication Hooks

#### useAuth
Manages user authentication state and operations.

```typescript
interface AuthState {
  isAuthenticated: boolean;
  user: User | null;
  isLoading: boolean;
  isInitialized: boolean;
}

interface AuthActions {
  login: (email: string, password: string) => Promise<void>;
  logout: () => Promise<void>;
  register: (email: string, password: string) => Promise<void>;
  resetPassword: (email: string) => Promise<void>;
}

const useAuth = (): AuthState & AuthActions => {
  // Implementation
};

// Usage
const { isAuthenticated, user, login, logout, isLoading } = useAuth();
```

### Data Hooks

#### useWeekData
Fetches and manages weekly module data.

```typescript
interface WeekData {
  weekNumber: number;
  title: string;
  description: string;
  activities: Activity[];
  completed: boolean;
  progress: number;
}

const useWeekData = (weekNumber: number) => {
  const [data, setData] = useState<WeekData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const refetch = useCallback(async () => {
    // Refetch logic
  }, [weekNumber]);

  return { data, loading, error, refetch };
};

// Usage
const { data: weekOneData, loading, error } = useWeekData(1);
```

#### useDateNight
Manages date night favorites and interactions.

```typescript
interface DateNightState {
  favorites: DateNightIdea[];
  loading: boolean;
  error: string | null;
}

interface DateNightActions {
  addToFavorites: (idea: DateNightIdea) => Promise<void>;
  removeFromFavorites: (ideaId: string) => Promise<void>;
  isFavorite: (ideaId: string) => boolean;
  refetch: () => Promise<void>;
}

const useDateNight = (): DateNightState & DateNightActions => {
  // Implementation
};

// Usage
const { 
  favorites, 
  addToFavorites, 
  removeFromFavorites, 
  isFavorite 
} = useDateNight();
```

#### useMilestones
Manages relationship milestones and timeline.

```typescript
interface Milestone {
  id: string;
  title: string;
  date: string;
  description?: string;
  type: 'first_kiss' | 'first_date' | 'anniversary' | 'custom';
  metadata?: Record<string, any>;
}

const useMilestones = () => {
  const [milestones, setMilestones] = useState<Milestone[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const addMilestone = useCallback(async (milestone: Omit<Milestone, 'id'>) => {
    // Add milestone logic
  }, []);

  const updateMilestone = useCallback(async (id: string, updates: Partial<Milestone>) => {
    // Update milestone logic
  }, []);

  const deleteMilestone = useCallback(async (id: string) => {
    // Delete milestone logic
  }, []);

  return {
    milestones,
    loading,
    error,
    addMilestone,
    updateMilestone,
    deleteMilestone,
    refetch: () => fetchMilestones()
  };
};
```

### UI Hooks

#### useTheme
Provides access to the current theme and theme switching.

```typescript
interface Theme {
  colors: ColorPalette;
  typography: Typography;
  spacing: Spacing;
  borderRadius: BorderRadius;
}

const useTheme = () => {
  const { currentTheme, toggleTheme, isDarkMode } = useGlobalTheme();
  
  return {
    theme: currentTheme,
    colors: currentTheme.colors,
    toggleTheme,
    isDarkMode
  };
};

// Usage
const { theme, colors, toggleTheme } = useTheme();
```

#### useErrorReporting
Manages error reporting and user feedback.

```typescript
interface ErrorReport {
  id: string;
  message: string;
  stack?: string;
  timestamp: string;
  userId?: string;
  context?: Record<string, any>;
}

const useErrorReporting = () => {
  const [errors, setErrors] = useState<ErrorReport[]>([]);
  
  const reportError = useCallback((error: Error, context?: Record<string, any>) => {
    // Error reporting logic
  }, []);

  const clearErrors = useCallback(() => {
    setErrors([]);
  }, []);

  return {
    errors,
    reportError,
    clearErrors,
    hasErrors: errors.length > 0
  };
};
```

## 🔧 Services

### DateNightService
Handles date night data operations.

```typescript
class DateNightService {
  /**
   * Get all date night ideas with optional filtering
   */
  static async getIdeas(filters?: DateNightFilters): Promise<DateNightIdea[]> {
    const { data, error } = await supabase
      .from('date_night_ideas')
      .select('*')
      .match(filters || {});
    
    if (error) throw new Error(error.message);
    return data;
  }

  /**
   * Get user's favorite date night ideas
   */
  static async getFavorites(userId: string): Promise<DateNightIdea[]> {
    const { data, error } = await supabase
      .from('date_night_favorites')
      .select(`
        *,
        date_night_ideas (*)
      `)
      .eq('user_id', userId);
    
    if (error) throw new Error(error.message);
    return data.map(fav => fav.date_night_ideas);
  }

  /**
   * Add idea to user's favorites
   */
  static async addToFavorites(userId: string, ideaId: string): Promise<void> {
    const { error } = await supabase
      .from('date_night_favorites')
      .insert({ user_id: userId, idea_id: ideaId });
    
    if (error) throw new Error(error.message);
  }

  /**
   * Remove idea from user's favorites
   */
  static async removeFromFavorites(userId: string, ideaId: string): Promise<void> {
    const { error } = await supabase
      .from('date_night_favorites')
      .delete()
      .eq('user_id', userId)
      .eq('idea_id', ideaId);
    
    if (error) throw new Error(error.message);
  }
}
```

### MilestoneService
Handles milestone data operations.

```typescript
class MilestoneService {
  /**
   * Get all milestones for a couple
   */
  static async getMilestones(coupleId: string): Promise<Milestone[]> {
    const { data, error } = await supabase
      .from('milestones')
      .select('*')
      .eq('couple_id', coupleId)
      .order('date', { ascending: true });
    
    if (error) throw new Error(error.message);
    return data;
  }

  /**
   * Create a new milestone
   */
  static async createMilestone(milestone: Omit<Milestone, 'id'>): Promise<Milestone> {
    const { data, error } = await supabase
      .from('milestones')
      .insert(milestone)
      .select()
      .single();
    
    if (error) throw new Error(error.message);
    return data;
  }

  /**
   * Update an existing milestone
   */
  static async updateMilestone(id: string, updates: Partial<Milestone>): Promise<Milestone> {
    const { data, error } = await supabase
      .from('milestones')
      .update(updates)
      .eq('id', id)
      .select()
      .single();
    
    if (error) throw new Error(error.message);
    return data;
  }

  /**
   * Delete a milestone
   */
  static async deleteMilestone(id: string): Promise<void> {
    const { error } = await supabase
      .from('milestones')
      .delete()
      .eq('id', id);
    
    if (error) throw new Error(error.message);
  }
}
```

### WeekService
Handles weekly module data operations.

```typescript
class WeekService {
  /**
   * Get specific week data
   */
  static async getWeekData(weekNumber: number): Promise<WeekData> {
    const { data, error } = await supabase
      .from('weekly_modules')
      .select('*')
      .eq('week_number', weekNumber)
      .single();
    
    if (error) throw new Error(error.message);
    return data;
  }

  /**
   * Get all weeks with completion status
   */
  static async getAllWeeks(coupleId: string): Promise<WeekData[]> {
    const { data, error } = await supabase
      .from('weekly_modules')
      .select(`
        *,
        week_completions!inner(completed, completed_at)
      `)
      .eq('week_completions.couple_id', coupleId);
    
    if (error) throw new Error(error.message);
    return data;
  }

  /**
   * Mark week as completed
   */
  static async markWeekCompleted(coupleId: string, weekNumber: number): Promise<void> {
    const { error } = await supabase
      .from('week_completions')
      .upsert({
        couple_id: coupleId,
        week_number: weekNumber,
        completed: true,
        completed_at: new Date().toISOString()
      });
    
    if (error) throw new Error(error.message);
  }
}
```

## 🛠️ Utilities

### Storage Utilities (`utils/`)

#### secureStorage
Secure data storage using Expo SecureStore.

```typescript
class SecureStorage {
  /**
   * Store data securely
   */
  static async setItem(key: string, value: string): Promise<void> {
    try {
      await SecureStore.setItemAsync(key, value);
    } catch (error) {
      throw new Error(`Failed to store ${key}: ${error.message}`);
    }
  }

  /**
   * Retrieve data securely
   */
  static async getItem(key: string): Promise<string | null> {
    try {
      return await SecureStore.getItemAsync(key);
    } catch (error) {
      throw new Error(`Failed to retrieve ${key}: ${error.message}`);
    }
  }

  /**
   * Remove data securely
   */
  static async removeItem(key: string): Promise<void> {
    try {
      await SecureStore.deleteItemAsync(key);
    } catch (error) {
      throw new Error(`Failed to remove ${key}: ${error.message}`);
    }
  }
}
```

#### validation
Input validation utilities.

```typescript
class Validation {
  /**
   * Validate email format
   */
  static isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  /**
   * Validate password strength
   */
  static isValidPassword(password: string): { valid: boolean; errors: string[] } {
    const errors: string[] = [];
    
    if (password.length < 8) {
      errors.push('Password must be at least 8 characters long');
    }
    
    if (!/[A-Z]/.test(password)) {
      errors.push('Password must contain at least one uppercase letter');
    }
    
    if (!/[a-z]/.test(password)) {
      errors.push('Password must contain at least one lowercase letter');
    }
    
    if (!/\d/.test(password)) {
      errors.push('Password must contain at least one number');
    }
    
    return {
      valid: errors.length === 0,
      errors
    };
  }

  /**
   * Validate date format
   */
  static isValidDate(dateString: string): boolean {
    const date = new Date(dateString);
    return date instanceof Date && !isNaN(date.getTime());
  }
}
```

### Theme Utilities

#### colors
Color system and theme management.

```typescript
interface ColorPalette {
  primary: string;
  secondary: string;
  accent: string;
  background: string;
  surface: string;
  text: string;
  textSecondary: string;
  border: string;
  error: string;
  warning: string;
  success: string;
}

interface Theme {
  light: ColorPalette;
  dark: ColorPalette;
}

const colors: Theme = {
  light: {
    primary: '#ff6b6b',
    secondary: '#4ecdc4',
    accent: '#45b7d1',
    background: '#ffffff',
    surface: '#f8f9fa',
    text: '#2c3e50',
    textSecondary: '#7f8c8d',
    border: '#e9ecef',
    error: '#e74c3c',
    warning: '#f39c12',
    success: '#27ae60'
  },
  dark: {
    primary: '#ff6b6b',
    secondary: '#4ecdc4',
    accent: '#45b7d1',
    background: '#1a1a1a',
    surface: '#2d2d2d',
    text: '#ffffff',
    textSecondary: '#b0b0b0',
    border: '#404040',
    error: '#e74c3c',
    warning: '#f39c12',
    success: '#27ae60'
  }
};
```

## 📝 Types

### Core Types

```typescript
// User and Authentication
interface User {
  id: string;
  email: string;
  name: string;
  avatar?: string;
  coupleId?: string;
  createdAt: string;
  updatedAt: string;
}

interface AuthState {
  isAuthenticated: boolean;
  user: User | null;
  isLoading: boolean;
  isInitialized: boolean;
}

// Date Night
interface DateNightIdea {
  id: string;
  title: string;
  description: string;
  category: 'restaurant' | 'activity' | 'experience';
  price: '$' | '$$' | '$$$' | '$$$$';
  location?: string;
  duration?: string;
  imageUrl?: string;
  tags: string[];
  rating?: number;
}

interface DateNightFilters {
  category?: string;
  price?: string;
  location?: string;
  tags?: string[];
}

// Milestones
interface Milestone {
  id: string;
  coupleId: string;
  title: string;
  date: string;
  description?: string;
  type: 'first_kiss' | 'first_date' | 'anniversary' | 'custom';
  metadata?: Record<string, any>;
  createdAt: string;
  updatedAt: string;
}

// Weekly Modules
interface WeekData {
  weekNumber: number;
  title: string;
  description: string;
  activities: Activity[];
  completed: boolean;
  progress: number;
  unlockedAt?: string;
  completedAt?: string;
}

interface Activity {
  id: string;
  title: string;
  description: string;
  type: 'quiz' | 'discussion' | 'activity' | 'reflection';
  completed: boolean;
  points: number;
}
```

## 📊 Constants

### App Constants

```typescript
// Feature Flags
export const FEATURE_FLAGS = {
  ENABLE_MILESTONES: true,
  ENABLE_PHOTO_UPLOAD: true,
  ENABLE_NOTIFICATIONS: true,
  ENABLE_ANALYTICS: true
} as const;

// API Endpoints
export const API_ENDPOINTS = {
  AUTH: {
    LOGIN: '/auth/login',
    REGISTER: '/auth/register',
    LOGOUT: '/auth/logout',
    REFRESH: '/auth/refresh'
  },
  DATE_NIGHT: {
    IDEAS: '/date-night/ideas',
    FAVORITES: '/date-night/favorites'
  },
  MILESTONES: {
    LIST: '/milestones',
    CREATE: '/milestones',
    UPDATE: '/milestones/:id',
    DELETE: '/milestones/:id'
  }
} as const;

// Storage Keys
export const STORAGE_KEYS = {
  AUTH_TOKEN: 'auth_token',
  USER_PREFERENCES: 'user_preferences',
  ONBOARDING_COMPLETED: 'onboarding_completed',
  THEME_PREFERENCE: 'theme_preference'
} as const;

// Validation Rules
export const VALIDATION_RULES = {
  EMAIL: {
    MIN_LENGTH: 5,
    MAX_LENGTH: 254,
    PATTERN: /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  },
  PASSWORD: {
    MIN_LENGTH: 8,
    MAX_LENGTH: 128,
    REQUIRE_UPPERCASE: true,
    REQUIRE_LOWERCASE: true,
    REQUIRE_NUMBER: true,
    REQUIRE_SPECIAL_CHAR: false
  },
  NAME: {
    MIN_LENGTH: 2,
    MAX_LENGTH: 50,
    PATTERN: /^[a-zA-Z\s]+$/
  }
} as const;
```

---

## 🔍 Usage Examples

### Complete Component Example

```typescript
import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { GradientCard } from '../components/shared/GradientCard';
import { useDateNight } from '../hooks/useDateNight';
import { useTheme } from '../hooks/useTheme';

const DateNightScreen = () => {
  const { theme, colors } = useTheme();
  const { favorites, addToFavorites, removeFromFavorites, isFavorite } = useDateNight();
  const [selectedIdea, setSelectedIdea] = useState<DateNightIdea | null>(null);

  const handleIdeaPress = (idea: DateNightIdea) => {
    setSelectedIdea(idea);
  };

  const handleFavoriteToggle = (idea: DateNightIdea) => {
    if (isFavorite(idea.id)) {
      removeFromFavorites(idea.id);
    } else {
      addToFavorites(idea);
    }
  };

  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      <Text style={[styles.title, { color: colors.text }]}>
        Date Night Ideas
      </Text>
      
      {favorites.map((idea) => (
        <GradientCard
          key={idea.id}
          gradientColors={[colors.primary, colors.secondary]}
          pressable={true}
          onPress={() => handleIdeaPress(idea)}
        >
          <Text style={styles.ideaTitle}>{idea.title}</Text>
          <Text style={styles.ideaDescription}>{idea.description}</Text>
        </GradientCard>
      ))}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  ideaTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#ffffff',
  },
  ideaDescription: {
    fontSize: 14,
    color: '#ffffff',
    opacity: 0.9,
    marginTop: 4,
  },
});

export default DateNightScreen;
```

---

*This API reference provides comprehensive documentation for all components, hooks, services, and utilities in the Nestled app. For implementation details and examples, refer to the source code and other documentation files.*
