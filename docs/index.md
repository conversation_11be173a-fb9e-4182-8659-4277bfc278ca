# Everlasting Us Documentation

Technical documentation for the relationship app built with React Native, TypeScript, and Supabase. Organized by user journeys.

## User Journeys

### [Onboarding](./journeys/onboarding/)
Authentication, couple pairing, and profile setup.
- `useAuth`, `useUserProfile`, `useCouplePairing`

### [Daily](./journeys/daily/)
Daily questions, streaks, and weekly challenges.
- [Daily Questions System](./journeys/daily/daily-questions.md)
- `useDailyQuestions`, `useStreakData`

### [Activities](./journeys/activities/)
Date nights, games, and surprise functionality.
- [Date Night System](./journeys/activities/date-night-system.md)
- [Favorites System](./journeys/activities/favorites-system.md)
- [Games Hub](./journeys/activities/games-hub.md)
- [Match Game](./journeys/activities/match-game.md)
- `useDateNightIdeasSupabase`, `useMatchGame`, `useFavorites`

### [Memories](./journeys/memories/)
Timeline, photos, milestones, and scrapbook.
- `useOriginStoryData`, `useTimeline`, `useMilestones`

### [Planning](./journeys/planning/)
User preferences and goal setting.
- `useUserPreferences`, `useAppSettings`

### [Progress](./journeys/progress/)
Points, analytics, and achievements.
- `usePointsSystemSupabase`, `useUserEvents`, `useEngagementSystem`

## Shared Resources

### [Components](./shared/components/)
Reusable UI components with centralized design system.
- No hardcoded colors, spacing, or layouts
- All values use design tokens
- TypeScript with JSDoc documentation

```typescript
// ✅ Correct - use design system
import { Button, Colors, Spacing } from 'src/shared/components';

// ❌ Never hardcode values
const badStyle = { backgroundColor: '#9CAF88', padding: 16 };
```

### [Hooks](./shared/hooks/)
React hooks for common functionality.
- Authentication and session management
- Data fetching with caching
- UI interactions with analytics

### [Services](./shared/services/)
Business logic and API integrations.
- Supabase client with TypeScript types
- Real-time synchronization
- Image storage and optimization
- Analytics and performance monitoring

### [Types](./shared/types/)
TypeScript definitions.
- Database schema (auto-generated)
- API contracts
- Component props

### [Utils](./shared/utils/)
Helper functions and utilities.
- Date/time handling
- Validation functions
- Performance utilities
- Security helpers

## 🏗️ **System Documentation**

### **Development & Setup**
- [Developer Guide](./system/developer-guide.md) - Complete development setup
- [Build Guide](./system/build-guide.md) - Build and deployment
- [Install Dependencies](./system/install-dependencies.md) - Required packages

### **Code Quality & Standards**
- [Code Standards](./system/code-standards.md) - Coding conventions
- [Refactoring Guide](./system/refactoring-guide.md) - Code organization
- [Performance Optimization](./system/performance-optimization.md) - Performance best practices

### **Security & Production**
- [Security Guide](./system/security.md) - Security implementation
- [Secure Keys](./system/secure-keys.md) - Key management
- [Production Readiness](./system/production-readiness-checklist.md) - Production checklist

### **Maintenance & Support**
- [Error Reporting](./system/error-reporting-guide.md) - Error handling
- [Troubleshooting](./system/troubleshooting.md) - Common issues
- [FAQ](./system/faq.md) - Frequently asked questions
- [Migration Status](./system/migration-status.md) - Feature migration tracking

### **App Store & Distribution**
- [App Store Metadata](./system/app-store-metadata.md) - Store listing information

## 🗄️ **Database Documentation**

Complete database documentation is maintained separately:
- **[Database README](../database/README.md)** - Database documentation index
- **[Schema Reference](../database/docs/schema-reference.md)** - Table overview
- **[Architecture Rationale](../database/docs/architecture-rationale.md)** - Design decisions

## 🎨 **Assets Documentation**

Asset management and guidelines:
- **[Assets Guide](./shared/utils/assets-guide.md)** - Asset requirements and usage
- **[Assets Directory](../src/assets/README.md)** - Asset organization

---

## 📋 **Documentation Structure**

This documentation follows the **target user-journey architecture**:

```
docs/
├── journeys/           # User journey documentation
│   ├── onboarding/     # Getting started experience
│   ├── daily/          # Daily engagement features
│   ├── activities/     # Games and activities
│   ├── memories/       # Memory and milestone features
│   ├── planning/       # Planning and goal setting
│   └── progress/       # Progress tracking and analytics
├── shared/             # Technical documentation
│   ├── components/     # UI components and design system
│   ├── hooks/          # React hooks documentation
│   ├── services/       # Business logic and APIs
│   ├── types/          # TypeScript definitions
│   └── utils/          # Utilities and helpers
└── system/             # System and infrastructure docs
```

This structure maps to the **ideal future src/ organization** and helps developers understand both current implementation and target architecture.

---
*Documentation organized by user journeys for better developer experience*
