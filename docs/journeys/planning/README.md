# Planning Journey

User preferences, goal setting, and activity planning.

## Features

- **Date Night Planning** - Browse and plan date experiences
- **Meal Planning** - Recipe exploration and meal coordination
- **Activity Scheduling** - Weekly challenges and game sessions
- **Goal Setting** - Relationship milestones and progress tracking

## Key Components

### `src/journeys/planning/useUserPreferences.ts`
Hook for managing user and couple preferences.

### `src/journeys/planning/useAppSettings.ts`
Application settings and configuration management.

## Database Tables
- `date_night_ideas_global` - System-provided date ideas
- `date_night_ideas_user` - User-created date ideas
- `meal_ideas_global` - System-provided meal ideas
- `meal_ideas_users` - User meal planning
- `weekly_data` - Weekly challenge progress
- `user_preferences` - Planning preferences and filters

## Usage

```typescript
import {
  useUserPreferences,
  useAppSettings
} from 'src/journeys/planning';

const { preferences, updatePreferences } = useUserPreferences();
const { settings, updateSettings } = useAppSettings();
```
