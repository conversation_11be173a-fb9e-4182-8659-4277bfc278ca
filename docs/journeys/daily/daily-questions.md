# Daily Questions System

## Overview
Daily question delivery system with partner interactions, streak tracking, and notifications.

## Database Tables
- `daily_questions` - Question library
- `daily_question_responses` - User responses
- `daily_question_schedule` - Question assignments
- `daily_question_reactions` - Partner reactions
- `daily_question_comments` - Partner comments

## Features
- One question per day with no repeats
- Partner response viewing and reactions
- Streak tracking with milestones
- Morning reminders and evening prompts
- Achievement system for engagement

## Components

### `src/journeys/daily/useDailyQuestions.ts`
Main hook for question management and responses.

### `src/journeys/daily/useStreakData.ts`
Hook for streak tracking and calculations.

## Usage

```typescript
import { useDailyQuestions, useStreakData } from 'src/journeys/daily';

const {
  todaysQuestion,
  submitResponse,
  userResponse,
  partnerResponse,
  isLoading
} = useDailyQuestions();

const {
  currentStreak,
  longestStreak,
  totalAnswered
} = useStreakData();
```

## Question Categories
- Deep Questions - Introspective and growth-focused
- Fun Questions - Light-hearted and playful
- Funny Questions - Humor and laughter
- Memories Questions - Relationship history
- Dreams Questions - Future aspirations
- Gratitude Questions - Appreciation and thankfulness

## Features
- 30-minute edit window after submission
- Partner reactions and comments
- Streak tracking with milestones
- Morning and evening notifications
- Achievement system
