# Daily Journey

Daily questions, streaks, and weekly challenges for relationship engagement.

## Features

- **Daily Questions** - One thoughtful question per day with partner interactions
- **Streak Tracking** - Consecutive day participation with milestones
- **Weekly Challenges** - Structured relationship building activities
- **Notifications** - Morning reminders and evening prompts

## Key Components

### `src/journeys/daily/useDailyQuestions.ts`
Hook for daily question management, responses, and partner interactions.

### `src/journeys/daily/useStreakData.ts`
Hook for streak tracking, calculations, and milestone achievements.

### `src/journeys/daily/useDailyQuestionsNotifications.ts`
Hook for notification settings and delivery management.

## Database Tables
- `daily_questions` - Question library with categories
- `daily_question_responses` - User responses and partner interactions
- `daily_question_schedule` - Question assignments and timing
- `daily_question_reactions` - Partner reactions to responses
- `daily_question_comments` - Partner comments and conversations

## Usage

```typescript
import { 
  useDailyQuestions, 
  useStreakData,
  useDailyQuestionsNotifications 
} from 'src/journeys/daily';

const { 
  todaysQuestion, 
  submitResponse, 
  userResponse,
  partnerResponse 
} = useDailyQuestions();

const { 
  currentStreak, 
  longestStreak, 
  totalAnswered 
} = useStreakData();

const { 
  notificationSettings, 
  updateSettings 
} = useDailyQuestionsNotifications();
```

## Question Categories
- Deep Questions - Introspective and growth-focused
- Fun Questions - Light-hearted and playful
- Funny Questions - Humor and laughter
- Memories Questions - Relationship history
- Dreams Questions - Future aspirations
- Gratitude Questions - Appreciation and thankfulness
