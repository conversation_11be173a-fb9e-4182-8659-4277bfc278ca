# Memories Journey

Timeline, photos, milestones, and scrapbook for relationship memories.

## Features

- **Origin Story** - How we met narrative creation
- **Timeline** - Chronological relationship events
- **Photo Management** - Upload and organize couple photos
- **Milestones** - Track anniversaries and special moments
- **Scrapbook** - Digital memory compilation

## Key Components

### `src/journeys/memories/useOriginStoryData.ts`
Hook for creating and managing the couple's origin story.

### `src/journeys/memories/useTimeline.ts`
Timeline event management and chronological display.

### `src/journeys/memories/useMilestones.ts`
Milestone tracking and anniversary management.

## Database Tables
- `origin_story` - Couple origin stories with photos
- `timeline_events` - Chronological relationship events
- `timeline_photos` - Photos associated with events
- `scrapbook` - Memory collections and entries
- `milestone_templates` - Available milestone types
- `couple_milestones` - Couple-specific milestone progress

## Usage

```typescript
import {
  useOriginStoryData,
  useTimeline,
  useMilestones
} from 'src/journeys/memories';

const { story, updateStory } = useOriginStoryData();
const { events, addEvent } = useTimeline();
const { milestones, trackMilestone } = useMilestones();
```
