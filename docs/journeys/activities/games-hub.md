# Games Hub

## Overview
Interactive relationship games organized by category and difficulty.

## Features
- 30+ relationship games across 3 categories
- Filtering by category and difficulty
- Real-time search
- Game metadata (duration, players, points)

## Components

### `src/journeys/activities/GamesHub.tsx`
Main games interface with filtering and search.

### `app/(tabs)/games.tsx`
Games tab screen hosting the GamesHub component.

## Game Categories
- **Getting to Know You** (12 games)
- **Communication** (12 games)
- **Fun & Active** (3 games)

## Difficulty Levels
- Easy, Medium, Hard

## Usage

```typescript
import { GamesHub } from 'src/journeys/activities';

// Game data structure
interface Game {
  id: string;
  title: string;
  category: 'getting-to-know' | 'communication' | 'fun';
  difficulty: 'easy' | 'medium' | 'hard';
  duration: string;
  players: number;
  points: number;
}
```

## Navigation
Games link directly to weekly modules via the `route` property.
