# Activities Journey

Interactive activities and games for couples.

## Components

### Date Night System
Date night ideas with favorites and composite ID system.
- [Date Night System](./date-night-system.md)

### Favorites System  
Unified favorites system for all content types.
- [Favorites System](./favorites-system.md)

### Games Hub
Interactive relationship games with filtering and search.
- [Games Hub](./games-hub.md)

### Match Game
Database-driven match game with question management.
- [Match Game](./match-game.md)

## Key Hooks

```typescript
import { 
  useDateNightIdeasSupabase,
  useDateNightFavorites,
  useMatchGame,
  useFavorites 
} from 'src/journeys/activities';
```

## Architecture

Activities are organized around content types:
- Date night ideas (global and user-generated)
- Interactive games with categories and difficulty levels
- Favorites system with optimistic updates
- Surprise functionality for activity recommendations
