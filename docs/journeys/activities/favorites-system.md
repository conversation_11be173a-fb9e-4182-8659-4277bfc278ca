# Favorites System

## Overview

Unified favorites system for all content types with optimistic updates and offline support.

## Setup

### Database
Run the migration in Supabase:
```sql
\i supabase-favorites-migration.sql
```

### App Provider
```tsx
import { FavoritesProvider } from 'src/shared/contexts/FavoritesContext';

export default function App() {
  return (
    <AuthProvider>
      <FavoritesProvider>
        {/* App content */}
      </FavoritesProvider>
    </AuthProvider>
  );
}
```

## Usage

```tsx
import { useFavorites } from 'src/shared/hooks/useFavorites';

const MealCard = ({ meal }) => {
  const { toggleFavorite, isFavorited, isLoading } = useFavorites('meals');

  return (
    <HeartToggle
      isFavorited={isFavorited(meal.id)}
      onToggle={() => toggleFavorite(meal.id)}
      disabled={isLoading(meal.id)}
    />
  );
};
```

## Available Hooks

- `useFavorites(contentType)` - Base hook
- `useDateNightFavorites()` - Date night ideas
- `useContentFavorites(contentType)` - Generic content

## API

```tsx
const {
  toggleFavorite,
  isFavorited,
  isLoading,
  favorites
} = useFavorites('meals');

// Toggle favorite
await toggleFavorite(itemId);

// Check if favorited
const favorited = isFavorited(itemId);
```

## Troubleshooting

**Provider Error**: Ensure `FavoritesProvider` wraps your app
**Sync Issues**: Verify all components use same content type
**Database Errors**: Run migration script and check RLS policies
