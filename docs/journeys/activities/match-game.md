# Match Game System

## Overview
Database-driven match game with 400+ questions across 10 categories and 3 difficulty levels.

## Database Tables
- `match_game_questions` - Game questions
- `match_game_user_answers` - User responses
- `match_game_sessions` - Game sessions
- `match_game_results` - Results and statistics

## Components

### `src/journeys/activities/useMatchGame.ts`
Hook for game state management and question handling.

### `src/journeys/activities/MatchGame.tsx`
Main game component with question display and answer collection.

## Usage

```typescript
import { useMatchGame } from 'src/journeys/activities';

const {
  loadQuestions,
  saveAnswer,
  createSession,
  isLoading,
  error
} = useMatchGame();

// Load questions for a session
const questions = await loadQuestions({
  count: 10,
  balanced: true
});

// Save user answer
await saveAnswer({
  questionId,
  answer: userAnswer
});
```

## Database Migration

Run the Supabase migrations to set up the match game tables:
```bash
supabase db push
```
