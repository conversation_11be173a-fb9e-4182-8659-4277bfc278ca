# Date Night Favorites System

## Overview

Date night ideas exist in two tables with overlapping IDs:
- `date_night_ideas_global` - System ideas (ID: 1, 2, 3...)
- `date_night_ideas_user` - User ideas (ID: 1, 2, 3...)

The favorites system uses composite IDs to distinguish between sources.

## Composite ID System

### Format
- Global ideas: `"global:123"`
- User ideas: `"user:456"`

### Benefits
- Unique identification across tables
- No changes to existing favorites table
- Backward compatible with existing data

## Implementation

### Service Layer
`src/journeys/activities/dateNightIdeasService.ts`
- Creates composite IDs (`"global:123"`, `"user:456"`)
- Loads data from both tables
- Handles CRUD operations

### Favorites Hook
`src/journeys/activities/useDateNightFavorites.ts`
- Works with composite ID system
- Handles both global and user ideas
- Error handling and loading states

### Component Integration
`src/journeys/activities/DateNight.tsx`
- Uses composite ID system
- Integrates with favorites hook

## Usage

```typescript
import { useDateNightFavorites } from 'src/journeys/activities';

const { favorites, toggleFavorite, isLoading } = useDateNightFavorites();

// Toggle favorite for global idea
toggleFavorite('global:123');

// Toggle favorite for user idea
toggleFavorite('user:456');
```

## Testing

1. Navigate to date night screen
2. Favorite both global and user ideas
3. Verify persistence across app restarts
4. Check error handling for network issues
