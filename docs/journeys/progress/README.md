# Progress Journey

Points, analytics, achievements, and engagement tracking.

## Features

- **Activity Tracking** - Daily question streaks and participation logging
- **Achievement System** - Points, levels, and badge unlocking
- **Analytics** - Engagement trends and relationship insights
- **Goal Monitoring** - Milestone tracking and completion celebration

## Key Components

### `src/journeys/progress/usePointsSystemSupabase.ts`
Hook for points calculation and level progression.

### `src/journeys/progress/useUserEvents.ts`
User activity tracking and event logging.

### `src/journeys/progress/useEngagementSystem.ts`
Engagement analytics and pattern analysis.

## Database Tables
- `user_events` - Central activity tracking
- `points_system` - User points, levels, achievements
- `daily_question_responses` - Daily engagement tracking
- `couple_milestones` - Milestone completion status
- `error_stats` - System health and usage analytics

## Usage

```typescript
import {
  usePointsSystemSupabase,
  useUserEvents,
  useEngagementSystem
} from 'src/journeys/progress';

const { points, level, addPoints } = usePointsSystemSupabase();
const { logEvent, getEvents } = useUserEvents();
const { analytics, insights } = useEngagementSystem();
```
