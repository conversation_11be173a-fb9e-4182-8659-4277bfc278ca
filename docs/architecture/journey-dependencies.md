# Journey Dependencies Architecture

## Overview

This document defines the dependency relationships between user journeys in the Everlasting Us app. It ensures clean architecture boundaries and prevents circular dependencies.

## Dependency Graph

```mermaid
graph TD
    %% Core Dependencies
    A[Onboarding Journey] --> S[Shared Resources]
    B[Daily Journey] --> S
    C[Activities Journey] --> S
    D[Memories Journey] --> S
    E[Planning Journey] --> S
    F[Progress Journey] --> S
    
    %% Cross-Journey Dependencies (Limited)
    B --> A
    C --> A
    D --> A
    E --> A
    F --> A
    
    %% Shared Resources
    S --> EXT[External Libraries]
    S --> DB[(Database)]
    S --> API[APIs & Services]
    
    %% App Layer (can use all journeys)
    APP[App Router] --> A
    APP --> B
    APP --> C
    APP --> D
    APP --> E
    APP --> F
```

## Dependency Rules

### ✅ **Allowed Dependencies**

#### **All Journeys → Shared Resources**
- All journeys can import from `src/shared/`
- Shared resources provide common functionality
- No restrictions on shared imports

#### **All Journeys → Onboarding Journey**
- All journeys can import authentication from onboarding
- Limited to: `useAuth`, `useUserProfile`, `AuthContext`
- Reason: Authentication is foundational to all features

#### **App Router → All Journeys**
- App router screens can import from any journey
- Enables composition of journey features
- No architectural restrictions at app level

### ❌ **Forbidden Dependencies**

#### **Journey → Journey (Except Onboarding)**
- Daily journey cannot import from Activities, Memories, Planning, or Progress
- Activities journey cannot import from Daily, Memories, Planning, or Progress
- And so on...
- **Reason**: Maintains clear separation of concerns

#### **Shared → Journeys**
- Shared code cannot import from any journey-specific code
- **Reason**: Keeps shared code truly reusable and independent

#### **Circular Dependencies**
- No journey can create circular import chains
- **Reason**: Prevents build issues and maintains clean architecture

## Journey Responsibilities

### 🚀 **Onboarding Journey**
**Purpose**: Foundation for all other journeys
- User authentication and session management
- Couple pairing and relationship setup
- Initial app configuration
- Profile creation and management

**Exports for Other Journeys**:
- `useAuth()` - Authentication state and methods
- `useUserProfile()` - User and couple profile data
- `AuthContext` - Authentication context provider

### 📅 **Daily Journey**
**Purpose**: Daily engagement and habit building
- Daily questions and responses
- Streak tracking and gamification
- Weekly challenges and progress
- Daily notifications and reminders

**Dependencies**:
- `useAuth()` from Onboarding (for user identification)
- `useUserProfile()` from Onboarding (for couple data)
- Shared utilities and components

### 🎮 **Activities Journey**
**Purpose**: Fun activities and games for couples
- Date night ideas and planning
- Match games and couple activities
- Meal planning and recipes
- Favorites and content curation

**Dependencies**:
- `useAuth()` from Onboarding (for user identification)
- `useUserProfile()` from Onboarding (for couple preferences)
- Shared utilities and components

### 💕 **Memories Journey**
**Purpose**: Capturing and celebrating relationship milestones
- Origin story creation and editing
- Timeline events and photo management
- Milestone tracking and achievements
- Scrapbook organization and sharing

**Dependencies**:
- `useAuth()` from Onboarding (for user identification)
- `useUserProfile()` from Onboarding (for couple data)
- Shared utilities and components

### 📋 **Planning Journey**
**Purpose**: Goal setting and future planning
- User preferences and settings
- Relationship goal setting
- Activity scheduling and planning
- Configuration management

**Dependencies**:
- `useAuth()` from Onboarding (for user identification)
- `useUserProfile()` from Onboarding (for couple preferences)
- Shared utilities and components

### 📈 **Progress Journey**
**Purpose**: Analytics and achievement tracking
- Points system and level progression
- Engagement analytics and insights
- Achievement badges and rewards
- Performance monitoring

**Dependencies**:
- `useAuth()` from Onboarding (for user identification)
- `useUserProfile()` from Onboarding (for couple data)
- Shared utilities and components

## Shared Resources

### 🔧 **Shared Components**
- UI components and design system
- Reusable React components
- Layout and navigation components
- Form components and inputs

### 🪝 **Shared Hooks**
- Common React hooks
- Data fetching utilities
- UI interaction hooks
- Performance monitoring hooks

### ⚙️ **Shared Services**
- API clients and data services
- Database connection and queries
- External service integrations
- Caching and storage services

### 📝 **Shared Types**
- TypeScript type definitions
- API response interfaces
- Database schema types
- Common data structures

### 🛠️ **Shared Utils**
- Utility functions and helpers
- Date and time utilities
- Validation and formatting
- Constants and configuration

## Import Patterns

### ✅ **Correct Import Examples**

```typescript
// ✅ Journey importing from shared
import { Button } from '../../../shared/components/Button';
import { useApi } from '../../../shared/hooks/useApi';

// ✅ Journey importing from onboarding
import { useAuth } from '../onboarding/useAuth';
import { useUserProfile } from '../onboarding/useUserProfile';

// ✅ App router importing from journeys
import { useDailyQuestions } from '../src/journeys/daily/useDailyQuestions';
import { useDateNightIdeas } from '../src/journeys/activities/useDateNightIdeas';
```

### ❌ **Incorrect Import Examples**

```typescript
// ❌ Journey importing from another journey (except onboarding)
import { useDateNightIdeas } from '../activities/useDateNightIdeas'; // From daily journey

// ❌ Shared importing from journey
import { useAuth } from '../journeys/onboarding/useAuth'; // From shared code

// ❌ Circular dependency
import { useProgress } from '../progress/useProgress'; // If progress also imports from this journey
```

## Validation

### **ESLint Rules**
The `.eslintrc.journey.js` configuration enforces these dependency rules:
- Prevents cross-journey imports (except onboarding)
- Prevents shared code from importing journeys
- Allows app router to import from anywhere

### **Build-Time Checks**
- TypeScript compilation will fail on circular dependencies
- Import path validation in CI/CD pipeline
- Dependency graph analysis in health checks

### **Runtime Monitoring**
- Journey analytics track cross-journey usage
- Performance monitoring identifies dependency bottlenecks
- Error tracking highlights architectural violations

## Benefits

1. **🎯 Clear Separation**: Each journey has a single responsibility
2. **🔧 Maintainable**: Changes in one journey don't affect others
3. **🚀 Scalable**: New journeys can be added without refactoring
4. **🧪 Testable**: Journeys can be tested in isolation
5. **📦 Reusable**: Shared code is truly reusable across journeys
6. **🔍 Debuggable**: Clear dependency chain makes debugging easier

## Migration Notes

- Existing code may violate these rules initially
- Gradual refactoring to align with dependency rules
- Use shared resources for common functionality
- Extract reusable logic to shared utilities
- Keep journey-specific logic within journey boundaries
