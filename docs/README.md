# 📱 Nestled - Developer Documentation

> **Relationship growth app built with React Native, Expo, and Supabase**

## 🚀 Quick Start

### Prerequisites
- Node.js 18+
- Expo CLI (`npm install -g @expo/cli`)
- iOS Simulator (Mac) or Android Studio

### Setup
```bash
git clone <repository-url>
cd nestled
npm install
npm run dev
```

Scan QR code with Expo Go app or press 'i' for iOS simulator.

## 📚 Documentation

### 🏗️ **Setup & Architecture**
- **[Developer Guide](./developer-guide.md)** - Setup, architecture, and workflow
- **[API Reference](./api-reference.md)** - Components, hooks, and services
- **[Schema Architecture](./schema-architecture-rationale.md)** - Database design
- **[Migration Status](./migration-status.md)** - Feature migration tracking

### 🎨 **Design**
- **[Design System](./design-system.md)** - Colors, typography, components
- **[Component Library](./component-library.md)** - UI components (planned)
- **[UI Patterns](./ui-patterns.md)** - Best practices (planned)

### 🔧 **Development**
- **[Build Guide](./build-guide.md)** - Production builds and deployment
- **[Testing Guide](./testing-guide.md)** - Testing strategies (planned)
- **[Performance Guide](./performance-guide.md)** - Optimization (planned)

### 🚨 **Support**
- **[Troubleshooting](./troubleshooting.md)** - Common issues and solutions
- **[Error Handling](./error-handling.md)** - Error boundaries and logging (planned)
- **[FAQ](./faq.md)** - Frequently asked questions

### 📱 **Platform-Specific**
- **[iOS Guide](./ios-guide.md)** - iOS-specific setup, testing, and deployment (planned)
- **[Android Guide](./android-guide.md)** - Android-specific setup, testing, and deployment (planned)
- **[Web Guide](./web-guide.md)** - Web platform considerations and optimization (planned)

## 🎯 **Key Features**

### Core Functionality
- **Weekly Modules** - 12 relationship-building activities
- **Date Night Planning** - Restaurant recommendations and activity suggestions
- **Milestone Tracking** - Relationship timeline and memories
- **Photo Scrapbook** - Visual memory collection
- **Points System** - Gamified relationship growth

### Technical Highlights
- **React Native + Expo** - Cross-platform mobile development
- **Supabase Backend** - Real-time database and authentication
- **TypeScript** - Full type safety throughout
- **Modular Architecture** - Reusable components and hooks
- **Error Handling** - Comprehensive error boundaries and reporting

## 🏃‍♂️ **Common Tasks**

### Daily Development
```bash
npm run dev              # Start development server
npm run lint             # Check code quality
npm run build:web        # Build for web
```

### Testing
```bash
npm run test             # Run all tests
npm run test:watch       # Watch mode
npm run test:coverage    # Coverage report
```

### Production
```bash
npm run build:production # Build for app stores
npm run security:check   # Security audit
```

## 📞 **Getting Help**

### Documentation Issues
- Check the [FAQ](./faq.md) first
- Search existing [GitHub Issues](https://github.com/your-repo/issues)
- Review [Common Issues](./troubleshooting.md)

### Development Questions
- **Architecture**: See [Developer Guide](./developer-guide.md)
- **Components**: See [API Reference](./api-reference.md)
- **API**: See [API Reference](./api-reference.md)

### Emergency Issues
- **Build Failures**: See [Build Guide](./build-guide.md#troubleshooting)
- **Runtime Errors**: See [Troubleshooting Guide](./troubleshooting.md)
- **Performance**: See [Troubleshooting Guide](./troubleshooting.md)

## 🎨 **Design Philosophy**

This app follows a **relationship-first** design philosophy:
- **Warm, inviting colors** that feel personal and intimate
- **Playful interactions** that make relationship building fun
- **Progress visualization** that celebrates growth
- **Privacy-first** approach to personal data

## 🔄 **Contributing**

1. **Read** the [Developer Guide](./developer-guide.md)
2. **Follow** the [Code Standards](./code-standards.md)
3. **Test** your changes thoroughly
4. **Document** any new features or APIs
5. **Submit** a pull request with clear description

---

**Built with ❤️ for helping couples grow together**

*Last updated: $(date)*
