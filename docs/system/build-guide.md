# 🏗️ Build Guide

> **Complete guide to building and deploying the Nestled app**

## 📋 Table of Contents

1. [Quick Build Commands](#-quick-build-commands)
2. [Development Builds](#-development-builds)
3. [Production Builds](#-production-builds)
4. [Platform-Specific Builds](#-platform-specific-builds)
5. [App Store Deployment](#-app-store-deployment)
6. [Web Deployment](#-web-deployment)
7. [Troubleshooting Builds](#-troubleshooting-builds)

## ⚡ Quick Build Commands

### Most Common Build Commands
```bash
# Development (most common)
npm run dev                    # Start development server

# Web builds
npm run build:web             # Build for web deployment

# Mobile builds (when ready for stores)
npm run build:production      # Build for app stores (EAS)
```

### Emergency Build Reset
```bash
# If builds are failing, try this complete reset
rm -rf node_modules
rm -rf .expo
rm -rf dist
npm install
npm run build:web
```

## 🔧 Development Builds

### Local Development
```bash
# Start development server
npm run dev

# Start with specific platform
npx expo start --web          # Web browser
npx expo start --ios          # iOS simulator
npx expo start --android      # Android emulator
npx expo start --clear        # Clear cache and start
```

### Development Testing
```bash
# Test on physical device
npm run dev
# Scan QR code with Expo Go app

# Test on simulator/emulator
npx expo start --ios          # iOS Simulator
npx expo start --android      # Android Emulator
```

### Development Build Verification
1. **App loads successfully** - No white screen or crashes
2. **Navigation works** - All tabs and screens accessible
3. **Features function** - Core features work as expected
4. **No console errors** - Check Metro bundler for errors
5. **Performance is good** - App feels responsive

## 🚀 Production Builds

### Web Production Build
```bash
# Build for web deployment
npm run build:web

# Output will be in dist/ folder
# Deploy dist/ folder to your web hosting service
```

**Web Build Output:**
```
dist/
├── index.html              # Main HTML file
├── _expo/                  # Expo assets
├── assets/                 # Static assets
└── (tabs)/                 # Route-specific HTML files
```

### Mobile Production Builds

#### Using EAS Build (Recommended)
```bash
# Install EAS CLI
npm install -g @expo/eas-cli

# Login to Expo account
eas login

# Configure build
eas build:configure

# Build for all platforms
eas build --platform all

# Build for specific platform
eas build --platform ios
eas build --platform android
```

#### Using Expo Build (Legacy)
```bash
# iOS build
npx expo build:ios

# Android build
npx expo build:android

# Check build status
npx expo build:status
```

### Production Build Configuration

#### EAS Configuration (`eas.json`)
```json
{
  "cli": {
    "version": ">= 3.0.0"
  },
  "build": {
    "development": {
      "developmentClient": true,
      "distribution": "internal"
    },
    "preview": {
      "distribution": "internal",
      "ios": {
        "resourceClass": "m-medium"
      }
    },
    "production": {
      "ios": {
        "resourceClass": "m-medium"
      }
    }
  },
  "submit": {
    "production": {
      "ios": {
        "appleId": "<EMAIL>",
        "ascAppId": "**********",
        "appleTeamId": "ABCD123456"
      },
      "android": {
        "serviceAccountKeyPath": "./google-service-account.json",
        "track": "production"
      }
    }
  }
}
```

## 📱 Platform-Specific Builds

### iOS Builds

#### Prerequisites
- **Mac computer** (required for iOS builds)
- **Apple Developer Account** ($99/year)
- **Xcode** (latest version)
- **iOS Simulator** (for testing)

#### Build Process
```bash
# 1. Configure iOS build
eas build:configure

# 2. Build for iOS
eas build --platform ios

# 3. Download and install on device
# Build will be available in Expo dashboard
```

#### iOS Build Settings
```json
{
  "ios": {
    "bundleIdentifier": "com.nestled.app",
    "buildNumber": "1",
    "version": "1.0.0",
    "icon": "./assets/icon.png",
    "splash": {
      "image": "./assets/splash.png",
      "resizeMode": "contain",
      "backgroundColor": "#ffffff"
    }
  }
}
```

### Android Builds

#### Prerequisites
- **Android Studio** (for emulator testing)
- **Google Play Console Account** ($25 one-time fee)
- **Java Development Kit** (JDK 11 or 17)

#### Build Process
```bash
# 1. Configure Android build
eas build:configure

# 2. Build for Android
eas build --platform android

# 3. Download APK/AAB file
# Upload to Google Play Console
```

#### Android Build Settings
```json
{
  "android": {
    "package": "com.nestled.app",
    "versionCode": 1,
    "version": "1.0.0",
    "icon": "./assets/icon.png",
    "splash": {
      "image": "./assets/splash.png",
      "resizeMode": "contain",
      "backgroundColor": "#ffffff"
    }
  }
}
```

### Web Builds

#### Prerequisites
- **Node.js** (18+)
- **Web hosting service** (Vercel, Netlify, etc.)

#### Build Process
```bash
# 1. Build for web
npm run build:web

# 2. Test locally
npx serve dist

# 3. Deploy to hosting service
# Upload dist/ folder contents
```

#### Web Build Optimization
```javascript
// metro.config.js
const { getDefaultConfig } = require('expo/metro-config');

const config = getDefaultConfig(__dirname);

// Web-specific optimizations
config.resolver.platforms = ['ios', 'android', 'native', 'web'];

module.exports = config;
```

## 🏪 App Store Deployment

### iOS App Store

#### Prerequisites
- **Apple Developer Account** (active)
- **App Store Connect** access
- **App icons and screenshots**
- **App description and metadata**

#### Deployment Process
```bash
# 1. Build for production
eas build --platform ios --profile production

# 2. Submit to App Store
eas submit --platform ios

# 3. Or submit manually via App Store Connect
# Download build from Expo dashboard
# Upload via Xcode or Transporter
```

#### App Store Requirements
- **App icons**: 1024x1024px (required)
- **Screenshots**: Various device sizes
- **App description**: Clear, compelling description
- **Privacy policy**: Required for app approval
- **Age rating**: Appropriate content rating

### Google Play Store

#### Prerequisites
- **Google Play Console** account
- **App signing key** (generated by EAS)
- **App icons and screenshots**
- **App description and metadata**

#### Deployment Process
```bash
# 1. Build for production
eas build --platform android --profile production

# 2. Submit to Play Store
eas submit --platform android

# 3. Or submit manually via Play Console
# Download AAB file from Expo dashboard
# Upload via Play Console
```

#### Play Store Requirements
- **App icons**: 512x512px (required)
- **Screenshots**: Various device sizes
- **App description**: Clear, compelling description
- **Privacy policy**: Required for app approval
- **Content rating**: Appropriate content rating

## 🌐 Web Deployment

### Deployment Options

#### Vercel (Recommended)
```bash
# 1. Install Vercel CLI
npm install -g vercel

# 2. Build the app
npm run build:web

# 3. Deploy to Vercel
vercel --prod

# 4. Configure custom domain (optional)
vercel domains add your-domain.com
```

#### Netlify
```bash
# 1. Build the app
npm run build:web

# 2. Deploy to Netlify
# Drag and drop dist/ folder to Netlify dashboard
# Or use Netlify CLI:
npx netlify deploy --prod --dir=dist
```

#### GitHub Pages
```bash
# 1. Build the app
npm run build:web

# 2. Copy dist/ contents to gh-pages branch
# 3. Push to GitHub
# 4. Enable GitHub Pages in repository settings
```

### Web Build Configuration
```javascript
// app.config.js
export default {
  expo: {
    name: "Nestled",
    slug: "nestled",
    web: {
      bundler: "metro",
      output: "static",
      favicon: "./assets/favicon.png"
    }
  }
};
```

## 🚨 Troubleshooting Builds

### Common Build Issues

#### Metro Bundler Issues
```bash
# Clear Metro cache
npx expo start --clear

# Reset Metro configuration
rm -rf .expo
npx expo start --clear
```

#### Dependency Issues
```bash
# Clear node_modules and reinstall
rm -rf node_modules
npm install

# Check for conflicting dependencies
npm ls
```

#### iOS Build Issues
```bash
# Clear iOS build cache
cd ios
rm -rf build
rm -rf Pods
rm Podfile.lock
pod install
cd ..
npx expo run:ios
```

#### Android Build Issues
```bash
# Clear Android build cache
cd android
./gradlew clean
cd ..
npx expo run:android
```

### Build Error Solutions

#### "Module not found" Errors
```bash
# Install missing dependencies
npm install <missing-package>

# For Expo packages, use:
npx expo install <package>
```

#### "Bundle failed" Errors
```bash
# Check for syntax errors
npm run lint

# Check TypeScript errors
npx tsc --noEmit

# Clear all caches
rm -rf node_modules .expo dist
npm install
npx expo start --clear
```

#### "Build timeout" Errors
```bash
# Increase build timeout in eas.json
{
  "build": {
    "production": {
      "timeout": 1800
    }
  }
}
```

### Performance Optimization

#### Bundle Size Optimization
```bash
# Analyze bundle size
npx expo export --platform web --dev
npx bundle-analyzer dist/_expo/static/js/*.js

# Remove unused dependencies
npm prune
```

#### Build Speed Optimization
```bash
# Use faster build machines
{
  "build": {
    "production": {
      "ios": {
        "resourceClass": "m-medium"
      }
    }
  }
}
```

## 📊 Build Monitoring

### Build Status Tracking
```bash
# Check build status
eas build:list

# View build logs
eas build:view [BUILD_ID]

# Download build artifacts
eas build:download [BUILD_ID]
```

### Build Notifications
- **Email notifications** for build completion
- **Slack integration** for team notifications
- **GitHub status checks** for CI/CD integration

## 🔄 Continuous Integration

### GitHub Actions
```yaml
# .github/workflows/build.yml
name: Build and Deploy
on:
  push:
    branches: [main]
  pull_request:
    branches: [main]

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
      - run: npm install
      - run: npm run lint
      - run: npm test
      - run: npm run build:web
```

### Automated Deployment
```yaml
# Deploy to staging on PR
# Deploy to production on main branch merge
# Run tests before deployment
# Notify team of deployment status
```

---

## 🎯 Build Checklist

### Before Building
- [ ] **Code is tested** - All tests passing
- [ ] **Dependencies updated** - Latest stable versions
- [ ] **Environment variables** - Production values set
- [ ] **Assets optimized** - Images compressed, icons ready
- [ ] **Documentation updated** - Build instructions current

### After Building
- [ ] **Build successful** - No errors or warnings
- [ ] **App loads correctly** - No crashes or white screens
- [ ] **Features work** - Core functionality tested
- [ ] **Performance good** - App feels responsive
- [ ] **Store requirements met** - Icons, screenshots, descriptions ready

---

*This build guide provides comprehensive instructions for building and deploying the Nestled app across all platforms. For specific issues, refer to the [Troubleshooting Guide](./troubleshooting.md).*
