# 🔐 Secure Keys Storage

## ⚠️ IMPORTANT SECURITY NOTICE

**NEVER** put the `service_role` key in your `.env` file or client-side code!

## Supabase Configuration:

### ✅ Client-Side (Safe to use in .env):
- **Project URL**: Available in your `.env` file as `EXPO_PUBLIC_SUPABASE_URL`
- **Anon Key**: Available in your `.env` file as `EXPO_PUBLIC_SUPABASE_ANON_KEY`

### 🔒 Server-Side Only (NEVER in client code):
- **Service Role Key**: Available in Supabase dashboard under Settings > API
- **⚠️ CRITICAL**: Never store service role keys in client-side code or documentation

## When to Use Each Key:

### Anon Key (Client-Side):
- ✅ User authentication
- ✅ Reading public data
- ✅ User-specific data (with RLS policies)
- ✅ Safe to include in your app

### Service Role Key (Server-Side Only):
- ❌ NEVER in client-side code
- ✅ Server-side operations only
- ✅ Admin operations
- ✅ Bypasses Row Level Security
- ✅ Store in secure server environment variables

## Security Best Practices:

1. **Anon key** is designed for public use - it's safe in your app
2. **Service role key** bypasses all security - keep it secret!
3. **Row Level Security (RLS)** policies protect your data
4. **Environment variables** keep keys out of your code
5. **Never commit** service role keys to git

## Your Current Setup:
- ✅ Anon key is in `.env` (correct)
- ✅ Service role key is secured in Supabase dashboard only
- ✅ `.env` is in `.gitignore` (safe)
- ✅ No sensitive keys exposed in client code
