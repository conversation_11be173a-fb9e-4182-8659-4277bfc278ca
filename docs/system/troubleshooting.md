# 🚨 Troubleshooting

> **Solutions to common development issues**

## 📋 Contents

1. [Quick Fixes](#-quick-fixes)
2. [Development Issues](#-development-issues)
3. [Build Issues](#-build-issues)
4. [Runtime Errors](#-runtime-errors)
5. [Performance Issues](#-performance-issues)
6. [Platform Issues](#-platform-issues)
7. [Database Issues](#-database-issues)
8. [Authentication Issues](#-authentication-issues)

## ⚡ Quick Fixes

### Try These First

```bash
# Clear Metro cache (fixes 80% of issues)
npx expo start --clear

# Reset node_modules
rm -rf node_modules
npm install
npx expo start --clear

# Clear Expo cache
npx expo r -c

# 4. Reset iOS simulator
npx expo run:ios --clear

# 5. Reset Android emulator
npx expo run:android --clear
```

### Emergency Reset
If nothing else works, try this complete reset:

```bash
# Complete environment reset
rm -rf node_modules
rm -rf .expo
rm -rf dist
npm install
npx expo start --clear
```

## 🔧 Development Issues

### App Won't Start

#### Problem: Metro bundler fails to start
```bash
Error: Metro bundler failed to start
```

**Solutions:**
1. **Check Node.js version** (requires 18+):
   ```bash
   node --version
   # Should be 18.x.x or higher
   ```

2. **Clear Metro cache**:
   ```bash
   npx expo start --clear
   ```

3. **Check for port conflicts**:
   ```bash
   # Kill any processes using port 8081
   lsof -ti:8081 | xargs kill -9
   npx expo start
   ```

4. **Reset Metro configuration**:
   ```bash
   rm -rf .expo
   npx expo start --clear
   ```

#### Problem: "Module not found" errors
```bash
Error: Unable to resolve module `@react-native-community/datetimepicker`
```

**Solutions:**
1. **Install missing dependencies**:
   ```bash
   npm install @react-native-community/datetimepicker
   ```

2. **For Expo projects, use Expo install**:
   ```bash
   npx expo install @react-native-community/datetimepicker
   ```

3. **Check package.json** for correct dependencies

### Hot Reload Not Working

#### Problem: Changes not reflecting in app
**Solutions:**
1. **Enable fast refresh**:
   ```bash
   # In Metro bundler, press 'r' to reload
   # Or shake device and select "Reload"
   ```

2. **Check for syntax errors**:
   ```bash
   npm run lint
   ```

3. **Restart development server**:
   ```bash
   npx expo start --clear
   ```

### TypeScript Errors

#### Problem: Type errors preventing compilation
```typescript
Error: Property 'xyz' does not exist on type 'ABC'
```

**Solutions:**
1. **Check type definitions**:
   ```typescript
   // Make sure interfaces are properly defined
   interface MyComponentProps {
     xyz: string; // Add missing property
   }
   ```

2. **Update type imports**:
   ```typescript
   import { MyType } from '../types/MyType';
   ```

3. **Use type assertions when necessary**:
   ```typescript
   const data = response as MyExpectedType;
   ```

## 🏗️ Build Issues

### Web Build Failures

#### Problem: Web build fails with module errors
```bash
Error: Module not found: Can't resolve 'react-native-screens'
```

**Solutions:**
1. **Check web-compatible dependencies**:
   ```bash
   # Some React Native packages don't work on web
   # Use web alternatives or conditional imports
   ```

2. **Update metro.config.js**:
   ```javascript
   const { getDefaultConfig } = require('expo/metro-config');
   
   const config = getDefaultConfig(__dirname);
   
   // Add web-specific resolver
   config.resolver.platforms = ['ios', 'android', 'native', 'web'];
   
   module.exports = config;
   ```

3. **Use conditional imports**:
   ```typescript
   import { Platform } from 'react-native';
   
   const MyComponent = Platform.select({
     web: () => require('./WebComponent').default,
     default: () => require('./MobileComponent').default,
   });
   ```

### iOS Build Issues

#### Problem: iOS build fails with pod install errors
```bash
Error: CocoaPods could not find compatible versions
```

**Solutions:**
1. **Update CocoaPods**:
   ```bash
   sudo gem install cocoapods
   cd ios && pod install
   ```

2. **Clear iOS build cache**:
   ```bash
   cd ios
   rm -rf build
   rm -rf Pods
   rm Podfile.lock
   pod install
   cd ..
   npx expo run:ios
   ```

3. **Check iOS deployment target**:
   ```bash
   # In ios/Podfile, ensure minimum iOS version
   platform :ios, '13.0'
   ```

### Android Build Issues

#### Problem: Android build fails with Gradle errors
```bash
Error: Could not resolve all dependencies
```

**Solutions:**
1. **Clean Gradle cache**:
   ```bash
   cd android
   ./gradlew clean
   cd ..
   npx expo run:android
   ```

2. **Update Android SDK**:
   ```bash
   # In Android Studio, update SDK tools
   # Or via command line:
   sdkmanager --update
   ```

3. **Check Java version**:
   ```bash
   java --version
   # Should be Java 11 or 17
   ```

## 🐛 Runtime Errors

### Navigation Errors

#### Problem: Navigation errors in Expo Router
```typescript
Error: Could not find a route named "xyz"
```

**Solutions:**
1. **Check file structure**:
   ```
   app/
   ├── (tabs)/
   │   ├── index.tsx      # Route: /(tabs)/
   │   └── profile.tsx    # Route: /(tabs)/profile
   └── settings.tsx       # Route: /settings
   ```

2. **Use proper navigation**:
   ```typescript
   import { router } from 'expo-router';
   
   // Correct
   router.push('/(tabs)/profile');
   
   // Incorrect
   router.push('/profile');
   ```

3. **Check route parameters**:
   ```typescript
   // For dynamic routes like [id].tsx
   router.push(`/user/${userId}`);
   ```

### State Management Errors

#### Problem: Context provider errors
```typescript
Error: useAuth must be used within an AuthProvider
```

**Solutions:**
1. **Wrap components with providers**:
   ```typescript
   // In _layout.tsx
   <AuthProvider>
     <SettingsProvider>
       <App />
     </SettingsProvider>
   </AuthProvider>
   ```

2. **Check provider order**:
   ```typescript
   // Dependencies should be outer
   <AuthProvider>        // No dependencies
     <SettingsProvider>  // Depends on Auth
       <App />
     </SettingsProvider>
   </AuthProvider>
   ```

### Async/Await Errors

#### Problem: Unhandled promise rejections
```typescript
Error: Unhandled promise rejection
```

**Solutions:**
1. **Always handle errors**:
   ```typescript
   try {
     const result = await someAsyncFunction();
     setData(result);
   } catch (error) {
     console.error('Error:', error);
     setError(error.message);
   }
   ```

2. **Use error boundaries**:
   ```typescript
   <ErrorBoundary>
     <MyComponent />
   </ErrorBoundary>
   ```

## ⚡ Performance Issues

### Slow App Performance

#### Problem: App feels sluggish or freezes
**Solutions:**
1. **Check for memory leaks**:
   ```typescript
   // Clean up subscriptions
   useEffect(() => {
     const subscription = someService.subscribe();
     return () => subscription.unsubscribe();
   }, []);
   ```

2. **Optimize re-renders**:
   ```typescript
   // Use React.memo for expensive components
   const ExpensiveComponent = React.memo(({ data }) => {
     return <ComplexVisualization data={data} />;
   });
   
   // Use useCallback for event handlers
   const handlePress = useCallback(() => {
     // Handle press
   }, [dependencies]);
   ```

3. **Optimize images**:
   ```typescript
   // Use React Native Image component
   <Image
     source={{ uri: imageUrl }}
     style={styles.image}
     resizeMode="cover"
   />
   ```

### Large Bundle Size

#### Problem: App bundle is too large
**Solutions:**
1. **Use dynamic imports**:
   ```typescript
   const LazyComponent = React.lazy(() => import('./LazyComponent'));
   ```

2. **Remove unused dependencies**:
   ```bash
   npm run analyze
   # Check for unused packages
   ```

3. **Optimize assets**:
   ```bash
   # Compress images
   npx expo optimize
   ```

## 📱 Platform-Specific Issues

### iOS Issues

#### Problem: iOS simulator won't start
**Solutions:**
1. **Reset iOS simulator**:
   ```bash
   xcrun simctl shutdown all
   xcrun simctl erase all
   npx expo start --ios
   ```

2. **Check Xcode version**:
   ```bash
   xcodebuild -version
   # Should be Xcode 14+ for React Native 0.72+
   ```

#### Problem: iOS app crashes on device
**Solutions:**
1. **Check device logs**:
   ```bash
   # Connect device and check logs
   npx react-native log-ios
   ```

2. **Test on simulator first**:
   ```bash
   npx expo start --ios
   ```

### Android Issues

#### Problem: Android emulator won't start
**Solutions:**
1. **Check emulator configuration**:
   ```bash
   # List available emulators
   emulator -list-avds
   
   # Start specific emulator
   emulator -avd Pixel_4_API_30
   ```

2. **Enable hardware acceleration**:
   ```bash
   # In Android Studio AVD Manager
   # Enable "Hardware - GLES 2.0"
   ```

#### Problem: Android app crashes on device
**Solutions:**
1. **Check device logs**:
   ```bash
   npx react-native log-android
   ```

2. **Enable USB debugging**:
   ```bash
   # On device: Settings > Developer Options > USB Debugging
   ```

### Web Issues

#### Problem: Web app doesn't load
**Solutions:**
1. **Check browser compatibility**:
   ```bash
   # Test in Chrome, Firefox, Safari
   npx expo start --web
   ```

2. **Check for web-specific errors**:
   ```typescript
   // Use Platform.select for web-specific code
   import { Platform } from 'react-native';
   
   const styles = StyleSheet.create({
     container: {
       ...Platform.select({
         web: {
           cursor: 'pointer',
         },
         default: {},
       }),
     },
   });
   ```

## 🗄️ Database Issues

### Supabase Connection Issues

#### Problem: Can't connect to Supabase
```typescript
Error: Failed to connect to Supabase
```

**Solutions:**
1. **Check environment variables**:
   ```bash
   # In .env.local
   EXPO_PUBLIC_SUPABASE_URL=your_supabase_url
   EXPO_PUBLIC_SUPABASE_ANON_KEY=your_anon_key
   ```

2. **Verify Supabase project**:
   ```bash
   # Check if project is active
   # Verify URL and keys in Supabase dashboard
   ```

3. **Check network connectivity**:
   ```typescript
   import NetInfo from '@react-native-community/netinfo';
   
   NetInfo.fetch().then(state => {
     console.log('Connection type:', state.type);
     console.log('Is connected:', state.isConnected);
   });
   ```

### Database Query Issues

#### Problem: Database queries fail
```typescript
Error: relation "table_name" does not exist
```

**Solutions:**
1. **Check table names**:
   ```typescript
   // Ensure table names match exactly
   const { data, error } = await supabase
     .from('date_night_ideas')  // Correct table name
     .select('*');
   ```

2. **Check RLS policies**:
   ```sql
   -- In Supabase SQL editor
   SELECT * FROM pg_policies WHERE tablename = 'your_table';
   ```

3. **Verify user permissions**:
   ```typescript
   // Check if user is authenticated
   const { data: { user } } = await supabase.auth.getUser();
   console.log('User:', user);
   ```

## 🔐 Authentication Issues

### Login/Logout Problems

#### Problem: User can't log in
```typescript
Error: Invalid login credentials
```

**Solutions:**
1. **Check email/password format**:
   ```typescript
   // Validate email format
   const isValidEmail = (email: string) => {
     return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
   };
   ```

2. **Check Supabase auth settings**:
   ```bash
   # In Supabase dashboard
   # Authentication > Settings
   # Verify email confirmation settings
   ```

3. **Check for typos**:
   ```typescript
   // Debug login attempt
   console.log('Email:', email);
   console.log('Password length:', password.length);
   ```

#### Problem: User stays logged in after logout
**Solutions:**
1. **Clear all auth state**:
   ```typescript
   const logout = async () => {
     await supabase.auth.signOut();
     // Clear any local storage
     await SecureStore.deleteItemAsync('auth_token');
   };
   ```

2. **Check auth state management**:
   ```typescript
   // Ensure auth context is properly updated
   useEffect(() => {
     const { data: { subscription } } = supabase.auth.onAuthStateChange(
       (event, session) => {
         setUser(session?.user ?? null);
         setIsAuthenticated(!!session);
       }
     );
     
     return () => subscription.unsubscribe();
   }, []);
   ```

## 🔍 Debugging Tools

### Console Debugging
```typescript
// Add comprehensive logging
console.log('Debug info:', {
  user: user?.id,
  timestamp: new Date().toISOString(),
  platform: Platform.OS,
  data: someData
});
```

### React Native Debugger
```bash
# Install React Native Debugger
npm install -g react-native-debugger

# Start with debugger
npx expo start
# Then open React Native Debugger
```

### Flipper Integration
```bash
# Install Flipper
# Download from https://fbflipper.com/

# Enable Flipper in development
npx expo start --dev-client
```

## 📞 Getting Help

### When to Ask for Help
- ✅ You've tried the solutions above
- ✅ You've searched existing issues
- ✅ You can reproduce the problem consistently
- ✅ You have error logs and context

### What to Include in Bug Reports
1. **Error message** (exact text)
2. **Steps to reproduce**
3. **Expected vs actual behavior**
4. **Environment details**:
   - OS version
   - Node.js version
   - Expo CLI version
   - Device/simulator details
5. **Relevant code snippets**
6. **Console logs**

### Emergency Contacts
- **Critical Issues**: Create GitHub issue with "urgent" label
- **Security Issues**: Email <EMAIL>
- **General Questions**: Check FAQ first, then create discussion

---

## 🎯 Prevention Tips

### Best Practices to Avoid Issues
1. **Keep dependencies updated**:
   ```bash
   npm outdated
   npm update
   ```

2. **Use TypeScript strictly**:
   ```json
   // tsconfig.json
   {
     "strict": true,
     "noImplicitAny": true,
     "noImplicitReturns": true
   }
   ```

3. **Test on multiple platforms**:
   ```bash
   # Test on iOS, Android, and Web
   npx expo start --ios
   npx expo start --android
   npx expo start --web
   ```

4. **Use error boundaries**:
   ```typescript
   <ErrorBoundary fallback={<ErrorScreen />}>
     <MyComponent />
   </ErrorBoundary>
   ```

5. **Monitor performance**:
   ```typescript
   // Add performance monitoring
   import { Performance } from 'react-native-performance';
   ```

---

*This troubleshooting guide covers the most common issues developers encounter. For issues not covered here, check the [FAQ](./faq.md) or create a GitHub issue with detailed information.*
