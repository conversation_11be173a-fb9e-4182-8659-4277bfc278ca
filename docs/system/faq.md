# ❓ FAQ

> **Common questions about Nestled development**

## 📋 Contents

1. [General](#-general)
2. [Development](#-development)
3. [Architecture](#-architecture)
4. [Platform](#-platform)
5. [Database](#-database)
6. [Deployment](#-deployment)

## 🤔 General

### What is Nestled?
Relationship growth app for couples featuring:
- **Weekly Modules**: 12 relationship activities
- **Date Night Planning**: Restaurant and activity recommendations
- **Milestone Tracking**: Relationship timeline
- **Photo Scrapbook**: Visual memories
- **Points System**: Gamified growth

### Technologies
- **Frontend**: React Native + Expo
- **Backend**: Supabase (PostgreSQL + Auth + Real-time)
- **Language**: TypeScript
- **Navigation**: Expo Router
- **State**: React Context + Hooks
- **Styling**: StyleSheet + Custom Theme

### Who is the target audience?
The app is designed for couples who want to:
- Strengthen their relationship through guided activities
- Create meaningful memories together
- Track their relationship milestones
- Have fun while growing together

## 🔧 Development Questions

### How do I get started with development?
1. **Clone the repository**
2. **Install dependencies**: `npm install`
3. **Set up environment variables** (copy `.env.example` to `.env.local`)
4. **Start development server**: `npm run dev`
5. **Scan QR code** with Expo Go app on your phone

See the [Developer Guide](./developer-guide.md) for detailed setup instructions.

### What's the development workflow?
1. **Create feature branch** from main
2. **Make changes** following [Code Standards](../CODE_STANDARDS_SUMMARY.md)
3. **Test changes** on multiple platforms
4. **Run linting**: `npm run lint`
5. **Run tests**: `npm test`
6. **Submit pull request** for review

### How do I add a new feature?
1. **Plan the feature** - Create issue or discussion
2. **Create feature branch**: `git checkout -b feature/new-feature`
3. **Implement feature** following existing patterns
4. **Add tests** for new functionality
5. **Update documentation** if needed
6. **Submit PR** with clear description

### What coding standards should I follow?
- **TypeScript**: Use strict typing throughout
- **Component Structure**: Follow existing patterns in `components/shared/`
- **Naming**: Use descriptive, consistent names
- **Documentation**: Add JSDoc comments for all public APIs
- **Testing**: Write tests for new functionality

See [Code Standards Summary](../CODE_STANDARDS_SUMMARY.md) for details.

### How do I debug issues?
1. **Check console logs** in Metro bundler
2. **Use React Native Debugger** for advanced debugging
3. **Check device logs** for platform-specific issues
4. **Use error boundaries** to catch React errors
5. **Refer to [Troubleshooting Guide](./troubleshooting.md)**

## 🏗️ Architecture Questions

### How is the app structured?
The app follows a modular architecture:

```
app/                    # Expo Router pages
├── (tabs)/            # Tab navigation screens
├── _layout.tsx        # Root layout with providers
└── *.tsx              # Individual screens

components/            # Reusable UI components
├── shared/           # Shared component library
├── ui/               # Basic UI primitives
└── */                # Feature-specific components

hooks/                # Custom React hooks
contexts/             # React Context providers
services/             # Business logic and API calls
utils/                # Utility functions and constants
```

### How does state management work?
The app uses React Context for global state:
- **AuthContext**: User authentication state
- **SettingsContext**: App settings and preferences
- **FavoritesContext**: User favorites and preferences
- **ThemeProvider**: Theme and styling state

Local state is managed with `useState` and `useReducer` hooks.

### How does navigation work?
The app uses Expo Router for file-based routing:
- **Tab Navigation**: Main app sections (Home, Modules, Date Night, Activities)
- **Stack Navigation**: Screen transitions within sections
- **Modal Navigation**: Overlay screens and forms

### How are components organized?
- **Shared Components**: Reusable across multiple screens
- **UI Components**: Basic design system components
- **Feature Components**: Specific to individual features
- **Layout Components**: Screen structure and navigation

## 📱 Platform Questions

### Which platforms are supported?
- **iOS**: iPhone and iPad (iOS 13+)
- **Android**: Phones and tablets (API level 21+)
- **Web**: Modern browsers (Chrome, Firefox, Safari, Edge)

### How do I test on different platforms?
```bash
# iOS Simulator (Mac only)
npx expo start --ios

# Android Emulator
npx expo start --android

# Web browser
npx expo start --web

# Physical device (scan QR code)
npx expo start
```

### Are there platform-specific considerations?
Yes, some features work differently across platforms:
- **Camera**: Different APIs for iOS/Android
- **Storage**: SecureStore vs AsyncStorage
- **Navigation**: Platform-specific navigation patterns
- **Styling**: Platform-specific style properties

### How do I handle platform differences?
Use `Platform.select()` for platform-specific code:

```typescript
import { Platform } from 'react-native';

const styles = StyleSheet.create({
  container: {
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
      },
      android: {
        elevation: 5,
      },
    }),
  },
});
```

## 🗄️ Database Questions

### What database does the app use?
The app uses **Supabase**, which provides:
- **PostgreSQL database** for data storage
- **Authentication** system
- **Real-time subscriptions** for live updates
- **Row Level Security** for data protection

### How do I add a new table?
1. **Create migration** in Supabase dashboard
2. **Define TypeScript types** in `types/supabase.ts`
3. **Create service functions** in `services/`
4. **Add hooks** for data management
5. **Update components** to use new data

### How does authentication work?
- **Email/Password**: Standard authentication
- **Social Login**: Google, Apple (planned)
- **Session Management**: Automatic token refresh
- **Security**: Row Level Security (RLS) policies

### How do I handle offline functionality?
The app uses:
- **AsyncStorage** for local data caching
- **Supabase offline support** for data sync
- **Error boundaries** for graceful offline handling
- **Retry mechanisms** for failed requests

## 🚀 Deployment Questions

### How do I build for production?
```bash
# Web deployment
npm run build:web

# iOS App Store
npx expo build:ios

# Google Play Store
npx expo build:android

# EAS Build (recommended)
npx eas build --platform all
```

### How do I deploy to app stores?
1. **Build production app**: `npx eas build --platform all`
2. **Submit to stores**: Use EAS Submit or manual submission
3. **Configure app store listings**: Use [App Store Metadata](../app-store-metadata.md)
4. **Test on TestFlight/Play Console**: Before public release

### How do I set up CI/CD?
The app uses GitHub Actions for:
- **Automated testing** on pull requests
- **Code quality checks** (linting, type checking)
- **Automated builds** for testing
- **Deployment** to staging/production

### How do I monitor app performance?
- **Expo Analytics**: Built-in performance monitoring
- **Error Reporting**: Custom error tracking system
- **User Analytics**: Privacy-focused usage analytics
- **Performance Metrics**: App startup time, memory usage

## 🔒 Security Questions

### How is user data protected?
- **Encryption**: All data encrypted in transit and at rest
- **Authentication**: Secure token-based authentication
- **Authorization**: Row Level Security (RLS) policies
- **Privacy**: Minimal data collection, user control

### What security measures are in place?
- **Input Validation**: All user inputs validated
- **SQL Injection Protection**: Parameterized queries
- **XSS Protection**: Content sanitization
- **Secure Storage**: Encrypted local storage
- **HTTPS**: All network traffic encrypted

### How do I handle sensitive data?
- **Secure Storage**: Use `expo-secure-store` for sensitive data
- **Environment Variables**: Store secrets in environment files
- **No Hardcoded Secrets**: All secrets in environment variables
- **Regular Security Audits**: Automated security scanning

## 🎨 Design Questions

### How does the design system work?
The app uses a centralized design system:
- **Colors**: Consistent color palette with light/dark themes
- **Typography**: Standardized font sizes and weights
- **Spacing**: Consistent spacing scale
- **Components**: Reusable UI components

### How do I add new colors or styles?
1. **Update color system** in `utils/colors.ts`
2. **Add to theme provider** in `components/shared/ThemeProvider.tsx`
3. **Update components** to use new colors
4. **Test on both themes** (light/dark)

### How do I create new components?
1. **Follow existing patterns** in `components/shared/`
2. **Add TypeScript interfaces** for props
3. **Include JSDoc documentation**
4. **Add to component library** exports
5. **Write tests** for new components

## 🧪 Testing Questions

### How do I run tests?
```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage

# Run specific test file
npm test -- MyComponent.test.tsx
```

### What testing tools are used?
- **Jest**: Test runner and assertion library
- **React Native Testing Library**: Component testing utilities
- **@testing-library/react-hooks**: Hook testing utilities
- **MSW**: API mocking for integration tests

### How do I write good tests?
1. **Test behavior, not implementation**
2. **Use descriptive test names**
3. **Test edge cases and error conditions**
4. **Mock external dependencies**
5. **Keep tests simple and focused**

### What should I test?
- **Components**: Rendering, user interactions, props
- **Hooks**: State changes, side effects, error handling
- **Services**: API calls, data transformation, error handling
- **Utilities**: Input/output, edge cases, error conditions

## 📊 Performance Questions

### How do I optimize app performance?
1. **Use React.memo** for expensive components
2. **Use useCallback** for event handlers
3. **Use useMemo** for expensive calculations
4. **Optimize images** and assets
5. **Use FlatList** for large lists
6. **Monitor bundle size**

### How do I measure performance?
- **React Native Performance**: Built-in performance monitoring
- **Flipper**: Network, layout, and performance debugging
- **Expo Analytics**: App performance metrics
- **Custom metrics**: User interaction timing

### What are common performance issues?
- **Unnecessary re-renders**: Use memoization
- **Large bundle size**: Code splitting, lazy loading
- **Memory leaks**: Clean up subscriptions
- **Slow images**: Optimize and cache images
- **Blocking operations**: Use background processing

## 🔄 Maintenance Questions

### How do I update dependencies?
```bash
# Check for updates
npm outdated

# Update all dependencies
npm update

# Update specific package
npm install package-name@latest

# Check for security vulnerabilities
npm audit
npm audit fix
```

### How do I handle breaking changes?
1. **Read migration guides** for major updates
2. **Test thoroughly** after updates
3. **Update code** to match new APIs
4. **Update documentation** if needed
5. **Deploy to staging** first

### How do I maintain code quality?
- **Regular linting**: `npm run lint`
- **Type checking**: `npx tsc --noEmit`
- **Code reviews**: All changes reviewed
- **Automated testing**: CI/CD pipeline
- **Documentation**: Keep docs updated

---

## 🆘 Still Need Help?

### Before Asking for Help
1. **Search existing issues** on GitHub
2. **Check this FAQ** for common questions
3. **Read relevant documentation**
4. **Try troubleshooting steps**

### When Asking for Help
1. **Be specific** about your problem
2. **Include error messages** and logs
3. **Describe what you've tried**
4. **Provide code examples**
5. **Include environment details**

### Where to Get Help
- **GitHub Issues**: Bug reports and feature requests
- **GitHub Discussions**: General questions and ideas
- **Documentation**: Comprehensive guides and references
- **Code Comments**: Inline documentation in code

---

*This FAQ covers the most common questions about developing the Nestled app. For questions not covered here, check the other documentation files or create a GitHub issue.*
