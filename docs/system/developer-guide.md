# 🏗️ Developer Guide

> **Setup, architecture, and development workflow for Nestled**

## 📋 Contents

1. [Quick Start](#-quick-start)
2. [Architecture](#-architecture)
3. [Development](#-development)
4. [Code Organization](#-code-organization)
5. [State Management](#-state-management)
6. [Data Flow](#-data-flow)
7. [Testing](#-testing)
8. [Performance](#-performance)

## 🚀 Quick Start

### Prerequisites
- Node.js 18+
- Expo CLI (`npm install -g @expo/cli`)
- iOS Simulator (Mac) or Android Studio

### Setup
```bash
git clone <repository-url>
cd nestled
npm install
cp .env.example .env.local
# Add your Supabase credentials to .env.local
npm run dev
```

### Verify
1. Metro bundler shows QR code
2. Scan with Expo Go app
3. App loads on landing screen

## 🏗️ Project Architecture

### High-Level Architecture

```mermaid
graph TB
    A[React Native App] --> B[Expo Router]
    B --> C[Tab Navigation]
    C --> D[Home Screen]
    C --> E[Modules Screen]
    C --> F[Date Night Screen]
    C --> G[Activities Screen]
    
    A --> H[Context Providers]
    H --> I[Auth Context]
    H --> J[Settings Context]
    H --> K[Favorites Context]
    H --> L[Theme Provider]
    
    A --> M[Supabase Client]
    M --> N[Database]
    M --> O[Authentication]
    M --> P[Real-time Updates]
    
    A --> Q[Shared Components]
    Q --> R[UI Components]
    Q --> S[Layout Components]
    Q --> T[Business Logic Hooks]
```

### Technology Stack

| Layer | Technology | Purpose |
|-------|------------|---------|
| **Frontend** | React Native + Expo | Cross-platform mobile development |
| **Navigation** | Expo Router | File-based routing system |
| **State** | React Context + Hooks | Local state management |
| **Backend** | Supabase | Database, auth, real-time |
| **Styling** | StyleSheet + Theme System | Consistent design system |
| **Type Safety** | TypeScript | Compile-time error prevention |
| **Testing** | Jest + React Native Testing Library | Unit and integration tests |

### File Structure Overview

```
nestled/
├── app/                    # Expo Router pages
│   ├── (tabs)/            # Tab navigation screens
│   ├── _layout.tsx        # Root layout with providers
│   └── *.tsx              # Individual screens
├── components/            # Reusable UI components
│   ├── shared/           # Shared component library
│   ├── ui/               # Basic UI primitives
│   └── */                # Feature-specific components
├── hooks/                # Custom React hooks
├── contexts/             # React Context providers
├── services/             # Business logic and API calls
├── utils/                # Utility functions and constants
├── types/                # TypeScript type definitions
├── lib/                  # Third-party integrations
└── docs/                 # Documentation
```

## 🔄 Development Workflow

### Daily Development Cycle

```bash
# 1. Start development server
npm run dev

# 2. Make changes to code
# 3. See changes reflected in app (hot reload)

# 4. Run linting
npm run lint

# 5. Run tests
npm run test

# 6. Commit changes
git add .
git commit -m "feat: add new feature"
```

### Feature Development Process

1. **Plan** - Create feature branch and plan implementation
2. **Develop** - Write code following [Code Standards](../CODE_STANDARDS_SUMMARY.md)
3. **Test** - Add unit tests and test manually
4. **Review** - Self-review code for quality and standards
5. **Document** - Update relevant documentation
6. **Merge** - Submit pull request for review

### Code Quality Gates

- [ ] **TypeScript** - No type errors
- [ ] **ESLint** - No linting errors
- [ ] **Tests** - All tests passing
- [ ] **Documentation** - New features documented
- [ ] **Performance** - No obvious performance issues

## 📁 Code Organization

### Component Architecture

#### Shared Components (`components/shared/`)
Reusable components used across multiple screens:

```typescript
// Example: GradientCard component
interface GradientCardProps {
  gradientColors: string[];
  pressable?: boolean;
  onPress?: () => void;
  children: React.ReactNode;
}

export const GradientCard: React.FC<GradientCardProps> = ({
  gradientColors,
  pressable = false,
  onPress,
  children
}) => {
  // Implementation
};
```

#### Feature Components
Components specific to a single feature:

```
components/
├── date-night/           # Date night specific components
├── milestones/          # Milestone tracking components
├── activities/          # Activity components
└── ui/                  # Basic UI primitives
```

### Hook Organization

#### Business Logic Hooks (`hooks/`)
Custom hooks that encapsulate business logic:

```typescript
// Example: useDateNight hook
export const useDateNight = () => {
  const [favorites, setFavorites] = useState<DateNightIdea[]>([]);
  const [loading, setLoading] = useState(false);

  const addToFavorites = useCallback(async (idea: DateNightIdea) => {
    // Implementation
  }, []);

  return {
    favorites,
    loading,
    addToFavorites,
    removeFromFavorites
  };
};
```

#### Data Hooks
Hooks that manage data fetching and caching:

```typescript
// Example: useWeekData hook
export const useWeekData = (weekNumber: number) => {
  const [data, setData] = useState<WeekData | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchWeekData(weekNumber).then(setData).finally(() => setLoading(false));
  }, [weekNumber]);

  return { data, loading, refetch };
};
```

### Service Layer (`services/`)

Business logic and API interactions:

```typescript
// Example: dateNightService.ts
export class DateNightService {
  static async getFavorites(userId: string): Promise<DateNightIdea[]> {
    const { data, error } = await supabase
      .from('date_night_favorites')
      .select('*')
      .eq('user_id', userId);
    
    if (error) throw error;
    return data;
  }

  static async addFavorite(userId: string, idea: DateNightIdea): Promise<void> {
    // Implementation
  }
}
```

## 🔄 State Management

### Context Providers

The app uses React Context for global state management:

#### Auth Context
```typescript
interface AuthContextType {
  isAuthenticated: boolean;
  user: User | null;
  login: (email: string, password: string) => Promise<void>;
  logout: () => Promise<void>;
  isLoading: boolean;
}
```

#### Settings Context
```typescript
interface SettingsContextType {
  theme: 'light' | 'dark';
  notifications: boolean;
  updateSettings: (settings: Partial<Settings>) => void;
}
```

#### Favorites Context
```typescript
interface FavoritesContextType {
  dateNightFavorites: DateNightIdea[];
  addFavorite: (idea: DateNightIdea) => void;
  removeFavorite: (ideaId: string) => void;
}
```

### Local State Patterns

#### Component State
```typescript
const [loading, setLoading] = useState(false);
const [data, setData] = useState<DataType | null>(null);
const [error, setError] = useState<string | null>(null);
```

#### Form State
```typescript
const [formData, setFormData] = useState<FormData>({
  field1: '',
  field2: '',
  field3: false
});

const updateField = (field: keyof FormData, value: any) => {
  setFormData(prev => ({ ...prev, [field]: value }));
};
```

## 📊 Data Flow

### Data Fetching Pattern

```typescript
// 1. Component requests data
const MyComponent = () => {
  const { data, loading, error } = useWeekData(1);
  
  if (loading) return <LoadingSpinner />;
  if (error) return <ErrorMessage error={error} />;
  
  return <DataDisplay data={data} />;
};

// 2. Hook fetches data
const useWeekData = (weekNumber: number) => {
  const [data, setData] = useState<WeekData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        const result = await WeekService.getWeekData(weekNumber);
        setData(result);
      } catch (err) {
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [weekNumber]);

  return { data, loading, error };
};

// 3. Service makes API call
class WeekService {
  static async getWeekData(weekNumber: number): Promise<WeekData> {
    const { data, error } = await supabase
      .from('weekly_modules')
      .select('*')
      .eq('week_number', weekNumber)
      .single();
    
    if (error) throw new Error(error.message);
    return data;
  }
}
```

### Real-time Updates

```typescript
// Subscribe to real-time updates
useEffect(() => {
  const subscription = supabase
    .channel('favorites')
    .on('postgres_changes', 
      { event: '*', schema: 'public', table: 'date_night_favorites' },
      (payload) => {
        // Update local state based on real-time changes
        updateFavorites(payload);
      }
    )
    .subscribe();

  return () => {
    subscription.unsubscribe();
  };
}, []);
```

## 🧪 Testing Strategy

### Testing Pyramid

```
    /\
   /  \     E2E Tests (Few)
  /____\    
 /      \   Integration Tests (Some)
/________\  
/          \ Unit Tests (Many)
/____________\
```

### Unit Testing

```typescript
// Example: Component test
import { render, fireEvent } from '@testing-library/react-native';
import { GradientCard } from '../GradientCard';

describe('GradientCard', () => {
  it('renders children correctly', () => {
    const { getByText } = render(
      <GradientCard gradientColors={['#ff0000', '#00ff00']}>
        <Text>Test Content</Text>
      </GradientCard>
    );
    
    expect(getByText('Test Content')).toBeTruthy();
  });

  it('calls onPress when pressed', () => {
    const onPress = jest.fn();
    const { getByTestId } = render(
      <GradientCard 
        gradientColors={['#ff0000', '#00ff00']}
        pressable
        onPress={onPress}
      >
        <Text>Test</Text>
      </GradientCard>
    );
    
    fireEvent.press(getByTestId('gradient-card'));
    expect(onPress).toHaveBeenCalled();
  });
});
```

### Integration Testing

```typescript
// Example: Hook test
import { renderHook, act } from '@testing-library/react-hooks';
import { useDateNight } from '../useDateNight';

describe('useDateNight', () => {
  it('adds favorite correctly', async () => {
    const { result } = renderHook(() => useDateNight());
    
    const testIdea = { id: '1', title: 'Test Idea' };
    
    await act(async () => {
      await result.current.addToFavorites(testIdea);
    });
    
    expect(result.current.favorites).toContain(testIdea);
  });
});
```

### Running Tests

```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage

# Run specific test file
npm test -- GradientCard.test.tsx
```

## ⚡ Performance Considerations

### React Native Performance

#### Memoization
```typescript
// Memoize expensive components
const ExpensiveComponent = React.memo(({ data }) => {
  return <ComplexVisualization data={data} />;
});

// Memoize expensive calculations
const MyComponent = ({ items }) => {
  const expensiveValue = useMemo(() => {
    return items.reduce((sum, item) => sum + item.value, 0);
  }, [items]);

  return <Text>{expensiveValue}</Text>;
};

// Memoize callbacks
const MyComponent = ({ onPress }) => {
  const handlePress = useCallback(() => {
    onPress();
  }, [onPress]);

  return <TouchableOpacity onPress={handlePress} />;
};
```

#### List Performance
```typescript
// Use FlatList for large lists
<FlatList
  data={items}
  renderItem={({ item }) => <ItemComponent item={item} />}
  keyExtractor={(item) => item.id}
  getItemLayout={(data, index) => ({
    length: ITEM_HEIGHT,
    offset: ITEM_HEIGHT * index,
    index,
  })}
  removeClippedSubviews={true}
  maxToRenderPerBatch={10}
  windowSize={10}
/>
```

### Image Optimization

```typescript
// Use React Native Image component
import { Image } from 'react-native';

<Image
  source={{ uri: imageUrl }}
  style={styles.image}
  resizeMode="cover"
/>
```

### Bundle Size Optimization

```typescript
// Lazy load components
const LazyComponent = React.lazy(() => import('./LazyComponent'));

// Use dynamic imports
const loadFeature = async () => {
  const { FeatureComponent } = await import('./FeatureComponent');
  return FeatureComponent;
};
```

## 🔧 Development Tools

### Essential VS Code Extensions
- **ES7+ React/Redux/React-Native snippets**
- **TypeScript Importer**
- **Prettier - Code formatter**
- **ESLint**
- **React Native Tools**
- **Expo Tools**

### Debugging Tools
- **React Native Debugger** - Advanced debugging
- **Flipper** - Network, layout, and performance debugging
- **Expo Dev Tools** - Built-in debugging tools

### Performance Monitoring
```typescript
// Add performance monitoring
import { Performance } from 'react-native-performance';

const startTime = Performance.now();
// ... expensive operation
const endTime = Performance.now();
console.log(`Operation took ${endTime - startTime} milliseconds`);
```

---

## 🎯 Next Steps

1. **Read** the [API Reference](./api-reference.md) for component details
2. **Explore** the [API Reference](./api-reference.md) for UI components
3. **Check** the [Design System](./design-system.md) for styling guidelines
4. **Review** the [Troubleshooting Guide](./troubleshooting.md) for common issues

---

*This guide is your starting point for understanding and contributing to the Nestled app. For specific implementation details, refer to the other documentation files.*
