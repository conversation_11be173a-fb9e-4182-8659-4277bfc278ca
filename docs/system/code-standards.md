# Code Standards

## Naming Conventions

### Components
- Use PascalCase for component names
- Descriptive names without unnecessary prefixes
- Example: `PointsDisplay`, `<PERSON><PERSON>r<PERSON>and<PERSON>`

### Hooks
- Use camelCase starting with "use"
- Descriptive names indicating purpose
- Example: `usePointsSystemSupabase`, `useDailyQuestions`

### Files
- Match component/hook names exactly
- Use kebab-case for directories
- Example: `src/journeys/daily/useDailyQuestions.ts`

## Documentation Standards

### Component Documentation
All components must include:
- JSDoc comments for all props and functions
- TypeScript interfaces with descriptions
- Usage examples in comments

### Example Documentation Format
```typescript
/**
 * Daily Questions Hook
 *
 * Manages daily question delivery, responses, and partner interactions.
 */

interface DailyQuestionsProps {
  /** User ID for question assignment */
  userId: string;
  /** Optional couple ID for partner interactions */
  coupleId?: string;
}

/**
 * Hook for daily questions functionality
 * @param props - Configuration options
 * @returns Question state and actions
 */
export const useDailyQuestions = (props: DailyQuestionsProps) => {
  // Implementation
};
```

## Design System Standards

### No Hardcoded Values
- Use design tokens from `src/shared/components/design-system`
- No hardcoded colors, spacing, or typography
- All values must be centralized and reusable

### Import Patterns
```typescript
// ✅ Correct - use design system
import { Colors, Spacing, Typography } from 'src/shared/components';

// ❌ Never hardcode values
const styles = {
  backgroundColor: '#9CAF88',
  padding: 16,
  fontSize: 18
};
```

## Journey Architecture Standards

### Import Structure
```typescript
// Journey-specific imports
import { useAuth } from 'src/journeys/onboarding';
import { useDailyQuestions } from 'src/journeys/daily';

// Shared imports
import { Button, Colors } from 'src/shared/components';
import { dateUtils } from 'src/shared/utils';
```

### Dependency Rules
- Journeys can only import from `shared/` and `onboarding/`
- Shared code cannot import from journeys
- No cross-journey imports (except onboarding)
